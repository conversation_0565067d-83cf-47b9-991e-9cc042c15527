.container-menu {
  .ant-menu-submenu-title {
    .ant-menu-title-content {
      font-weight: bold;
    }
  }

  .ant-menu-title-content {
    font-size: 12px !important;
  }

  .ant-menu-inline .ant-menu-item {
    height: 32px;
  }

  .ant-menu-submenu-title {
    height: 32px !important;
  }

  .ant-menu-title-content {
    margin-inline-end: 10px;
  }

  .icon-subMenu {
    font-size: 12px !important;
    position: absolute;
    top: 50%;
    inset-inline-end: 16px;
    width: 10px;
    color: currentcolor;
    transform: translateY(-50%);
    transition:
      transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),
      opacity 0.3s;
  }

  .ant-menu-item-group-title {
    padding-inline-start: 50px !important;
    font-size: 12px;
    font-weight: 600;
    line-height: 20px;
    text-align: left;
    padding: 8px 0;
    margin-top: 10px;
  }
  .no-results {
    .ant-menu-title-content {
      text-align: center;
    }
  }
}
