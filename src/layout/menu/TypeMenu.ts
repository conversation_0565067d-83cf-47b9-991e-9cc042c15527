// type menu
export interface MenuItem {
  key: string;
  label: string;
  path?: string;
  children?: MenuItem[];
  permission?: string[][];
}

export interface MenuGroup {
  key: string;
  label: string;
  children: MenuItem[];
  permission?: string[][];
}

export interface SubMenuData {
  key: string;
  label: string;
  icon?: React.ReactNode;
  children: MenuItem[];
  groups: MenuGroup[];
  path?: string;
  permission?: string[][];
}
