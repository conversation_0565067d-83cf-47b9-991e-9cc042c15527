import { <PERSON><PERSON>, Badge, Col, Dropdown, Flex, Layout, MenuProps, Row } from 'antd';
import { BellOutlined, MenuOutlined } from '@ant-design/icons';

import './HeaderLayout.scss';
import Logo from '../../components/logo/Logo';
import { usePage } from '../../context/PageContext';
import { useNavigate } from 'react-router-dom';
import { sendLogoutRequest } from '../../service/clients/auth.service';
import { UserProfile } from '../../types/infoUser';

const { Header } = Layout;
type Props = {
  dataAccount?: UserProfile;
};

function HeaderLayout(props: Props) {
  const { dataAccount } = props;
  const jobTitle = dataAccount?.jobTitles?.[0];
  const navigate = useNavigate();
  const { isOpenSideBar, setOpenSideBar } = usePage();

  const handlerLogout = async () => {
    await sendLogoutRequest();
  };

  const items: MenuProps['items'] = [
    {
      key: '1',
      label: dataAccount?.fullName || '-',
    },
    {
      key: '2',
      label: dataAccount?.username || '-',
      disabled: true,
    },
    {
      key: '3',
      label: `${jobTitle?.jobTitleCode} - ${jobTitle?.jobTitleName}`,
      disabled: true,
    },
    {
      type: 'divider',
    },
    {
      key: '4',
      label: 'Tài khoản',
    },
    {
      key: '5',
      label: <span className="logout">Đăng xuất</span>,
      onClick: () => handlerLogout(),
    },
  ];

  return (
    <Header className="container-header">
      <Row style={{ height: '100%' }}>
        <Col span={12}>
          {!isOpenSideBar && (
            <Flex style={{ height: '100%' }} className={'header-sider'} justify="flex-start" align="center">
              <MenuOutlined
                onClick={() => {
                  setOpenSideBar(!isOpenSideBar);
                }}
                className="menuOutlined"
                size={24}
              />
              <Logo onClick={() => navigate('/')} />
            </Flex>
          )}
        </Col>
        <Col span={12}>
          <Flex style={{ height: '100%' }} justify="flex-end" align="center">
            <Badge dot>
              <Avatar
                className="avatar-bellOutlined"
                size={24}
                shape="circle"
                icon={<BellOutlined style={{ fontSize: '16px' }} />}
              />
            </Badge>
            <Dropdown overlayClassName="dropdown-account" menu={{ items }} trigger={['click']} placement="bottomRight">
              <Avatar size={24} className="avatar" shape="square">
                A
              </Avatar>
            </Dropdown>
          </Flex>
        </Col>
      </Row>
    </Header>
  );
}

export default HeaderLayout;
