.container-header {
  padding: 0;
  width: 100%;
  line-height: initial;
  min-height: 40px;
  height: 40px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  background: var(--colorPrimaryTextHover, #003eb3);

  padding: 0 24px;

  .avatar {
    border: none;
    margin-left: 16px;
    cursor: pointer;
  }
  .avatar-bellOutlined {
    cursor: pointer;
  }

  .header-sider {
    .menuOutlined {
      color: white;
      margin-right: 12px;
    }
    .container-logo {
      .text-logo {
        color: white;
      }
    }
  }
}

.dropdown-account {
  width: 314px;
  inset: 40px 3px auto auto !important;

  .ant-dropdown-menu {
    border-radius: 0;

    .ant-dropdown-menu-item {
      .logout {
        color: var(--Dropdown-colorError, rgba(255, 77, 79, 1));
      }
    }
  }
}
