import Sider from 'antd/es/layout/Sider';

import MenuCustom from '../menu/MenuCustom';

import './SideBarLayout.scss';
import { Flex, Input } from 'antd';
import { MenuOutlined, SearchOutlined } from '@ant-design/icons';
import Logo from '../../components/logo/Logo';
import { usePage } from '../../context/PageContext';

const SideBarLayout = () => {
  const { isOpenSideBar, setOpenSideBar } = usePage();

  return (
    <Sider
      className="container-sider"
      trigger={null}
      collapsible
      collapsed={false}
      width={262}
      breakpoint="lg"
      collapsedWidth="80"
    >
      <Flex className={'header-sider'} justify="flex-start" align="center">
        <MenuOutlined
          onClick={() => {
            setOpenSideBar(!isOpenSideBar);
          }}
          className="menuOutlined"
          size={24}
        />
        <Logo />
      </Flex>
      <Flex className={'header-sider-search'} justify="flex-start" align="center">
        <Input
          placeholder="Tìm kiếm"
          suffix={
            <SearchOutlined
              style={{
                color: ' var(--Select-colorTextPlaceholder, rgba(0, 0, 0, 0.25))',
              }}
            />
          }
        />
      </Flex>
      <MenuCustom />
    </Sider>
  );
};

export default SideBarLayout;
