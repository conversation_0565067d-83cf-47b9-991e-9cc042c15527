import { But<PERSON>, Divider, <PERSON> } from 'antd';
import { ReactNode } from 'react';
import BreadCrumbComponent from '../../components/breadCrumb';
import './styles.scss';

interface Props {
  titleBreadCrumb?: string;
  children?: ReactNode;
  handleSubmit?: () => void;
  handleCancel?: () => void;
  isEditableSubmit?: boolean;
  loadingSubmit?: boolean;
}

const PageLayoutDetail = (props: Props) => {
  const { titleBreadCrumb, children, handleSubmit, handleCancel, isEditableSubmit, loadingSubmit } = props;

  return (
    <div className="layout-detail">
      <BreadCrumbComponent titleBread={titleBreadCrumb} />
      <>{children}</>
      <div className="footer">
        <Divider />
        <Space size={'middle'}>
          <Button size="small" type="primary" onClick={handleCancel}>
            Huỷ
          </Button>
          <Button
            size="small"
            type="default"
            onClick={handleSubmit}
            disabled={isEditableSubmit}
            loading={loadingSubmit}
          >
            <PERSON><PERSON><PERSON> thay đổi
          </Button>
        </Space>
      </div>
    </div>
  );
};

export default PageLayoutDetail;
