import { QueryKey } from '@tanstack/react-query';
import { Pagination } from 'antd';
import { useEffect, useState } from 'react';
import { useSubscribeList, useSubscribeQuery } from '../../hooks';
import useFilter from '../../hooks/filter';
import './styles.scss';

type PaginationProps = {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
};

export default function PaginationComponent({
  queryKeyArr,
  defaultFilter, // truyền vào filter mặc định bên cạnh page và pageSize, vd: type: 'all'
  onChangePagination,
  isWithParamsUrl = true,
}: {
  queryKeyArr?: QueryKey;
  defaultFilter?: object;
  onChangePagination?: (page: string, pageSize: string) => void;
  isWithParamsUrl?: boolean;
}) {
  const [filter, setFilter] = useFilter();
  const [pagination, setPagination] = useState<PaginationProps>();
  const subscribeListData = useSubscribeList(queryKeyArr, defaultFilter);
  const subscribeQueryData = useSubscribeQuery(queryKeyArr || []);

  const data = !isWithParamsUrl ? subscribeQueryData : subscribeListData;
  const dataPage = data?.data?.data;

  useEffect(() => {
    if (dataPage) {
      setPagination(dataPage);
    }
  }, [dataPage]);

  const handlePaginationChange = (page: number, pageSize: number) => {
    setPagination(prev => ({
      ...prev,
      page: page,
      pageSize: pageSize,
      total: prev?.total || 0,
      totalPages: prev?.totalPages || 0,
    }));

    // Gửi dữ liệu phân trang lên component cha
    onChangePagination?.(String(page), String(pageSize));
  };

  return (
    <Pagination
      className="pagination"
      total={pagination?.total || 1}
      current={pagination?.page || 1}
      pageSize={pagination?.pageSize || 10}
      onChange={(page, size) => {
        !isWithParamsUrl
          ? handlePaginationChange(page, size)
          : setFilter({
              ...filter,
              page: String(page),
              pageSize: String(size),
            });
      }}
      showSizeChanger
      pageSizeOptions={[5, 10, 20, 50, 100]}
    />
  );
}
