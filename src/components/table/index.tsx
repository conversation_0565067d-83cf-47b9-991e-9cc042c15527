import { ConfigProvider, Empty, Table, TableProps } from 'antd';
import PaginationComponent from '../pagination/Pagination';
import './styles.scss';
import { QueryKey } from '@tanstack/react-query';
interface Props extends TableProps {
  queryKeyArr: QueryKey;
  heightIsSubtractedScroll?: number;
  isPagination?: boolean;
  defaultFilter?: object;
  onChangePagination?: (page: string, pageSize: string) => void;
  isWithParamsUrl?: boolean;
}

const TableComponent = (props: Props) => {
  const {
    loading,
    columns,
    queryKeyArr,
    scroll,
    heightIsSubtractedScroll = 300,
    isPagination = true,
    defaultFilter, //truyền vào filter mặc định bên cạnh page và pageSize, vd: type: 'all'
    onChangePagination,
    isWithParamsUrl = true,
    ...restProps
  } = props;

  return (
    <div className="wrap-table">
      <ConfigProvider renderEmpty={() => <Empty description="Danh sách hiện tại đang trống" />}>
        <Table
          {...restProps}
          columns={columns}
          loading={loading as boolean}
          scroll={
            scroll || {
              x: '1000px',
              y: `calc(100vh - ${heightIsSubtractedScroll}px)`,
              scrollToFirstRowOnChange: true,
            }
          }
          pagination={false}
        />
      </ConfigProvider>
      {isPagination && (
        <PaginationComponent
          queryKeyArr={queryKeyArr}
          defaultFilter={defaultFilter}
          onChangePagination={onChangePagination}
          isWithParamsUrl={isWithParamsUrl}
        />
      )}
    </div>
  );
};

export default TableComponent;
