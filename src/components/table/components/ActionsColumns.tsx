import { MoreOutlined } from '@ant-design/icons';
import { Button, Dropdown, MenuProps } from 'antd';
import { ReactNode } from 'react';
import { Link } from 'react-router-dom';

export interface IButtonAction {
  icon?: ReactNode;
  children?: ReactNode | string;
  handleAction?: () => void;
  path?: string;
}
export interface ItemCollapseAction {
  handleViewDetail?: () => void;
  handleCloneRole?: () => void;
  handleModified?: () => void;
  textModalConfirmActive?: string | ReactNode;
  handleActive?: () => void;
  handleDelete?: () => void;
  moreActions?: MenuProps['items'];
  handlerActive?: () => void;
  disabled?: boolean;
  overlayClassName?: string;
  handleViewInsuranceProgram?: () => void;
  handleEdit?: () => void;
  handleAdjust?: () => void;
  handlePrint?: () => void;
  handleApprove?: () => void;
  handleCancel?: () => void;
}
export const ActionsColumns = ({
  handleViewDetail,
  handleCloneRole,
  textModalConfirmActive,
  handleActive,
  handleDelete,
  moreActions,
  handlerActive,
  disabled,
  overlayClassName,
  handleViewInsuranceProgram,
  handleEdit,
  handleAdjust,
  handlePrint,
  handleApprove,
  handleCancel,
}: ItemCollapseAction) => {
  const items: MenuProps['items'] = [
    handleViewDetail && {
      label: 'Xem chi tiết',
      key: 1,
      onClick: () => handleViewDetail(),
    },
    handleActive && {
      label: textModalConfirmActive,
      key: 2,
      onClick: () => handleActive(),
    },
    handleCloneRole && {
      label: 'Tạo bản sao',
      key: 3,
      onClick: () => handleCloneRole(),
    },
    handleDelete && {
      label: 'Xóa',
      key: 4,
      onClick: () => handleDelete(),
    },
    handlerActive && {
      label: 'Chỉnh sửa trạng thái',
      key: 5,
      onClick: () => handlerActive(),
    },
    handleViewInsuranceProgram && {
      label: 'Vận hành CTBH',
      key: 6,
      onClick: () => handleViewInsuranceProgram(),
    },
    handleEdit && {
      label: 'Chỉnh sửa',
      key: 7,
      onClick: () => handleEdit(),
    },
    handleAdjust && {
      label: 'Điều chỉnh',
      key: 8,
      onClick: () => handleAdjust(),
    },
    handleApprove && {
      label: 'Duyệt',
      key: 10,
      onClick: () => handleApprove(),
    },
    handlePrint && {
      label: 'In',
      key: 9,
      onClick: () => handlePrint(),
    },

    handleCancel && {
      label: 'Hủy phiếu',
      key: 11,
      onClick: () => handleCancel(),
    },
    ...(moreActions ?? []),
  ].filter(Boolean) as MenuProps['items'];
  return (
    <Dropdown
      menu={{ items }}
      placement="bottomRight"
      overlayStyle={{ width: 150 }}
      overlayClassName={overlayClassName}
      disabled={disabled}
    >
      <Button type="text" style={{ padding: '4px 8px' }}>
        <MoreOutlined style={{ fontSize: '20px', cursor: 'pointer' }} />
      </Button>
    </Dropdown>
  );
};

export const SingleActionColumns = ({ handleAction, icon, children = 'Xem chi tiết', path }: IButtonAction) => {
  return path ? (
    <Link to={path}>
      <Button type="primary" icon={icon}>
        {children}
      </Button>
    </Link>
  ) : (
    <Button type="primary" icon={icon} onClick={() => handleAction?.()}>
      {children}
    </Button>
  );
};
