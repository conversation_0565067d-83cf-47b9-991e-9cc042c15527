import { <PERSON><PERSON>, Col, Dropdown, Flex, Modal, Row, Typography } from 'antd';
import { MoreOutlined } from '@ant-design/icons';
import { useDeleteField } from '../../hooks';
import { AxiosResponse } from 'axios';
import { IItemDocument } from '.';

const { confirm } = Modal;

type TDataMutation = {
  data: Response;
};

export interface IDropdownAction {
  //   form: FormInstance;
  record: IItemDocument;
  isLoadingDelete?: boolean;
  type?: string;
  modelId?: string;
  stateIdIteamSelect?: string;
  handleViewDetailDocument?: (item: IItemDocument) => Promise<void>;
  handleChangeName?: () => void;
  handleConfirmDeleteDocument?: (id: string, type: string) => Promise<TDataMutation | undefined>;
  downloadFile?: (path: string, fileName: string) => void;
  deleteDocument: (params: IItemDocument) => Promise<AxiosResponse>;
  deleteItemDocument: (params: { documentId?: string; itemId?: string }) => Promise<AxiosResponse>;
}
function DropDownAction(props: IDropdownAction) {
  const {
    record,
    stateIdIteamSelect,
    type,
    handleViewDetailDocument,
    handleChangeName,
    deleteDocument,
    modelId,
    deleteItemDocument,
  } = props;

  const downloadFile = (path: string | undefined, fileName: string | undefined) => {
    const link = document.createElement('a');
    link.href = `${import.meta.env.VITE_S3_IMAGE_URL}/${path}`;
    link.download = fileName ?? '';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const { mutateAsync: handleDeleteDocument, isPending: isLoadingDelete } = useDeleteField({
    apiQuery: deleteDocument,
    keyOfDetailQuery: ['getDocument', modelId],
    label: 'tài liệu',
  });

  const { mutateAsync: handleDeleteFileDocument } = useDeleteField({
    apiQuery: deleteDocument,
    keyOfDetailQuery: ['getDocument', modelId],
    label: 'tài liệu',
  });

  const { mutateAsync: handleDeleteItemDocument } = useDeleteField({
    apiQuery: deleteItemDocument,
    keyOfDetailQuery: ['getDocumentItems', stateIdIteamSelect],
    label: 'tài liệu',
  });

  const handleConfirmDeleteDocument = async (id: string, type: string | undefined) => {
    try {
      if (stateIdIteamSelect)
        return await handleDeleteItemDocument({
          documentId: stateIdIteamSelect,
          itemId: id,
        });
      if (type === 'file') return await handleDeleteFileDocument({ id });
      return await handleDeleteDocument({ id });
    } catch (error) {
      console.log('');
    } finally {
      Modal.destroyAll();
    }
  };
  const actionItems = [
    {
      label: 'Xem chi tiết',
      key: 1,
      onClick: () => handleViewDetailDocument && handleViewDetailDocument(record),
    },
    {
      label: 'Đổi tên',
      key: 2,
      onClick: () => handleChangeName && handleChangeName(),
    },
    {
      label: 'Xoá',
      key: 3,
      onClick: async () => {
        confirm({
          title: 'Xác nhận xoá tài liệu',
          content: (
            <>
              <Row>
                <Col span={24}>
                  <Typography.Text>
                    Tài liệu đã xoá và sẽ không thể phục hồi, bạn có chắc chắn muốn xoá không?
                  </Typography.Text>
                </Col>
              </Row>
              <Row>
                <Col span={24}>
                  <Flex gap="small" justify="end" style={{ marginTop: '16px' }}>
                    <Button
                      onClick={() => {
                        Modal.destroyAll();
                      }}
                      type="primary"
                    >
                      Hủy
                    </Button>
                    <Button
                      onClick={() => {
                        handleConfirmDeleteDocument && handleConfirmDeleteDocument(record?.id, record.type);
                      }}
                      loading={isLoadingDelete}
                    >
                      Xoá tài liệu
                    </Button>
                  </Flex>
                </Col>
              </Row>
            </>
          ),
          footer: null,
        });
      },
    },
  ];
  const fileActionItems = [
    {
      label: 'Tải xuống',
      key: 1,
      onClick: () => downloadFile && downloadFile(record.path, record.name),
    },
    {
      label: 'Xoá',
      key: 3,
      onClick: async () => {
        confirm({
          title: 'Xác nhận xoá tài liệu',
          content: (
            <>
              <Row>
                <Col span={24}>
                  <Typography.Text>
                    Tài liệu đã xoá và sẽ không thể phục hồi, bạn có chắc chắn muốn xoá không?
                  </Typography.Text>
                </Col>
              </Row>
              <Row>
                <Col span={24}>
                  <Flex gap="small" justify="end" style={{ marginTop: '16px' }}>
                    <Button
                      onClick={() => {
                        Modal.destroyAll();
                      }}
                      type="primary"
                    >
                      Hủy
                    </Button>
                    <Button
                      onClick={() => {
                        handleConfirmDeleteDocument && handleConfirmDeleteDocument(record.id, record.type);
                      }}
                      loading={isLoadingDelete}
                    >
                      Xoá tài liệu
                    </Button>
                  </Flex>
                </Col>
              </Row>
            </>
          ),
          footer: null,
        });
      },
    },
  ];

  const items = type === 'file' ? fileActionItems : actionItems;

  return (
    <Dropdown
      menu={{
        items,
      }}
      placement="bottomRight"
      overlayStyle={{ width: 150 }}
    >
      <Button type="text" style={{ padding: '4px 8px' }}>
        <MoreOutlined style={{ fontSize: '20px', cursor: 'pointer' }} />
      </Button>
    </Dropdown>
  );
}

export default DropDownAction;
