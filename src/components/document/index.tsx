import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Col,
  Flex,
  Form,
  FormProps,
  Input,
  Modal,
  Row,
  TableColumnsType,
  Typography,
  Upload,
} from 'antd';
import { RcFile } from 'antd/es/upload';
import { AxiosError, AxiosResponse } from 'axios';
import TableComponent from '../table';
import React, { useMemo } from 'react';
import { useCreateField, useFetch, useUpdateField } from '../../hooks';
import { FileTextOutlined, FolderOutlined, PlusOutlined, UploadOutlined } from '@ant-design/icons';
import DropDownAction from './DropdownAction';
import { ItemType } from 'antd/lib/breadcrumb/Breadcrumb';
import { uploadMedia } from '../../service/upload';
import { useParams } from 'react-router-dom';
import './styles.scss';

const { confirm } = Modal;

export interface IItemDocument {
  id: string;
  name?: string;
  path?: string;
  sizeFile?: number;
  type?: string;
}

export interface IDocument {
  id: string;
  //   modelId: string;
  type: string;
  name: string;
  path: string;
  sizeFile: number;
  items: IItemDocument[];
  softDelete: boolean;
  createdDate: Date;
  updatedDate: Date;
}

export interface IFilterDocument {
  page?: number;
  pageSize?: number;
  search?: string;
  customerID?: string;
}

export interface IFormDocument {
  // modelId?: string;
  name?: string;
  id?: string;
  type?: string;
  path?: string;
  sizeFile?: number;
}

interface IItemTypeBreadcrumb extends ItemType {
  label?: string;
}

interface Props {
  //   modelId?: string;
  keyQueryList: string;
  keyQueryItems: string;
  modelIdFieldName?: string;
  //   dataDocument?: FetchResponse<T[]>;
  getDocument: <T extends IFilterDocument>(params: T) => Promise<AxiosResponse>;
  getDocumentItems: (params: { documentId?: string }) => Promise<AxiosResponse>;
  addDocument: <T extends IFormDocument>(params: T) => Promise<AxiosResponse>;
  updateDocument: <T extends IFormDocument>(params: T) => Promise<AxiosResponse>;
  addItemsDocument: <T extends IItemDocument>(params: T) => Promise<AxiosResponse>;
  deleteDocument: <T extends IFormDocument>(params: T) => Promise<AxiosResponse>;
  deleteItemDocument: (params: { documentId?: string; itemId?: string }) => Promise<AxiosResponse>;
}

const DocumentComponent = <T extends IDocument>(props: Props) => {
  const [form] = Form.useForm();
  const { id: modelId } = useParams<{ id: string }>();

  const {
    // modelId,
    keyQueryList,
    keyQueryItems,
    modelIdFieldName,
    getDocument,
    getDocumentItems,
    addDocument,
    updateDocument,
    addItemsDocument,
    deleteDocument,
    deleteItemDocument,
  } = props;

  const [isFormChanged, setIsFormChanged] = React.useState<boolean>(false);
  const [stateShowModal, setStateShowModal] = React.useState<boolean>(false);
  const [stateIsLoadingUpload, setStateIsLoadingUpload] = React.useState<boolean>(false);

  const [stateIdIteamSelect, setStateIdIteamSelect] = React.useState<string>('');
  const [stateBreadcrumb, setStateBreadcrumb] = React.useState<IItemTypeBreadcrumb[]>([
    {
      title: 'Danh sách tài liệu',
    },
  ]);

  const {
    data: dataDocument,
    isFetching,
    refetch: refetchDocument,
  } = useFetch<IDocument[]>({
    queryKeyArr: [keyQueryList, modelId],
    api: getDocument,
    moreParams: { [`${modelIdFieldName}ID`]: modelId },
  });

  const { data: dataDocumentItems, isFetching: isFetchingDocumentItems } = useFetch<T>({
    queryKeyArr: [keyQueryItems, stateIdIteamSelect],
    api: getDocumentItems,
    moreParams: { documentId: stateIdIteamSelect },
    enabled: !!stateIdIteamSelect,
  });

  const listDocumentItems = useMemo(() => {
    const data = dataDocumentItems?.data.data;
    const items = data?.items || ([] as IItemDocument[]);
    return items.map(item => ({
      ...item,
      type: 'file',
    }));
  }, [dataDocumentItems]);

  const listDocument = useMemo(() => {
    return dataDocument?.data?.data || [];
  }, [dataDocument]);

  const { mutateAsync: handleCreateDocument, isPending: isLoadingCreate } = useCreateField({
    apiQuery: addDocument,
    keyOfDetailQuery: [keyQueryList, modelId],
    label: 'thư mục',
    isMessageError: false,
  });

  const { mutateAsync: handleCreateFolderDocument } = useCreateField({
    apiQuery: addDocument,
    keyOfDetailQuery: [keyQueryList, modelId],
    // label: 'tài liệu',
    isMessageError: false,
    messageSuccess: 'Tải nhập tài liệu thành công',
  });

  const { mutateAsync: handleUpdateDocument, isPending: isLoadingUpdate } = useUpdateField({
    apiQuery: updateDocument,
    keyOfDetailQuery: [keyQueryList, modelId],
    label: 'tài liệu',
    // label: 'thư mục',
    messageSuccess: 'Đổi tên thành công!',
    isMessageError: false,
  });

  const { mutateAsync: handleAddItemsDocument } = useUpdateField({
    apiQuery: addItemsDocument,
    keyOfDetailQuery: [keyQueryItems, stateIdIteamSelect],
    // label: 'tài liệu',
    isMessageError: false,
    messageSuccess: 'Tải nhập tài liệu thành công',
    isMessageSuccess: true,
  });

  const handleViewDetailDocument = async (item: IItemDocument) => {
    setStateIdIteamSelect(item.id);
    setStateBreadcrumb([
      {
        title: <span style={{ cursor: 'pointer' }}>Danh sách tài liệu</span>,
        onClick: () => {
          setStateBreadcrumb([
            {
              title: 'Danh sách tài liệu',
            },
          ]);
          setStateIdIteamSelect('');
          refetchDocument();
        },
      },
      {
        title: (
          <>
            <FolderOutlined />
            <span> {item.name}</span>
          </>
        ),
        label: item.name,
      },
    ]);
  };

  const onFinish: FormProps<IDocument>['onFinish'] = async values => {
    try {
      if (!values.name?.trim()) {
        return form.setFields([
          {
            name: 'name',
            errors: ['Vui lòng nhập tên thư mục'],
          },
        ]);
      }
      const params = {
        ...values,
        [`${modelIdFieldName}ID`]: modelId,
        type: 'folder',
      };
      if (!values.id) {
        setStateShowModal(!stateShowModal);
        setIsFormChanged(false);
        form.resetFields();
        return await handleCreateDocument(params);
      } else {
        setStateShowModal(!stateShowModal);
        setIsFormChanged(false);
        form.resetFields();
        return await handleUpdateDocument(params);
      }
    } catch (error) {
      console.log('');
    }
  };

  const handleCancel = React.useCallback(() => {
    if (isFormChanged) {
      Modal.confirm({
        title: 'Xác nhận hủy',
        content: 'Dữ liệu đang chưa được lưu, bạn có chắc muốn huỷ dữ liệu đang nhập không?',
        cancelText: 'Quay lại',
        okButtonProps: {
          type: 'default',
        },
        cancelButtonProps: {
          type: 'primary',
        },
        onOk: () => {
          setStateShowModal(false);
          setIsFormChanged(false);
          form.resetFields();
        },
      });
    } else {
      setStateShowModal(false);
      setIsFormChanged(false);
      form.resetFields();
    }
  }, [form, isFormChanged]);

  const formatFileSize = (size: number) => {
    if (size >= 1024 * 1024 * 1024) {
      return `${(size / (1024 * 1024 * 1024)).toFixed(1)} GB`;
    }
    if (size >= 1024 * 1024) {
      return `${(size / (1024 * 1024)).toFixed(1)} MB`;
    }
    if (size >= 1024) {
      return `${(size / 1024).toFixed(1)} KB`;
    }
    return `${size} B`; // Nếu file rất nhỏ (< 1KB)
  };

  const columns: TableColumnsType<T> = [
    {
      title: '',
      dataIndex: 'name',
      key: 'name',
      width: '60%',
      render: (text: string, record) => (
        <Flex align="center">
          {record.type === 'folder' && (
            <div className="icon-type-cell">
              <FolderOutlined />
            </div>
          )}

          {record.type === 'file' && (
            <div className="icon-type-cell">
              <FileTextOutlined />
            </div>
          )}
          <div className="file-name">
            <Typography.Text ellipsis={{ tooltip: true }}>{text}</Typography.Text>
          </div>
        </Flex>
      ),
    },
    {
      title: 'Dung lượng',
      dataIndex: 'sizeFile',
      key: 'sizeFile',
      width: '20%',
      align: 'center',
      render: (text: number, record) => {
        let sizeFile = text; // Mặc định lấy size từ dataIndex

        // Nếu là folder, tính tổng dung lượng của tất cả items
        if (record.type === 'folder') {
          sizeFile = record.items.reduce(
            (total: number, item: IItemDocument) => total + (item['sizeFile'] ? item['sizeFile'] : 0) || 0,
            0,
          );
        }

        return sizeFile > 0 ? <Typography.Text>{formatFileSize(sizeFile)}</Typography.Text> : <></>;
      },
    },
    {
      title: 'Hành động',
      dataIndex: 'address',
      key: 'address',
      width: '20%',
      align: 'center',
      render: (_, record) => {
        const handleChangeName = () => {
          form.setFieldsValue({
            name: record.name,
            id: record.id,
          });
          setStateShowModal(true);
        };
        return (
          <DropDownAction
            record={record}
            stateIdIteamSelect={stateIdIteamSelect}
            type={record?.type}
            handleViewDetailDocument={handleViewDetailDocument}
            handleChangeName={handleChangeName}
            deleteDocument={deleteDocument}
            modelId={modelId}
            deleteItemDocument={deleteItemDocument}
          />
        );
      },
    },
  ];

  return (
    <div>
      <Row>
        <Col span={24}>
          <Breadcrumb items={stateBreadcrumb} />
        </Col>
      </Row>
      <Row>
        <Col span={24}>
          <Flex gap="small" wrap justify="flex-end" align="center">
            <Upload
              listType="picture"
              maxCount={1}
              showUploadList={false} // Ẩn danh sách file đã upload
              disabled={stateIsLoadingUpload}
              customRequest={async ({ file, onSuccess, onError }) => {
                try {
                  setStateIsLoadingUpload(true);
                  const fileData = file as RcFile;
                  const isLtFile = fileData.size / 1024 / 1024 > 100;
                  if (isLtFile) {
                    confirm({
                      title: 'Không thể upload file',
                      icon: null,
                      content: 'Kích thước file vượt quá giới hạn 100MB',
                      footer: [
                        <Flex style={{ marginTop: '10px' }} gap="small" justify="end">
                          <Button
                            key="submit"
                            type="primary"
                            size="small"
                            onClick={() => {
                              Modal.destroyAll();
                            }}
                            style={{ width: '50%' }}
                          >
                            Đóng
                          </Button>
                        </Flex>,
                      ],
                    });
                    return;
                  }
                  const resp = await uploadMedia(
                    fileData,
                    stateBreadcrumb[1]
                      ? `${modelId}/${modelIdFieldName}s-document/${stateBreadcrumb[1].label}`
                      : `${modelId}/${modelIdFieldName}s-document`,
                  );
                  const data = resp.data.data;
                  if (stateIdIteamSelect)
                    return handleAddItemsDocument({
                      name: fileData.name,
                      sizeFile: fileData.size,
                      path: data.key || data.Key,
                      id: stateIdIteamSelect,
                    });
                  await handleCreateFolderDocument({
                    name: fileData.name,
                    [`${modelIdFieldName}ID`]: modelId,
                    type: 'file',
                    sizeFile: fileData.size,
                    path: data.key || data.Key,
                  });
                  onSuccess?.('ok');
                } catch (error: unknown) {
                  onError?.(error as AxiosError);
                } finally {
                  setStateIsLoadingUpload(false);
                }
              }}
            >
              <Button loading={stateIsLoadingUpload} icon={<UploadOutlined />}>
                Tải nhập file
              </Button>
            </Upload>
            {!stateIdIteamSelect && (
              <Button
                onClick={() => {
                  setStateShowModal(!stateShowModal);
                }}
                icon={<PlusOutlined />}
                type="primary"
              >
                Tạo mới Folder
              </Button>
            )}
          </Flex>
        </Col>
      </Row>
      <Row style={{ marginTop: '16px' }}>
        <Col span={24}>
          <TableComponent
            queryKeyArr={[keyQueryList, modelId]}
            columns={columns}
            loading={isFetching || isFetchingDocumentItems}
            dataSource={stateIdIteamSelect ? listDocumentItems : listDocument}
            rowKey="id"
            isPagination={false}
          />
        </Col>
      </Row>

      <Modal className="ModalFormFolder" title="Tên thư mục" open={stateShowModal} closeIcon={false} footer={null}>
        <Form
          form={form}
          layout="vertical"
          name="basic"
          initialValues={{ remember: true }}
          onValuesChange={() => setIsFormChanged(!isFormChanged)}
          onFinish={onFinish}
        >
          <Form.Item
            name="name"
            rules={[
              { required: true, message: 'Vui lòng nhập tên thư mục' },
              { max: 200, message: 'Tên thư mục không được vượt quá 200 ký tự' },
            ]}
          >
            <Input maxLength={200} placeholder="Nhập tên thư mục" />
          </Form.Item>
          <Form.Item name="id" style={{ display: 'none' }}>
            <Input placeholder="Nhập tên thư mục" />
          </Form.Item>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Button
                onClick={() => {
                  handleCancel();
                }}
                style={{ width: '100%', backgroundColor: '#f0f0f0' }}
              >
                Hủy
              </Button>
            </Col>
            <Col span={12}>
              <Button
                htmlType="submit"
                loading={isLoadingUpdate || isLoadingCreate}
                type="primary"
                style={{ width: '100%' }}
              >
                Tạo
              </Button>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
};

export default DocumentComponent;
