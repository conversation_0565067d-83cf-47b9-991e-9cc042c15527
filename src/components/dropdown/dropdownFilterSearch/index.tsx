import { FilterOutlined } from '@ant-design/icons';
import { Button, Dropdown, Form, FormInstance, InputProps } from 'antd';
import { SearchProps } from 'antd/es/input';
import { Input } from 'antd/lib';
import debounce from 'lodash/debounce';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useLocation } from 'react-router-dom';
import useFilter from '../../../hooks/filter';
import { normalizeString } from '../../../utilities/regex';
import { hasNonEmptyValues } from '../../../utilities/shareFunc';
import './styles.scss';

export interface IFilterSearch {
  keySearch?: string;
  showParams?: boolean;
  onChangeSearch?: (e: string) => void;
  placeholder?: string;
  rootClassName?: string;
  submitFilter?: (values: { [key: string]: unknown }) => void;
  extraFormItems?: React.ReactNode;
  initialValues?: { [key: string]: unknown };
  handleOpenChange: (value: boolean) => void;
  isOpenFilter: boolean;
  form?: FormInstance;
  onClearFilters?: () => void;
  defaultValueSearch?: string;
  keyInputSearch?: string;
  searchButtonText?: string;
  isReadOnly?: boolean;
}

function DropdownFilterSearch(props: IFilterSearch) {
  const {
    placeholder = 'Tìm kiếm',
    keySearch = 'search',
    onChangeSearch,
    showParams = true,
    rootClassName,
    submitFilter,
    extraFormItems,
    initialValues,
    handleOpenChange,
    isOpenFilter,
    form,
    onClearFilters,
    defaultValueSearch = '',
    keyInputSearch,
    searchButtonText,
    isReadOnly = false,
  } = props;
  const [filter, setFilter] = useFilter();
  const { search } = useLocation();
  const params = useMemo(() => new URLSearchParams(search), [search]);
  const [hasValues, setHasValues] = useState(false);
  const formValues = Form?.useWatch([], form);

  const debouncedHandleSearch = useMemo(
    () =>
      debounce((searchTerm: string) => {
        const normalizeSearchTerm = normalizeString(searchTerm);
        showParams
          ? setFilter({ ...filter, page: '1', [keySearch]: normalizeSearchTerm })
          : onChangeSearch?.(normalizeSearchTerm);
      }, 1000),
    [showParams, keySearch, filter, setFilter, onChangeSearch],
  );

  const handleChange: InputProps['onChange'] = event => {
    const searchTerm = event.target.value;
    debouncedHandleSearch(searchTerm);
  };

  const handleOnSearch: SearchProps['onSearch'] = value => {
    const normalizeSearchTerm = normalizeString(value);
    showParams
      ? setFilter({ ...filter, page: '1', [keySearch]: normalizeSearchTerm })
      : onChangeSearch?.(normalizeSearchTerm);
  };

  const checkFormValues = useCallback(() => {
    if (!form) return false;

    return hasNonEmptyValues(formValues);
  }, [form, formValues]);

  const hasInitialValues = useMemo(() => {
    return hasNonEmptyValues(initialValues);
  }, [initialValues]);

  useEffect(() => {
    if (form) {
      const hasFormValues = hasNonEmptyValues(formValues);
      setHasValues(hasFormValues || hasInitialValues);
    } else {
      setHasValues(hasInitialValues);
    }
  }, [formValues, hasInitialValues, form]);

  const handleClearFilter = () => {
    setHasValues(false);
    form?.resetFields();
    onClearFilters?.();
    // Cập nhật URL params nếu cần
    if (showParams) {
      setFilter({ page: '1', pageSize: filter?.pageSize, search: filter?.search ?? '' });
    }
  };

  const handleFormValuesChange = () => {
    setHasValues(checkFormValues());
  };

  const filterContent = (
    <Form
      layout="vertical"
      onFinish={submitFilter}
      initialValues={initialValues}
      form={form}
      onValuesChange={handleFormValuesChange}
    >
      {extraFormItems}

      <Form.Item style={{ margin: 0 }}>
        <Button type="primary" htmlType="submit">
          {searchButtonText || 'Tìm kiếm'}
        </Button>
        <Button style={{ marginRight: '8px' }} onClick={handleClearFilter}>
          Xóa bộ lọc
        </Button>
      </Form.Item>
    </Form>
  );

  return (
    <Dropdown
      open={isOpenFilter}
      rootClassName={rootClassName}
      dropdownRender={() => <div className="wrapper-dropdown-content">{filterContent}</div>}
      onOpenChange={() => handleOpenChange(false)}
      trigger={['click']}
      placement="bottomRight"
    >
      <Input.Search
        readOnly={isReadOnly}
        key={keyInputSearch}
        className="filter-search"
        onChange={handleChange}
        defaultValue={showParams ? (params.get(keySearch) ?? '') : defaultValueSearch}
        suffix={
          <div onClick={() => handleOnSearch} style={{ cursor: 'pointer' }}>
            <FilterOutlined
              onClick={() => handleOpenChange(true)}
              style={{
                color: hasValues ? '#1677FF' : undefined,
              }}
            />
          </div>
        }
        placeholder={placeholder}
        allowClear
        onSearch={handleOnSearch}
        onClick={() => {
          if (isReadOnly) {
            handleOpenChange(true);
          }
        }}
      />
    </Dropdown>
  );
}

export default DropdownFilterSearch;
