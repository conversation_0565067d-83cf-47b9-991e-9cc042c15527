import * as React from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';

interface RickEditorProps {
  value: string;
  onChange: (value: string) => void;
  ref: React.RefObject<ReactQuill>;
  placeholder?: string;
}

const toolbarOptions = [
  [{ font: [] }],
  [{ header: ['1', '2', '3', '4', '5', false] }],
  [{ align: [] }],
  ['bold', 'italic', 'underline', 'strike'],
  [{ list: 'ordered' }, { list: 'bullet' }, { indent: '-1' }, { indent: '+1' }],
  [{ color: [] }, { background: [] }],
  [{ script: 'sub' }, { script: 'super' }],
  ['blockquote', 'code-block'],
  ['link', 'image', 'video', 'formula'],
  [{ direction: 'rtl' }],
  ['clean'],
];

export const RichEditor: React.FC<RickEditorProps> = ({ value, onChange, ref, placeholder }) => {
  return (
    <ReactQuill
      theme="snow"
      modules={{
        toolbar: toolbarOptions,
      }}
      placeholder={placeholder}
      onChange={onChange}
      value={value}
      ref={ref}
    />
  );
};
export default RichEditor;
