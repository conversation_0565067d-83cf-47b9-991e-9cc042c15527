import { DownOutlined, UpOutlined } from '@ant-design/icons';
import { Table } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import React from 'react';
import './styles.scss';

type Props<T> = {
  id: string;
  columns: ColumnsType<T>;
  dataSource: T[];
  level?: number; //level tree
  isLoading?: boolean;
  isFetching?: boolean;
  isPlaceholderData?: boolean;
};

interface RecordExpand {
  key: string;
  nameProject?: string;
  type?: string;
  code?: string;
  children?: RecordExpand[];
}

function TableExpandComponent<T extends RecordExpand>(props: Props<T>) {
  const { id, columns, dataSource, level = 1, isLoading, isFetching, isPlaceholderData } = props;
  const [expandedRowKeys, setExpandedRowKeys] = React.useState<string[]>([dataSource[0]?.key]);

  const handleRowClick = (record: T) => {
    const isExpanded = expandedRowKeys?.includes(record.key);
    const newKeys = isExpanded ? expandedRowKeys.filter(key => key !== record.key) : [...expandedRowKeys, record.key];
    setExpandedRowKeys(newKeys);
  };

  React.useEffect(() => {
    // Expand cha đầu tiên
    if (dataSource.length > 0) {
      const firstProject = dataSource[0];
      const firstProduct = firstProject.children?.[0];
      firstProject.key &&
        firstProduct?.key &&
        setExpandedRowKeys([firstProject.key, firstProduct?.key].filter(Boolean));
    }
  }, [dataSource]);
  return (
    <Table<T>
      id={id}
      columns={columns}
      onRow={record => ({
        onClick: () => {
          if (record?.children?.length) {
            handleRowClick(record);
          }
        },
      })}
      expandable={{
        expandedRowKeys,
        onExpandedRowsChange: keys => setExpandedRowKeys([...(keys as string[])]),
        expandIconColumnIndex: columns.length - 1,
        expandIcon: ({ expanded, onExpand, record }) =>
          record?.nameProject || (level === 2 && record?.code) ? (
            expanded ? (
              <DownOutlined onClick={e => onExpand(record, e)} style={{ cursor: 'pointer', fontSize: 12 }} />
            ) : (
              <UpOutlined onClick={e => onExpand(record, e)} style={{ cursor: 'pointer', fontSize: 12 }} />
            )
          ) : (
            <></>
          ),
      }}
      rowClassName={record => {
        return record?.nameProject ? 'level1-parent' : level === 2 && record?.code ? 'level2-parent' : '';
      }}
      className="expand-table-custom"
      dataSource={dataSource}
      pagination={{ pageSize: 10 }}
      rowKey="key"
      loading={isLoading || isFetching || isPlaceholderData}
    />
  );
}
export default TableExpandComponent;
