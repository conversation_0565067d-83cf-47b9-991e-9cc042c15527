.wrap-modal {
  .ant-modal {
    max-width: 100vw !important;
    height: 100vh;
    top: 0;
    bottom: 0;
    margin: 0;
    .ant-modal-content {
      padding: 0;
      height: 100vh;
      .ant-modal-close {
        inset-inline-end: 120px;
      }
      .ant-modal-header {
        padding: 20px 120px;
        border-bottom: 1px solid #d9d9d9;
        margin-bottom: 0;
      }
      .ant-modal-body {
        padding: 16px 120px;
        height: calc(100vh - 130px);
        overflow-y: auto;
        overflow-x: hidden;
        .ant-divider.ant-divider-horizontal.dash-header {
          position: absolute;
          left: 0;
          margin: 0;
        }
        .ant-form {
          padding: 24px 0;
        }
      }
    }
    .ant-modal-footer {
      position: absolute;
      bottom: 0;
      left: 0;
      display: flex;
      border-block-start: 1px solid #0000000f;
      padding-top: 16px;
      width: 100vw;
      justify-content: flex-end;
      padding: 16px 120px;
    }
  }
}
@media screen and (max-width: 500px) {
  .wrap-modal {
    .ant-modal {
      .ant-modal-content {
        .ant-modal-header {
          padding: 16px 24px;
        }
        .ant-modal-body {
          padding: 16px 24px;
        }
        .ant-modal-close {
          inset-inline-end: 24px;
        }
      }
      .ant-modal-footer {
        padding: 16px 24px;
      }
    }
  }
}

.ant-modal-confirm {
  .ant-modal-content {
    border-radius: 2px;
    button {
      border-radius: 2px;
    }
  }
}
