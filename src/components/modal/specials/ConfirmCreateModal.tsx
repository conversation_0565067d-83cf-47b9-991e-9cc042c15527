import { MutationFunction, QueryKey } from '@tanstack/react-query';
import { Button, Form, Input, Modal, notification } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { useDeleteField } from '../../../hooks';
import './styleModalDelete.scss';
import { useState } from 'react';

interface ConfirmCreateModalProps {
  open: boolean;
  onCancel: () => void;
  title?: string;
  description?: string;
  keyOfListQuery?: QueryKey;
  keyOfDetailQuery?: QueryKey;
  apiQuery: MutationFunction | MutationFunction<unknown, unknown>;
  label?: string;
  idDetail?: string;
  path?: string;
  disable?: boolean;
}

const ConfirmCreateModal: React.FC<ConfirmCreateModalProps> = ({
  open,
  onCancel,
  title,
  description,
  keyOfListQuery,
  apiQuery,
  label,
  path,
  keyOfDetailQuery,
  disable = true,
}) => {
  const [form] = useForm();
  const [disableButton, setDisableButton] = useState(disable);

  const create = useDeleteField({
    keyOfListQuery,
    keyOfDetailQuery,
    apiQuery,
    label,
    path,
    isMessageError: false,
    isMessageSuccess: false,
  });

  const handleConfirm = async (value: unknown) => {
    if (!value) return;
    const res = await create.mutateAsync({ ...value });
    if (res?.data?.statusCode === '0') {
      notification.success({ message: `${title} thành công` });
      onCancel();
      form.resetFields();
    }
  };

  const handleCancel = () => {
    onCancel();
    form.resetFields();
  };

  const handleChangeValue = (value: string) => {
    value ? setDisableButton(false) : setDisableButton(true);
  };

  return (
    <Modal
      className="modal-confirm-delete"
      open={open}
      title={title}
      centered
      closable={false}
      okText="Tạo"
      cancelText="Huỷ"
      destroyOnClose
      footer={[
        <Button key="cancel" className="btn-cancel" type="text" onClick={handleCancel}>
          Huỷ
        </Button>,
        <Button
          key="confirm"
          className="btn-confirm"
          type="primary"
          onClick={form.submit}
          loading={create.status === 'pending'}
          disabled={disableButton}
        >
          Tạo
        </Button>,
      ]}
    >
      <Form form={form} onFinish={handleConfirm} onValuesChange={handleChangeValue}>
        <p className="description">{description}</p>
        <Form.Item name="nameVN" rules={[{ required: true, message: 'Vui lòng nhập tên thư mục!' }]}>
          <Input placeholder={`Nhập tên thư mục ${title}`} />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ConfirmCreateModal;
