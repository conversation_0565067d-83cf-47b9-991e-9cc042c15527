import React, { useState } from 'react';
import { Modal, Radio, Input, Button, message, RadioChangeEvent, Form } from 'antd';
import './styleModalLiquidation.scss';

interface RadioOption {
  label: string;
  value: string;
}

interface ApiFunctions {
  [key: string]: (note: string) => Promise<{ success: boolean }>;
}

interface LiquidationModalProps {
  visible: boolean;
  onCancel: () => void;
  title?: string;
  description?: string;
  options?: RadioOption[];
  apiFunctions?: ApiFunctions;
  isPlaceholderNote?: string;
  disable?: boolean;
  fieldNameReason?: string;
  labelConfirm?: string;
  maxLength?: number;
  code?: string;
}

const LiquidationModal: React.FC<LiquidationModalProps> = ({
  visible,
  onCancel,
  title = 'THANH LÝ',
  code,
  description = 'Bạn có chắc muốn thanh lý sản phẩm',
  disable = true,
  fieldNameReason = 'note',
  labelConfirm = 'Thanh lý',
  maxLength = 255,
  options = [
    { label: 'Hoàn tiền đặt cọc', value: '1' },
    { label: 'Thanh lý không hoàn tiền', value: '2' },
    { label: 'Đổi tên/Chuyển nhượng', value: '3' },
  ],
  apiFunctions = {
    refundDeposit: async (note: string) => {
      console.log('Calling Hoàn tiền đặt cọc API with note:', note);
      return Promise.resolve({ success: true });
    },
    liquidateWithoutRefund: async (note: string) => {
      console.log('Calling Thanh lý không hoàn tiền API with note:', note);
      return Promise.resolve({ success: true });
    },
    transferOwnership: async (note: string) => {
      console.log('Calling Đổi tên/Chuyển nhượng API with note:', note);
      return Promise.resolve({ success: true });
    },
  },
  isPlaceholderNote = 'Nhập ghi chú',
}) => {
  const [form] = Form.useForm();
  const [selectedOption, setSelectedOption] = useState<string | null>(null);
  const [disableButton, setDisableButton] = useState(disable);

  const handleLiquidation = async (value: unknown) => {
    if (!selectedOption) {
      message.error('Please select an option!');
      return;
    }
    if (!value) {
      message.error('Please enter a note!');
      return;
    }

    try {
      const apiFunction = apiFunctions[selectedOption];
      if (apiFunction) {
        const result = await apiFunction('');
        if (result.success) {
          message.success('Liquidation successful!');
          onCancel();
        }
      }
    } catch (error) {
      message.error('An error occurred, please try again!');
    } finally {
    }
  };

  const handleCancel = () => {
    onCancel();
    form.resetFields();
    setDisableButton(true);
  };

  const handleChangeValue = (value: Record<string, string>) => {
    const hasNonSpaceCharacters = value[fieldNameReason]?.trim().length > 0;
    setDisableButton(!hasNonSpaceCharacters);
  };

  return (
    <Modal
      className="modal-liquidation"
      title={title}
      visible={visible}
      closable={false}
      footer={[
        <Button key="cancel" className="btn-cancel" type="text" onClick={handleCancel}>
          Huỷ
        </Button>,
        <Button key="confirm" className="btn-confirm" type="primary" onClick={form.submit} disabled={disableButton}>
          {labelConfirm}
        </Button>,
      ]}
      width={600}
    >
      <Form form={form} onFinish={handleLiquidation} onValuesChange={handleChangeValue}>
        <p className="description">{`${description} ${code || ''}?`}</p>
        <Radio.Group
          className="radio-group"
          onChange={(e: RadioChangeEvent) => setSelectedOption(e.target.value)}
          value={selectedOption}
        >
          {options.map(option => (
            <Radio key={option.value} value={option.value} className="radio-item">
              {option.label}
            </Radio>
          ))}
        </Radio.Group>
        <Form.Item
          name={fieldNameReason}
          rules={[{ required: true, message: 'Vui lòng nhập ghi chú!', whitespace: true }]}
        >
          <Input placeholder={isPlaceholderNote ? 'Nhập ghi chú' : `Nhập ghi chú ${title}`} maxLength={maxLength} />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default LiquidationModal;
