import { Modal } from 'antd';

type ConfirmCancelModalProps = {
  onConfirm: () => void;
};

export const showConfirmCancelModal = ({ onConfirm }: ConfirmCancelModalProps) => {
  Modal.confirm({
    title: '<PERSON><PERSON>c nhận hủy',
    content: '<PERSON><PERSON> liệu đang chưa được lưu, bạn có chắc muốn huỷ dữ liệu đang nhập không?',
    cancelText: 'Quay lại',
    okText: 'Hủy nhập',
    onOk: onConfirm,
    okButtonProps: {
      type: 'default',
    },
    cancelButtonProps: {
      type: 'primary',
    },
  });
};
