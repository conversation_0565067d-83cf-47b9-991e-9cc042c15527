import { ModalFuncProps } from 'antd';
import { useAppProps } from 'antd/es/app/context';
import { ReactNode } from 'react';
import './stylesModalConfirm.scss';
interface Props extends ModalFuncProps {
  handleConfirm?: () => void;
  cancelText?: null | string | ReactNode;
  okText?: null | string | ReactNode;
  modal: useAppProps['modal'];
  loading?: boolean;
}
export const modalConfirm = (props: Props) => {
  const {
    title,
    cancelText = 'Huỷ',
    okText = 'Xác nhận',
    handleConfirm,
    content,
    icon = null,
    centered = true,
    modal,
    loading,
    ...restProps
  } = props;
  modal.confirm({
    ...restProps,
    cancelText: cancelText,
    cancelButtonProps: cancelText === null ? { style: { display: 'none' } } : { type: 'text' },
    className: `wrap-modal-confirm wrap-btn `,
    title: title,
    content: content,
    centered: centered,
    icon: icon,
    okButtonProps: { loading: loading },
    onOk() {
      handleConfirm && handleConfirm();
    },
    okText,
  });
};
