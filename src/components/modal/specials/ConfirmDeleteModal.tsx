import { MutationFunction, QueryKey } from '@tanstack/react-query';
import { Button, Form, Input, Modal } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { useState } from 'react';
import { useDeleteField, useSubscribeList, useUpdateField } from '../../../hooks';
import './styleModalDelete.scss';

interface ConfirmDeleteModalProps {
  open: boolean;
  onCancel: () => void;
  title?: string;
  description?: string;
  keyOfListQuery?: QueryKey;
  keyOfDetailQuery?: QueryKey;
  apiQuery: MutationFunction | MutationFunction<unknown, unknown>;
  label?: string;
  idDetail?: string;
  path?: string;
  disable?: boolean;
  extraValues?: { [key: string]: unknown };
  maxLength?: number;
  fieldNameReason?: string;
  isTitlePlaceholder?: boolean;
  labelConfirm?: string;
  isUpdate?: boolean;
  isMessageSuccess?: string;
  setHideModal?: () => void;
  defaultFilter?: Record<string, unknown>;
}

interface ListData {
  rows?: unknown[];
  page?: number;
}

const ConfirmDeleteModal: React.FC<ConfirmDeleteModalProps> = ({
  open,
  onCancel,
  title,
  description,
  keyOfListQuery,
  apiQuery,
  label,
  idDetail,
  path,
  keyOfDetailQuery,
  disable = true,
  extraValues,
  maxLength = 255,
  fieldNameReason = 'softDeleteReason',
  isTitlePlaceholder = false,
  labelConfirm = 'Xác nhận',
  isUpdate = false,
  isMessageSuccess,
  setHideModal,
  defaultFilter = {},
}) => {
  const [form] = useForm();
  const [disableButton, setDisableButton] = useState(disable);
  const subscribeListData = useSubscribeList(keyOfListQuery, defaultFilter);

  const data = (subscribeListData?.data?.data || {}) as ListData;
  const checkIsDefaultParams =
    data?.rows && Array.isArray(data?.rows) && data?.rows.length === 1 && data?.page && data.page > 1 ? true : false;

  const softUpdate = useUpdateField({
    keyOfListQuery,
    keyOfDetailQuery,
    apiQuery,
    label,
    path,
    isMessageError: false,
    messageSuccess: isMessageSuccess,
  });

  const softDelete = useDeleteField({
    keyOfListQuery,
    keyOfDetailQuery,
    apiQuery,
    label,
    path,
    isMessageError: false,
    useDefaultParams: checkIsDefaultParams ? true : false,
    isDelete: true,
  });

  const handleConfirm = async (value: unknown) => {
    if (!value) return;
    const res = isUpdate
      ? await softUpdate.mutateAsync({ id: idDetail, ...extraValues, ...value })
      : await softDelete.mutateAsync({ id: idDetail, ...extraValues, ...value });

    if (res?.data?.statusCode === '0') {
      onCancel();
      form.resetFields();
      setDisableButton(true);
      if (setHideModal) {
        setHideModal();
      } else {
        onCancel();
      }
      return Promise.resolve();
    } else {
      return Promise.reject();
    }
  };

  const handleCancel = () => {
    onCancel();
    form.resetFields();
    setDisableButton(true);
  };

  const handleChangeValue = (value: Record<string, string>) => {
    const hasNonSpaceCharacters = value[fieldNameReason]?.trim().length > 0;
    setDisableButton(!hasNonSpaceCharacters);
  };

  return (
    <Modal
      className="modal-confirm-delete"
      open={open}
      title={title}
      centered
      closable={false}
      okText="Xác nhận"
      cancelText="Hủy"
      destroyOnClose
      footer={[
        <Button key="cancel" className="btn-cancel" type="text" onClick={handleCancel}>
          Hủy
        </Button>,
        <Button
          key="confirm"
          className="btn-confirm"
          type="primary"
          onClick={form.submit}
          loading={softDelete.status === 'pending'}
          disabled={disableButton}
        >
          {labelConfirm}
        </Button>,
      ]}
    >
      <Form form={form} onFinish={handleConfirm} onValuesChange={handleChangeValue}>
        <p className="description">{description}</p>
        <Form.Item
          name={fieldNameReason}
          rules={[{ required: true, message: 'Vui lòng nhập lý do!', whitespace: true }]}
        >
          <Input placeholder={isTitlePlaceholder ? 'Nhập lý do' : `Nhập lý do ${title}`} maxLength={maxLength} />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ConfirmDeleteModal;
