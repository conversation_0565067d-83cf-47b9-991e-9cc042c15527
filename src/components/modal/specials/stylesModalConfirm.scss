@import '../../../constants/colors.scss';

.wrap-modal-confirm {
  .ant-modal-content {
    border-radius: 2px;
    .ant-modal-confirm-title {
      font-size: 20px;
    }
    .ant-modal-confirm-body {
      align-items: center;
      text-align: center;
      flex-direction: column;
    }
    .ant-modal-confirm-btns {
      display: flex;
      justify-content: flex-end;
      margin-top: 26px;
      .ant-btn {
        width: 50%;
        border-radius: 2px;
      }
      .ant-btn-text {
        background: #0000000f;
        border: none;
        &:hover {
          background: #00000014;
        }
      }
    }
  }
}
.text-area-content {
  margin-bottom: 0;
  margin-top: 16px;
}

.ant-modal-confirm .ant-modal-confirm-paragraph {
  max-width: 100%;
}
