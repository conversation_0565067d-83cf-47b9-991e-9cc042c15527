import React from 'react';
import { Select, Spin } from 'antd';
import debounce from 'lodash/debounce';

interface OptionType {
  value: string;
  label: string;
}

interface SearchableInfiniteScrollSelectProps {
  options: OptionType[];
  onLoadMore: () => void;
  onSearch: (searchTerm: string) => void;
  isLoading: boolean;
  hasMore: boolean;
  placeholder?: string;
  onChange?: (value: { value: string; label: string } | null) => void;
  [key: string]: unknown;
  isFetching: boolean;
}

const SearchableInfiniteScrollSelect: React.FC<SearchableInfiniteScrollSelectProps> = ({
  options,
  onLoadMore,
  onSearch,
  isLoading,
  hasMore,
  onChange,
  isFetching,
  ...rest
}) => {
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const target = e.target as HTMLDivElement;
    const distanceFromBottom = target.scrollHeight - target.scrollTop - target.clientHeight;

    if (distanceFromBottom < 100 && !isLoading && hasMore) {
      onLoadMore();
    }
  };

  const handleSearch = debounce((value: string) => {
    const formattedValue = value.trim();
    onSearch(formattedValue);
  }, 500);

  return (
    <Select
      showSearch
      onSearch={handleSearch}
      onPopupScroll={handleScroll}
      filterOption={false}
      options={options}
      loading={isLoading}
      onChange={onChange}
      {...rest}
      notFoundContent={isFetching ? <Spin size="small" /> : 'Không tìm thấy dữ liệu'}
    />
  );
};

export default SearchableInfiniteScrollSelect;
