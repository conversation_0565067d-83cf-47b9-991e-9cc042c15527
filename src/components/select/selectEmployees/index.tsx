import { CheckOutlined } from '@ant-design/icons';
import { useInfiniteQuery } from '@tanstack/react-query';
import { Select, Space, Spin, Tag } from 'antd';
import isArray from 'lodash/isArray';
import debounce from 'lodash/debounce';
import React, { ReactNode, useEffect, useMemo, useRef, useState } from 'react';
import { FetchResponse, TDataList, useScrollHandlerLazyLoading } from '../../../hooks';
import { getListEmployeeInternal } from '../../../service/employee';
import { EmployeeInternal } from '../../../types/employee/employee';
import './styles.scss';
import { normalizeString } from '../../../utilities/regex';
import { DEFAULT_PARAMS_PAGESIZE } from '../../../constants/common';

export type OptionType = {
  label: string | JSX.Element;
  value: string;
  styles?: React.CSSProperties;
  email?: string;
};

type TSelectEmployees = {
  isOpen?: boolean;
  defaultValues?: string[];
  handleChangeListShare?: (values: OptionType[]) => void;
};

function SelectEmployees(props: TSelectEmployees) {
  const { isOpen, defaultValues, handleChangeListShare } = props;
  const selectRef = useRef<HTMLDivElement>(null);

  const [filterData, setFilterData] = useState<OptionType[] | undefined>();
  const [listSelectValues, setListSelectValues] = useState<OptionType[]>([]);
  const [openDropdown, setOpenDropdown] = useState(false);
  const [search, setSearch] = useState<string>();

  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, isLoading } = useInfiniteQuery<
    FetchResponse<TDataList<EmployeeInternal[]>>
  >({
    queryKey: ['getAllDataEmployees', search],
    queryFn: ({ pageParam = 1 }) =>
      getListEmployeeInternal({ page: pageParam as number, pageSize: DEFAULT_PARAMS_PAGESIZE, search }).then(
        res => res.data,
      ),
    initialPageParam: 1,
    staleTime: 50000,
    enabled: !!isOpen || true,
    getNextPageParam: lastPage => {
      const hasMore = lastPage?.data?.rows?.length > 0;
      return hasMore ? lastPage?.data?.page + 1 : undefined;
    },
  });

  const scrollHandlerLazy = useScrollHandlerLazyLoading({
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
  });

  const flatData = useMemo(() => data?.pages.flatMap(page => page?.data?.rows) || [], [data]);

  useEffect(() => {
    if (defaultValues) {
      setListSelectValues(
        defaultValues.map((value: string) => ({
          value: value,
          label: value,
          email: value,
        })) || [],
      );
    }
  }, [defaultValues]);

  useEffect(() => {
    if (flatData) {
      setFilterData(
        flatData?.map(item => ({
          value: item.email,
          label: (
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <span>
                {item.name} - {item.email}
              </span>
              {listSelectValues.some(option => option.value === item.email) ? (
                <CheckOutlined style={{ color: '#1677ff' }} />
              ) : undefined}
            </div>
          ),
          style: listSelectValues.some(option => option.value === item.email)
            ? { fontWeight: '600', backgroundColor: '#e6f4ff' } // Màu cho các mục đã chọn
            : undefined, // Mặc định cho mục chưa chọn
        })),
      );
    }
  }, [flatData, listSelectValues]);

  const handleSearch = debounce((value: string) => {
    setSearch(value ? normalizeString(value) : undefined);
  }, 500);

  // Hàm để trích xuất email từ label
  const extractEmail = (label: ReactNode): string => {
    const labelText =
      typeof label === 'string'
        ? label
        : React.isValidElement(label)
          ? (label.props.children?.[0]?.props.children as string)
          : '';
    return labelText ? labelText[2] : '';
  };

  const handleSelect = (_: string, options: OptionType | OptionType[]) => {
    const selectedOptions = isArray(options) ? options : [options];

    //Kiểm tra phần tử đã tồn tại trong listSelectValues chưa
    const isExistence = listSelectValues.some(selected => selected.value === selectedOptions?.[0].value);

    // Trích xuất email từ label
    const newSelectedOptions = selectedOptions.map(option => ({
      ...option,
      email: extractEmail(option.label),
    }));

    // Nếu phần tử đã tồn tại thì xóa phần tử đó ra khỏi listSelectValues ngược lại thì thêm vào
    const listSelectedOption = isExistence
      ? listSelectValues.filter(option => option.value !== selectedOptions?.[0].value)
      : [...listSelectValues, ...newSelectedOptions];

    if (listSelectedOption.length > 0) {
      setFilterData(filterData?.filter(item => !listSelectedOption.some(option => option.value === item?.value)));
    }
    setListSelectValues(listSelectedOption);
    setSearch(undefined);
    handleChangeListShare && handleChangeListShare(listSelectedOption);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setOpenDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div ref={selectRef}>
      <Select
        showSearch
        open={openDropdown}
        getPopupContainer={trigger => trigger.parentNode as HTMLElement}
        onClick={() => setOpenDropdown(true)}
        onPopupScroll={scrollHandlerLazy}
        popupClassName="popup-select-employee"
        placeholder="Chọn nhân viên"
        suffixIcon=""
        onSelect={handleSelect}
        value={[]}
        filterOption={false}
        onSearch={handleSearch}
        dropdownRender={menu =>
          isLoading ? (
            <div className="wrapper-spin">
              <Spin />
            </div>
          ) : (
            <div>{menu}</div>
          )
        }
        style={{ width: '100%' }}
        options={filterData}
      />
      <div className="wrapper-tag-employee">
        <Space size={[0, 8]} wrap>
          {listSelectValues?.map((value, index) => (
            <Tag
              key={index}
              closable
              onClose={() => {
                const list = listSelectValues.filter(v => v.value !== value.value);
                handleChangeListShare && handleChangeListShare(list);
                setListSelectValues(list);
              }}
            >
              {value.email}
            </Tag>
          ))}
        </Space>
      </div>
    </div>
  );
}

export default SelectEmployees;
