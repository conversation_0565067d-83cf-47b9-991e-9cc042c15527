import { CheckOutlined, SearchOutlined } from '@ant-design/icons';
import { useInfiniteQuery } from '@tanstack/react-query';
import { Input, Radio, Select, Space, Spin, Tag, Tooltip, Typography } from 'antd';
import debounce from 'lodash/debounce';
import isArray from 'lodash/isArray';
import isEqual from 'lodash/isEqual';
import { memo, useEffect, useMemo, useRef, useState } from 'react';
import { DEFAULT_PARAMS_PAGESIZE } from '../../../constants/common';
import { FetchResponse, TDataList, useScrollHandlerLazyLoading } from '../../../hooks';
import { ISelectLazyLoading, OptionTypeSelect } from '../../../types/common/common';
import { normalizeString } from '../../../utilities/regex';
import './styles.scss';

function areEqual<T>(prevProps: TMultiSelectLazy<T>, nextProps: TMultiSelectLazy<T>) {
  return (
    isEqual(prevProps.queryKey, nextProps.queryKey) &&
    isEqual(prevProps.moreParams, nextProps.moreParams) &&
    isEqual(prevProps.defaultValues, nextProps.defaultValues) &&
    isEqual(prevProps.disabled, nextProps.disabled) &&
    isEqual(prevProps.enabled, nextProps.enabled) &&
    isEqual(prevProps.apiQuery1, nextProps.apiQuery1) &&
    isEqual(prevProps.apiQuery2, nextProps.apiQuery2) &&
    isEqual(prevProps.selectedType, nextProps.selectedType) &&
    isEqual(prevProps.showSelectAll, nextProps.showSelectAll) &&
    isEqual(prevProps.keysLabelOption1, nextProps.keysLabelOption1) &&
    isEqual(prevProps.keysTagOption1, nextProps.keysTagOption1) &&
    isEqual(prevProps.keysLabelOption2, nextProps.keysLabelOption2) &&
    isEqual(prevProps.keysTagOption2, nextProps.keysTagOption2)
  );
}

interface TMultiSelectLazy<T> extends Omit<ISelectLazyLoading<T>, 'defaultValues'> {
  handleListSelect?: (values: unknown[]) => void;
  moreParams?: Record<string, unknown>;
  defaultValues?: { label: string | JSX.Element; value: string; [key: string]: string | JSX.Element }[];
  showSelectAll?: boolean;
  selectedType?: 'option1' | 'option2';
  apiQuery1?: (params: unknown) => Promise<FetchResponse<TDataList<{ [key: string]: unknown }[]>>>;
  apiQuery2?: (params: unknown) => Promise<FetchResponse<TDataList<{ [key: string]: unknown }[]>>>;
  onChangeSelectedType?: (type: 'option1' | 'option2') => void;
  keysLabelOption1?: string | string[];
  keysTagOption1?: string | string[];
  keysLabelOption2?: string | string[];
  keysTagOption2?: string | string[];
  textOption1?: string;
  textOption2?: string;
}

function MultiSelectLazy<T>(props: TMultiSelectLazy<T>) {
  const {
    enabled = true,
    defaultValues,
    handleListSelect,
    apiQuery1,
    apiQuery2,
    selectedType: propSelectedType = 'option1',
    onChangeSelectedType,
    queryKey,
    placeholder,
    suffixIcon,
    moreParams,
    showSelectAll,
    disabled = false,
    keysLabelOption1,
    keysTagOption1,
    keysLabelOption2,
    keysTagOption2,
    textOption1,
    textOption2,
  } = props;
  const selectRef = useRef<HTMLDivElement>(null);

  const [filterData, setFilterData] = useState<OptionTypeSelect[] | undefined>();
  const [listSelectValues, setListSelectValues] = useState<OptionTypeSelect[]>([]);
  const [openDropdown, setOpenDropdown] = useState(false);
  const [search, setSearch] = useState<string>();
  const [selectedType, setSelectedType] = useState<'option1' | 'option2'>(propSelectedType);

  // Chọn keysLabel và keysTag dựa trên selectedType
  const activeKeysLabel = selectedType === 'option1' ? keysLabelOption1 : keysLabelOption2;
  const activeKeysTag = selectedType === 'option1' ? keysTagOption1 : keysTagOption2;

  // Cập nhật selectedType từ props
  useEffect(() => {
    setSelectedType(propSelectedType);
  }, [propSelectedType]);

  const apiQuery = selectedType === 'option1' ? apiQuery1 : apiQuery2;

  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, isLoading } = useInfiniteQuery<
    FetchResponse<TDataList<{ [key: string]: unknown }[]>>
  >({
    queryKey: [...(queryKey || []), search, selectedType, ...(moreParams ? Object.values(moreParams) : [])],
    queryFn: ({ pageParam = 1 }) =>
      apiQuery
        ? apiQuery({ page: pageParam as number, pageSize: DEFAULT_PARAMS_PAGESIZE, search, ...moreParams }).then(
            res => {
              return res?.data as unknown as FetchResponse<TDataList<{ [key: string]: unknown }[]>>;
            },
          )
        : Promise.reject(new Error('error fetch API')),
    initialPageParam: 1,
    staleTime: 50000,
    enabled: !!enabled && !!apiQuery,
    getNextPageParam: lastPage => {
      const hasMore = lastPage?.data?.rows?.length > 0 && lastPage?.data?.totalPages > lastPage?.data?.page;
      return hasMore ? Number(lastPage?.data?.page) + 1 : undefined;
    },
  });

  const scrollHandlerLazy = useScrollHandlerLazyLoading({
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
  });

  const flatData = useMemo(() => {
    const apiData =
      data?.pages.flatMap((page: FetchResponse<TDataList<{ [key: string]: unknown }[]>>) => page?.data?.rows ?? []) ||
      [];
    return apiData;
  }, [data]);

  useEffect(() => {
    const updatedValues = (defaultValues || []).map(item => ({
      ...item,
      option: item,
    }));
    setListSelectValues(updatedValues as OptionTypeSelect[]);
  }, [defaultValues]);

  useEffect(() => {
    if (flatData && activeKeysLabel) {
      const newFilterData = flatData.map((item: { [key: string]: unknown }) => {
        const isSelected = listSelectValues?.some(option => option.value === item?.id || option.value === item?.value);
        const label = isArray(activeKeysLabel)
          ? activeKeysLabel
              .map(name => String(item[name as keyof typeof item] ?? ''))
              .filter(Boolean)
              .join(' - ')
          : String(item[activeKeysLabel as keyof typeof item] ?? '');

        return {
          option: item,
          id: item?.id as string,
          name: (item?.name || '') as string,
          value: (item?.id || item?.value) as string,
          label: (
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Tooltip title={label || 'Không có dữ liệu'}>
                <Typography.Text ellipsis style={{ verticalAlign: 'middle' }}>
                  {label || 'Không có dữ liệu'}
                </Typography.Text>
              </Tooltip>
              {isSelected && <CheckOutlined style={{ color: '#1677ff' }} />}
            </div>
          ),
          style: isSelected ? { fontWeight: '600', backgroundColor: '#e6f4ff' } : undefined,
        };
      });

      if (showSelectAll) {
        newFilterData.unshift({
          value: 'all',
          option: { value: 'all', label: 'Tất cả' },
          id: 'all',
          name: 'Tất cả',
          label: (
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Typography.Text ellipsis style={{ verticalAlign: 'middle' }}>
                Tất cả
              </Typography.Text>
              {listSelectValues.length === flatData.length && flatData.length > 0 && (
                <CheckOutlined style={{ color: '#1677ff' }} />
              )}
            </div>
          ),
          style:
            listSelectValues.length === flatData.length && flatData.length > 0
              ? { fontWeight: '600', backgroundColor: '#e6f4ff' }
              : undefined,
        });
      }

      setFilterData(newFilterData);
    } else {
      setFilterData([]);
    }
  }, [flatData, activeKeysLabel, listSelectValues, showSelectAll, selectedType]);

  console.log(filterData);
  const handleSearch = debounce((value: string) => {
    setSearch(value ? normalizeString(value) : undefined);
  }, 500);

  const handleSelect = (_: string, options: OptionTypeSelect | OptionTypeSelect[]) => {
    const selectedOptions = isArray(options) ? options : [options];

    if (selectedOptions[0].value === 'all') {
      const allSelected = listSelectValues.length === flatData.length;
      const updatedList = allSelected
        ? []
        : flatData.map((item: { [key: string]: unknown }) => {
            const label = isArray(activeKeysLabel)
              ? activeKeysLabel
                  .map(name => String(item[name as keyof typeof item] ?? ''))
                  .filter(Boolean)
                  .join(' - ')
              : String(item[activeKeysLabel as keyof typeof item] ?? '');
            return {
              option: item,
              id: item.id as string,
              name: (item.name || '') as string,
              value: (item.id || item.value) as string,
              label: (
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Tooltip title={label || 'Không có dữ liệu'}>
                    <Typography.Text ellipsis style={{ verticalAlign: 'middle' }}>
                      {label || 'Không có dữ liệu'}
                    </Typography.Text>
                  </Tooltip>
                  <CheckOutlined style={{ color: '#1677ff' }} />
                </div>
              ),
              style: { fontWeight: '600', backgroundColor: '#e6f4ff' },
            };
          });

      setListSelectValues(updatedList);
      handleListSelect && handleListSelect(updatedList as T[]);
    } else {
      const isExistence = listSelectValues.some(selected => selected.value === selectedOptions[0].value);
      const updatedList = isExistence
        ? listSelectValues.filter(option => option.value !== selectedOptions[0].value)
        : [...listSelectValues, ...selectedOptions];

      setListSelectValues(updatedList);
      handleListSelect && handleListSelect(updatedList as T[]);
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setOpenDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const renderTag = (value: OptionTypeSelect, index: number) => {
    const tagContent = isArray(activeKeysTag)
      ? activeKeysTag
          .map(name => String(value?.option?.[name as keyof typeof value.option] ?? ''))
          .filter(Boolean)
          .join(' - ')
      : String(value?.option?.[activeKeysTag as keyof typeof value.option] ?? value.label ?? '');

    const maxLength = 40;
    const displayText = tagContent.length > maxLength ? `${tagContent.substring(0, maxLength)}...` : tagContent;

    return (
      <Tag
        key={index}
        closable
        onClose={() => {
          const list = listSelectValues.filter(v => v.value !== value.value);
          handleListSelect && handleListSelect(list as unknown as T[]);
          setListSelectValues(list);
        }}
      >
        <Tooltip title={tagContent || 'Không có dữ liệu'}>
          <Typography.Text
            ellipsis
            style={{
              maxWidth: '250px',
              display: 'inline-block',
              fontSize: '12px',
              lineHeight: '20px',
            }}
          >
            {displayText || 'Không có dữ liệu'}
          </Typography.Text>
        </Tooltip>
      </Tag>
    );
  };

  return (
    <div ref={selectRef}>
      <Select
        showSearch
        autoClearSearchValue={false}
        disabled={disabled}
        open={openDropdown}
        getPopupContainer={trigger => trigger.parentNode as HTMLElement}
        onClick={() => setOpenDropdown(true)}
        onPopupScroll={scrollHandlerLazy}
        popupClassName="popup-multil-select-dropdown-lazy"
        placeholder={placeholder}
        suffixIcon={suffixIcon}
        onSelect={handleSelect}
        value={[]}
        filterOption={false}
        onSearch={handleSearch}
        dropdownRender={menu => (
          <Spin spinning={isFetchingNextPage || isLoading} size="small" tip="Đang tải...">
            <div style={{ padding: 8 }}>
              <Radio.Group
                value={selectedType}
                onChange={e => {
                  const newType = e.target.value as 'option1' | 'option2';
                  setSelectedType(newType);
                  onChangeSelectedType?.(newType);
                  setSearch(undefined);
                  setListSelectValues([]);
                }}
                style={{ display: 'flex' }}
              >
                <Radio value="option1">{textOption1}</Radio>
                <Radio value="option2">{textOption2}</Radio>
              </Radio.Group>
            </div>
            <div style={{ padding: 8 }}>
              <Input
                placeholder="Tìm kiếm"
                prefix={<SearchOutlined />}
                onClick={e => e.stopPropagation()}
                onChange={e => handleSearch(e.target.value)}
              />
            </div>
            {menu}
          </Spin>
        )}
        style={{ width: '100%' }}
        options={filterData}
      />
      <div className="wrapper-tag-multi">
        <Space size={[0, 8]} wrap>
          {listSelectValues?.map((value, index) => renderTag(value, index))}
        </Space>
      </div>
    </div>
  );
}

export default memo(MultiSelectLazy, areEqual);
