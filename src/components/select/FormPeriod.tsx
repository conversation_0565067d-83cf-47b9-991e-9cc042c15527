import { Col, DatePicker, Form, Row, Select } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useMemo, useRef } from 'react';
import { FORMAT_DATE } from '../../constants/common';
import { useFetch } from '../../hooks';
import { getTimeConfigFeeCommission } from '../../service/timeConfigFeeCommission';
import { TPeriod } from '../../types/timeConfigFeeCommission';

interface Props {
  required?: boolean;
  label: string;
  enabled?: boolean;
  disabled?: boolean;
  fieldPos: string;
}

const FormPeriod = (props: Props) => {
  const { required, label, enabled = true, disabled, fieldPos } = props;
  const form = Form.useFormInstance();
  const formYear = Form.useWatch('year', form);
  const orgCharts = Form.useWatch(fieldPos, form);

  const listIdOrgCharts = Array.isArray(orgCharts) ? orgCharts?.map(item => item?.id).join('.') : orgCharts?.id;
  const prevQueryParamsRef = useRef<{ year?: string; orgcharts?: string }>({});

  const { data } = useFetch<TPeriod[]>({
    api: () => getTimeConfigFeeCommission({ year: formYear, orgcharts: listIdOrgCharts }),
    queryKeyArr: ['period', formYear, listIdOrgCharts],
    withFilter: false,
    enabled: !!enabled && !!formYear && !!listIdOrgCharts,
  });

  // Kiểm tra và reset period khi các tham số của API thay đổi
  useEffect(() => {
    // Chỉ thực hiện khi có đủ điều kiện để gọi API
    if (!!enabled && !!formYear && !!listIdOrgCharts) {
      const currentParams = { year: formYear, orgcharts: listIdOrgCharts };
      const prevParams = prevQueryParamsRef.current;

      // Kiểm tra xem tham số có thay đổi hay không
      if (
        (prevParams.year && Number(prevParams.year) !== Number(formYear)) ||
        (prevParams.orgcharts && prevParams.orgcharts !== listIdOrgCharts)
      ) {
        // Reset period về undefined khi tham số thay đổi
        form.setFieldValue('period', undefined);
      }

      // Cập nhật tham số mới
      prevQueryParamsRef.current = currentParams;
    }
  }, [formYear, listIdOrgCharts, enabled, form]);

  const dataPeriod = useMemo(
    () => (formYear && listIdOrgCharts ? data?.data?.data : []),
    [data?.data?.data, formYear, listIdOrgCharts],
  );

  const handleSelect = (_: string, option: TPeriod) => {
    form.setFieldsValue({
      periodObj: {
        periodName: option?.periodName,
        periodTo: option?.periodEndDate,
        periodFrom: option?.periodStartDate,
      },
    });
  };

  return (
    <Form.Item label={label} className="wrapper-period" required={required}>
      <Row gutter={24} className="group-period">
        <Col span={12}>
          <Form.Item
            name="year"
            style={{ width: '100%' }}
            rules={[{ required: true, message: 'Vui lòng chọn năm' }]}
            initialValue={dayjs().format('YYYY')}
            normalize={value => value || dayjs().format('YYYY')}
            getValueFromEvent={date => (date ? dayjs(date).format('YYYY') : null)}
            getValueProps={value => {
              return {
                value: value ? dayjs().year(value).startOf('year') : null,
              };
            }}
          >
            <DatePicker picker="year" placeholder="Chọn năm" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="period"
            style={{ width: '100%' }}
            rules={[{ required: true, message: 'Vui lòng chọn kỳ tính phí' }]}
          >
            <Select
              placeholder="Chọn kỳ tính phí"
              options={dataPeriod?.map(item => ({
                ...item,
                value: `${dayjs(item?.periodStartDate).format(FORMAT_DATE)} → ${dayjs(item?.periodEndDate).format(FORMAT_DATE)}`,
              }))}
              onSelect={handleSelect}
              allowClear
              disabled={disabled || !orgCharts || !formYear}
            />
          </Form.Item>
        </Col>
      </Row>
    </Form.Item>
  );
};

export default FormPeriod;
