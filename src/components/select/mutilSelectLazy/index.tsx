import { CheckOutlined } from '@ant-design/icons';
import { useInfiniteQuery } from '@tanstack/react-query';
import { Select, Space, Spin, Tag, Tooltip, Typography } from 'antd';
import debounce from 'lodash/debounce';
import isArray from 'lodash/isArray';
import isEqual from 'lodash/isEqual';
import { memo, useEffect, useMemo, useRef, useState } from 'react';
import { DEFAULT_PARAMS_PAGESIZE } from '../../../constants/common';
import { FetchResponse, TDataList, useScrollHandlerLazyLoading } from '../../../hooks';
import { ISelectLazyLoading, OptionTypeSelect } from '../../../types/common/common';
import { normalizeString } from '../../../utilities/regex';
import './styles.scss';

function areEqual<T>(prevProps: TMultiSelectLazy<T>, nextProps: TMultiSelectLazy<T>) {
  return (
    isEqual(prevProps.queryKey, nextProps.queryKey) &&
    isEqual(prevProps.moreParams, nextProps.moreParams) &&
    isEqual(prevProps.defaultValues, nextProps.defaultValues) &&
    isEqual(prevProps.externalData, nextProps.externalData) &&
    isEqual(prevProps.disabled, nextProps.disabled) &&
    isEqual(prevProps.enabled, nextProps.enabled) &&
    isEqual(prevProps.apiQuery, nextProps.apiQuery) &&
    isEqual(prevProps.showSelectAll, nextProps.showSelectAll)
  );
}
interface TMultiSelectLazy<T> extends Omit<ISelectLazyLoading<T>, 'defaultValues'> {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  handleListSelect?: (values: any[]) => void;
  keysTag?: string | string[];
  moreParams?: Record<string, unknown>;
  defaultValues?: {
    label: string | JSX.Element;
    value: string;
    [key: string]: string | JSX.Element;
  }[];
  showSelectAll?: boolean;
  externalData?: { [key: string]: unknown }[]; // Thêm prop externalData
}

function MultiSelectLazy<T>(props: TMultiSelectLazy<T>) {
  const {
    enabled = true,
    defaultValues,
    handleListSelect,
    apiQuery,
    queryKey,
    keysLabel,
    keysTag,
    placeholder,
    suffixIcon,
    moreParams,
    showSelectAll,
    disabled = false,
    externalData,
  } = props;
  const selectRef = useRef<HTMLDivElement>(null);

  const [filterData, setFilterData] = useState<OptionTypeSelect[] | undefined>();
  const [listSelectValues, setListSelectValues] = useState<OptionTypeSelect[]>([]);
  const [openDropdown, setOpenDropdown] = useState(false);
  const [search, setSearch] = useState<string>();

  // Chỉ sử dụng useInfiniteQuery nếu không có externalData
  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, isLoading } = useInfiniteQuery<
    FetchResponse<TDataList<{ [key: string]: unknown }[]>>
  >({
    queryKey: [...(queryKey || []), search, ...(moreParams ? Object.values(moreParams) : [])],
    queryFn: ({ pageParam = 1 }) =>
      apiQuery
        ? apiQuery({ page: pageParam as number, pageSize: DEFAULT_PARAMS_PAGESIZE, search, ...moreParams }).then(
            res => res.data as FetchResponse<TDataList<{ [key: string]: unknown }[]>>,
          )
        : Promise.reject(),
    initialPageParam: 1,
    staleTime: 50000,
    enabled: !!enabled && !!apiQuery,
    getNextPageParam: lastPage => {
      const hasMore = lastPage?.data?.rows?.length > 0 && lastPage?.data?.totalPages > lastPage?.data?.page;
      return hasMore ? Number(lastPage?.data?.page) + 1 : undefined;
    },
  });

  // Chỉ sử dụng scrollHandlerLazy nếu không có externalData
  const scrollHandlerLazy = useScrollHandlerLazyLoading({
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
  });

  // Sử dụng externalData nếu có, nếu không thì lấy từ API
  const flatData = useMemo(() => {
    if (externalData) {
      return externalData;
    }
    return (
      data?.pages.flatMap(
        (page: FetchResponse<TDataList<{ [key: string]: unknown }[]>>) => page?.data?.rows ?? page?.data,
      ) || []
    );
  }, [data, externalData]);

  useEffect(() => {
    const updatedValues = (defaultValues || []).map(item => ({
      ...item,
      option: item,
    }));
    setListSelectValues(updatedValues as OptionTypeSelect[]);
  }, [defaultValues]);

  useEffect(() => {
    if (flatData) {
      const newFilterData = flatData.map((item: { [key: string]: unknown }) => {
        const isSelected = listSelectValues?.some(option => option.value === item?.id || option.value === item?.value);

        const label = isArray(keysLabel)
          ? keysLabel.map(name => String(item[name as keyof typeof item])).join(' - ')
          : String(item[keysLabel as keyof typeof item]);

        return {
          option: item,
          id: item?.id,
          name: item?.name,
          value: (item?.id || item.value) as string,
          label: (
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Tooltip title={label}>
                <Typography.Text ellipsis style={{ verticalAlign: 'middle' }}>
                  {label}
                </Typography.Text>
              </Tooltip>
              {isSelected && <CheckOutlined style={{ color: '#1677ff' }} />}
            </div>
          ),
          style: isSelected ? { fontWeight: '600', backgroundColor: '#e6f4ff' } : undefined,
        };
      });

      if (showSelectAll) {
        newFilterData.unshift({
          value: 'all',
          option: { value: 'all', label: 'Tất cả' },
          id: 'all',
          name: 'Tất cả',
          label: (
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Typography.Text ellipsis style={{ verticalAlign: 'middle' }}>
                Tất cả
              </Typography.Text>
              {listSelectValues.length === flatData.length && <CheckOutlined style={{ color: '#1677ff' }} />}
            </div>
          ),
          style:
            listSelectValues.length === flatData.length ? { fontWeight: '600', backgroundColor: '#e6f4ff' } : undefined,
        });
      }

      setFilterData(newFilterData);
    }
  }, [flatData, keysLabel, listSelectValues, showSelectAll]);

  const handleSearch = debounce((value: string) => {
    setSearch(value ? normalizeString(value) : undefined);
  }, 500);

  const handleSelect = (_: string, options: OptionTypeSelect | OptionTypeSelect[]) => {
    const selectedOptions = isArray(options) ? options : [options];

    if (selectedOptions[0].value === 'all') {
      const allSelected = listSelectValues.length === flatData.length;
      const updatedList = allSelected
        ? []
        : flatData.map((item: { [key: string]: unknown }) => ({
            option: item,
            id: item.id,
            name: item.name,
            value: item.id as string,
            label: (
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Tooltip title={String(item[keysLabel as keyof typeof item])}>
                  <Typography.Text ellipsis style={{ verticalAlign: 'middle' }}>
                    {String(item[keysLabel as keyof typeof item])}
                  </Typography.Text>
                </Tooltip>
                <CheckOutlined style={{ color: '#1677ff' }} />
              </div>
            ),
            style: { fontWeight: '600', backgroundColor: '#e6f4ff' },
          }));

      setListSelectValues(updatedList);
      handleListSelect && handleListSelect(updatedList as T[]);
    } else {
      const isExistence = listSelectValues.some(selected => selected.value === selectedOptions?.[0].value);
      const updatedList = isExistence
        ? listSelectValues.filter(option => option.value !== selectedOptions[0].value)
        : [...listSelectValues, ...selectedOptions];

      setListSelectValues(updatedList);
      handleListSelect && handleListSelect(updatedList as T[]);
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setOpenDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Hàm render tag với tooltip khi nội dung dài
  const renderTag = (value: OptionTypeSelect, index: number) => {
    const tagContent = isArray(keysTag)
      ? keysTag.map(name => String(value?.option?.[name as keyof typeof value])).join(' - ')
      : String(value[keysTag as keyof typeof value]);

    // Cắt ngắn nếu quá dài (ví dụ: 40 ký tự)
    const maxLength = 40;
    const displayText = tagContent.length > maxLength ? `${tagContent.substring(0, maxLength)}...` : tagContent;

    return (
      <Tag
        key={index}
        closable
        onClose={() => {
          const list = listSelectValues.filter(v => v.value !== value.value);
          handleListSelect && handleListSelect(list as unknown as T[]);
          setListSelectValues(list);
        }}
      >
        <Tooltip title={tagContent}>
          <Typography.Text
            ellipsis
            style={{
              maxWidth: '250px',
              display: 'inline-block',
              fontSize: '12px',
              lineHeight: '20px',
            }}
          >
            {displayText}
          </Typography.Text>
        </Tooltip>
      </Tag>
    );
  };

  return (
    <div ref={selectRef}>
      <Select
        showSearch
        autoClearSearchValue={false}
        disabled={disabled}
        open={openDropdown}
        getPopupContainer={trigger => trigger.parentNode as HTMLElement}
        onClick={() => setOpenDropdown(true)}
        onPopupScroll={externalData ? undefined : scrollHandlerLazy} // Không dùng scrollHandlerLazy nếu có externalData
        popupClassName="popup-select-multi"
        placeholder={placeholder}
        suffixIcon={suffixIcon}
        onSelect={handleSelect}
        value={[]}
        filterOption={false}
        onSearch={handleSearch}
        dropdownRender={menu => (
          <Spin
            spinning={externalData ? false : isLoading || isFetchingNextPage} // Không hiển thị loading nếu dùng externalData
            size="small"
            tip="Loading..."
          >
            {menu}
          </Spin>
        )}
        style={{ width: '100%' }}
        options={filterData}
      />
      <div className="wrapper-tag-multi">
        <Space size={[0, 8]} wrap>
          {listSelectValues?.map((value, index) => renderTag(value, index))}
        </Space>
      </div>
    </div>
  );
}

export default memo(MultiSelectLazy, areEqual);
