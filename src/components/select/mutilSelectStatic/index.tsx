import { CheckOutlined } from '@ant-design/icons';
import { Select, Space, Tag, Tooltip, Typography } from 'antd';
import debounce from 'lodash/debounce';
import isArray from 'lodash/isArray';
import { useEffect, useMemo, useRef, useState } from 'react';
import { OptionTypeSelect } from '../../../types/common/common';
import { normalizeString } from '../../../utilities/regex';
import './styles.scss';
import { PRIMARY_STATUS } from '../../../constants/common';

interface TMultiSelectStatic<T> {
  data: OptionTypeSelect[];
  handleListSelect?: (values: T[]) => void;
  keysTag?: string | string[];
  defaultValues?: { label: string | JSX.Element; value: string }[];
  value?: string[]; // Thêm prop value để nhận giá trị từ form
  placeholder?: string;
  suffixIcon?: React.ReactNode;
  showSelectAll?: boolean;
  disabled?: boolean;
}

function MultiSelectStatic<T>(props: TMultiSelectStatic<T>) {
  const {
    data,
    handleListSelect,
    keysTag,
    defaultValues,
    value, // Nhận prop value
    placeholder,
    suffixIcon,
    showSelectAll,
    disabled = false,
  } = props;
  const selectRef = useRef<HTMLDivElement>(null);

  const [filterData, setFilterData] = useState<OptionTypeSelect[]>(data);
  const [listSelectValues, setListSelectValues] = useState<OptionTypeSelect[]>([]);
  const [openDropdown, setOpenDropdown] = useState(false);
  const [search, setSearch] = useState<string>();

  // Đồng bộ listSelectValues với value hoặc defaultValues
  useEffect(() => {
    if (value !== undefined) {
      // Nếu có prop value, đồng bộ với giá trị từ form
      const selectedValues = data.filter(item => value.includes(item?.value as string));
      setListSelectValues(selectedValues);
    } else {
      // Nếu không có value, dùng defaultValues
      setListSelectValues((defaultValues as unknown as OptionTypeSelect[]) || []);
    }
  }, [value, defaultValues, data]);

  // Lọc dữ liệu dựa trên tìm kiếm và cập nhật trạng thái chọn
  const filteredData = useMemo(() => {
    let result = data;
    if (search) {
      const normalizedSearch = normalizeString(search);
      result = data.filter(item => normalizeString(String(item.label)).includes(normalizedSearch));
    }

    return result.map(item => {
      const isSelected = listSelectValues.some(option => option.value === item.value);
      const statusClass = PRIMARY_STATUS.find(status => status.value === item.value)?.className || '';

      return {
        ...item,
        label: (
          <div
            style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}
            className={`status-tag ${statusClass}`}
          >
            <Tooltip title={item.label}>
              <Typography.Text ellipsis style={{ verticalAlign: 'middle' }}>
                {item.label}
              </Typography.Text>
            </Tooltip>
            {isSelected && <CheckOutlined style={{ color: '#1677ff' }} />}
          </div>
        ),
        style: isSelected ? { fontWeight: '600', backgroundColor: '#e6f4ff' } : undefined,
      };
    });
  }, [data, search, listSelectValues]);

  // Thêm tùy chọn "Tất cả" nếu showSelectAll = true
  useEffect(() => {
    const newFilterData = [...filteredData];
    if (showSelectAll) {
      newFilterData.unshift({
        value: 'all',
        label: (
          <div className="select-all-option">
            <Typography.Text ellipsis style={{ verticalAlign: 'middle' }}>
              Tất cả
            </Typography.Text>
            {listSelectValues.length === data.length && <CheckOutlined style={{ color: '#1677ff' }} />}
          </div>
        ),
        style: listSelectValues.length === data.length ? { fontWeight: '600', backgroundColor: '#e6f4ff' } : undefined,
      });
    }
    setFilterData(newFilterData);
  }, [filteredData, showSelectAll, listSelectValues, data.length]);

  const handleSearch = debounce((value: string) => {
    setSearch(value ? normalizeString(value) : undefined);
  }, 500);

  const handleSelect = (_: string, options: OptionTypeSelect | OptionTypeSelect[]) => {
    const selectedOptions = isArray(options) ? options : [options];

    if (selectedOptions[0].value === 'all') {
      const allSelected = listSelectValues.length === data.length;
      const updatedList = allSelected
        ? []
        : data.map(item => ({
            ...item,
            label: (
              <div className="select-all-option">
                <Tooltip title={item.label}>
                  <Typography.Text ellipsis style={{ verticalAlign: 'middle' }}>
                    {item.label}
                  </Typography.Text>
                </Tooltip>
                <CheckOutlined style={{ color: '#1677ff' }} />
              </div>
            ),
            style: { fontWeight: '600', backgroundColor: '#e6f4ff' },
          }));

      setListSelectValues(updatedList);
      handleListSelect && handleListSelect(updatedList as T[]);
    } else {
      const isExistence = listSelectValues.some(selected => selected.value === selectedOptions[0].value);
      const updatedList = isExistence
        ? listSelectValues.filter(option => option.value !== selectedOptions[0].value)
        : [...listSelectValues, selectedOptions[0]];

      setListSelectValues(updatedList);
      handleListSelect && handleListSelect(updatedList as T[]);
    }
    setSearch(undefined);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setOpenDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const renderTag = (value: OptionTypeSelect, index: number) => {
    const tagContent = keysTag
      ? isArray(keysTag)
        ? keysTag.map(name => String(value[name as keyof typeof value])).join(' - ')
        : String(value[keysTag as keyof typeof value])
      : String(value.label);

    const maxLength = 40;
    const displayText = tagContent.length > maxLength ? `${tagContent.substring(0, maxLength)}...` : tagContent;

    const statusClass = PRIMARY_STATUS.find(status => status.value === value.value)?.className || '';

    return (
      <Tag
        key={index}
        closable
        onClose={() => {
          const list = listSelectValues.filter(v => v.value !== value.value);
          handleListSelect && handleListSelect(list as unknown as T[]);
          setListSelectValues(list);
        }}
        className={`status-tag ${statusClass}`}
      >
        <Tooltip title={tagContent}>
          <Typography.Text className="tag-text" ellipsis>
            {displayText}
          </Typography.Text>
        </Tooltip>
      </Tag>
    );
  };

  return (
    <div ref={selectRef}>
      <Select
        showSearch
        disabled={disabled}
        open={openDropdown}
        getPopupContainer={trigger => trigger.parentNode as HTMLElement}
        onClick={() => setOpenDropdown(true)}
        popupClassName="popup-select-multi"
        placeholder={placeholder}
        suffixIcon={suffixIcon}
        onSelect={handleSelect}
        value={[]}
        filterOption={false}
        onSearch={handleSearch}
        style={{ width: '100%' }}
        options={filterData}
      />
      <div className="wrapper-tag-multi">
        <Space size={[0, 8]} wrap>
          {listSelectValues.map((value, index) => renderTag(value, index))}
        </Space>
      </div>
    </div>
  );
}

export default MultiSelectStatic;
