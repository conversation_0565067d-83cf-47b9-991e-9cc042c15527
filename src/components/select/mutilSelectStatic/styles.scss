.popup-select-multi {
  .wrapper-spin {
    text-align: center;
    .ant-spin {
      padding: 8px;
    }
  }
  .ant-select-item {
    padding: 5px 16px;
  }
}
.wrapper-tag-multi {
  margin-top: 8px;
  .ant-space-item .ant-tag {
    display: flex;
  }
}
.select-all-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.tag-text {
  max-width: 250px;
  display: inline-block;
  font-size: 12px;
  line-height: 20px;
}

.status-tag {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  display: inline-block;
  margin-right: 4px;

  &.status-lock {
    color: #f5222d;
    background-color: #fff1f0;
    border: 1px solid #ffa39e;
  }

  &.status-close {
    color: #13c2c2;
    background-color: #e6fffb;
    border: 1px solid #87e8de;
  }

  &.status-coming {
    color: #fadb14;
    background-color: #feffe6;
    border: 1px solid #fffb8f;
  }

  &.status-processing {
    color: #faad14;
    background-color: #fffbe6;
    border: 1px solid #ffe58f;
  }

  &.status-confirm {
    color: #1677ff;
    background-color: #e6f4ff;
    border: 1px solid #91caff;
  }

  &.status-mconfirm {
    color: #2f54eb;
    background-color: #f0f5ff;
    border: 1px solid #adc6ff;
  }

  &.status-success {
    color: #52c41a;
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
  }

  &.status-msuccess {
    color: #237804;
    background-color: #d9f7be;
    border: 1px solid #73d13d;
  }

  &.status-lock-confirm {
    color: #722ed1;
    background-color: #f9f0ff;
    border: 1px solid #d3adf7;
  }

  &.status-lock-confirm-lock {
    color: #fa541c;
    background-color: #fff2e8;
    border: 1px solid #ffbb96;
  }

  &.status-unsuccess {
    color: #fa8c16;
    background-color: #fff7e6;
    border: 1px solid #ffd591;
  }

  &.status-cancel {
    color: #434343;
    background-color: #f5f5f5;
    border: 1px solid #bfbfbf;
  }

  &.status-moved {
    color: #eb2f96;
    background-color: #fff0f6;
    border: 1px solid #ffadd2;
  }
}
