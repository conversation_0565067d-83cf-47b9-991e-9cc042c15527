import { useState } from 'react';
import { DatePicker, message, DatePickerProps, Skeleton } from 'antd';
import dayjs from 'dayjs';
import { FORMAT_DATE } from '../../constants/common';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import localeData from 'dayjs/plugin/localeData';
import weekday from 'dayjs/plugin/weekday';
import weekOfYear from 'dayjs/plugin/weekOfYear';
import weekYear from 'dayjs/plugin/weekYear';

dayjs.extend(customParseFormat);
dayjs.extend(advancedFormat);
dayjs.extend(weekday);
dayjs.extend(localeData);
dayjs.extend(weekOfYear);
dayjs.extend(weekYear);
interface MyDatePickerProps extends Omit<DatePickerProps, 'onChange' | 'value'> {
  onDateChange: (date: dayjs.Dayjs | null) => void;
  value: dayjs.Dayjs | null;
  loading?: boolean;
}

const MyDatePicker: React.FC<MyDatePickerProps> = ({ onDateChange, value, loading, ...restProps }) => {
  const [date, setDate] = useState<dayjs.Dayjs | null>(null);

  const handleChange = (value: dayjs.Dayjs | null) => {
    setDate(value);
    onDateChange(value);
  };

  const handleBlur = () => {
    if (!date) {
      return;
    }

    const trimmedValue = date.format(FORMAT_DATE).trim();
    const parsedDate = dayjs(trimmedValue, FORMAT_DATE, true);

    if (trimmedValue && !parsedDate.isValid()) {
      message.error('Ngày không hợp lệ. Vui lòng nhập đúng định dạng!');
      setDate(null);
      return;
    }

    setDate(parsedDate.isValid() ? parsedDate : null);
    onDateChange(parsedDate.isValid() ? parsedDate : null);
  };

  return loading ? (
    <Skeleton.Input active style={{ width: '100%' }} />
  ) : (
    <DatePicker
      style={{ width: '100%' }}
      format={FORMAT_DATE}
      value={value}
      inputReadOnly={false}
      onBlur={handleBlur}
      onChange={handleChange}
      placeholder=""
      allowClear
      {...restProps}
    />
  );
};

export default MyDatePicker;
