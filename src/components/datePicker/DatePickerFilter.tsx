import { Col, DatePicker, Form, Row } from 'antd';
import dayjs from 'dayjs';
import { FORMAT_DATE } from '../../constants/common';

const DatePickerFilter = ({ startDate, endDate }: { startDate: string; endDate: string }) => {
  const form = Form.useFormInstance();
  return (
    <Row gutter={16}>
      <Col span={12}>
        <Form.Item label="Từ ngày" name={startDate}>
          <DatePicker
            format={FORMAT_DATE}
            disabledDate={current => {
              const endCreatedDate = form.getFieldValue(endDate);
              return (
                (current && current > dayjs().startOf('day')) ||
                (endCreatedDate && current > dayjs(endCreatedDate).startOf('day')) // Disable ngày sau "Đến ngày"
              );
            }}
          />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item label="Đến ngày" name={endDate}>
          <DatePicker
            format={FORMAT_DATE}
            disabledDate={current => {
              const startCreatedDate = form.getFieldValue(startDate);
              return (
                (current && current > dayjs().startOf('day')) ||
                (startCreatedDate && current < dayjs(startCreatedDate).startOf('day')) // Disable ngày trước "Ngày tạo"
              );
            }}
          />
        </Form.Item>
      </Col>
    </Row>
  );
};

export default DatePickerFilter;
