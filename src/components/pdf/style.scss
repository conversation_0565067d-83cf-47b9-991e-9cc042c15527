.pdf-container {
  max-width: 900px;
  margin: 0 auto;
  overflow: auto;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
  border-radius: 8px;

  .ant-card {
    border-radius: 8px;
    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
    overflow: hidden;
    width: 100%;

    .pdf-controls {
      margin-bottom: 16px;
      text-align: center;

      .ant-btn {
        background-color: #ffffff;
        border-color: #d9d9d9;
        color: #1890ff;

        &:hover {
          background-color: #1890ff;
          border-color: #1890ff;
          color: #ffffff;
        }

        &:disabled {
          background-color: #f5f5f5;
          border-color: #d9d9d9;
          color: rgba(0, 0, 0, 0.25);
          cursor: not-allowed;
        }
      }
    }

    .pdf-page-container {
      display: flex;
      justify-content: center;
      width: 100%;
      overflow: auto;

      .react-pdf__Page {
        box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25); // Keeping the specified shadow
        border-radius: 4px;
        width: 100%;
      }
    }
  }
}
