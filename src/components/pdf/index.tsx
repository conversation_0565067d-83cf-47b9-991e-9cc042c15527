import React, { useState, useEffect } from 'react';
import { Document, Page } from 'react-pdf';
import { <PERSON><PERSON>, <PERSON>, Row, Col } from 'antd';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import './style.scss';
import '../../utilities/configPdf';

// import { set } from 'lodash';

interface PdfViewerProps {
  blob?: Blob;
  pdfBase64?: string | null;
}

const PdfViewer: React.FC<PdfViewerProps> = ({
  //  blob,
  pdfBase64,
}) => {
  const [numPages, setNumPages] = useState(1);
  const [pageNumber, setPageNumber] = useState(1);
  const [pageWidth, setPageWidth] = useState(800);
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);

  useEffect(() => {
    if (pdfBase64) {
      // sử dụng Base64 để tạo URL và privew PDF
      // setPdfUrl(`${pdfBase64}`);
      // console.log(pdfUrl);
      const base64 = pdfBase64.replace(/^data:application\/pdf;base64,/, '');
      const byteCharacters = atob(base64);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: 'application/pdf' });
      const pdfUrl = URL.createObjectURL(blob);
      setPdfUrl(pdfUrl);
    }
  }, [pdfBase64]);

  useEffect(() => {
    const updateWidth = () => setPageWidth(window.innerWidth <= 576 ? 300 : window.innerWidth <= 768 ? 500 : 800);
    updateWidth();
    window.addEventListener('resize', updateWidth);
    return () => window.removeEventListener('resize', updateWidth);
  }, []);

  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => setNumPages(numPages);
  const prevPage = () => setPageNumber(prev => Math.max(prev - 1, 1));
  const nextPage = () => setPageNumber(prev => Math.min(prev + 1, numPages));

  return (
    <div className="pdf-container">
      {pdfUrl ? (
        <Card>
          {numPages > 1 && (
            <Row justify="center" gutter={8} className="pdf-controls">
              <Col>
                <Button size="small" icon={<LeftOutlined />} onClick={prevPage} disabled={pageNumber === 1} />
              </Col>
              <Col>
                <span>
                  {pageNumber} / {numPages}
                </span>
              </Col>
              <Col>
                <Button size="small" icon={<RightOutlined />} onClick={nextPage} disabled={pageNumber === numPages} />
              </Col>
            </Row>
          )}
          <div className="pdf-page-container">
            <Document file={pdfUrl} onLoadSuccess={onDocumentLoadSuccess}>
              <Page pageNumber={pageNumber} width={pageWidth} />
            </Document>
          </div>
        </Card>
      ) : (
        <p>Không có file PDF</p>
      )}
    </div>
  );
};

export default PdfViewer;
