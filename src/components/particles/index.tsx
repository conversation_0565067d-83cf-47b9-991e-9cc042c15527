import { type ISourceOptions } from '@tsparticles/engine';
import Particles, { initParticlesEngine } from '@tsparticles/react';
import { loadSlim } from '@tsparticles/slim';
import { useEffect, useState } from 'react';

const ParticlesComponent = () => {
  const [init, setInit] = useState(false);
  const initPE = () => {
    initParticlesEngine(async engine => {
      await loadSlim(engine);
    }).then(() => {
      setInit(true);
    });
  };
  useEffect(() => {
    initPE();
  }, []);
  const options: ISourceOptions = {
    name: 'Mouse Follow',

    interactivity: {
      events: {
        onHover: {
          enable: true,
          mode: ['bubble', 'connect'],
        },
      },
      modes: {
        bubble: {
          distance: 200,
          duration: 2,
          opacity: 0.5,
          size: 3,
          color: {
            value: ['#fffff'],
          },
        },
        connect: {
          distance: 100,
          links: {
            opacity: 0.2,
          },
          radius: 200,
        },
      },
    },
    particles: {
      color: {
        value: '#ffff',
      },
      move: {
        direction: 'none',
        enable: true,
        speed: 2,
      },
      number: {
        density: {
          enable: true,
        },
        value: 350,
      },
      opacity: {
        value: 0,
      },
      shape: {
        type: 'circle',
      },
      size: {
        value: {
          min: 1,
          max: 5,
        },
      },
    },
    detectRetina: true,
  };
  return init ? <Particles id="tsparticles" options={options} /> : null;
};

export default ParticlesComponent;
