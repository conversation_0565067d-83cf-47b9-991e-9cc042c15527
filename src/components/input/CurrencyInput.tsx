import React, { useState, useEffect } from 'react';
import { Input, InputProps } from 'antd';
import { formatCurrency } from '../../utilities/shareFunc';

// Đ<PERSON>nh nghĩa kiểu dữ liệu cho props
interface CurrencyInputProps extends Omit<InputProps, 'onChange'> {
  value?: string; // Giá trị hiện tại của input
  onChange?: (value: string) => void; // Callback để cập nhật giá trị
  maxLength?: number;
  type?: string;
  placeholder?: string;
  suffix?: string;
  [key: string]: unknown;
}

const CurrencyInput: React.FC<CurrencyInputProps> = props => {
  const { value = '', onChange, type = 'text', maxLength = 17, suffix = 'VND', ...restProps } = props;
  const [displayValue, setDisplayValue] = useState<string>(formatCurrency(value));

  // <PERSON><PERSON><PERSON> bộ giá trị hiển thị khi giá trị từ ngoài thay đổi
  useEffect(() => {
    setDisplayValue(formatCurrency(value));
  }, [value]);

  // Xử lý thay đổi giá trị
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value.replace(/,/g, ''); // Loại bỏ dấu phẩy
    if (/^\d{0,13}$/.test(inputValue)) {
      setDisplayValue(formatCurrency(inputValue));

      onChange && onChange(inputValue); // Trả về giá trị số thô
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Cho phép: số, các phím điều hướng, Delete, Backspace
    const allowedKeys = ['ArrowLeft', 'ArrowRight', 'Delete', 'Backspace', 'Tab', 'Enter', 'Home', 'End'];
    if (!allowedKeys.includes(e.key) && !/^\d$/.test(e.key)) {
      e.preventDefault();
    }
  };

  return (
    <Input
      {...restProps}
      type={type}
      onKeyDown={handleKeyDown}
      value={displayValue}
      maxLength={maxLength}
      onChange={handleChange}
      suffix={suffix}
    />
  );
};

export default CurrencyInput;
