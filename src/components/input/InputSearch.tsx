import { Input } from 'antd';
import { SearchProps } from 'antd/es/input';
import { InputProps } from 'antd/lib';
import debounce from 'lodash/debounce';
import { useLocation } from 'react-router-dom';
import useFilter from '../../hooks/filter';
import { SearchOutlined } from '@ant-design/icons';
import { normalizeString } from '../../utilities/regex';

interface ISearch extends InputProps {
  keySearch?: string;
  showParams?: boolean;
  onChange?: (e: unknown) => void;
}

function InputSearch(props: ISearch) {
  const { placeholder = 'Tìm kiếm', keySearch = 'search', onChange, showParams = true, ...restProps } = props;
  const [filter, setFilter] = useFilter();
  const { search } = useLocation();
  const params = new URLSearchParams(search);

  const debouncedHandleSearch = debounce((searchTerm: string) => {
    const normalizeSearchTerm = normalizeString(searchTerm);
    showParams
      ? setFilter({ ...filter, page: '1', [keySearch]: normalizeSearchTerm })
      : onChange?.(normalizeSearchTerm);
  }, 1000);

  const handleChange: InputProps['onChange'] = event => {
    const searchTerm = event.target.value;
    debouncedHandleSearch(searchTerm);
  };
  const handleOnSearch: SearchProps['onSearch'] = value => {
    const normalizeSearchTerm = normalizeString(value);
    showParams
      ? setFilter({ ...filter, page: '1', [keySearch]: normalizeSearchTerm })
      : onChange?.(normalizeSearchTerm);
  };
  return (
    <Input
      className="input-search"
      onChange={handleChange}
      defaultValue={showParams ? (params.get(keySearch) ?? '') : ''}
      suffix={
        <div onClick={() => handleOnSearch} style={{ cursor: 'pointer' }}>
          <SearchOutlined style={{ color: 'rgba(0,0,0,.25)' }} />
        </div>
      }
      placeholder={placeholder}
      allowClear
      onClick={() => handleOnSearch}
      {...restProps}
    />
  );
}

export default InputSearch;
