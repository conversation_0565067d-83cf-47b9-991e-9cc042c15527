import { Input } from 'antd';
import { useEffect, useState } from 'react';

interface PercentInputProps {
  value?: string;
  onChange?: (value: string) => void;
  onBlur?: (value: string) => void;
  type?: string;
  maxLengthInt?: number;
  placeholder?: string;
  suffix?: string;
  max?: number;
  [key: string]: unknown;
}

const PercentInput = (props: PercentInputProps) => {
  const {
    value,
    onChange,
    type = 'text',
    placeholder,
    suffix = '%',
    maxLengthInt = 3,
    onBlur,
    max,
    ...restProps
  } = props;
  const [internalValue, setInternalValue] = useState(value || '');

  useEffect(() => {
    setInternalValue(value || '');
  }, [value]);

  // Kiểm tra định dạng xxx.xx (tối đa 3 số trước dấu chấm, 2 số sau dấu chấm)
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;

    if (inputValue === '') {
      // Cho phép chuỗi rỗng
      setInternalValue('');
      onChange && onChange('');
      return;
    }

    const regex = new RegExp(`^(\\d{0,${maxLengthInt}})(\\.\\d{0,2})?$`);
    if (regex.test(inputValue)) {
      const numericValue = parseFloat(inputValue);
      if (max !== undefined && numericValue > max) {
        setInternalValue(max.toString());
        onChange && onChange(max.toString());
      } else {
        setInternalValue(inputValue);
        onChange && onChange(inputValue);
      }
    }
  };

  const formatValue = (value: string): string => {
    if (!value || value === '') return '0';

    let formattedValue = value;

    // Xử lý các trường hợp đặc biệt
    if (formattedValue === '.') {
      formattedValue = '0.00';
    } else if (formattedValue.startsWith('.')) {
      formattedValue = '0' + formattedValue;
    }

    // Thêm phần thập phân nếu cần
    if (formattedValue.includes('.')) {
      // Đảm bảo có đúng 2 chữ số sau dấu chấm
      const parts = formattedValue.split('.');
      formattedValue = parts[0] + '.' + (parts[1] + '00').slice(0, 2);
    } else if (formattedValue !== '' && formattedValue !== '0') {
      // Nếu không có phần thập phân, thêm .00
      formattedValue = formattedValue + '.00';
    }

    return formattedValue;
  };

  const handleBlur = () => {
    // Format giá trị
    const formattedValue = formatValue(internalValue);

    setInternalValue(formattedValue);

    onChange && onChange(formattedValue);
    onBlur && onBlur(formattedValue);
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleBlur();
      e.preventDefault();
    }
  };

  return (
    <Input
      {...restProps}
      type={type}
      value={internalValue}
      onChange={handleChange}
      onBlur={handleBlur}
      onKeyDown={handleKeyPress}
      placeholder={placeholder}
      suffix={suffix}
    />
  );
};

export default PercentInput;
