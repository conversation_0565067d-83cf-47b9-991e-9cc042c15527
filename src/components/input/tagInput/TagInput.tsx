import React, { useState, KeyboardEvent, ChangeEvent } from 'react';
import { Input, Tag } from 'antd';
import './style.scss';

interface TagInputProps {
  value: string[];
  onChange: (newTags: string[]) => void;
  placeholder?: string;
}

const TagInput: React.FC<TagInputProps> = ({ value = [], onChange, placeholder }) => {
  const [inputValue, setInputValue] = useState('');

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const handleInputConfirm = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && inputValue && !value.includes(inputValue)) {
      const newTags = [...value, inputValue];
      onChange(newTags);
      setInputValue('');
    }
  };

  const handleClose = (removedTag: string) => {
    const newTags = value.filter(tag => tag !== removedTag);
    onChange(newTags);
  };

  return (
    <div className="tag-input-container">
      <Input
        placeholder={placeholder || 'Enter a tag'}
        value={inputValue}
        onChange={handleInputChange}
        onKeyDown={handleInputConfirm}
        className="tag-input"
        maxLength={50}
      />
      <div className="tags-container">
        {value.map((tag, index) => (
          <Tag key={index} closable onClose={() => handleClose(tag)}>
            {tag}
          </Tag>
        ))}
      </div>
    </div>
  );
};

export default TagInput;
