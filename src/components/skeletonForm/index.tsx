import { Checkbox, Input, InputNumber, Select, Skeleton, Switch, TimePicker } from 'antd';
import { ComponentType } from 'react';

const withSkeleton = <P extends object>(Component: ComponentType<P>) => {
  return ({ loading, ...props }: P & { loading: boolean }) =>
    loading ? <Skeleton.Input active style={{ width: '100%' }} /> : <Component {...(props as P)} />;
};

export const SkeletonFormInput = withSkeleton(Input);
export const SkeletonFormSelect = withSkeleton(Select);
export const SkeletonFormCheckBox = withSkeleton(Checkbox);
export const SkeletonFormInputNumber = withSkeleton(InputNumber);
export const SkeletonFormTimePickerRangePicker = withSkeleton(TimePicker.RangePicker);
export const SkeletonFormSwitch = withSkeleton(Switch);
