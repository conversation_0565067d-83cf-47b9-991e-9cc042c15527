import React, { useEffect, useState } from 'react';
import { UploadOutlined } from '@ant-design/icons';
import { Upload, Button } from 'antd';
import type { UploadFile } from 'antd';
import { UploadRequestError, UploadRequestOption } from 'rc-upload/lib/interface';
import { uploadMedia } from '../../service/upload';
import { RcFile } from 'antd/es/upload/interface';
import { FileSample } from '../../types/formReport';
import './style.scss';
import { v4 as uuid } from 'uuid';

const ALLOWED_FILE_TYPES = {
  DOC: 'application/msword', // .doc
  XLS: 'application/vnd.ms-excel', // xls
  DOCX: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
  XLSX: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
};

const DEFAULT_MAX_FILE_SIZE_MB = 10;
const DEFAULT_UPLOAD_PATH = 'your/upload/path';

interface FileUploadProps {
  maxFileSizeMB?: number;
  uploadPath?: string;
  originalname?: string;
  fileUrl?: string;
  onUploadSuccess?: (fileSample: Partial<FileSample>) => void;
  onUploadError?: (error: Error | unknown) => void;
}

const validateFileType = (file: UploadFile): boolean => Object.values(ALLOWED_FILE_TYPES).includes(file.type || '');

const validateFileSize = (file: UploadFile, maxSizeMB: number): boolean =>
  file.size ? file.size / 1024 / 1024 < maxSizeMB : false;

const showError = (message: string): void => alert(message);

const FileUpload: React.FC<FileUploadProps> = ({
  maxFileSizeMB = DEFAULT_MAX_FILE_SIZE_MB,
  uploadPath = DEFAULT_UPLOAD_PATH,
  originalname,
  fileUrl,
  onUploadSuccess,
  onUploadError,
}) => {
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  useEffect(() => {
    if (originalname && !fileList.length) {
      setFileList([
        {
          uid: uuid(),
          name: originalname,
          status: 'done',
          url: fileUrl?.startsWith(`${import.meta.env.VITE_S3_IMAGE_URL}`)
            ? fileUrl
            : `${import.meta.env.VITE_S3_IMAGE_URL}/${fileUrl}`,
        },
      ]);
    }
  }, [fileList.length, fileUrl, originalname]);

  const handleBeforeUpload = (file: UploadFile, maxSizeMB: number): boolean | typeof Upload.LIST_IGNORE => {
    const isValidType = validateFileType(file);
    const isValidSize = validateFileSize(file, maxSizeMB);

    if (!isValidType) {
      showError('Định dạng file không hợp lệ');
      return Upload.LIST_IGNORE;
    }
    if (!isValidSize) {
      showError(`Dung lượng file vượt quá giới hạn ${maxFileSizeMB}MB`);
      return Upload.LIST_IGNORE;
    }
    return true;
  };

  const handleCustomRequest = async ({ file, onSuccess, onError }: UploadRequestOption) => {
    try {
      // 1. Upload file
      const uploadResp = await uploadMedia(file as RcFile, uploadPath);
      const fileUrl = uploadResp.data.data.Key;
      const originalname = uploadResp.data.data.originalname;
      const fullFileUrl = `${import.meta.env.VITE_S3_IMAGE_URL}/${fileUrl}`; // URL đầy đủ
      // 2. Cập nhật fileList
      const updatedFileList = fileList.map(f => {
        return f.uid === (file as RcFile).uid
          ? { ...f, status: 'done' as UploadFile['status'], url: fullFileUrl }
          : { ...f };
      });
      setFileList(updatedFileList);

      // 3. Callback onUploadSuccess nếu có
      if (onUploadSuccess) {
        const fileSampleData: Partial<FileSample> = {
          fileUrl: `${fileUrl}`,
          originalname: originalname,
        };
        onUploadSuccess(fileSampleData);
      }

      // 4. Gọi onSuccess để hoàn tất quá trình upload
      onSuccess?.(uploadResp.data.data);
    } catch (uploadError) {
      onError?.(uploadError as UploadRequestError);
      if (onUploadError) {
        onUploadError(uploadError);
      }
      showError('Tải file lên thất bại!');
      setFileList(prev =>
        prev.map(f => (f.uid === (file as RcFile).uid ? { ...f, status: 'error' as UploadFile['status'] } : f)),
      );
    }
  };
  const handleRemove = () => {
    setFileList([]);
    onUploadSuccess?.({
      fileUrl: '',
      originalname: '',
    });
  };
  const uploadButton = (
    <Button icon={<UploadOutlined />} style={{ width: '100%' }} danger={originalname ? false : true}>
      <span className="ellipsis" style={{ width: '100%' }}>
        {originalname ? originalname : `Upload file (docx, xlxs <= ${maxFileSizeMB}MB)`}
      </span>
    </Button>
  );
  return (
    <div className={'upload-wrapper'}>
      <Upload
        fileList={fileList}
        beforeUpload={file => handleBeforeUpload(file, maxFileSizeMB)}
        customRequest={handleCustomRequest}
        onRemove={handleRemove}
        showUploadList={{
          showDownloadIcon: false,
          showRemoveIcon: true,
        }}
      >
        {fileList.length === 0 && uploadButton}
      </Upload>
    </div>
  );
};

export default FileUpload;
