import React, { useState } from 'react';
import { Upload, Button, App, Typography } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import type { UploadProps, UploadFile } from 'antd';
import { RcFile } from 'antd/es/upload/interface';
import { uploadMedia } from '../../service/upload';
import { modalConfirm } from '../modal/specials/ModalConfirm';
import './style.scss';

interface ExtendedUploadFile extends UploadFile {
  key?: string;
  size?: number;
}

interface CommonFileUploadProps {
  fileList: ExtendedUploadFile[];
  setFileList: (fileList: ExtendedUploadFile[]) => void;
  uploadPath: string;
  allowedMimeTypes?: string[];
  allowedExtensions?: string[];
  maxFileSize?: number; // Kích thước tối đa của mỗi file (MB)
  maxTotalSize?: number; // Tổng kích thước tối đa (MB)
  maxFiles?: number; // Số lượng file tối đa
  label?: React.ReactNode;
  buttonText?: string;
  buttonWidth?: string | number;
}

const CommonFileUpload: React.FC<CommonFileUploadProps> = ({
  fileList,
  setFileList,
  uploadPath,
  allowedMimeTypes = [
    'image/png',
    'image/jpeg',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/pdf',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  ],
  allowedExtensions = ['png', 'jpg', 'jpeg', 'xls', 'xlsx', 'doc', 'docx', 'pdf', 'ppt', 'pptx'],
  maxFileSize = 10, // MB
  maxTotalSize = 25, // MB
  maxFiles = 10,
  label = (
    <>
      Tải tối đa {maxFiles} files, mỗi file không quá {maxFileSize}MB <br />
      Tổng dung lượng file không quá {maxTotalSize}MB
    </>
  ),
  buttonText = 'Upload',
  buttonWidth = '100px',
}) => {
  const { modal } = App.useApp();
  const [uploading, setUploading] = useState(false);

  // Hàm kiểm tra file
  const validateFile = (file: RcFile): boolean => {
    const fileListLength = fileList?.length ?? 0;
    const fileSize = file.size;
    const fileMimeType = file.type;
    const fileExtension = file.name.split('.').pop()?.toLowerCase();

    // Kiểm tra số lượng file tối đa
    if (fileListLength >= maxFiles) {
      modalConfirm({
        modal,
        title: 'Không thể upload file',
        content: `Đã đạt số lượng file tối đa ${maxFiles} file`,
        okText: 'Đóng',
      });
      return false;
    }

    // Kiểm tra định dạng và kích thước file
    const fileSizeMB = fileSize / (1024 * 1024);
    const isValidMimeType = fileMimeType && allowedMimeTypes.includes(fileMimeType);
    const isValidExtension = fileExtension && allowedExtensions.includes(fileExtension);

    if (!isValidMimeType || !isValidExtension) {
      const extensionsText = allowedExtensions.map(ext => `.${ext}`).join(', ');
      modalConfirm({
        modal,
        title: 'Không thể upload file',
        content: `Không thể upload file (chỉ chấp nhận ${extensionsText})`,
        okText: 'Đóng',
      });
      return false;
    }

    if (fileSizeMB > maxFileSize) {
      modalConfirm({
        modal,
        title: 'Không thể upload file',
        content: `Kích thước file vượt quá giới hạn ${maxFileSize}MB`,
        okText: 'Đóng',
      });
      return false;
    }

    // Kiểm tra tổng kích thước của tất cả file (bao gồm file mới)
    const totalSize = fileList.reduce((sum, item) => sum + (item.size || 0), 0) + fileSize;
    const totalSizeMB = totalSize / (1024 * 1024);

    if (totalSizeMB > maxTotalSize) {
      modalConfirm({
        modal,
        title: 'Không thể upload file',
        content: `Tổng kích thước các file vượt quá giới hạn ${maxTotalSize}MB`,
        okText: 'Đóng',
      });
      return false;
    }

    return true;
  };

  // Hàm xử lý upload file
  const handleUpload = async (file: RcFile): Promise<boolean> => {
    if (!validateFile(file)) {
      return false;
    }
    setUploading(true);
    try {
      const uploadResp = await uploadMedia(file, uploadPath);
      const fileUrl = uploadResp?.data?.data?.key
        ? `${import.meta.env.VITE_S3_IMAGE_URL}/${uploadResp.data.data.key}`
        : '';
      const uploadedFile: ExtendedUploadFile = {
        uid: file.uid,
        name: file.name,
        status: 'done',
        url: fileUrl,
        key: uploadResp?.data?.data?.key,
        size: file.size, // Lưu kích thước file
      };

      // Thêm tệp mới vào filelist hiện có
      setFileList([...fileList, uploadedFile]);
      return false;
    } catch (error) {
      return false;
    } finally {
      setUploading(false);
    }
  };

  // Upload props
  const props: UploadProps = {
    name: 'file',
    multiple: false,
    beforeUpload: handleUpload,
    fileList,
    onRemove: (file: UploadFile) => {
      const newFileList = fileList.filter(item => item.uid !== file.uid);
      setFileList(newFileList);
    },
    onPreview: file => {
      window.open(file.url, '_blank');
    },
    onChange: () => {},
  };

  return (
    <Upload {...props}>
      <Button
        icon={<UploadOutlined />}
        style={{ width: buttonWidth, marginBottom: 8 }}
        loading={uploading}
        disabled={uploading}
      >
        {buttonText}
      </Button>
      <div>
        <Typography.Text style={{ fontSize: 12, color: '#00000073' }}>{label}</Typography.Text>
      </div>
    </Upload>
  );
};

export default CommonFileUpload;
