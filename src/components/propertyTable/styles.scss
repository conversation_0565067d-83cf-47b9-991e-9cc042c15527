.custom-table-room {
  display: flex;
  gap: 52px;
  .name-block {
    margin-bottom: 0;
    background-color: #00000005;
    padding: 8px 16px;
  }

  .virtual-table-cell {
    box-sizing: border-box;
    padding: 0;
    border-top: 1px solid #f0f0f0b7;
    border-bottom: 1px solid #f0f0f0b7;
    border-right: 1px solid #f0f0f097;
    font-size: 12px;
    line-height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    word-break: break-word;
    text-align: center;
    &.header {
      background-color: #fafafa;
      font-weight: 400;
    }
  }

  .status-cell {
    display: flex;
    width: 100%;
    height: 100%;
    align-items: center;
    justify-content: center;
  }

  .status-lock {
    color: #52c41a;
    background-color: #f6ffed;
    border: 1px solid #b7ebbf;
  }

  .status-close {
    color: #ffad14;
    background-color: #fffbe6;
    border: 1px solid #ffe58f;
  }

  .status-coming {
    color: #ffffff;
    background-color: #ffa500;
    border: 1px solid #ffa500;
  }

  .status-processing {
    color: #ffffff;
    background-color: #0000ff;
    border: 1px solid #0000ff;
  }

  .status-confirm {
    color: #ffffff;
    background-color: #008000;
    border: 1px solid #008000;
  }

  .status-mconfirm {
    color: #ffffff;
    background-color: #800080;
    border: 1px solid #800080;
  }

  .status-success {
    color: #ffffff;
    background-color: #00ff00;
    border: 1px solid #00ff00;
  }

  .status-msuccess {
    color: #ffffff;
    background-color: #00ffff;
    border: 1px solid #00ffff;
  }

  .status-lock-confirm {
    color: #ffffff;
    background-color: #ffd700;
    border: 1px solid #ffd700;
  }

  .status-lock-confirm-lock {
    color: #ffffff;
    background-color: #ff4500;
    border: 1px solid #ff4500;
  }

  .status-unsuccess {
    color: #ffffff;
    background-color: #a52a2a;
    border: 1px solid #a52a2a;
  }

  .status-cancel {
    color: #ffffff;
    background-color: #000000;
    border: 1px solid #000000;
  }

  .status-moved {
    color: #ffffff;
    background-color: #1e90ff;
    border: 1px solid #1e90ff;
  }

  .status-default {
    color: inherit;
    background-color: inherit;
    border: 1px solid inherit;
  }
}
