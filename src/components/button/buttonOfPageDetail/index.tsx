import { App, Button, Space } from 'antd';
import './styles.scss';

interface Props {
  handleSubmit?: () => void;
  handleCancel?: () => void;
  loadingSubmit?: boolean;
  isShowModal?: boolean;
}

const ButtonOfPageDetail = (props: Props) => {
  const { handleSubmit, handleCancel, loadingSubmit, isShowModal = true } = props;
  const { modal } = App.useApp();

  const handleResetField = () => {
    isShowModal
      ? modal.confirm({
          title: '<PERSON><PERSON>c nhận hủy',
          content: 'Dữ liệu đang chưa đư<PERSON> lư<PERSON>, bạn có chắc muốn huỷ dữ liệu đang nhập không?',
          cancelText: 'Quay lại',
          onOk: handleCancel,
          okButtonProps: {
            type: 'default',
          },
          cancelButtonProps: {
            type: 'primary',
          },
        })
      : handleCancel && handleCancel();
  };

  return (
    <div className="buttons-detail-footer">
      <Space className="space-button" size={'middle'}>
        {handleCancel && (
          <Button size="small" type="primary" onClick={handleResetField}>
            Huỷ
          </Button>
        )}
        {handleSubmit && (
          <Button size="small" type="default" onClick={handleSubmit} loading={loadingSubmit}>
            Lưu thay đổi
          </Button>
        )}
      </Space>
    </div>
  );
};

export default ButtonOfPageDetail;
