import { UploadOutlined } from '@ant-design/icons';
import { App, Button, Modal, Upload, UploadFile } from 'antd';
import { RcFile } from 'antd/es/upload';
import { AxiosResponse } from 'axios';
import React, { useState } from 'react';
import './styles.scss';
import { downloadExcelTemplate } from './downloadExcelTemplate';
export type DownLoadTempExcelT = { nameFile: string; dataTemplateExcel: string[][] };
interface ErrorT {
  data: {
    errors: {
      message: string;
    };
  };
}

interface PopupExcelUploadProps extends DownLoadTempExcelT {
  isVisible: boolean;
  onClose: () => void;
  refetch: () => void;
  api: (data: FormData) => Promise<AxiosResponse<unknown, unknown>>;
}

const PopupExcelUpload: React.FC<PopupExcelUploadProps> = (props: PopupExcelUploadProps) => {
  const { isVisible, onClose, nameFile, dataTemplateExcel, api, refetch } = props;
  const { notification } = App.useApp();
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [uploadedFile, setUploadedFile] = useState<RcFile | null>(null);

  const handleUploadChange = ({ fileList }: { fileList: UploadFile[] }) => {
    setFileList(fileList);
    if (fileList.length > 0) {
      setUploadedFile(fileList[0].originFileObj as RcFile);
    } else {
      setUploadedFile(null);
    }
  };

  const handleSubmit = async () => {
    if (!uploadedFile) return;

    const formData = new FormData();
    formData.append('files', uploadedFile);

    try {
      await api?.(formData);
      notification.success({ message: 'Nhập dữ liệu thành công' });
      onClose?.();
      refetch();
      setFileList([]);
    } catch (error: ErrorT | unknown) {
      const message = (error as ErrorT).data?.errors?.message || 'Có lỗi xảy ra';
      notification.error({ message: message });
    }
  };

  return (
    <Modal
      title="Nhập biểu mẫu Excel"
      className="modal-upload-excel"
      open={isVisible}
      onCancel={onClose}
      centered
      footer={[
        <Button disabled={fileList.length === 0} key="submit" type="primary" onClick={handleSubmit}>
          Nhập dữ liệu
        </Button>,
      ]}
    >
      <p className="title-1">Upload file theo mẫu để nhập dữ liệu nhân viên vào hệ thống</p>
      <p>
        Nếu bạn chưa có file excel template mẫu, vui lòng tải tại{' '}
        <a
          href="#"
          onClick={e => {
            e.preventDefault();
            downloadExcelTemplate({ nameFile, dataTemplateExcel });
          }}
          style={{ padding: 0, textDecoration: 'none', color: 'blue' }}
        >
          đây
        </a>
      </p>

      <Upload
        className="file-upload"
        listType="picture"
        fileList={fileList}
        onChange={handleUploadChange}
        beforeUpload={() => false}
        accept=".xlsx,.xls"
      >
        {fileList.length === 0 && (
          <Button className="upload" icon={<UploadOutlined />}>
            Upload
          </Button>
        )}
      </Upload>
    </Modal>
  );
};

export default PopupExcelUpload;
