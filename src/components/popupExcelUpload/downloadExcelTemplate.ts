import ExcelJS, { Cell } from 'exceljs';

export const downloadExcelTemplate = async ({
  nameFile,
  dataTemplateExcel,
}: {
  nameFile: string;
  dataTemplateExcel: string[][];
}) => {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Template');

  // Thêm dữ liệu vào worksheet
  dataTemplateExcel.forEach((row, rowIndex) => {
    const excelRow = worksheet.addRow(row);
    excelRow.eachCell((cell: Cell) => {
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      if (rowIndex === 1) {
        excelRow.hidden = true;
      }
      if (rowIndex === 0) {
        cell.font = { bold: true };
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFFFE0B2' },
        };
        if (cell.value && cell.value.toString().includes('*')) {
          const parts = cell.value.toString().split('*');
          cell.value = {
            richText: [{ text: parts[0] }, { text: '*', font: { color: { argb: 'FFFF0000' } } }],
          };
        }
      }
    });
  });

  worksheet.columns = dataTemplateExcel[0].map((header, index) => {
    const maxLength = dataTemplateExcel.reduce((max, row) => Math.max(max, row[index].length), header.length);
    return { width: maxLength + 2 };
  });

  // Xuất workbook ra file Excel
  const buffer = await workbook.xlsx.writeBuffer();

  //lưu file
  const blob = new Blob([buffer], { type: 'application/octet-stream' });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = `${nameFile}.xlsx`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};
