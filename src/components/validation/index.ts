import { Rule } from 'antd/es/form';
import { AddressObject, District, Province, Ward } from '../../types/investor/investor';

export const returnNumericValue = (event: React.KeyboardEvent<HTMLInputElement>) => {
  const { key } = event;
  const pattern = RegExp(/([0-9]|(Backspace)|(Delete))/);
  if (key.match(pattern)) return;
};

export const checkLengthEqualTen = (_rule: Rule, _value: string | undefined) => {
  if (_value !== undefined && _value.length !== 10 && _value.length !== 0) {
    return Promise.reject();
  }
  return Promise.resolve();
};
type FirstPhoneNumber = '+84' | '84' | '0';
export const regexPhoneNumber = (firstPhoneNumber: FirstPhoneNumber, phoneNumber: string) => {
  if (firstPhoneNumber === '+84') return /([+84]+[2|3|5|7|8|9])+([0-9]{8})\b/g.test(phoneNumber);
  if (firstPhoneNumber === '84') return /([84]+[2|3|5|7|8|9])+([0-9]{8})\b/g.test(phoneNumber);
  if (firstPhoneNumber === '0') return /([0]+[2|3|5|7|8|9])+([0-9]{8})\b/g.test(phoneNumber);
  return false;
};

export const checkFormatPhoneNumber = (_rule: Rule, _value: string | undefined) => {
  if (_value !== undefined && _value?.length === 10 && !regexPhoneNumber('0', _value)) {
    return Promise.reject();
  }
  return Promise.resolve();
};
export const checkLengthIdCard = (_rule: Rule, _value: string | undefined) => {
  if (_value == undefined || _value.length === 0) {
    return Promise.resolve();
  } else if (_value.length == 9 || _value.length == 12) {
    return Promise.resolve();
  }
  return Promise.reject();
};

export const validateAddressObject = (addressObject: AddressObject) => {
  // Destructure addressObject
  const { province, district, ward } = addressObject;

  // Validate each field independently
  const validatedObject: { province?: Province; district?: District; ward?: Ward } = {};

  if (province && province.code && province.name) {
    validatedObject.province = province;
  }

  if (district && district.code && district.name) {
    validatedObject.district = district;
  }

  if (ward && ward.code && ward.name) {
    validatedObject.ward = ward;
  }

  // Return the validated object or an empty object if none are valid
  return Object.keys(validatedObject).length > 0 ? validatedObject : {};
};
