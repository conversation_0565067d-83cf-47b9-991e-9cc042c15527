import { Flex, Typography } from 'antd';
import './Logo.scss';

const { Text } = Typography;
interface LogoProps {
  fontSize?: number;
  onClick?: () => void;
}
const Logo: React.FC<LogoProps> = ({ fontSize = 14, onClick }) => {
  return (
    <Flex className="container-logo" justify="center" align="center" onClick={onClick}>
      <Text className="text-logo" style={{ fontSize: `${fontSize}px` }}>
        CRM
      </Text>
    </Flex>
  );
};
export default Logo;
