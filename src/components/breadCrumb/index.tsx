import { Breadcrumb } from 'antd';
import { Link, useLocation } from 'react-router-dom';
import { MenuGroup, MenuItem, menuItems, SubMenuData } from '../../layout/menu/MenuItem';
import { BreadcrumbItemType, BreadcrumbSeparatorType } from 'antd/lib/breadcrumb/Breadcrumb';

interface CustomBreadcrumbItemType extends BreadcrumbItemType {
  label: string;
  path?: string; // Đường dẫn tùy chỉnh cho link
}

interface Props {
  titleBread?: string;
  noMenu?: boolean; // Giữ nguyên prop noMenu
  customItems?: CustomBreadcrumbItemType[]; // Thêm prop để truyền item tùy chỉnh
}

function BreadCrumbComponent(props: Props) {
  const { titleBread, noMenu, customItems = [] } = props;
  const location = useLocation();

  const pathSegments = location.pathname.split('/').filter(Boolean); // Split path into segments

  const findMenuBranchByPath = (menuItems: SubMenuData[], path: string): SubMenuData | undefined => {
    for (const menuItem of menuItems) {
      const currentMenu = { ...menuItem };

      if (menuItem.children && menuItem.children.length > 0) {
        const filteredChildren = menuItem.children
          .map(child => {
            if (child.path === path) {
              return child;
            } else if (child.children && child.children.length > 0) {
              const foundChild = findMenuBranchByPath(
                [
                  {
                    ...child,
                    children: child.children,
                    groups: [],
                  },
                ],
                path,
              );
              return foundChild ? { ...child, children: foundChild.children } : undefined;
            }
            return undefined;
          })
          .filter(Boolean);

        if (filteredChildren.length > 0) {
          currentMenu.children = filteredChildren as MenuItem[];
          return currentMenu;
        }
      }

      if (menuItem.groups && menuItem.groups.length > 0) {
        const filteredGroups = menuItem.groups
          .map(group => {
            const filteredItems = group.children.filter(item => item.path === path);
            return filteredItems.length > 0 ? { ...group, children: filteredItems } : undefined;
          })
          .filter(Boolean);

        if (filteredGroups.length > 0) {
          currentMenu.children = filteredGroups as MenuGroup[];
          return currentMenu;
        }
      }
    }
    return undefined;
  };

  const flattenMenu = (menu: MenuItem[]): MenuItem[] => {
    let result: MenuItem[] = [];
    menu.forEach(item => {
      const newItem = { ...item };
      delete newItem.children;
      result.push(newItem);
      if (item.children && item.children.length > 0) {
        result = result.concat(flattenMenu(item.children));
      }
    });
    return result;
  };

  const items = () => {
    let breadcrumbItems: CustomBreadcrumbItemType[] = [];

    if (!noMenu) {
      // Thử tìm trong menuItems trước
      const _items = pathSegments
        .map((_segment, index) => {
          const currentPath = `/${pathSegments.slice(0, index + 1).join('/')}`;
          return findMenuBranchByPath(menuItems, currentPath);
        })
        .filter(Boolean) as SubMenuData[];

      if (_items.length > 0) {
        // Nếu tìm thấy trong menuItems, sử dụng menuItems
        breadcrumbItems = flattenMenu(_items);
      } else {
        // Nếu không tìm thấy trong menuItems, tạo breadcrumb từ pathSegments
        breadcrumbItems = pathSegments.map((segment, index) => {
          const path = `/${pathSegments.slice(0, index + 1).join('/')}`;
          return {
            key: path,
            label: segment.charAt(0).toUpperCase() + segment.slice(1), // Viết hoa chữ cái đầu
            path,
          };
        });
      }
    }

    // Thêm customItems (ví dụ: project?.name) nếu có
    breadcrumbItems = [
      ...breadcrumbItems,
      ...customItems.map(item => ({
        key: item.label,
        label: item.label,
        path: item.path, // Lưu path tùy chỉnh
      })),
    ];

    // Thêm titleBread nếu có
    if (titleBread) {
      breadcrumbItems.push({
        key: 'title',
        label: titleBread,
      });
    }

    return breadcrumbItems;
  };

  const itemRender = (
    route: Partial<CustomBreadcrumbItemType & BreadcrumbSeparatorType>,
    _params: MenuItem[],
    routes: Partial<CustomBreadcrumbItemType & BreadcrumbSeparatorType>[],
    paths: string[],
  ) => {
    const isLast = routes[routes.length - 1] === route; // Kiểm tra phần tử cuối
    if (!route.label) {
      return null;
    }
    const label = route.label as string;

    return isLast ? (
      <span style={{ color: 'rgba(0, 0, 0, 0.88)' }}>{label}</span>
    ) : route.path ? (
      <Link to={route.path} style={{ color: 'rgba(0, 0, 0, 0.45)' }}>
        {label}
      </Link>
    ) : (
      <Link to={`/${paths.join('/')}`} style={{ color: 'rgba(0, 0, 0, 0.45)' }}>
        {label}
      </Link>
    );
  };

  return <Breadcrumb style={{ paddingBottom: '24px' }} itemRender={itemRender} items={items()} />;
}

export default BreadCrumbComponent;
