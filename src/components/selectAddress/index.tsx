import { Form, Select, Tabs, TabsProps } from 'antd';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import './styles.scss';
import { TabSelect } from './TabSelect';
import { useFetch } from '../../hooks';
import { getDistricts, getProvinces, getWards } from '../../service/address';

export type AddressType = {
  code: string;
  nameVN: string;
  name: string;
};

interface Props {
  parentName: string;
  placeHolder?: string;
  isDisable?: boolean;
  address?: {
    province: AddressType;
    district: AddressType;
    ward: AddressType;
  };
  handleAddressChange?: () => void;
  isTouch?: boolean;
}

const SelectAddress = ({ parentName, placeHolder, isDisable, address, handleAddressChange }: Props) => {
  const form = Form.useFormInstance();
  const isDropdownOpening = useRef(false);
  const selectRef = useRef<HTMLDivElement | null>(null);

  const [open, setOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<string>('province');

  const [selectedProvince, setSelectedProvince] = useState<AddressType | null>(null);
  const [selectedDistrict, setSelectedDistrict] = useState<AddressType | null>(null);
  const [selectedWard, setSelectedWard] = useState<AddressType | null>(null);

  const { data: dataProvinces } = useFetch<AddressType[]>({
    queryKeyArrWithFilter: ['get-province'],
    api: getProvinces,
  });

  const { data: dataDistricts } = useFetch<AddressType[]>({
    queryKeyArr: ['districts', selectedProvince?.code],
    api: () => selectedProvince?.code && getDistricts(selectedProvince.code),
    enabled: !!selectedProvince?.code,
    cacheTime: 10,
  });

  const { data: dataWards } = useFetch<AddressType[]>({
    queryKeyArr: ['wards', selectedDistrict?.code],
    api: () => selectedDistrict?.code && getWards(selectedDistrict.code),
    enabled: !!selectedDistrict?.code,
    cacheTime: 10,
  });

  const provinces = dataProvinces?.data?.data;
  const districts = dataDistricts?.data?.data;
  const wards = dataWards?.data?.data;

  useEffect(() => {
    if (address) {
      const { province, district, ward } = address;
      setSelectedProvince(province);
      setSelectedDistrict(district);
      setSelectedWard(ward);

      form.setFieldsValue({
        [parentName]: {
          province: province ? { code: province.code, nameVN: province.name } : null,
          district: district ? { code: district.code, nameVN: district.name } : null,
          ward: ward ? { code: ward.code, nameVN: ward.name } : null,
        },
      });
    }
  }, [address, form, parentName]);

  const getFullAddress = useMemo(() => {
    const parts = [
      selectedProvince?.name || selectedProvince?.nameVN,
      selectedDistrict?.name || selectedDistrict?.nameVN,
      selectedWard?.name || selectedWard?.nameVN,
    ];
    return parts.filter(Boolean).join(', ');
  }, [selectedProvince, selectedDistrict, selectedWard]);

  const handleDropdownVisibleChange = useCallback((isOpen: boolean) => {
    if (isOpen) {
      setOpen(isOpen);
    }
  }, []);

  const handleSelectChange = useCallback(
    (type: 'province' | 'district' | 'ward', code: string) => {
      if (type === 'province') {
        const province = provinces?.find(p => p.code === code) || null;
        setSelectedProvince(province);
        setSelectedDistrict(null);
        setSelectedWard(null);
        form.setFieldsValue({
          [parentName]: {
            province: province ? { code: province.code, name: province.nameVN } : null,
            district: null,
            ward: null,
          },
        });
        form.validateFields([parentName]);
        setActiveTab('district');
      } else if (type === 'district') {
        const district = districts?.find(d => d.code === code) || null;
        setSelectedDistrict(district);
        setSelectedWard(null);
        form.setFieldsValue({
          [parentName]: {
            ...form.getFieldValue(parentName),
            district: district ? { code: district.code, name: district.nameVN } : null,
            ward: null,
          },
        });
        form.validateFields([parentName]);
        setActiveTab('ward');
      } else if (type === 'ward') {
        const ward = wards?.find(w => w.code === code) || null;
        setSelectedWard(ward);
        form.setFieldsValue({
          [parentName]: {
            ...form.getFieldValue(parentName),
            ward: ward ? { code: ward.code, name: ward.nameVN } : null,
          },
        });
        setOpen(false);
        handleAddressChange && handleAddressChange();
      }
    },
    [provinces, form, parentName, districts, wards, handleAddressChange],
  );

  const handleClear = useCallback(() => {
    setSelectedProvince(null);
    setSelectedDistrict(null);
    setSelectedWard(null);
    form.setFieldsValue({
      [parentName]: {
        province: null,
        district: null,
        ward: null,
      },
    });
    form.validateFields([parentName]);
    setOpen(true);
    setActiveTab('province');
  }, [form, parentName]);

  const itemRender: TabsProps['items'] = useMemo(() => {
    return [
      {
        label: 'Tỉnh/Thành phố',
        key: 'province',
        children: (
          <TabSelect
            items={provinces?.map(p => ({ label: p.nameVN, key: p.code })) || []}
            onClick={({ key }) => handleSelectChange('province', key)}
            selected={selectedProvince?.code}
          />
        ),
      },
      {
        label: 'Quận/Huyện',
        key: 'district',
        disabled: !selectedProvince,
        children: (
          <TabSelect
            items={districts?.map(d => ({ label: d.nameVN, key: d.code })) || []}
            onClick={({ key }) => handleSelectChange('district', key)}
            selected={selectedDistrict?.code}
          />
        ),
      },
      {
        label: 'Phường/Xã',
        key: 'ward',
        disabled: !selectedDistrict,
        children: (
          <TabSelect
            items={wards?.map(w => ({ label: w.nameVN, key: w.code })) || []}
            onClick={({ key }) => handleSelectChange('ward', key)}
            selected={selectedWard?.code}
          />
        ),
      },
    ].filter(Boolean) as TabsProps['items'];
  }, [provinces, selectedProvince, districts, selectedDistrict, wards, selectedWard?.code, handleSelectChange]);

  const handleClickOutside = useCallback(
    (event: MouseEvent) => {
      if (isDropdownOpening.current) {
        isDropdownOpening.current = false;
        return;
      }
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        form.setFieldValue([parentName, 'fullAddress'], getFullAddress);
        setOpen(false);
        handleAddressChange && handleAddressChange();
      }
    },
    [form, getFullAddress, handleAddressChange, parentName],
  );

  useEffect(() => {
    const handleMouseDown = (event: MouseEvent) => {
      handleClickOutside(event);
    };

    if (open) {
      isDropdownOpening.current = true;
      document.addEventListener('mousedown', handleMouseDown);
    }

    return () => {
      document.removeEventListener('mousedown', handleMouseDown);
    };
  }, [open, handleClickOutside]);

  return (
    <Select
      disabled={isDisable}
      placeholder={placeHolder || 'Chọn thông tin Tỉnh/Thành phố, Quận/Huyện, Phường/Xã'}
      popupClassName="select-address"
      allowClear
      open={open}
      value={getFullAddress || null}
      onClear={handleClear}
      onDropdownVisibleChange={handleDropdownVisibleChange}
      onChange={handleAddressChange}
      dropdownRender={() => (
        <div ref={selectRef}>
          <Tabs activeKey={activeTab} onChange={key => setActiveTab(key)} items={itemRender} />
        </div>
      )}
    />
  );
};

export default SelectAddress;
