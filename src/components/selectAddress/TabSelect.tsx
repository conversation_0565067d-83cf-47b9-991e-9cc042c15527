import { MenuItemType } from 'antd/es/menu/interface';
import { useEffect, useState } from 'react';
import type { MenuInfo } from 'rc-menu/lib/interface';
import InputSearch from '../input/InputSearch';
import { Menu } from 'antd';

interface PropTabs {
  items?: MenuItemType[];
  onClick: (e: MenuInfo) => void;
  keyName?: string | string[];
  selected?: string | null;
}

export const TabSelect = (props: PropTabs) => {
  const { items, onClick, selected } = props;
  const [search, setSearch] = useState<string>('');
  const [selectedItem, setSelectedItem] = useState<string>('');
  useEffect(() => {
    setSelectedItem(selected as string);
  }, [selected]);

  const handleSearch = (value: unknown) => {
    setSearch(value as string);
  };
  const handleClick = (e: MenuInfo) => {
    setSelectedItem(e.key);
    onClick(e);
  };

  const filterItems = items?.filter(
    item => typeof item?.label === 'string' && item?.label?.toLowerCase().includes(search?.toLowerCase()),
  );
  return (
    <>
      <InputSearch keySearch="code" showParams={false} onChange={handleSearch} />
      <Menu items={filterItems} onClick={handleClick} selectedKeys={[selectedItem]} />
    </>
  );
};
