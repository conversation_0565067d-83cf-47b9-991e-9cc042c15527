import React, { useCallback, useState, useEffect } from 'react';
import { Input, Button, Typography, Form, Tabs, Modal, Select, notification } from 'antd';
import ModalComponent from '../modal';
import { EditOutlined, EyeOutlined } from '@ant-design/icons';
import PdfViewer from '../pdf';
import { createDiscountPolicyProposalNumber, getListTemplate, getPdf } from '../../service/report';
import { useCreateField, useFetch } from '../../hooks';
import { useProjectStore } from '../../page/discountPolicyManagement/store';
import { FileTemplate, Pdf, Template } from '../../types/discountPolicy';

const { Title } = Typography;
const { TextArea } = Input;
export interface File {
  name?: string;
  url?: string;
}
interface PolicyReportModalProps {
  isOpen?: boolean;
  onClose?: () => void;
  onSubmit?: (formData: unknown) => void;
  title?: string;
  reload?: () => void;
  record?: any;
  typePolicy?: string;
}

const PolicyReportModal: React.FC<PolicyReportModalProps> = ({
  isOpen = false,
  onClose,
  title,
  reload,
  record,
  typePolicy,
}) => {
  const [form] = Form.useForm();
  const { projectId } = useProjectStore();
  const [activeTab, setActiveTab] = useState('edit');
  const [pdfBlob, setPdfBlob] = useState<Blob | null>(null);
  const [pdfBase64, setPdfBase64] = useState<string | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [hasError, setHasError] = useState<boolean>(false);
  const formId = '991c4223-7fdf-4fc0-929e-ffe80ecea284';

  const { mutateAsync: _createDiscountPolicyProposalNumber } = useCreateField<unknown>({
    keyOfDetailQuery: ['get-detail-discount-policy'],
    apiQuery: createDiscountPolicyProposalNumber,
    isMessageError: false,
    messageSuccess: 'Gửi tờ trình thành công!',
  });

  const { data, isLoading } = useFetch<Template>({
    queryKeyArr: ['get-template', projectId, formId],
    api: () => getListTemplate({ projectId, formId }),
    moreParams: {
      projectId: projectId,
      formId: formId,
    },
    enabled: !!projectId,
  });

  const teamplates: Template[] = Array.isArray(data?.data?.data)
    ? (data?.data?.data as Template[])
    : ([] as Template[]);

  const { refetch } = useFetch<Pdf>({
    queryKeyArr: ['preview-pdf'],
    api: async () => {
      const payload = {
        policyId: record?.id,
        formId: formId,
        fileUrl: selectedTemplate,
        dear: form.getFieldValue('dear'),
        body: form.getFieldValue('body'),
      };
      const resp = await getPdf(payload);
      return resp;
    },
    enabled: false,
  });

  const handleTabChange = async (key: string) => {
    setActiveTab(key);
    if (key === 'preview') {
      if (!selectedTemplate) {
        notification.error({ message: 'Vui lòng chọn một mẫu tờ trình trước khi xem trước!' });
        setPdfBlob(null);
        setPdfBase64(null);
        setHasError(true);
        return;
      }

      try {
        const resp = await refetch();
        const dataRaw = resp.data?.data?.data;
        if (resp?.data?.data?.statusCode === '0') {
          setPdfBlob(dataRaw as unknown as Blob);
          setPdfBase64(typeof dataRaw === 'string' ? dataRaw : null);
          setHasError(false);
        } else {
          notification.error({ message: 'Tệp mẫu không đúng định dạng!' });
          setPdfBlob(null);
          setPdfBase64(null);
          setHasError(true);
        }
      } catch (error) {
        notification.error({ message: 'Đã xảy ra lỗi khi tải file PDF!' });
        setPdfBlob(null);
        setPdfBase64(null);
        setHasError(true);
      }
    }
  };

  const handleChange = (value: unknown) => {
    setSelectedTemplate(value as string | null);
    setHasError(false);
  };

  useEffect(() => {
    if (teamplates?.length > 0 && (teamplates[0]?.files ?? []).length > 0) {
      const defaultTemplate = teamplates[0]?.files?.[0]?.fileUrl ?? null;
      setSelectedTemplate(defaultTemplate);
      form.setFieldsValue({ filename: defaultTemplate });
    }
  }, [teamplates, form]);

  const handleSubmit = async () => {
    try {
      await form.validateFields();
      const payload = {
        type: typePolicy,
        id: record?.id,
        sheetName: record?.name,
        sheetUrl: 'primary-contract/policy/payment/1742882394289.xlsx',
        files: record?.files?.map((file?: File) => ({
          name: file?.name,
          url: file?.url || '',
        })),
      };
      const resp = await _createDiscountPolicyProposalNumber(payload);
      if (resp?.data?.statusCode === '0') {
        form.resetFields();
        onClose?.();
      }
    } catch (error) {
      console.log('Validation failed:', error);
    }
  };

  const handleCancel = useCallback(async () => {
    if (form.isFieldsTouched()) {
      Modal.confirm({
        title: 'Xác nhận hủy',
        content: 'Dữ liệu chưa được lưu, bạn có chắc chắn muốn thoát khỏi Trang không?',
        cancelText: 'Quay lại',
        onOk: async () => {
          onClose?.();
          form.resetFields();
          reload?.();
        },
        okButtonProps: { type: 'default' },
        cancelButtonProps: { type: 'primary' },
        destroyOnClose: true,
      });
    } else {
      onClose?.();
      reload?.();
    }
  }, [form, onClose, reload]);

  const tabItems = [
    {
      key: 'edit',
      label: (
        <div>
          <EditOutlined />
          <span style={{ marginLeft: 10 }}>Soạn thảo</span>
        </div>
      ),
      children: (
        <Form
          form={form}
          labelAlign="left"
          style={{ maxWidth: 600 }}
          labelCol={{
            xs: { span: 24 },
            sm: { span: 24 },
            md: { span: 6 },
            lg: { span: 6 },
            xl: { span: 5 },
          }}
          wrapperCol={{
            xs: { span: 24 },
            sm: { span: 24 },
            md: { span: 18 },
            lg: { span: 18 },
            xl: { span: 19 },
          }}
        >
          <Form.Item
            label="Tên tập mẫu"
            name="filename"
            rules={[{ required: true, message: 'Vui lòng chọn tên tệp mẫu' }]}
          >
            <Select
              style={{ width: '100%' }}
              placeholder="Chọn mẫu tờ trình"
              loading={isLoading}
              value={selectedTemplate}
              onChange={handleChange}
              options={teamplates?.[0]?.files?.map((item: FileTemplate) => ({
                value: item.fileUrl,
                label: item.fileName,
              }))}
              notFoundContent="Không tìm thấy mẫu tờ trình"
            />
          </Form.Item>

          <Form.Item label="Kính gửi" name="dear">
            <TextArea rows={2} placeholder="Nhập nội dung kính gửi" maxLength={255} />
          </Form.Item>

          <Form.Item label="Nội dung" name="body">
            <TextArea rows={2} placeholder="Nhập nội dung" maxLength={500} />
          </Form.Item>
        </Form>
      ),
    },
    {
      key: 'preview',
      label: (
        <div>
          <EyeOutlined />
          <span style={{ marginLeft: 10 }}>Xem trước</span>
        </div>
      ),
      children: <div style={{ marginTop: 33 }}>{pdfBlob && <PdfViewer pdfBase64={pdfBase64} />}</div>,
    },
  ];
  return (
    <ModalComponent
      title={title}
      open={isOpen}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          Hủy
        </Button>,
        <Button
          key="submit"
          type="primary"
          onClick={handleSubmit}
          disabled={hasError || !selectedTemplate || activeTab === 'edit'}
        >
          Gửi tờ trình
        </Button>,
      ]}
    >
      <Title level={5} style={{ marginBottom: 16, color: 'rgba(0, 0, 0, 1)' }}>
        Tên chính sách
      </Title>
      <Tabs
        activeKey={activeTab}
        onChange={handleTabChange}
        items={tabItems}
        tabBarStyle={{ marginBottom: 16 }}
        renderTabBar={() => (
          <div style={{ display: 'flex', gap: 8 }}>
            {tabItems.map(item => (
              <Button
                key={item.key}
                onClick={() => handleTabChange(item.key)}
                style={{
                  backgroundColor: activeTab === item.key ? '#f0f0f0' : '#fff',
                  border: activeTab === item.key ? 'none' : '1px solid #d9d9d9',
                  borderRadius: 4,
                  padding: '4px 12px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: 4,
                }}
              >
                {item.label}
              </Button>
            ))}
          </div>
        )}
      />
    </ModalComponent>
  );
};

export default PolicyReportModal;
