import React, { useState, useEffect } from 'react';
import { Upload, Typography, message, Modal } from 'antd';
import { PictureOutlined } from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd';
import './styles.scss';

const { Text } = Typography;

interface ImageUploaderProps {
  action: string;
  defaultImageUrl?: string;
}

const getBase64 = (file: File): Promise<string> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = error => reject(error);
  });

const ImageUploader: React.FC<ImageUploaderProps> = ({ action, defaultImageUrl }) => {
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [fileList, setFileList] = useState<UploadFile[]>([]);

  useEffect(() => {
    if (defaultImageUrl) {
      setFileList([
        {
          uid: '-1',
          name: 'logo',
          status: 'done',
          url: defaultImageUrl,
        } as UploadFile,
      ]);
    }
  }, [defaultImageUrl]);

  const beforeUpload: UploadProps['beforeUpload'] = file => {
    const isValidFormat = ['image/jpeg', 'image/jpg', 'image/png'].includes(file.type);
    const isValidSize = file.size / 1024 / 1024 < 25;

    if (!isValidFormat) {
      message.error('Chỉ hỗ trợ định dạng JPEG, JPG, PNG!');
      return Upload.LIST_IGNORE;
    }

    if (!isValidSize) {
      message.error('Dung lượng tối đa là 25MB!');
      return Upload.LIST_IGNORE;
    }

    return true;
  };

  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as File);
    }

    setPreviewImage(file.url || (file.preview as string));
    setPreviewVisible(true);
  };

  const handleCancel = () => setPreviewVisible(false);

  return (
    <div className="container-upload">
      <Upload
        style={{ width: 1000 }}
        className="upload-image"
        action={action}
        listType="picture-card"
        maxCount={1}
        beforeUpload={beforeUpload}
        onChange={info => {
          setFileList(info.fileList.filter(file => file.status === 'done' || file.status === 'error'));
        }}
        onPreview={handlePreview}
        fileList={fileList}
      >
        {fileList.length < 1 && (
          <button style={{ border: 0, background: 'none' }} type="button">
            <PictureOutlined />
            <div style={{ marginTop: 8 }}>Upload</div>
          </button>
        )}
      </Upload>
      <Text type="secondary">Định dạng .jpeg, .jpg, .png</Text>

      <Modal open={previewVisible} footer={null} onCancel={handleCancel}>
        <img alt="preview" style={{ width: '100%' }} src={previewImage} />
      </Modal>
    </div>
  );
};

export default ImageUploader;
