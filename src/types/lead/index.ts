import { Dayjs } from 'dayjs';
import { TRepo } from '../leadCommon';
import { TEmployeeAll } from '../customers';
import { TUpdatedBy } from '../common/common';

export type TUnits = {
  value: string;
  name: string;
  code: string;
  level: number;
  id: string;
  staffIds: TStaffIds[];
  type: string; // E.g., "internal"
};
export interface TStaffIds {
  id: string;
  name: string;
  email: string;
  code: string;
  selected?: boolean;
}

export interface IWorkingTime {
  startTime?: string;
  endTime?: string;
}

export interface IOrgChart {
  staffIds: TStaffIds[] | string[];
  deassignedStaffIds: string[];
  _id: string;
  id: string;
  name: string;
}

export interface IConfig {
  orgChartIds: string[];
  active: number;
  visiblePhone: boolean;
  deliverType: number;
  isWorkingTime: boolean;
  softDelete: boolean;
  softDeleteReason: string | null;
  _id: string;
  project: {
    id: string;
    name: string;
  };
  orgChartQueue: string[];
  orgCharts: IOrgChart[];
  surveys: ISurvey[];
  workingTime: IWorkingTime[] | Dayjs[];
  exploitTime?: [Dayjs, Dayjs] | { from: string; to: string };
  notification?: { [key: string]: TNotification } | string[];
  assignDuration?: number;
  projectId: string;
  name: string;
  code: string;
  deassignLimit?: number;
  assignDurationInMinute: number;
}
export interface ISurvey {
  type: string;
  name: string;
  values: IValuesSurvey[] | [];
}
export interface IValuesSurvey {
  code: string;
  name: string;
  value?: string;
}

export interface IConfigHot {
  orgChartIds: string[];
  visiblePhone: boolean;
  deliverType: number;
  isWorkingTime: boolean;
  orgChartQueue: string[];
  orgCharts: IOrgChart[];
  workingTime: IWorkingTime[] | Dayjs[];
  assignDuration?: number;
  notification: { [key: string]: TNotification } | string[];
  deassignLimit?: number;
  assignDurationInMinute: number;
}

export type TNotification = {
  title: string;
  content: string;
  active: boolean;
};
export interface ILead {
  id: string;
  code?: string;
  name: string;
  repo?: TRepo;
  takeCare?: TEmployeeAll;
  createdDate?: string | Dayjs | null;
  updatedBy?: string;
  updatedDate?: string | Dayjs | null;
  createdBy?: string;
  configs?: IConfig[];
  configHot: IConfigHot;
  isHot?: boolean;
  createdByObj: TUpdatedBy;
  modifiedByObj: TUpdatedBy;
  updatedByObj: TUpdatedBy;
}

export type TListHistoryImportLeads = {
  fileName: string;
  processBy: {
    isAdmin: boolean;
    isActive: boolean;
    status: number;
    modifiedBy: string | null;
    id: string;
    username: string;
    code: string;
    referCode: string;
    fullName: string;
    email: string;
    orgCode: string;
    orgName: string;
    accountType: string;
    position: string;
    createdDate: string;
    modifiedDate: string;
  };
  success: number;
  fail: number;
  type: string;
  createdDate: string;
  updatedDate: string;
  failedFileUrl: string;
  status: string;
  id: string;
  failedFileName: string;
};

export type TStaff = {
  code: string;
  name: string;
};
