import { Dayjs } from 'dayjs';
import { TCreatedBy } from '../common/common';
import { TProject } from '../leadAssigned';
import { TListDropdown } from '../salesPolicy';

export type TListOfDeliveryConfig = {
  project: TProject;
  items: string;
  expectedStartDate: string;
  expectedEndDate: string;
  status: number;
  createdDate: string;
  createdBy: TCreatedBy;
  updatedDate: string;
  updatedBy: TCreatedBy;
  id: string;
};

export type TFilterDeliveryConfig = {
  search?: string;
  startCreatedDate?: string | Dayjs | null;
  endCreatedDate?: string | Dayjs | null;
};

export type projectAllForDeliveryConfig = {
  // address: null;
  code: string;
  id: string;
  investor: string;
  lock: boolean;
  name: string;
  setting: {
    isRetailCustomer: boolean;
    // rootPos: null; salesUnit: []; adviceType: null
  };
  status: string;
  type: string;
};

export type DeliveryConfig = {
  status: number;
  items: THandover[];
  orgCharts: TListDropdown[];
  expectedStartDate: string; // ISO date string
  expectedEndDate: string; // ISO date string
  emailTemplate: string;
  smsTemplate: string;
  smsBrandName: string;
  emailTitle: string;
  emailFrom: string;
  mondayFrame?: TTimeFrame[];
  tuesdayFrame?: TTimeFrame[];
  wednesdayFrame?: TTimeFrame[];
  thursdayFrame?: TTimeFrame[];
  fridayFrame?: TTimeFrame[];
  saturdayFrame?: TTimeFrame[];
  sundayFrame?: TTimeFrame[];
  timeFrames?: TTimeFrame[];
  hotline: string;
  accountingConfirm: number;
  project: {
    name: string;
    id: string;
  };
  paymentPercent: number;
  emailCC?: string;
  emailBCC?: string[];
  createdDate: string;
  createdBy: TCreatedBy;
  updatedDate: string;
  updatedBy: TCreatedBy;
};

export type TDeliveryConfigGen = {
  project: {
    name: string;
    id: string;
  };
  expectedDateRange: [Dayjs, Dayjs];
  status: boolean;
  items: THandover[];
  orgCharts: TListDropdown[];
  paymentPercent: string;
  accountingConfirm: boolean;
  hotline: string;
  timeFrames?: TTimeFrame[];
};

export type TDeliveryConfigNoti = {
  emailTemplate: string;
  smsTemplate: string;
  smsBrandName: string;
  emailTitle: string;
  emailFrom: string;
  emailCC?: string;
  emailBCC?: string[];
};

export type TTimeFrame = {
  amount?: number;
  startTime: string;
  endTime: string;
  day?: string;
  timeRange?: (Dayjs | undefined)[];
};

export type THandover = {
  title: string;
  name: string;
  id?: string;
  list: THandoverCategory[];
  children?: THandoverCategory[];
};

export type THandoverCategory = {
  type: string;
  description: string;
  title: string;
};
