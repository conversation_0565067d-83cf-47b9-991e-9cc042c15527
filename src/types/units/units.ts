interface AddressType {
  code: string;
  name: string;
}
export interface Units {
  addressObject: {
    province: AddressType;
    district: AddressType;
    ward: AddressType;
  };
  representAddressObject: {
    province: AddressType;
    district: AddressType;
    ward: AddressType;
  };
  representIssuedDate: string;
  representIssuedPlace: string;
  nameVN?: string;
  _id?: string;
  branchName?: string;
  certNumber?: string;
  contactAddress?: string;
  contactPhone?: string;
  contactFax?: string;
  bank: {
    bankCode: string;
    bankName: string;
  };
  bankInfo?: string;
  taxNumber?: string;
  representBy?: string;
  parentId?: string;
  status?: string;
  accountantIds?: [];
  canAssignStaff?: boolean;
  canContainPOS?: boolean;
  isPublicView?: boolean;
  logo?: string;
  description?: string;
  active?: boolean;
  modifiedBy?: string;
  softDelete?: boolean;
  saleAdminIds?: string[];
  paymentSmsConfig?: null;
  paymentEmailConfig?: null;
  paymentAccountConfig?: null;
  paymentEmailTemplates?: [
    {
      subject: string;
      body: null;
      name: string;
    },
  ];
  paymentSuccessEmailTemplates?: [];
  buildingEmailTemplates?: [];
  salerEmailTemplates?: [];
  accessBuildingConfig?: null;
  isCheckinConfig?: boolean;
  images?: {
    list: string[];
    videos: [
      {
        url: string;
      },
    ];
    zones: string[];
  };
  name?: string;
  code?: string;
  type?: string;
  erpConfig?: null;
  smsConfig?: null;
  callConfig?: null;
  templateFiles?: [];
  xpath?: string;
  level?: number;
  configs?: Configs[];
  createdBy?: string;
  id?: string;
  managerIds?: string[];
  staffIds?: string[];
  devBusinessIds?: string[];
  createdDate?: string;
  modifiedDate?: string;
  __v?: number;
  managerId?: string[];
  additionalFields?: {
    posInfo: {
      a: {
        '234234': '*********';
        a: 'a';
      };
    };
  };
  employee?: Employee[];
  key?: string;
  managerName?: string;
  children?: [];
  partnershipName: string;
  partnershipCode: string;
  partnershipLevel: string;
  taxCode: string;
  lineManager: string;
  updatedDate: string;
  lastUpdate: string;
  other: Other[];
}

export interface Configs {
  key?: string;
  type?: string;
  name?: string;
  value?: number;
}

export interface Employee {
  id?: string;
  name?: string;
  jobTitleName?: string;
  code?: string;
  nameVN?: string;
  status?: string;
  lineManager?: string;
  email?: string;
}

export interface Other {
  type: string;
  key: string;
  value: string;
  arrayData: string[];
  objectData: string[];
}

export interface LevelDVBH {
  0: string;
  1: string;
  2: string;
  3: string;
  4: string;
  5: string;
  6: string;
}
