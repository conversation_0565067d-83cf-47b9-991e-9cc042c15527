import { QueryKey } from '@tanstack/react-query';
import { AxiosResponse } from 'axios';
import { FetchResponse, TDataList } from '../../hooks';

export interface ParamsGetRequest {
  page?: number;
  pageSize?: number;
  search?: string;
  id?: string;
  partnerCode?: string;
  projectId?: string;
  formId?: string;
  bookingTicketId?: string;
}

export interface IBank {
  id: string;
  companyCode: string;
  bankCode: string;
  bankName: string;
  houseBank: string;
  isActive: boolean;
  branchCode: string;
  branchName: string;
  branchIsActvie: boolean;
  lastUpdate: string;
  source: string;
  createdDate: string;
  modifiedDate: string;
}

export interface ErrorMessage {
  message: string;
  description: string;
}

export interface Constraint {
  [key: string]: string;
}

export interface Error {
  field: string;
  constraints: Constraint;
}
export interface Response {
  statusCode: string;
  data?: unknown;
  message: string;
  success: boolean;
  errors?: Error[];
}

export type OptionTypeSelect = {
  [key: string]: unknown;
  label: string | JSX.Element | undefined;
  value: string | undefined;
  option?: { [key: string]: unknown };
};

export interface ISelectLazyLoading<T> {
  enabled?: boolean;
  defaultValues?: { label: string | JSX.Element | undefined; value: string | undefined };
  apiQuery?: (params: object) => Promise<AxiosResponse<FetchResponse<TDataList<T[]>>, unknown>>;
  queryKey?: QueryKey;
  keysLabel?: string | string[]; // Tên hiển thị từng field
  placeholder?: string;
  suffixIcon?: JSX.Element;
  moreParams?: object;
  disabled?: boolean;
}

export type TCreatedBy = {
  email?: string;
  userName: string;
  fullName?: string;
};

export type TUpdatedBy = {
  fullName: string;
  username: string;
};

export type OutputTimeFrames = {
  [key: string]: {
    amount: number;
    startTime: string;
    endTime: string;
  }[];
};
export type TWeek = 'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday' | 'sunday';

export type TProjectDropdown = {
  id: string;
  code: string;
  name: string;
};

export interface IResponseUpload {
  key: string;
  Location: string;
  eTag: string;
  bucket: string;
  id: string;
  originalname: string;
  Bucket: string;
}
