import { Dayjs } from 'dayjs';

export interface ILead {
  id: string;
  name: string;
  code: string;
  createdDate?: string | Dayjs | null;
  updatedDate?: string | Dayjs | null;
  updatedBy: string;
  createdBy: string;
  configNumber?: number;
}
export type TCreateLeadSource = {
  id: string | undefined;
  name: string;
  code: string;
  createdDate?: string | Dayjs | null;
  updatedDate?: string | Dayjs | null;
  updatedBy: string;
  createdBy: string;
  configNumber?: number;
};
export interface TManualDeliver {
  id: string;
  assignee?: string;
}
