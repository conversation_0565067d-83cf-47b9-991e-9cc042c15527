import { Dayjs } from 'dayjs';
import { TCreatedBy, TProjectDropdown } from '../common/common';

export interface ICommissionDebtPolicy {
  _id: string;
  reasonDelete: string | null;
  softDelete: boolean;
  name: string;
  period: string;
  periodFrom: string; // ISO date string
  periodTo: string; // ISO date string
  periodName: string | null;
  isActive: number;
  project: TProjectDropdown;
  penalty: TPenaltySanction[];
  projectDebt: IDebtPolicy;
  badDebt: IDebtPolicy;
  year: number;
  createdBy: TCreatedBy;
  modifiedBy: TCreatedBy;
  code?: string;
  id: string;
  createdDate: string; // ISO date
  modifiedDate: string; // ISO date
}

export interface IDebtPolicy {
  rate: number;
  isProgressive: boolean;
  type: string;
  isVAT: boolean;
  listRate: IListRate[];
}
export interface IListRate {
  topPrice: number | string;
  unit: string;
  recordedCommissionUnit: string;
  recordedCommission: number | string;
  bottomPrice: number | string;
}

export type TFilterDebtPolicy = {
  search?: string;
  startCreatedDate?: Dayjs | string | null;
  endCreatedDate?: Dayjs | string | null;
  isActive?: string | null;
  createdBy?: string[] | string | null;
  project?: string | null;
};

export type TDebtAgeItem = {
  name: string;
  fromDay: number;
  toDay: number;
  interestRate: number;
  isFinalRange: boolean;
  isBadDebt: boolean;
};

export type TDebtAgeConfig = {
  debtage: TDebtAgeItem[];
};

export type TPenaltySanction = {
  name: string;
  unit: string;
  amount: number;
  type?: string;
  key: string;
};

export type TCommissionRateItem = {
  key: string;
  topPrice: number | string;
  bottomPrice: number | string;
  unit: string;
  recordedCommission: number | string;
  recordedCommissionUnit: string;
  type?: string;
};

export type TCommissionRateConfig = {
  rate: number;
  isProgressive: boolean;
  isVAT: boolean;
  type: string;
  listRate: TCommissionRateItem[];
};

export type TPayloadCommissionDebtPolicy = {
  id?: string;
  periodFrom: string;
  periodTo: string;
  periodName: string;
  year: number;
  isActive: number;
  name: string;
  project: {
    id: string;
    name: string;
    code: string;
  };
  penalty: TPenaltySanction[];
  badDebt: TCommissionRateConfig;
  projectDebt: TCommissionRateConfig;
};

export type ProjectDebtPolicy = { name: string; id: string; code: string; setting: TDebtAgeConfig };
