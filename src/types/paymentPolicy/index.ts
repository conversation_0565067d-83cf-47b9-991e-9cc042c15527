import { Dayjs } from 'dayjs';

export interface IPaymentPolicy {
  approvedDate: string;
  defaultApply?: boolean;
  active?: boolean;
  status?: string;
  type?: string;
  description?: string;
  typeDiscount?: string;
  name?: string;
  project?: {
    id?: string;
    name?: string;
    code?: string;
  };
  startDate?: string;
  expiredDate?: string | null;
  value?: number;
  typeRealEstate?: string;
  files?: FilePaymentPolicy[];
  code?: string;
  createdBy?: string;
  id?: string;
  createdDate?: string;
  modifiedDate?: string;
  applicationPeriod?: [string, string];
  schedule: {
    installments: Installment[];
    templateFiles: FilePaymentPolicy[];
  };
  payments?: Payments[];
  ticketStatus?: string;
}
export interface Payments {
  productValue: number;
  name: string;
  type: string;
  value: string;
  houseValue: string;
  landValue: string;
  productCurrency: string;
  houseCurrency: string;
  landCurrency: string;
  time: string;
  exactDays: Dayjs | null;
  expiredDays: number;
  descriptionProgress: string;
  isToContract: boolean;
  transactionSuccessful: boolean;
}
export interface FilePaymentPolicy {
  _id?: string;
  name?: string;
  url?: string;
  absoluteUrl?: string;
  uploadName?: string;
}

export interface Installment {
  type2: string;
  value2: string;
  descriptionProgress: string;
  isToContract: string;
  transactionSuccessful: string;
  type: string;
  typeRealEstate: string;
  expiredDateType: string;
  exactDays: number | null;
  expiredDays: number;
  name: string;
  value: number;
}
