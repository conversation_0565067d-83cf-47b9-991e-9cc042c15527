enum OrgchartEnum {
  INTERNAL = 'INTERNAL',
  EXTERNAL = 'EXTERNAL',
}

enum DepositContractFormEnum {
  NO_DEPOSIT = 'NO_DEPOSIT',
}

interface Bank {
  code?: string;
  name?: string;
  accountNumber?: string;
  beneficiary?: string;
}

export interface Installment {
  id?: string;
  name?: string;
  paymentType?: string;
  type?: string;
  value?: number;
  expiredDateType?: string;
  exactDays?: string;
  expiredDays?: number;
  description?: string;
  convertedAmount?: number;
  transactionSuccessful?: boolean;
}

export interface DepositContract {
  _id?: string;
  id?: string;
  code?: string;
  projectId?: string;
  orgchart?: {
    id?: string;
    email?: string;
    type?: OrgchartEnum;
  };
  orgchartPartnerId?: string;
  bank?: Bank;
  depositSignedDate?: string;
  depositForm?: DepositContractFormEnum;
  depositTime?: number;
  depositAmount?: number;
  salePolicyId?: string;
  propertyUnitIds?: string[];
  installments?: Installment[];
  status?: number;
  softDelete?: boolean;
  createdDate?: string;
  modifiedDate?: string;
  createdBy?: string;
  modifiedBy?: string;
}
