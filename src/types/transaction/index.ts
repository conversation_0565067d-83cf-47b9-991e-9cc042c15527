export interface ITransaction {
  registeredDate: string;
  primaryStatus: string;
  escrowTicketCode: string;
  id?: string;
  code?: string;
  name?: string;
  status?: string;
  receiptNum: string;
  money?: number;
  propertyTicket?: {
    project?: {
      name?: string;
    };
    customer?: {
      personalInfo?: {
        name?: string;
      };
    };
    propertyUnit?: {
      code?: string;
    };
    bookingTicketCode?: string;
  };
  propertyUnit?: {
    code?: string;
  };
  pos?: {
    name?: string;
  };
  createdAt?: string;
  salesProgram: {
    code?: string;
    name?: string;
    dwellTime?: number;
    id?: string;
  };
}
