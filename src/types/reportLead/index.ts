import { TUpdatedBy } from '../common/common';

export type TUser = {
  id: string;
  name: string;
};

export type TPos = {
  id: string;
  name: string;
  code: string;
  type: string;
  parentId: string;
  address: string;
};

export type TReportLead = {
  id: string;
  code: string;
  createdDate: string;
  exploitStatus: 'done' | 'assign' | 'processing' | 'cancel'; // Thêm các trạng thái khác nếu có
  importedBy: TUser;
  modifiedBy: string;
  name: string;
  phone: string;
  pos: TPos;
  takeCare: TakeCare;
  updatedDate: string;
  visiblePhone: string | null;
  modifiedByObj: TUpdatedBy;
};
export type TakeCare = {
  email: string;
  id: string;
  name: string;
  phone: string;
};
export type TExportReportLead = {
  status: string;
  history?: boolean;
  startDate?: string;
  endDate?: string;
};

export type TCheckItem = { key: string; label: string; selected: boolean };
