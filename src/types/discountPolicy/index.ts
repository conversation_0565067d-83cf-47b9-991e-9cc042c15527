export interface DiscountPolicy {
  approvedDate: string;
  defaultApply?: boolean;
  active?: boolean;
  status?: string;
  type?: string;
  description?: string;
  typeDiscount?: string;
  name?: string;
  project?: {
    id?: string;
    name?: string;
    code?: string;
  };

  value?: number;
  startDate?: string;
  expiredDate?: string | null;
  typeRealEstate?: string;
  files?: FileDiscountPolicy[];
  code?: string;
  createdBy?: string;
  id?: string;
  createdDate?: string;
  modifiedDate?: string;
  applicationPeriod?: [string, string];
  eappNumber?: string;
}

export interface FileDiscountPolicy {
  uid: string;
  _id?: string;
  name?: string;
  url?: string;
  absoluteUrl?: string;
  uploadName?: string;
}

export interface Template {
  projectId?: string;
  formId?: string;
  formDetails?: {
    id?: string;
    variables?: Variables[];
    code?: string;
    name?: string;
    url?: string;
  };
  files?: FileTemplate[];
  id?: string;
  name?: string;
  code?: string;
  url?: string;
  variables?: Variables[];
}
export interface FileTemplate {
  orgUsing: string[];
  softDelete: boolean;
  isOriginal: boolean;
  status: number;
  id: string;
  fileName: string;
  fileUrl: string;
  originalname: string;
  createdBy: string;
  modifiedBy: string;
  modifiedDate: string;
  createdDate: string;
  _id: string;
}

export interface Variables {
  code: string;
  name: string;
  id: string;
}

export interface Pdf {
  data: string;
}
