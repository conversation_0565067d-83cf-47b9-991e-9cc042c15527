import { TUpdatedBy } from '../common/common';
import { ISurvey } from '../lead';
import { TImportedBy, TRepo } from '../leadCommon';

export type TExploitHistoryItem = {
  status: string;
  updatedAt: string;
  updatedBy?: string;
  takeCareId?: string;
  takeCareInfo?: TakeCareInfo;
};

export type TProject = {
  id: string;
  name: string;
};

export type TCallHistoryItem = {
  isInNeed: string;
  reasonNoNeed: string;
  otherReason: string;
  needLoan: boolean;
  isAppointment: boolean;
  isVisited: boolean;
  note: string;
  isCalled: boolean;
  callId: string;
  startCall: string | null;
  endCall: string | null;
  answerTime: number;
  updateDate: string;
};

export type TakeCareInfo = {
  id: string;
  name: string;
  phone: string;
  email: string;
  accountId: string;
};

export type LeadAssigned = {
  id: string;
  active: boolean;
  assignDuration: number;
  callHistory: TCallHistoryItem[];
  code: string;
  countAssign: number;
  createdDate: string;
  description: string;
  email: string;
  expireTime: string;
  exploitHistory: TExploitHistoryItem[];
  exploitStatus: string;
  importedBy: TImportedBy;
  incomePerMonth: number;
  isAppointment: boolean;
  isCalled: boolean;
  isHot: boolean;
  isVisited: boolean;
  lifeCycleStatus: string;
  name: string;
  needLoan: boolean;
  phone: string;
  project: TProject;
  reason: string[];
  repo: TRepo;
  repoConfigCode: string;
  repoId: string;
  source: string;
  status: string;
  surveys: ISurvey[];
  takeCare: TakeCareInfo;
  type: string;
  updatedDate: string;
  createdBy: string;
  modifiedBy: string;
  assignedDate: string;
  customer: {
    id: string;
    customerCode: string;
  };
  customerId: string;
  configData: TConfigData;
  createdByObj: TUpdatedBy;
  modifiedByObj: TUpdatedBy;
};

export type TConfigData = {
  surveys: ISurvey[];
};

export type TSubmitLeadAssigned = {
  id?: string;
  name: string;
  phone: string;
  email?: string;
  profileUrl?: string;
  note?: string;
};

export interface ICustomerFormLead {
  type?: string;
  name?: string;
  phone?: string;
  gender?: boolean;
  continueCreate?: boolean;
  leadCode?: string;
}

export type TLeadDashboard = {
  id: string;
  code: string;
  createdDate: string;
  name: string;
  phone: string;
  expireTime: string;
  assignDuration: number;
  updatedDate: string;
  exploitStatus: 'assign' | 'processing' | 'done' | 'cancel';
  exploitHistory: TExploitHistoryItem[];
  assignedDate: string;
};

export type TLeadDashboardData = {
  processing: TLeadDashboard[];
  assign: TLeadDashboard[];
  done: TLeadDashboard[];
  cancel: TLeadDashboard[];
  now: string;
};
