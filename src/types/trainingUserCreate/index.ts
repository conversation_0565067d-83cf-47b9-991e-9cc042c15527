import { Dayjs } from 'dayjs';

export interface ITrainingUser {
  id: string;
  name: string;
  code: string;
  createdDate?: string | Dayjs | null;
  updatedDate?: string | Dayjs | null;
  updatedBy: TCreatedBy;
  createdBy: TCreatedBy;
  configNumber?: number;
  phone?: string;
  customers?: TCustomer[];
}

export type TEvent = {
  id: string;
  name: string;
  code: string;
  eventName: string;
};

export type TCustomer = {
  id?: string;
  key: string;
  name?: string;
  code?: string;
  email?: string;
  phone?: string;
  isAddButton?: boolean;
  idEvent?: string;
  unRequiredEmailRegister?: boolean;
};

export type TCreateTrainingUser = {
  isFirst?: boolean;
  posId?: string;
  posName?: string;
  dvbh?: string;
  id?: string;
  code: string;
  name: string;
  email: string;
  phoneNumber: string;
  cmnd?: string;
  show?: boolean;
  idEvent?: string;
  customers?: TCustomer[];
};

export type TCreatedBy = {
  email?: string;
  userName: string;
  fullName?: string;
};
