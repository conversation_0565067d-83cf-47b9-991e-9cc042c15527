export interface FormReport {
  id?: string;
  name?: string;
  code?: string;
  url?: string;
  softDelete?: boolean;
  createdDate?: string;
  files?: FileSample[];
  variables?: Variable[];
}

export interface FileSample {
  id?: string;
  fileName?: string;
  originalname?: string;
  fileUrl?: string;
  orgUsing?: string[];
  status?: string;
  projectId?: string;
  formId?: string;
  isNew?: boolean;
}

export interface Variable {
  code?: string;
  id?: string;
  name?: string;
}
