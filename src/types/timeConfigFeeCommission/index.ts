import { Dayjs } from 'dayjs';
import { TCreatedBy } from '../common/common';

export type TTabTimeConfigFeeCommission = 'default' | 'orgChart';

export type TParamsTimeConfigFeeCommission = {
  page?: number;
  pageSize?: number;
  search?: string;
  status?: string;
  createdBy?: string;
  createdForm?: string;
  createdTo?: string;
  forOrg?: boolean;
};

export type TPeriod = {
  periodName: string;
  periodStartDate: string;
  periodEndDate: string;
};

export type TOrgChartTimeConfigFeeCommission = {
  code: string;
  id: string;
  name: string;
};

export type TListOfTimeConfigFeeCommission = {
  reasonDelete: string | null;
  softDelete: boolean;
  name: string;
  periodEndDate: number;
  periodStartDate: number;
  orgchart: TOrgChartTimeConfigFeeCommission[] | null;
  periodArray: TPeriod[];
  isActive: number;
  modifiedDate: string;
  createdDate: string;
  id: string;
  createdBy: TCreatedBy;
  modifiedBy: TCreatedBy;
  orgcharts: TOrgChartTimeConfigFeeCommission[] | null;
};

export type TFilterTimeConfigFeeCommission = {
  search?: string;
  startDate?: Dayjs | null;
  endDate?: Dayjs | null;
  isActive?: string | null;
  createdBy?: string[] | string | null;
  orgcharts?: string[] | string | null;
};

export type TSubmitTimeConfigFeeCommission = {
  name: string;
  periodStartDate: number;
  periodEndDate?: number;
  isActive: number;
  orgcharts?: TOrgChartTimeConfigFeeCommission[];
};
