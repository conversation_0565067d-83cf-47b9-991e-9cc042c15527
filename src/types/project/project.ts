import { Dayjs } from 'dayjs';
import { SalesUnitType } from '../../page/projectManagement/projectDetail/component/sellProgramTab/modalCreateSellProgram';

export interface Project {
  [key: string]: unknown;
  _id: string;
  name: string;
  type: string;
  imageUrl: string;
  status: string;
  code: string;
  investor: {
    id: string;
    name: string;
  };
  urlEsalekit?: string;
  esalekit?: boolean;
  salesProgramIds?: string[];
  banks?: IBankProject[];
  setting: {
    amountRegistration: number;
  };
}
export interface IBankProject {
  bankCode?: string;
  code: string;
  name: string;
  accountNumber: string;
}
interface DecimalValue {
  $numberDecimal?: string;
}

export type NumericValue = number | DecimalValue;

export interface AddressObject {
  address: string;
  province?: Province;
  district?: District;
  ward?: Ward;
  fullAddress?: string;
}

export interface Province {
  code?: string;
  name?: string;
}

export interface District {
  code?: string;
  name?: string;
}

export interface Ward {
  code?: string;
  name?: string;
}
export interface DetailProject {
  id: string;
  _id?: string;
  name: string;
  type: string;
  imageUrl: string;
  status: string;
  code: string;
  investor: {
    id: string;
    name?: string;
  };
  source: string;
  currentStatus: string;
  shortName: string;
  namePublic: string;
  legalName: string;
  banks?: IBankProject[];
  designUnit: string;
  builidingUnit: string;
  addressObject: AddressObject;
  address: string;
  urlProject: string;
  numberOfPropertys: number | null;
  numOfBlocks: number | null;
  expectedDate: string;
  urlEsalekit: string;
  innerUtility: [string];
  outerUtility: [string];
  outstandingAdv: [string];
  description: string;
  projectArea: NumericValue;
  constructionArea: NumericValue;
  parkArea: NumericValue;
  constructionPercent: NumericValue;
  constructionFloorArea: NumericValue;
  greenPercent: NumericValue;
  greenAreaOnBuilding: NumericValue;
  trafficArea: NumericValue;
  esalekit?: boolean;
  blocks: IBlock[];
  setting: {
    dateBeforeFirstDebtRemind: any[];
    dateBeforeNextDebtRemind: any[];
    latePaymentFee: number;
    daysPerYear: number;
    delayDay: number;
    onlyWorkday: boolean;
    dueDateReminderDays: number;
    interestJobTime: string;
    isRetailCustomer: boolean;
    isManualReceipt: boolean;
    paymentTypes: any[];
    paymentPolicies: any[];
    discountPolicies: any[];
    templateFileContract: any[];
    salesProgram: any[];
    onlinePaymentMethods: any[];
    requireDocument: any[];
    rootPos: RootPos;
    debtage: any[];
    debtReminder: any[];
    salesUnit: any[];
    templateFileHasStatus: any[];
    templateFileHasType: any[];
    isSyncCRMInvestor: boolean;
    allowOnlinePayment: boolean;
    allowSubPayment: boolean;
    amountByTypes: any[];
    templateFiles: TemplateFile[];
    marketPos: {
      id: string;
      name: string;
      code: string;
    };
    amountRegistration: number;
  };
}

interface RootPos {
  id: string;
  name: string;
  code: string;
}

interface TemplateFile {
  projectCustomFormId: string;
  name: string;
  url: string;
  amountLabel: string;
}

export interface DocumentProject {
  softDelete: boolean;
  name: string;
  id: string;
  isFolder: boolean;
  projectDocumentFiles: DocumentFilesProject[];
}

export interface DocumentFilesProject {
  softDelete: boolean;
  key: string;
  location: string;
  eTag: string;
  bucket: string;
  id: string;
  isFolder: boolean;
}

export interface OrgchartProject {
  id: string;
  nameVN: string;
  code: string;
  externalOrgcharts: ExternalOrgcharts[];
}

export interface ListĐVBH {
  _id: string;
  code: string;
  id: string;
  nameVN: string;
}

export interface ListĐTHT {
  id: string;
  partnershipCode: string;
  partnershipName: string;
}

export interface ExternalOrgcharts {
  id: string;
  partnershipCode: string;
  partnershipName: string;
}

export interface ProjectSaleProgram {
  blocks: string[] | IBlock[];
  saleTimeRange?: [Dayjs | null, Dayjs | null];
  id: string;
  openingTimeSale: string;
  endTimeSale: string;
  project: { id: string; name?: string };
  status: string;
  name: string;
  code: string;
  lockConfirmable: boolean;
  saleUnitLockConfirmable: boolean;
  combineSaleUnitConfirmable: boolean;
  allowSendCustomerStartPriority: boolean;
  isSuccessTransactionConfirmable: boolean;
  notSetPriority: boolean;
  customerConfirmRequired: boolean;
  customerSmsExpiredTime: number;
  customerSmsContent: string;
  customerSmsContentSuccess: string;
  allowBookingPriority: boolean;
  allowViewAllUnitProcessing: boolean;
  hasPopupAdminConfirm: boolean;
  holdPropertyWhenUnregister: boolean;
  unCountPermissionNumPropertyOfUnit: boolean;
  allowViewPricePublic: boolean;
  skipCountHasCustomer: boolean;
  dwellTime: number;
  dwellTimeBookingPriority: number;
  dwellTimeReBooking: number;
  discount: number;
  isWorkingTime: boolean;
  countByWorkingHours: boolean;
  workingTime: WorkingTime[];
  sharedPos: SharedPos[];
  externalOrgPos: externalOrgPos[];
  priceStatus: string[];
  internalOrgcharts: SalesUnitType[];
}

export interface WorkingTime {
  name: string;
  startTime: string | null;
  endTime: string | null;
}

export interface SharedPos {
  code: string;
  name: string;
}

export interface externalOrgPos {
  code: string;
  name: string;
}
export interface IBlock {
  id?: string;
  projectId?: string;
  block?: string;
  floors?: string[];
  rooms?: string[];
  softDelete?: boolean;
  createdDate?: Date;
  updatedDate?: Date;
}

export interface IGetSalesProgramParams {
  projectId: string;
  page: number;
  search: string;
}

export interface IGetPropertyUnit {
  projectId: string;
  salesProgramIds: string;
  _fields?: string; // Add the _fields property
  categoryId?: string; // Add the categoryId property
  primaryStatus?: string; // Add the primaryStatus property
}

export interface IPropertyUnit {
  id: any;
  project: any;
  code: string;
  primaryStatus: string;
  shortCode: string;
  floor: string;
  block: string;
  price: number;
  priceVat: number;
  priceAbove: number;
  priceAboveVat: number;
  contractPrice: number;
}

export interface IFormDocumentProject {
  name?: string;
  projectID?: string;
  id?: string;
  type?: string;
  path?: string;
  sizeFile?: number;
}
export interface IitemDocumentProject {
  id: string;
  name: string;
  path: string;
  sizeFile: number;
  type?: string;
}
export interface IDocumentProject {
  id: string;
  projectId: string;
  type: string;
  name: string;
  path: string;
  sizeFile: number;
  items: IitemDocumentProject[];
  softDelete: boolean;
  createdDate: Date;
  updatedDate: Date;
}

export interface IFilterDocumentProject {
  page?: number;
  pageSize?: number;
  search?: string;
  projectID?: string;
}
export interface DeletePartnerCodePayload {
  orgchartInternalId: string;
  orgchartExternalId: string;
}

export interface UpdatePartnerCodePayload {
  orgchartInternal: OrgchartInternal[];
}

export interface OrgchartInternal {
  id: string;
  orgchartExternalIds: string[];
}

export interface UpdateOrgchartProjectPayload {
  projectId: string;
  internalOrgcharts?: {
    id: string;
    externalOrgchartIds?: string[];
  }[];
  rootPosId?: string;
  marketPosId?: string;
}

export interface SelectValueDVHB {
  value: string;
  label: {
    nameVN: string;
    code: string;
  };
}

export type WorkingTimeConvert = {
  name: string;
  timeRange: [Dayjs | null, Dayjs | null];
};

export interface CycleWorkingTime {
  fromDay: string;
  toDay: string;
  timeRange: [Dayjs | null, Dayjs | null];
}

export interface IRoles {
  id: string;
  name: string;
}
export interface IInternalEmployee {
  id: string;
  accountId: string;
  email: string;
  name: string;
}

export interface IRoleGroup {
  id: string;
  name: string;
  roles?: IRoles[];
  internalEmployee?: IInternalEmployee[];
  origin?: string;
}
export interface ExportExcelProject {
  code: string;
  name: string;
  shortCode: string;
  apartmentType: string;
  block: string;
  status: string;
  floor: string;
  bedroom: number;
  direction: string;
  view2: string;
  corner: string;
  insideArea: string;
  area: string;
  outsideArea: string;
  priceAbove: string;
  priceAboveVat: string;
  price: string;
  priceVat: string;
  housePriceVat: string;
  landPriceVat: string;
  housePrice: string;
  landPrice: string;
  constructStatus: string;
  constructStatusXD: string;
}
