export interface ImportHistory {
  unitsFail?: string[];
  createdBy: string;
  fail: number;
  success: number;
  salesProgramId: string;
  projectId: string;
  status: string;
  id: string;
  importDate: string;
  projectName: string;
  email: string;
  userName: string;
}

export interface Project {
  code?: string;
  id?: string;
  name?: string;
}

export interface Uploader {
  email?: string;
  id?: string;
  name?: string;
  username?: string;
}

export interface FailedFile {
  failedFileName?: string;
  failedFileUrl?: string;
}
