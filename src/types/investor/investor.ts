export interface Investor {
  [key: string]: unknown;
  name?: string;
  shortName?: string;
  createdBy?: string;
  description?: string;
  active?: string;
  modifiedBy?: string;
  modifiedDate?: string;
  softDelete?: boolean;
  softDeleteReason?: string;
  _id?: string;
  code?: string;
  phone?: string;
  linkWebsite?: string;
  avatar?: string;
  id?: string;
  createdDate?: string;
  __v?: 0;
  investorCode?: string;
  logo?: string;
  website?: string;
  addressObject?: { province?: Province; district?: District; ward?: Ward };
}

export interface DetailInvestor {
  name?: string;
  shortName?: string;
  createdBy?: string;
  description?: string;
  active?: string;
  modifiedBy?: string;
  modifiedDate?: string;
  softDelete?: boolean;
  softDeleteReason?: string;
  _id?: string;
  code?: string;
  phone?: string;
  linkWebsite?: string;
  avatar?: string;
  id?: string;
  createdDate?: string;
  __v?: 0;
  investorCode?: string;
  logo?: string;
  website?: string;
  addressObject?: AddressObject;
}

export interface AddressObject {
  province?: Province;
  district?: District;
  ward?: Ward;
}

export interface Province {
  code?: string;
  name?: string;
}

export interface District {
  code?: string;
  name?: string;
}

export interface Ward {
  code?: string;
  name?: string;
}

export type TDataCreateDuplicate = {
  investorCode: string;
  name: string;
  phone: string;
};
