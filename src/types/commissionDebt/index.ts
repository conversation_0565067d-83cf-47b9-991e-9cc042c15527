import { Dayjs } from 'dayjs';
import { TCreatedBy, TProjectDropdown } from '../common/common';
import { TPeriodSelected } from '../../components/dropdown/dropdownFilterPeriod';
import { ICommissionDebtPolicy } from '../commissionDebtPolicy';

export interface ICommissionDebt {
  _id: string;
  id: string;
  code: string;
  name: string;
  isPublish: boolean;
  isActive: number;
  softDelete: boolean;
  softDeleteReason: string;
  type: string | null;
  status: string;
  period: string;
  periodFrom: string;
  periodTo: string;
  year: string;
  commissionPolicy: string;
  project: TProjectDropdown;
  createdBy: TCreatedBy;
  modifiedBy: TCreatedBy;
  adjustmentVersions: IAdjustmentVersions[];
  createdDate: string;
  modifiedDate: string;
  createdByObj: TCreatedBy;
  modifiedByObj: TCreatedBy;
}
export type TFilterCommissionDebt = {
  search?: string;
  startCreatedDate?: Dayjs | string | null;
  endCreatedDate?: Dayjs | string | null;
  period?: TPeriodSelected[] | string;
  year?: string | Dayjs | null;
  createdBy?: string[] | string | null;
  project?: string | null;
};

export interface TCommissionPeriodDetail {
  _id: string;
  id: string;
  code: string;
  name: string;
  isPublish: boolean;
  isActive: number;
  softDelete: boolean;
  softDeleteReason: string;
  type: string | null;
  status: string;
  period: string;
  periodFrom: string;
  periodTo: string;
  year: string;
  project: TProjectDropdown;
  commissionPolicy: ICommissionDebtPolicy;
  adjustmentVersions: IAdjustmentVersions[];
  createdBy: TCreatedBy;
  modifiedBy: TCreatedBy;
  createdDate: string;
  modifiedDate: string;
  transactions: ITransactionDebt[];
}

export interface IAdjustmentVersions {
  id: string;
  version: string;
  fileName: string;
  fileUrl: string;
  filePath: string;
  status: string;
  uploadDate: string;
  uploadBy: string;
}

export interface ITransactionDebt {
  index: number;
  id: string;
  code: string;
  commission: {
    name: string;
    period: string;
    type: string;
    code: string;
    id: string;
  };
  vatRate: number;
  project: TProjectDropdown;
  propertyUnit: {
    id: string;
    view1: string;
  };
  contract: {
    id: string;
    type: string;
    code: string;
    name: string;
  };
  customer: {
    code: string;
    name: string;
  };
  debtCollector: {
    id: string;
    accountId: string;
    code: string;
    name: string;
  };
  pos: {
    id: string;
    code: string;
    name: string;
  };
  isPublish: boolean;
  softDelete: boolean;
  softDeleteReason: string;
  debtType: string;
  installmentName: string;
  commissionPolicy: ICommissionPolicy;
  debtage: {
    name: string;
  };
  employees: IEmployeeCommission[];
  createdBy: string;
  createdDate: string;
  modifiedDate: string;
  adjustmentData: [];
}

export interface ICommissionPolicy {
  code: string;
  rate: number;
  isProgressive: boolean;
  type: string;
  isVAT: boolean;
  listRate: {
    topPrice: number;
    bottomPrice: number;
    unit: string;
    recordedCommissionUnit: string;
    recordedCommission: number;
  }[];
}

export interface IEmployeeCommission {
  id: string;
  code: string;
  name: string;
  commissions: {
    debtRevenue: number;
    totalDebtRevenue: number;
    recordedCommission: number;
    unit: string;
    debtRate: number;
    commissionVatRate: number;
    debtCommissionRevenue: number;
  }[];
}

export interface IDataSubmitCalculate {
  commissionCode: string;
  projectId: string;
  periodFrom: string;
  periodTo: string;
  year: string;
  commissionPolicyCode: string; // code
}

export interface IDataSubmitUpdate {
  id: string;
  periodName: string;
  commissionCode: string;
  projectId: string;
  periodFrom: string;
  periodTo: string;
  year: string;
  commissionPolicyCode: string; // code
}
