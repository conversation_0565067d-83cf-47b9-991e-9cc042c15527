export interface EmployeeExternal {
  [key: string]: unknown;
  isActive?: number;
  softDelete?: boolean;
  _id?: string;
  name?: string;
  partnership?: PartnerShip;
  isLineManager?: boolean;
  email?: string;
  phone?: string;
  identityCode?: string;
  dateOfIdentityCode?: string;
  placeOfIdentityCode?: string;
  gender?: number;
  dob?: string;
  contactAddress?: string;
  address?: { province?: Province; district?: District; ward?: Ward };
  id?: string;
  code?: string;
  createdDate?: string;
  updatedDate?: string;
}

export interface DetailEmployeeExternal {
  _id?: string;
  isActive?: number;
  softDelete?: boolean;
  name?: string;
  partnership?: PartnerShip;
  isLineManager?: boolean;
  email?: string;
  phone?: string;
  identityCode?: string;
  dateOfIdentityCode?: string;
  placeOfIdentityCode?: string;
  gender?: number;
  dob?: string;
  contactAddress?: string;
  address?: { province?: Province; district?: District; ward?: Ward };
  id?: string;
  code?: string;
  createdDate?: string;
  updatedDate?: string;
}

interface Province {
  code: string;
  name: string;
}

interface District {
  code: string;
  name: string;
}

interface Ward {
  code: string;
  name: string;
}

interface PartnerShip {
  code: string;
  name: string;
}
