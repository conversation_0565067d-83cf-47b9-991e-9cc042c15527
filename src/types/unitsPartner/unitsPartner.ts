export interface UnitPartner {
  [key: string]: unknown;
  _id?: string;
  partnerCode?: PartnerCode;
  bank?: Bank;
  representIssuedDate?: string;
  other?: [];
  active?: boolean;
  softDelete?: boolean;
  partnershipName?: string;
  taxCode?: string;
  partnershipCode?: string;
  partnershipLevel?: string;
  logo?: string;
  id?: string;
  lineManager: string;
  lastUpdate?: string;
  managerIds?: Manager[];
  createdDate?: string;
  modifiedBy?: string;
}

export interface ListCooperativeSalesUnits {
  [key: string]: unknown;
  _id?: string;
  bank?: Bank;
  representIssuedDate?: string;
  other?: [];
  active?: boolean;
  softDelete?: boolean;
  partnershipName?: string;
  level?: string;
  taxCode?: string;
  partnershipCode?: string;
  partnershipLevel?: string;
  partnerCode?: PartnerCode;
  logo?: string;
  id?: string;
  lastUpdate?: string;
  managerIds?: Manager[];
  createdDate?: string;
  softDeleteReason?: string;
  lineManager?: string;
}

export interface Manager {
  id?: string;
}

export interface Bank {}

export interface PartnerCode {
  _id?: string;
  status?: number;
  parentCode?: string;
  parentName?: string;
  lineManager?: string;
  managerId?: string;
  taxCode?: string;
  representBy?: string;
  representPhone?: string;
  representEmail?: string;
  representIDValue?: string;
  representIssuedDate?: string;
  representIssuedPlace?: string;
  representAddressObject?: string;
  representContactAddress?: string;
  representTaxNumber?: string;
  contactAddress?: string;
  contactPhone?: string;
  contactFax?: string;
  bank?: Bank;
  bankInfo?: string;
  addressObject?: string;
  phone?: string;
  branchName?: string;
  certNumber?: string;
  source?: string;
  other?: [];
  lastUpdate?: string;
  businessArea?: string;
  costCenter?: string;
  orgLayer?: string;
  level?: number;
  shortName?: string;
  nameEN?: string;
  nameVN?: string;
  code?: string;
  createdBy?: string;
  modifiedBy?: string;
  id?: string;
  createdDate?: string;
  label?: string;
  value?: string;
}
