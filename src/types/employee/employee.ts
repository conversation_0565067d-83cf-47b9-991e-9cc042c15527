export interface EmployeeInternal {
  accountId: string;
  code: string;
  createdDate: string;
  email: string;
  id: string;
  isActive: boolean;
  jobTitle: JobTitle;
  modifiedBy: string;
  modifiedDate: string;
  name: string;
  phone: string;
  source: string;
  level1: string;
  level3: string;
}
interface JobTitle {
  code: string;
  name: string;
}

interface Position {
  code: string;
  name: string;
}

export interface DetailEmployeeInternal {
  id?: string;
  code: string;
  name: string;
  email: string;
  phone: string;
  identityCode: string;
  gender: number; // Assuming 0 for male, 1 for female, etc.
  dob: string; // ISO 8601 date string
  street: string;
  city: string;
  isActive?: boolean;
  level1?: string;
  level1Name?: string;
  level2?: string;
  level2Name?: string;
  level3?: string;
  level3Name?: string;
  level4?: string;
  level4Name?: string;
  level5?: string;
  level5Name?: string;
  level6?: string;
  level6Name?: string;
  jobTitle?: JobTitle;
  position?: Position;
  accountId?: string;
  source?: string;
  identityCode2?: string;
}
