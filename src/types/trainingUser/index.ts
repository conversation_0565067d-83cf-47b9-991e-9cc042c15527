import { Dayjs } from 'dayjs';

export interface ITrainingUser {
  id: string;
  name: string;
  code: string;
  createdDate?: string | Dayjs | null;
  updatedDate?: string | Dayjs | null;
  updatedBy: TCreatedBy;
  createdBy: TCreatedBy;
  configNumber?: number;
  phone?: string;
}

export type TEvent = {
  id: string;
  name: string;
  code: string;
  eventName: string;
};

export type TCustomer = {
  id: string;
  key: string;
  name: string;
  code: string;
  email: string;
  phone: string;
  isAddButton?: boolean;
  idEvent?: string;
};

export type TCreatedBy = {
  email?: string;
  userName: string;
  fullName?: string;
};

export type TCustomerAttendance = {
  id: string;
  name: string;
  email: string;
  phoneNumber: string;
  checkInDate: string;
  checkOutDate: string;
  stt: number;
  timeAttend: number;
  ratio: string; // e.g., "100 %"
};
