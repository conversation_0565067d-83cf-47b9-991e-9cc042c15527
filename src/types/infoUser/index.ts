export interface UserProfile {
  id: string;
  username: string;
  isActive: boolean;
  isAdmin: boolean;
  code: string;
  email: string;
  phone: string;
  identity: string;
  fullName: string;
  gender: string;
  position: string;
  orgCode: string;
  orgName: string;
  jobTitles: JobTitle[];
  roles: Role[];
}

export interface JobTitle {
  type: number;
  jobTitleCode: string;
  jobTitleName: string;
  orgCode: string;
}

export interface Role {
  name: string;
  code: string;
  type: number;
  isDelete: boolean;
  isAdmSource: boolean;
  functionRoles: FunctionRole[];
  dataRoles: DataRole[];
}

export interface FunctionRole {
  name: string[];
  features: Feature[];
}

export interface Feature {
  featureName: string;
  msxName: string;
}

export interface DataRole {
  isDelete: boolean;
  name: string;
  code: string;
  type: string;
  source: string;
}
