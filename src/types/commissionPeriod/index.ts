export type TCommissionPeriodCreate = {
  periodFrom: string;
  periodTo: string;
  type: string;
  year: string;
  pos: TPos;
  salePolicy: string;
  period?: string;
  indicator: string;
  commissionPolicyPersonal: string;
  commissionPolicyManager: string;
};

export type TSalePolicy = {
  id: string;
  name: string;
  code?: string;
};

export type TPos = {
  id: string;
  code: string;
  name: string;
};
