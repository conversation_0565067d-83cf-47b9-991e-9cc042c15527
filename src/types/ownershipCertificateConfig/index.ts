import { Dayjs } from 'dayjs';
import { TCreatedBy } from '../common/common';
import { TProject } from '../leadAssigned';
import { TListDropdown } from '../salesPolicy';

export type TListOfOwnershipCertificateConfig = {
  project: TProject;
  items: string;
  expectedStartDate: string;
  expectedEndDate: string;
  isActive: number;
  createdDate: string;
  createdBy: TCreatedBy;
  updatedDate: string;
  updatedBy: TCreatedBy;
  id: string;
};

export type TFilterOwnershipCertificateConfig = {
  search?: string;
  startCreatedDate?: string | Dayjs | null;
  endCreatedDate?: string | Dayjs | null;
};

export type projectAllForOwnershipCertificateConfig = {
  // address: null;
  code: string;
  id: string;
  investor: string;
  lock: boolean;
  name: string;
  setting: {
    isRetailCustomer: boolean;
    // rootPos: null; salesUnit: []; adviceType: null
  };
  status: string;
  type: string;
};

export type TNotificationConfig = {
  emailTemplate: string;
  smsTemplate: string;
  smsBrandName: string;
  emailTitle: string;
  emailFrom: string;
  emailCC?: string;
  emailBCC?: string[];
};

export type OwnershipCertificateConfig = {
  status: number;
  itemsForCerReadyHandover: THandover[];
  itemsCerInProcess: THandover[];
  itemsForEligible: THandover[];
  orgCharts: TListDropdown[];
  emailForEligible: TNotificationConfig;
  emailCerHandedOver: TNotificationConfig;
  emailForCerReadyHandover: TNotificationConfig;
  expectedStartDate: string; // ISO date string
  expectedEndDate: string; // ISO date string
  mondayFrame?: TTimeFrame[];
  tuesdayFrame?: TTimeFrame[];
  wednesdayFrame?: TTimeFrame[];
  thursdayFrame?: TTimeFrame[];
  fridayFrame?: TTimeFrame[];
  saturdayFrame?: TTimeFrame[];
  sundayFrame?: TTimeFrame[];
  timeFrames?: TTimeFrame[];
  hotline: string;
  accountingConfirm: number;
  project: {
    name: string;
    id: string;
  };
  paymentPercent: number;

  createdDate: string;
  createdBy: TCreatedBy;
  updatedDate: string;
  updatedBy: TCreatedBy;
  isActive: number;
};

export type TOwnershipCertificateConfigGen = {
  project: {
    name: string;
    id: string;
  };
  expectedDateRange: [Dayjs, Dayjs];
  status: boolean;
  itemsForCerReadyHandover: THandover[];
  itemsCerInProcess: THandover[];
  itemsForEligible: THandover[];
  orgCharts: TListDropdown[];
  paymentPercent: string;
  accountingConfirm: boolean;
  hotline: string;
  timeFrames?: TTimeFrame[];
  isActive?: boolean;
};

export type TOwnershipCertificateConfigNoti = {
  emailForEligible: TNotificationConfig;
  emailCerHandedOver: TNotificationConfig;
  emailForCerReadyHandover: TNotificationConfig;
};

export type TTimeFrame = {
  amount?: number;
  startTime: string;
  endTime: string;
  day?: string;
  timeRange?: (Dayjs | undefined)[];
};

export type THandover = {
  title: string;
  name: string;
  id?: string;
  list: THandoverCategory[];
  children?: THandoverCategory[];
};

export type THandoverCategory = {
  type: string;
  description: string;
  title: string;
};

export type TOrgChart = {
  code: string;
  id: string;
  name: string;
};
