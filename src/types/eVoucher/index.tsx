import { Dayjs } from 'dayjs';
import { TCreatedBy } from '../common/common';

export interface IEVoucher {
  id?: string;
  code?: string;
  name?: string;
  startDate?: Dayjs;
  endDate?: Dayjs;
  isActive: number;
  isProgressive?: boolean;
  isVAT?: boolean;
  createdDate?: string;
  createdBy?: TCreatedBy;
  updatedDate?: string;
  updatedBy?: TCreatedBy;
  approvedDate?: string;
  approvedBy?: TCreatedBy;
  iscommissionListUsed?: boolean;
  activeTime?: number;
  amount?: number;
  maxAmountReduced?: number;
  promotionValue?: number;
  startTime?: Dayjs;
  endTime?: Dayjs;
  businessPartner?: TBusinessPartner[];
  applyNvkdAmount?: number;
  employeeApplyAmount?: number;
  employeeApplyCommonAmount?: number;
  applyNvkd?: TBusinessPartner[];
  employeeApply?: TBusinessPartner[];
  employeeApplyCommon?: TBusinessPartner[];
  avatarImage?: string;
  detailImage?: string;
  listSelfCreatedVoucher?: TPos[];
}

export type TCreateEVoucher = {
  id?: string;
  code?: string;
  name?: string;
  startDate?: Dayjs;
  endDate?: Dayjs;
  isActive: number;
  isProgressive?: boolean;
  isVAT?: boolean;
  createdDate?: string;
  createdBy?: TCreatedBy;
  updatedDate?: string;
  updatedBy?: TCreatedBy;
  iscommissionListUsed?: boolean;
  activeTime?: ActiveTime;
  amount?: number;
  maxAmountReduced?: number;
  promotionValue?: number;
  startTime?: string;
  endTime?: string;
  applyNvkdAmount?: number;
  employeeApplyAmount?: number;
  employeeApplyCommonAmount?: number;
  businessPartner?: TBusinessPartner[];
  applyNvkd?: TBusinessPartner[];
  employeeApply?: TBusinessPartner[];
  employeeApplyCommon?: TBusinessPartner[];
  avatarImage?: string;
  detailImage?: string;
};

export type ActiveTime = {
  activeTime: number;
  type: string;
};

export type TBusinessPartner = {
  id: string;
  code?: string;
  name?: string;
  merchantLimitAmount?: number;
  amount?: number;
  pos?: TPos;
};

export type TPos = {
  id: string;
  name: string;
};

export type TEmployee = {
  id: string;
  name?: string;
  email?: string;
  phone?: string;
  pos?: TPos;
};
