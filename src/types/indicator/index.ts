import dayjs, { Dayjs } from 'dayjs';
import { TCreatedBy } from '../common/common';

export type TListIndicator = {
  code: string;
  period: string;
  status: string;
  createdDate: string;
  createdBy: string;
  updatedDate: string;
  updatedBy: string;
};

export type TEmployee = {
  id: string;
  name: string;
  code: string;
  positionName: string;
};

export type TListIndicatorDetail = {
  id?: string;
  code?: string;
  employee?: TEmployee;
  position?: string;
  externalOrgchart?: string;
  internnalOrgchart?: string;
  indicatorRevenue?: string;
  indicatorProduct?: string;
};
export interface IIndicator {
  code?: string;
  key?: string;
  id?: string;
  year?: dayjs.Dayjs | string | number;
  nameVN?: string;
  period?: string;
  isActive?: string;
  pos?: TListPos;
  listPos?: TListPos[];
  indicators?: TListIndicatorDetail[];
  createdBy?: TCreatedBy;
  modifiedBy?: TCreatedBy;
  createdDate?: string; // Consider using Date type if parsing is needed
  modifiedDate?: string;
  periodTo: string;
  periodFrom: string;
  periodName: string;
}

export interface IIndicatorDetail {
  indicator?: IIndicator;
  indicatorList?: TListIndicatorDetail[];
}

export type TListPos = {
  id: string;
  code?: string;
  name?: string;
  value?: string;
  label?: string;
};

export type TCreateIndicator = {
  code?: string;
  key?: string;
  id?: string;
  year?: dayjs.Dayjs | string | number;
  period?: string;
  isActive?: string;
  pos?: TListPos;
  listPos?: TListPos[];
};

export interface ExternalOrgcharts {
  id: string;
  partnershipCode: string;
  partnershipName: string;
}

export interface OrgchartIndicator {
  id: string;
  nameVN: string;
  name: string;
  code: string;
  externalOrgcharts: ExternalOrgcharts[];
}

export type TResponsiveImport = {
  status: 'PENDING' | 'SUCCESS' | 'PARTIAL_ERROR' | 'ENTIRE_ERROR'; // Assuming possible statuses
  fileUrl: string;
  fileName: string;
  processBy: string;
  id: string;
  createdDate: string; // Consider using Date type if parsing is needed
  updatedDate: string;
};

export interface IModalCreateIndicator {
  isOpen: boolean;
  // toggleModal?: (isVisible: boolean) => void;
  handleCancel: () => void;
}

export type TFilterCommission = {
  period?: string;
  query?: string;
  type?: string;
  startDate?: string | Dayjs | null;
  endDate?: string | Dayjs | null;
  year?: string | Dayjs | null;
};

export type TOriginalData = {
  code: string;
  projectName: string;
  propertyUnitCode: string;
  attributeName: string;
  name: string;
  price: string;
  referencePrice: string;
  otherFee: string;
  revenueRate: string;
  registerFee: string;
  state: string;
  advance: string;
  escrowDate: string;
  consultingFee: string;
  total: string;
};

export type TExpense = {
  expenseList: TOriginalData[];
  totalProperty: number;
  listBonus: number;
  totalFee: number;
};

export type TAdjustmentVersion = {
  _id: string;
  id: string;
  version: string;
  uploadBy: string;
  logFile: string;
  status: string;
  uploadDate: string;
  fileName: string;
  fileUrl: string;
  commissionId: string;
};

export type TSalePolicy = {
  id: string;
  name: string;
};

export type TPos = {
  id: string;
  code: string;
  name: string;
};

export type TCommission = {
  isPublish: boolean;
  publishTo: string[];
  name: string;
  periodFrom: string;
  periodTo: string;
  period: string;
  type: string;
  year: string | Dayjs | null;
  pos: TPos;
  workflows: string[];
  isActive: number;
  softDelete: boolean;
  softDeleteReason: string;
  _id: string;
  salePolicy: TSalePolicy;
  adjustmentVersions: TAdjustmentVersion[];
  modifiedBy: string;
  createdBy: string;
  code: string;
  id: string;
  modifiedDate: string;
  createdDate: string;
};

export type TCommissionCreate = {
  periodFrom: string;
  periodTo: string;
  type: string;
  year: string;
  pos: TPos;
  salePolicy: TSalePolicy;
  adjustmentVersions?: TAdjustmentVersion[];
  isPublish?: boolean;
  period?: string;
};

export type TExpenseGetDraft = {
  salePolicyId: string;
  year: string;
  periodForm: string;
  periodTo: string;
};

export type TListHistoryImportCommissions = {
  fileName: string;
  processBy: {
    isAdmin: boolean;
    isActive: boolean;
    status: number;
    modifiedBy: string | null;
    id: string;
    username: string;
    code: string;
    referCode: string;
    fullName: string;
    email: string;
    orgCode: string;
    orgName: string;
    accountType: string;
    position: string;
    createdDate: string;
    modifiedDate: string;
  };
  success: number;
  fail: number;
  type: string;
  createdDate: string;
  updatedDate: string;
  failedFileUrl: string;
  status: string;
  id: string;
  failedFileName: string;
};
