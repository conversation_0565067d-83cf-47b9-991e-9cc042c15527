import { Dayjs } from 'dayjs';
import { TPeriodSelected } from '../../components/dropdown/dropdownFilterPeriod';
import { TCreatedBy, TProjectDropdown } from '../common/common';

export type TListOfCommission = {
  isPublish: boolean;
  name: string;
  period: string;
  type: string;
  status: string;
  year: string;
  pos: TPos;
  active: boolean;
  workflows: unknown[];
  modifiedBy: TCreatedBy;
  createdBy: TCreatedBy;
  code: string;
  id: string;
  modifiedDate: string;
  createdDate: string;
  adjustmentVersions: TAdjustmentVersion[];
  salePolicy: TSalePolicy;
  isActive: number;
};

export type TFilterCommission = {
  period?: TPeriodSelected[] | string;
  query?: string;
  type?: string;
  startDate?: string | Dayjs | null;
  endDate?: string | Dayjs | null;
  year?: string | Dayjs | null;
  orgchart?: string[] | string | null;
  createdBy?: string[] | string | null;
};

export type TOriginalData = {
  code: string;
  projectName: string;
  propertyUnitCode: string;
  attributeName: string;
  name: string;
  price: string;
  referencePrice: string;
  otherFee: string;
  revenueRate: string;
  registerFee: string;
  state: string;
  advance: string;
  escrowDate: string;
  consultingFee: string;
  total: string;
};

export type TExpense = {
  expenseList: TOriginalData[];
  totalProperty: number;
  listBonus: number;
  totalFee: number;
};

export type TAdjustmentVersion = {
  _id: string;
  id: string;
  version: string;
  uploadBy: string;
  logFile: string;
  status: string;
  uploadDate: string;
  fileName: string;
  fileUrl: string;
  commissionId: string;
};

export type TSalePolicy = {
  id: string;
  name: string;
  code?: string;
};

export type TPos = {
  id: string;
  code: string;
  name: string;
};

export type TCommission = {
  isPublish: boolean;
  publishTo: string[];
  name: string;
  periodFrom: string;
  periodTo: string;
  period: string;
  type: string;
  year?: string | Dayjs | null;
  pos: TPos;
  workflows: string[];
  isActive: number;
  softDelete: boolean;
  softDeleteReason: string;
  _id: string;
  salePolicy: TSalePolicy;
  adjustmentVersions: TAdjustmentVersion[];
  modifiedBy: TCreatedBy;
  createdBy: TCreatedBy;
  code: string;
  id: string;
  modifiedDate: string;
  createdDate: string;
  transactions?: CommissionTransaction[];
  indicator: {
    id: string;
    code: string;
    name: string;
  };
  commissionPolicyPersonal: {
    id: string;
    code: string;
    name: string;
  };
  commissionPolicyManager: {
    id: string;
    code: string;
    name: string;
  };
  periodStartDate: string;
  periodEndDate: string;
  periodName: string;
  periodObj: {
    periodFrom: string;
    periodTo: string;
    periodName: string;
  };
};

export type TCommissionCreate = {
  periodFrom: string;
  periodTo: string;
  type: string;
  year: string;
  pos: TPos;
  salePolicy: TSalePolicy;
  adjustmentVersions?: TAdjustmentVersion[];
  isPublish?: boolean;
  period?: string;
};

export type TExpenseGetDraft = {
  salePolicyId: string;
  year: string;
  periodForm: string;
  periodTo: string;
};

export type TListHistoryImportCommissions = {
  fileName: string;
  processBy: {
    isAdmin: boolean;
    isActive: boolean;
    status: number;
    modifiedBy: string | null;
    id: string;
    username: string;
    code: string;
    referCode: string;
    fullName: string;
    email: string;
    orgCode: string;
    orgName: string;
    accountType: string;
    position: string;
    createdDate: string;
    modifiedDate: string;
  };
  success: number;
  fail: number;
  type: string;
  createdDate: string;
  updatedDate: string;
  failedFileUrl: string;
  status: string;
  id: string;
  failedFileName: string;
};

export type CommissionTransaction = {
  id: string;
  code: string;
  index: number;
  transactionPhase: number;
  transactionState: string;
  transactionPrice: number;
  vatRate: number;
  escrowDate: string | null;
  submitDate: string | null;
  commission: {
    name: string;
    period: string;
    type: string;
    code: string;
    id: string;
  };
  project: TProjectDropdown;
  propertyUnit: TPropertyUnit;
  customer: {
    personalInfo: {
      name: string;
    };
  };
  pos: TPos;
  commissionRevenue: number;
  commissionReceived: number;
  revenue: TRevenue;
  isPublish: boolean;
  supportName: string;
  exchangeName: string;
  softDelete: boolean;
  softDeleteReason: string;
  employees: TEmployeesTransaction[];
  createdBy: string;
  createdDate: string;
  modifiedDate: string;
  __v: number;
};

type TPropertyUnit = {
  view1: string;
  price: number;
};
type TRevenue = {
  rate: number;
  receivedRate: number;
};

export type TEmployeesTransaction = {
  id: string;
  code: string;
  name: string;
  role: string;
  revenueRate: number;
  commissions: TEmployeeCommission[];
  managers?: TEmployeesTransaction[];
  children?: TEmployeesTransaction[];
};

export type TEmployeeCommission = {
  revenue: number;
  rate: number;
  bonus: number | null;
  advance: number | null;
  totalExpectedIncome: number;
  revenueReceived: number;
  revenueRemain: number;
  totalRevenue: number;
  totalReceived: number;
};

export type TDataSubmitCommissionPeriod = {
  id?: string;
  commissionCode?: string;
  pos?: TPos;
  periodFrom: string;
  periodTo: string;
  salePolicyCode?: string;
  year: string;
  indicatorCode: string;
  commissionPolicyPersonalCode: string;
  commissionPolicyManagerCode: string;
  periodName: string;
};
