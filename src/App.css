* {
  margin: 0;
  padding: 0;
}

main {
  height: calc(100vh - 39px);
  background-color: white;
}

.ant-form-item-label {
  > label::after {
    display: none;
  }
  .ant-form-item-required {
    &::after {
      content: '*' !important;
      display: inline;
      position: absolute;
      right: 0;
      color: red;
      font-size: 14px;
      font-weight: 600;
    }
  }
}
.ant-form-item {
  margin-bottom: 16px;
}
.ant-input-number {
  width: 100%;
}
.ant-picker {
  width: 100%;
}

.ant-form-item-required::before {
  content: none !important;
}

.ant-form-item-label > label::after {
  visibility: inherit !important;
}
.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  gap: 16px;
  .input-search {
    max-width: 384px;
  }
  .button-actions {
    display: flex;
    gap: 16px;
  }
}
@media screen and (max-width: 576px) {
  .header-content {
    flex-wrap: wrap;
  }
}
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-thumb {
  background: #c2c2c2;
  border-radius: 1px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #a8a8a8;
}

.wrapper-update-time {
  span {
    font-size: 12px;
    color: #00000040;
  }
}
