import { getRequest, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';
import { UserAdminType } from '../../page/userAdminManagement';

export const getListEmployeeInternal = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_employee}/${typeQueryVersionApi.api_v2}/internal-employee`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};
export const getDetailEmployeeInternal = async (id: string | undefined) => {
  const response = await getRequest(
    `${urlDomainApi.msx_employee}/${typeQueryVersionApi.api_v2}/internal-employee/${id}`,
  );
  return response;
};

export const getListUserAdmin = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_employee}/${typeQueryVersionApi.query}/employee/admin/allByQuery`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};

export const sendUpdatePassWordUser = async (payload: UserAdminType) => {
  const response = await putRequest(
    `${urlDomainApi.url_msx_sts}/${typeQueryVersionApi.domain}/user/admin/resetPwd`,
    payload,
  );
  return response;
};
