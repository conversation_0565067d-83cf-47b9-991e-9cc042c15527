import { getRequest, postRequest, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';
import { TDataSubmitCommissionPeriod } from '../../types/commission';

export const getListOfCommissionPeriod = async (params: unknown) => {
  return await getRequest(`${urlDomainApi.msx_commission}/${typeQueryVersionApi.query}/commission`, {
    ...(params as Record<string, unknown> | undefined),
    type: 'COMM',
  });
};

export const createCommissionPeriod = async (payload: unknown) => {
  return await postRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.domain}/commission`,
    payload as Record<string, unknown> | undefined,
  );
};

export const getSalesPolicyOfCommissionPeriod = async (params: { name?: string }) => {
  return await getRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.query}/sales-policy/pos`,
    params as Record<string, unknown> | undefined,
  );
};

export const getAllOfCommissionPolicyPos = async (params: { name?: string; type?: string }) => {
  return await getRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.query}/commission-policy/pos`,
    params as Record<string, unknown> | undefined,
  );
};

export const getAllOfCommissionIndicatorPos = async (params: { name?: string; period?: string }) => {
  return await getRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.query}/indicator/pos`,
    params as Record<string, unknown> | undefined,
  );
};

export const getByIdCommissionPeriod = async (params: unknown) => {
  const { id, ...restParams } = params as Record<string, unknown>;
  return await getRequest(`${urlDomainApi.msx_commission}/${typeQueryVersionApi.query}/commission/${id}`, restParams);
};

export const calculateCommissionPeriod = async (payload?: TDataSubmitCommissionPeriod) => {
  return await postRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.domain}/commission-list/calculate`,
    payload,
  );
};

export const putCommissionPeriod = async (payload?: TDataSubmitCommissionPeriod) => {
  return await putRequest(`${urlDomainApi.msx_commission}/${typeQueryVersionApi.domain}/commission-list`, payload);
};
