import { getRequest, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';
import { ParamsGetRequest } from '../../types/common/common';
import { ICustomers } from '../../types/customers';

export const getUserProfile = async () => {
  return await getRequest(`${urlDomainApi.msx_customer}/${typeQueryVersionApi.query}/customer/getProfile`);
};

export const getListOfficialCustomerPersonal = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_customer}/${typeQueryVersionApi.query}/customer?type=individual`,
    params as Record<string, unknown> | undefined,
  );
};

export const getListOfficialCustomerBusiness = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_customer}/${typeQueryVersionApi.query}/customer?type=business`,
    params as Record<string, unknown> | undefined,
  );
};

export const getDetailOfficialCustomer = async (id: string) => {
  return await getRequest(`${urlDomainApi.msx_customer}/${typeQueryVersionApi.query}/customer/${id}`);
};

export const updateAllOfficialCustomer = async (data: ICustomers) => {
  return await putRequest(
    `${urlDomainApi.msx_customer}/${typeQueryVersionApi.domain}/customer/updateAllCustomer`,
    data as Record<string, unknown>,
  );
};

export const updateCommonOfficialCustomer = async (data: ICustomers) => {
  return await putRequest(
    `${urlDomainApi.msx_customer}/${typeQueryVersionApi.domain}/customer/updateCommonCustomer`,
    data as Record<string, unknown>,
  );
};

export const getListEmployeeAll = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_customer}/${typeQueryVersionApi.query}/employee/dropdown`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};

export const getListTransactionInformation = async (params: ParamsGetRequest) => {
  const { id, ...restParams } = params;
  const response = await getRequest(
    `${urlDomainApi.msx_customer}/${typeQueryVersionApi.query}/customer/get-transaction-history/${id}`,
    restParams as Record<string, unknown> | undefined,
  );
  return response;
};
export const getListTransactionHistory = async (params: ParamsGetRequest) => {
  const { id, ...restParams } = params;
  const response = await getRequest(
    `${urlDomainApi.msx_customer}/${typeQueryVersionApi.query}/customer/get-property-transaction-history/${id}`,
    restParams as Record<string, unknown> | undefined,
  );
  return response;
};
