import { deleteRequest, getRequest, postRequest, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';
import { DeliveryConfig, TFilterDeliveryConfig } from '../../types/deliveryConfig';

export const getAllDeliveryConfig = async (params: TFilterDeliveryConfig) => {
  return await getRequest(`${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.query}/handover`, params);
};

export const changeStatusDeliveryConfig = async (payload: { id: string }) => {
  return await putRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.domain}/handover/change-status`,
    payload,
  );
};

export const deleteDeliveryConfig = async (payload: { id?: string; reasonDelete?: string }) => {
  const response = await deleteRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.domain}/handover`,
    payload,
  );
  return response;
};

export const postCreateDeliveryConfig = async (data: DeliveryConfig) => {
  return await postRequest(`${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.domain}/handover`, data);
};

export const getByIdDeliveryConfig = async (id?: string) => {
  return await getRequest(`${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.query}/handover/${id}`);
};

export const updateDeliveryConfig = async (data: DeliveryConfig) => {
  return await putRequest(`${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.domain}/handover`, data);
};
