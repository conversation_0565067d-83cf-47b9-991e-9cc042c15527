import { deleteRequest, getRequest, postRequest, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';
import { DiscountPolicy } from '../../types/discountPolicy';

export const getListDiscountPolicy = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/policy/discount/getAllByQuery`,
    params as Record<string, unknown> | undefined,
  );
};

export const getDetailDiscountPolicy = async (discountPolicyId: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/policy/${discountPolicyId}`,
  );
};

export const deleteDiscountPolicy = async (discountPolicyId: unknown) => {
  return await deleteRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/policy/${discountPolicyId}`,
  );
};

export const createDiscountPolicy = async (params: unknown) => {
  return await postRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/policy/discount`,
    params as Record<string, unknown> | undefined,
  );
};

export const updateDiscountPolicy = async (payload: DiscountPolicy) => {
  return await putRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/policy/discount/${payload.id}`,
    payload,
  );
};

export const getListStatusDiscountPolicy = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/policy/getListStatus`,
    params as Record<string, unknown> | undefined,
  );
};

export const getListStatusTicketPolicy = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/policy/getListTicketStatus`,
    params as Record<string, unknown> | undefined,
  );
};
