import { getRequest, postRequest, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';

export const getPriceCoefficients = async (payload: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/priceCoefficients`,
    payload as Record<string, unknown> | undefined,
  );

  return response;
};

export const createPriceCoefficients = async (payload: unknown) => {
  const response = await postRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/priceCoefficients`,
    payload,
  );

  return response;
};

export const getPriceCoefficientsDetail = async (id: string) => {
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/priceCoefficients/${id}`,
  );

  return response;
};

export const updatePriceCoefficients = async ({ payload, id }: { payload: unknown; id: string }) => {
  const response = await putRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/priceCoefficients/${id}`,
    payload,
  );

  return response;
};
