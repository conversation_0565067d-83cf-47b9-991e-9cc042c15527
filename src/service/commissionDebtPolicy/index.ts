import { deleteRequest, getRequest, postRequest, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';
import { TPayloadCommissionDebtPolicy } from '../../types/commissionDebtPolicy';

export const getAllOfCommissionDebtPolicy = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v1}/debt-commission-policy`,
    params,
  );
};

export const getDetailOfCommissionDebtPolicy = async (id: string) => {
  return await getRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v1}/debt-commission-policy/${id}`,
  );
};
export const postCreateOfCommissionDebtPolicy = async (payload?: TPayloadCommissionDebtPolicy) => {
  return await postRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v1}/debt-commission-policy`,
    payload,
  );
};
export const putUpdateCommissionDebtPolicy = async (payload?: TPayloadCommissionDebtPolicy) => {
  return await putRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v1}/debt-commission-policy/`,
    payload,
  );
};

export const deleteOfCommissionDebtPolicy = async (payload?: { id: string; reasonDelete: string }) => {
  return await deleteRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v1}/debt-commission-policy/`,
    payload,
  );
};
export const putUpdateStatusOfCommissionDebtPolicy = async (payload?: { id: string; isActive: number }) => {
  return await putRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v1}/debt-commission-policy/change-status`,
    payload,
  );
};
export const getProjectsForCommissionDebtPolicy = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v1}/debt-commission-policy/get-dropdown-projects`,
    params,
  );
};
