import { RcFile } from 'antd/es/upload';
import { axiosInstance, deleteRequest, getRequest, postRequest, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';
import { TTraining } from '../../types/training/training';

export const getAllTraining = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_training}/${typeQueryVersionApi.query}/training`,
    params as Record<string, unknown> | undefined,
  );
};

export const getDetailOfTraining = async (id?: string) => {
  return await getRequest(`${urlDomainApi.msx_training}/${typeQueryVersionApi.query}/training/${id}`);
};

export const putStatusTraining = async (payload: { id: string; isActive: boolean }) => {
  return await putRequest(
    `${urlDomainApi.msx_training}/${typeQueryVersionApi.domain}/training/activeTraining`,
    payload,
  );
};

export const getAllsSalesProgramDropdown = async (params: unknown) => {
  return await getRequest(`${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/salesProgram`, params);
};

export const postTraining = async (payload?: TTraining) => {
  return await postRequest(`${urlDomainApi.msx_training}/${typeQueryVersionApi.domain}/training`, payload);
};

export const putTraining = async (payload?: TTraining) => {
  return await putRequest(`${urlDomainApi.msx_training}/${typeQueryVersionApi.domain}/training`, payload);
};

export const getAllUserEvent = async (params: unknown) => {
  const { id, ...restParams } = params as Record<string, unknown>;
  return await getRequest(`${urlDomainApi.msx_training}/${typeQueryVersionApi.query}/user/event/${id}`, restParams);
};

export const deleteUserEvent = async (id?: string) => {
  return await deleteRequest(`${urlDomainApi.msx_training}/${typeQueryVersionApi.domain}/user/${id}`);
};

export const uploadSpinAdditional = async (file: RcFile, id: string) => {
  const formData = new FormData();
  formData.append('files', file);
  try {
    const response = await axiosInstance.post(
      `${urlDomainApi.msx_training}/${typeQueryVersionApi.domain}/training/${id}/importSpinAdditional`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    );
    return response;
  } catch (error) {
    console.error('Lỗi khi tải lên:', error);
    throw error;
  }
};

export const reSendEmail = async (payload: { id?: string; userIds?: string[] }) => {
  return await putRequest(`${urlDomainApi.msx_training}/${typeQueryVersionApi.domain}/training/resendEmail`, payload);
};

export const postImportUser = async (file: RcFile, id: string) => {
  const formData = new FormData();
  formData.append('files', file);
  try {
    const response = await axiosInstance.post(
      `${urlDomainApi.msx_training}/${typeQueryVersionApi.domain}/user/${id}/import`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    );
    return response;
  } catch (error) {
    console.error('Lỗi khi tải lên:', error);
    throw error;
  }
};

export const putUserGuestDeleteMany = async (payload: { lstId: string[] }) => {
  return await putRequest(`${urlDomainApi.msx_training}/${typeQueryVersionApi.domain}/user/guest/delete-many`, payload);
};

export const getExportUserGuest = async (id: string) => {
  return await axiosInstance.get(`${urlDomainApi.msx_training}/${typeQueryVersionApi.query}/user/${id}/export`, {
    responseType: 'arraybuffer',
  });
};
