import { deleteRequest, getRequest, postRequest, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';
import {
  ICustomers,
  IFilterDocumentCustomer,
  IFormDocumentCustomer,
  IItemDocumentCustomer,
} from '../../types/customers';

export const getUserProfile = async () => {
  return await getRequest(`${urlDomainApi.msx_customer}/${typeQueryVersionApi.query}/customer/getProfile`);
};

export const getListCustomerPersonal = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_demand}/${typeQueryVersionApi.query}/customer?type=individual`,
    params as Record<string, unknown> | undefined,
  );
};

export const getListCustomerBusiness = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_demand}/${typeQueryVersionApi.query}/customer?type=business`,
    params as Record<string, unknown> | undefined,
  );
};

export const getDetailCustomer = async (id: string) => {
  return await getRequest(`${urlDomainApi.msx_demand}/${typeQueryVersionApi.query}/customer/${id}`);
};

export const postCreateCustomer = async (data: ICustomers) => {
  return await postRequest(
    `${urlDomainApi.msx_demand}/${typeQueryVersionApi.domain}/customer`,
    data as Record<string, unknown>,
  );
};

export const putUpdateCustomer = async (data: ICustomers) => {
  return await putRequest(
    `${urlDomainApi.msx_demand}/${typeQueryVersionApi.domain}/customer`,
    data as Record<string, unknown>,
  );
};

export const changeStatusCustomer = async (data: { id: string; isActive: number }) => {
  return await putRequest(
    `${urlDomainApi.msx_demand}/${typeQueryVersionApi.domain}/customer/change-status`,
    data as Record<string, unknown>,
  );
};

export const softDeleteCustomer = async (payload: { [key: string]: unknown }) => {
  return await deleteRequest(
    `${urlDomainApi.msx_demand}/${typeQueryVersionApi.domain}/customer/${payload.id}`,
    payload as Record<string, unknown> | undefined,
  );
};

export const getListEmployeeAll = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_demand}/${typeQueryVersionApi.query}/employee/dropdown`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};

export const getByIdEmployeeAll = async (params: unknown) => {
  const { id, ...restParams } = params as Record<string, unknown>;
  const response = await getRequest(
    `${urlDomainApi.msx_demand}/${typeQueryVersionApi.query}/employee/dropdown/${id}`,
    restParams as Record<string, unknown> | undefined,
  );
  return response;
};

export const getEmployeeDropdownById = async (params?: { id?: string }) => {
  const response = await getRequest(
    `${urlDomainApi.msx_demand}/${typeQueryVersionApi.query}/employee/dropdownById`,
    params,
  );
  return response;
};

export const getListHistoryImportDemandCustomer = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_demand}/${typeQueryVersionApi.query}/customer/import-history`,
    params as Record<string, unknown> | undefined,
  );
};

export const importCustomer = async (payload: unknown) => {
  return await postRequest(`${urlDomainApi.msx_demand}/${typeQueryVersionApi.domain}/customer/import`, payload);
};
export const getCustomerDocument = async (params: IFilterDocumentCustomer) => {
  const { customerID, ...restParams } = params;
  const response = await getRequest(
    `${urlDomainApi.msx_customer}/${typeQueryVersionApi.api_v2}/customer-document/${customerID}`,
    restParams,
  );
  return response;
};
export const addCustomerDocument = async (params: IFormDocumentCustomer) => {
  const response = await postRequest(
    `${urlDomainApi.msx_customer}/${typeQueryVersionApi.api_v2}/customer-document/${params.customerID}`,
    params,
  );
  return response;
};
export const updateCustomerDocument = async (params: IFormDocumentCustomer) => {
  const response = await putRequest(
    `${urlDomainApi.msx_customer}/${typeQueryVersionApi.api_v2}/customer-document/${params.customerID}/${params.id}`,
    params,
  );
  return response;
};
export const deleteCustomerDocument = async (params: IFormDocumentCustomer) => {
  const response = await deleteRequest(
    `${urlDomainApi.msx_customer}/${typeQueryVersionApi.api_v2}/customer-document/${params.id}`,
  );
  return response;
};
export const getDocumentCustomerItems = async (params: { documentId?: string }) => {
  return await getRequest(
    `${urlDomainApi.msx_customer}/${typeQueryVersionApi.api_v2}/customer-document/item/${params.documentId}`,
  );
};

export const deleteItemCustomerDocument = async (params: { documentId?: string; itemId?: string }) => {
  const response = await deleteRequest(
    `${urlDomainApi.msx_customer}/${typeQueryVersionApi.api_v2}/customer-document/deleteItem/${params.documentId}/${params.itemId}`,
  );
  return response;
};

export const addItemsCustomerDocument = async (params: IItemDocumentCustomer) => {
  return await putRequest(
    `${urlDomainApi.msx_customer}/${typeQueryVersionApi.api_v2}/customer-document/addItem/${params.id}`,
    params,
  );
};
