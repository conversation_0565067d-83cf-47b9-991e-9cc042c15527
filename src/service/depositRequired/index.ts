import { getRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';

export const getListDepositRequired = async (params: unknown) => {
  const { idProject, ...payload } = params as Record<string, unknown>;
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/primaryTransaction/ticket/${idProject}`,
    payload as Record<string, unknown> | undefined,
  );
  return response;
};

export const getDetailDepositRequired = async (id: string | undefined) => {
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/primaryTransaction/ticketDetail/${id}`,
  );
  return response;
};

export const getListBookingApproved = async (params: unknown) => {
  const { idProject, ...payload } = params as Record<string, unknown>;
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/primaryTransaction/ticket/${idProject}/listBookingApproved`,
    payload as Record<string, unknown> | undefined,
  );
  return response;
};
