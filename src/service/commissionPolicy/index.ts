import { RcFile } from 'antd/lib/upload';
import { axiosInstance, deleteRequest, getRequest, postRequest, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';

// export const getUserProfile = async () => {
//   return await getRequest(`${urlDomainApi.msx_customer}/${typeQueryVersionApi.query}/customer/getProfile`);
// };
export const getDetailCommissionPolicy = async (id: string) => {
  return await getRequest(`${urlDomainApi.msx_commission}/${typeQueryVersionApi.query}/commission-policy/${id}`);
};

export const getListCommissionPolicy = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.query}/commission-policy`,
    params as Record<string, unknown> | undefined,
  );
};

export const getListPos = async () => {
  return await getRequest(`${urlDomainApi.msx_orgChart}/${typeQueryVersionApi.query}/orgchart/exchanges/all`);
};

export const createCommissionPolicy = async (data: unknown) => {
  return await postRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.domain}/commission-policy`,
    data as Record<string, unknown>,
  );
};

export const updateCommissionPolicy = async (data: unknown) => {
  return await putRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.domain}/commission-policy`,
    data as Record<string, unknown>,
  );
};

export const deleteCommissionPolicy = async (payload: { [key: string]: unknown }) => {
  return await deleteRequest(`${urlDomainApi.msx_commission}/${typeQueryVersionApi.domain}/commission-policy`, {
    id: payload.id,
    reasonDelete: payload.softDeleteReason,
  });
};

export const importCommissionPolicy = async (payload: unknown) => {
  return await postRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.api_v1}/commission-policy/import`,
    payload,
  );
};

export const changeStatusCommission = async (data: { id: string; isActive: number }) => {
  return await putRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.domain}/commission-policy/change-status`,
    data as Record<string, unknown>,
  );
};

export const cloneCommissionPolicy = async (data: { id: string }) => {
  return await getRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.query}/commission-policy/clone/${data?.id}`,
  );
};

export const uploadFile = async (files: RcFile[]) => {
  const formData = new FormData();
  files?.map(file => {
    formData.append(`files`, file);
  });

  try {
    const response = await axiosInstance.post(
      `${urlDomainApi.msx_commission}/api/v1/commission-policy/upload-file`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    );
    return response;
  } catch (error) {
    console.error('Lỗi khi tải lên:', error);
    throw error;
  }
};

export const getCommissionPolicyType = async () => {
  return await getRequest(`${urlDomainApi.msx_commission}/${typeQueryVersionApi.api_v1}/commission-policy/list-type`);
};
