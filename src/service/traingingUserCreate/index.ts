import { getRequest, postRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';
import { TCustomer } from '../../types/trainingUserCreate';

export const createTrainingUser = async (payload: TCustomer[]) => {
  return await postRequest(
    `${urlDomainApi.msx_training}/${typeQueryVersionApi.domain}/user/${payload[0]?.idEvent}/register`,
    payload as (Record<string, unknown> | undefined)[],
  );
};

export const getUsers = async () => {
  return await getRequest(`${urlDomainApi.msx_training}/${typeQueryVersionApi.query}/user`);
};

export const getEvents = async () => {
  return await getRequest(`${urlDomainApi.msx_training}/${typeQueryVersionApi.query}/training/canRegister`);
};
