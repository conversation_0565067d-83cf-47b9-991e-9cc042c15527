import { FormInstance } from 'antd/es/form';
import { commonErrors } from '../../constants/errorMessages/commonErrors';
import { Error, ErrorMessage, Response } from '../../types/common/common';
import { notification } from 'antd/lib';

//khi có thêm file mã lỗi cho từng service. import file mã lỗi và add thêm tại đây nhé.
const errorMessages = {
  ...commonErrors,
};

export const getErrorMessage = (errorCode: string | number): ErrorMessage => {
  return (
    errorMessages[errorCode] || {
      message: `Unknown Error ${errorCode} `,
      description: 'Vui lòng kiểm tra mã lỗi trong hệ thống',
    }
  );
};

// xử lý lỗi chung và hiển thị thông báo lỗi.
export const handleErrors = (response: Response, form?: FormInstance): void => {
  const { statusCode, errors } = response;
  const { message, description } = getErrorMessage(statusCode);

  switch (statusCode) {
    case '400':
      notification.error({ message, description });
      if (form) {
        return handleValidationErrors(form, errors);
      }
      break;
    default:
      notification.error({ message, description });
      break;
  }
};

// Xử lý lỗi trả về thông báo message cho từng field trong form khi validation lỗi.
const handleValidationErrors = (form: FormInstance, errors?: Error[]): void => {
  if (errors && errors.length > 0) {
    errors.forEach(error => {
      const { field, constraints } = error;
      const errorMessage = Object.values(constraints).join(', ');
      form.setFields([
        {
          name: field,
          errors: [errorMessage],
        },
      ]);
    });
  }
};
