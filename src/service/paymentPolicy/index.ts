import { getRequest, postRequest, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';
import { IPaymentPolicy } from '../../types/paymentPolicy';

export const getListPaymentPolicy = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/policy/payment/getAllByQuery`,
    params as Record<string, unknown> | undefined,
  );
};
export const createPaymentPolicy = async (params: unknown) => {
  return await postRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/policy/payment`,
    params as Record<string, unknown> | undefined,
  );
};
export const getDetailPaymentPolicy = async (paymentPolicyId: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/policy/${paymentPolicyId}`,
  );
};
export const updatePaymentPolicy = async (payload: IPaymentPolicy) => {
  return await putRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/policy/payment/${payload.id}`,
    payload,
  );
};
