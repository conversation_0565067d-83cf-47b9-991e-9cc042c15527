import { RcFile } from 'antd/es/upload';
import { axiosInstance } from '..';
import { urlDomainApi } from '../../configs/domainApi';

export const uploadMedia = async (file: RcFile, path: string) => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('path', path);

  try {
    const response = await axiosInstance.post(`${urlDomainApi.url_masterData}/api/v1/upload/file`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response;
  } catch (error) {
    console.error('Lỗi khi tải lên:', error);
    throw error;
  }
};
