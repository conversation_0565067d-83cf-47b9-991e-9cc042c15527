import { getRequest, postRequest, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';
import { UserAdminType } from '../../page/userAdminManagement';
import { EmployeeExternal } from '../../types/employeeExternal/employeeExternal';

export const getListEmployeeExternal = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_employee}/${typeQueryVersionApi.api_v2}/external-employee`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};
export const getDetailEmployeeExternal = async (id: string | undefined) => {
  const response = await getRequest(
    `${urlDomainApi.msx_employee}/${typeQueryVersionApi.api_v2}/external-employee/${id}`,
  );
  return response;
};

export const updateEmployeeExternal = async (data?: EmployeeExternal) => {
  const response = await putRequest(
    `${urlDomainApi.msx_employee}/${typeQueryVersionApi.api_v2}/external-employee/${data?.id}`,
    { ...data },
  );
  return response;
};

export const getListUserAdmin = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_employee}/${typeQueryVersionApi.query}/employee/admin/allByQuery`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};

export const sendUpdatePassWordUser = async (payload: UserAdminType) => {
  const response = await putRequest(
    `${urlDomainApi.url_msx_sts}/${typeQueryVersionApi.domain}/user/admin/resetPwd`,
    payload,
  );
  return response;
};
export const sendCreateEmployeeExternal = async (payload: EmployeeExternal) => {
  const response = await postRequest(
    `${urlDomainApi.msx_employee}/${typeQueryVersionApi.api_v2}/external-employee`,
    payload,
  );
  return response;
};
