import { getRequest, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';
import { Units } from '../../types/units/units';
import { ParamsGetRequest } from '../../types/common/common';

export const getListUnits = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_orgChart}/${typeQueryVersionApi.query_v2}/orgchart/allByQuery`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};
export const getListAffiliatedUnits = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_orgChart}/${typeQueryVersionApi.query_v2}/orgchart/filterChildren`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};

export const getDetailUnits = async (id?: string) => {
  const response = await getRequest(`${urlDomainApi.msx_orgChart}/${typeQueryVersionApi.query_v2}/orgchart/${id}`);
  return response;
};

export const getListOrgPartner = async (params?: ParamsGetRequest) => {
  const { id, ...query } = params || {};
  const response = await getRequest(
    `${urlDomainApi.msx_orgChart}/${typeQueryVersionApi.query_v2}/orgchart/listOrgPartner/${id}`,
    { ...query },
  );
  return response;
};

export const getListPersonalStaff = async (params: ParamsGetRequest) => {
  const { id, ...query } = params || {};
  const response = await getRequest(
    `${urlDomainApi.msx_orgChart}/${typeQueryVersionApi.query_v2}/orgchart/personalStaff/${id}`,
    { ...query },
  );
  return response;
};

export const sendUpdateUnits = async (data: Units) => {
  const response = await putRequest(`${urlDomainApi.msx_orgChart}/${typeQueryVersionApi.domain}/orgchart`, {
    ...data,
  });
  return response;
};
