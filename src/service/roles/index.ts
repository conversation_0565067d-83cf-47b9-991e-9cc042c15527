import { deleteRequest, getRequest, postRequest, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';
import { IManagementRole } from '../../page/userRolesManagement';

export const getListUserRoles = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.url_msx_sts}/${typeQueryVersionApi.query}/role`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};

export const getDetailUserRoles = async (id: string) => {
  const response = await getRequest(`${urlDomainApi.url_msx_sts}/${typeQueryVersionApi.query}/role/detail/${id}`);
  return response;
};

export const getListFeature = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.url_msx_sts}/${typeQueryVersionApi.query}/feature`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};

export const sendCreateRole = async (payload: IManagementRole) => {
  const response = await postRequest(`${urlDomainApi.url_msx_sts}/${typeQueryVersionApi.domain}/role`, payload);
  return response;
};

export const sendUpdateRole = async (payload: IManagementRole) => {
  const response = await putRequest(`${urlDomainApi.url_msx_sts}/${typeQueryVersionApi.domain}/role`, payload);
  return response;
};

export const sendGetAssignPersonsRole = async (params: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.url_msx_sts}/${typeQueryVersionApi.query}/role/list-user`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};

export const sendSoftDeleteRole = async (payload: IManagementRole) => {
  const response = await deleteRequest(
    `${urlDomainApi.url_msx_sts}/${typeQueryVersionApi.domain}/role/soft-delete/${payload.id}`,
    payload as Record<string, unknown> | undefined,
  );
  return response;
};

export const getRoleGroupProject = async (payload: { projectID?: string }) => {
  const response = await getRequest(
    `${urlDomainApi.url_msx_sts}/${typeQueryVersionApi.api_v2}/roleGroup/${payload.projectID}`,
  );
  return response;
};

export const addRoleGroupProject = async (payload: { projectID?: string; name?: string }) => {
  const response = await postRequest(
    `${urlDomainApi.url_msx_sts}/${typeQueryVersionApi.api_v2}/roleGroup/${payload.projectID}`,
    payload,
  );
  return response;
};

export const deleteRoleGroupProject = async (payload: { roleGroupID?: string }) => {
  const response = await deleteRequest(
    `${urlDomainApi.url_msx_sts}/${typeQueryVersionApi.api_v2}/roleGroup/${payload.roleGroupID}`,
  );
  return response;
};

export const updateRoleGroupProject = async (payload: { roleGroupID?: string; name?: string }) => {
  const response = await putRequest(
    `${urlDomainApi.url_msx_sts}/${typeQueryVersionApi.api_v2}/roleGroup/${payload.roleGroupID}`,
    payload,
  );
  return response;
};

export const addRoleToRoleGroup = async (payload: { roleGroupID?: string; roleId?: string[] }) => {
  const response = await putRequest(
    `${urlDomainApi.url_msx_sts}/${typeQueryVersionApi.api_v2}/roleGroup/${payload.roleGroupID}/addRole`,
    payload,
  );
  return response;
};

export const removeRoleInRoleGroup = async (payload: { roleGroupID?: string; roleId?: string }) => {
  const response = await putRequest(
    `${urlDomainApi.url_msx_sts}/${typeQueryVersionApi.api_v2}/roleGroup/${payload.roleGroupID}/removeRole/${payload.roleId}`,
  );
  return response;
};

export const addEployeeToRoleGroup = async (payload: {
  roleGroupID?: string;
  internalEmployee?: {
    email: string;
    accountId: string;
    id: string;
  }[];
}) => {
  const response = await putRequest(
    `${urlDomainApi.url_msx_sts}/${typeQueryVersionApi.api_v2}/roleGroup/${payload.roleGroupID}/addEployee`,
    payload,
  );
  return response;
};

export const removeEployeeInRoleGroup = async (payload: { roleGroupID?: string; employeeId?: string }) => {
  const response = await putRequest(
    `${urlDomainApi.url_msx_sts}/${typeQueryVersionApi.api_v2}/roleGroup/${payload.roleGroupID}/removeEployee/${payload.employeeId}`,
  );
  return response;
};
