import { axiosInstance, deleteRequest, getRequest, postRequest, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';
import { TSubmitLeadAssigned } from '../../types/leadAssigned';
import { TManualDeliver } from '../../types/leadToDeliver';

export const getListOfLeadAll = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_lead}/${typeQueryVersionApi.query}/lead-repo`,
    params as Record<string, unknown> | undefined,
  );
};
export const getDetailOfLeadAll = async (params: unknown) => {
  const { id, ...restParams } = params as Record<string, unknown>;
  return await getRequest(
    `${urlDomainApi.msx_lead}/${typeQueryVersionApi.query}/lead-repo/${id}`,
    restParams as Record<string, unknown> | undefined,
  );
};

export const getOrgChartDropdown = async (params: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_lead}/${typeQueryVersionApi.query}/orgchart/dropdown`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};

export const getByIdOrgChartDropdown = async (params: unknown) => {
  const { id, ...restParams } = params as Record<string, unknown>;
  const response = await getRequest(
    `${urlDomainApi.msx_lead}/${typeQueryVersionApi.query}/orgchart/dropdown/${id}`,
    restParams as Record<string, unknown> | undefined,
  );
  return response;
};

export const getOrgChartDropdownById = async (params?: { id?: string }) => {
  const response = await getRequest(
    `${urlDomainApi.msx_lead}/${typeQueryVersionApi.query}/orgchart/dropdownById`,
    params,
  );
  return response;
};

export const putLeadConfig = async (payload: unknown) => {
  return await putRequest(
    `${urlDomainApi.msx_lead}/${typeQueryVersionApi.domain}/lead-repo/config`,
    payload as Record<string, unknown> | undefined,
  );
};

export const createLeadRepo = async (payload: unknown) => {
  return await postRequest(
    `${urlDomainApi.msx_lead}/${typeQueryVersionApi.domain}/lead-repo`,
    payload as Record<string, unknown> | undefined,
  );
};

export const changeStatusLeadConfig = async (payload: unknown) => {
  return await putRequest(
    `${urlDomainApi.msx_lead}/${typeQueryVersionApi.domain}/lead-repo/config/changeStatus`,
    payload as Record<string, unknown> | undefined,
  );
};

export const putLeadConfigHot = async (payload: unknown) => {
  return await putRequest(
    `${urlDomainApi.msx_lead}/${typeQueryVersionApi.domain}/lead-repo`,
    payload as Record<string, unknown> | undefined,
  );
};

export const softDeleteLeadConfig = async (payload: unknown) => {
  return await deleteRequest(
    `${urlDomainApi.msx_lead}/${typeQueryVersionApi.domain}/lead-repo/config`,
    payload as Record<string, unknown> | undefined,
  );
};

export const getListOfLeadCommonAll = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_lead}/${typeQueryVersionApi.query}/lead/common`,
    params as Record<string, unknown> | undefined,
  );
};

export const createLeadCommonAll = async (payload: unknown) => {
  return await postRequest(
    `${urlDomainApi.msx_lead}/${typeQueryVersionApi.domain}/lead/common`,
    payload as Record<string, unknown> | undefined,
  );
};
export const createLeadCommonByImport = async (payload: unknown) => {
  const formData =
    payload instanceof FormData
      ? payload
      : Object.entries((payload as Record<string, unknown>) || {}).reduce((fd, [key, value]) => {
          if (value != null && (value instanceof File || value instanceof Blob)) {
            fd.append(key, value);
          } else if (value !== undefined && value !== null) {
            fd.append(key, String(value));
          }
          return fd;
        }, new FormData());
  return await postRequest(`${urlDomainApi.msx_lead}/${typeQueryVersionApi.domain}/lead/import-from-excel`, formData);
};

export const getListOfLeadAssigned = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_lead}/${typeQueryVersionApi.query}/lead/primary`,
    params as Record<string, unknown> | undefined,
  );
};
export const getDetailOfLeadCare = async (params: unknown) => {
  const { id, ...restParams } = params as Record<string, unknown>;
  return await getRequest(
    `${urlDomainApi.msx_lead}/${typeQueryVersionApi.query}/lead/${id}`,
    restParams as Record<string, unknown> | undefined,
  );
};

export const updateDetailOfLeadCare = async (payload: TSubmitLeadAssigned) => {
  return await putRequest(`${urlDomainApi.msx_lead}/${typeQueryVersionApi.domain}/lead`, payload);
};

export const updateExploitStatusLeadCare = async (payload: { id?: string; status?: string }) => {
  return await putRequest(`${urlDomainApi.msx_lead}/${typeQueryVersionApi.domain}/lead/update-exploit-status`, payload);
};

export const getListOfExploitLeadDashboard = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_lead}/${typeQueryVersionApi.query}/lead/assign`,
    params as Record<string, unknown> | undefined,
  );
};

export const getListHistoryImportLeads = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_lead}/${typeQueryVersionApi.query}/lead/history-import`,
    params as Record<string, unknown> | undefined,
  );
};

export const getListOfLeadToDeliver = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_lead}/${typeQueryVersionApi.query}/lead/toDeliver`,
    params as Record<string, unknown> | undefined,
  );
};

export const getListOfStaff = async () => {
  return await getRequest(`${urlDomainApi.msx_lead}/${typeQueryVersionApi.query}/employee/staffs`);
};

export const deleteAssigner = async (payload: { id?: string }) => {
  return await putRequest(`${urlDomainApi.msx_lead}/${typeQueryVersionApi.domain}/lead/revoke-assign`, payload);
};

export const manualDeliver = async (payload: TManualDeliver[]) => {
  return await putRequest(`${urlDomainApi.msx_lead}/${typeQueryVersionApi.domain}/lead/manual-deliver`, payload);
};

export const getReportLead = async (params: unknown) => {
  const { type, ...restParams } = params as Record<string, unknown>;
  return await getRequest(
    `${urlDomainApi.msx_lead}/${typeQueryVersionApi.query}/lead/report/exploit/${type}`,
    restParams as Record<string, unknown> | undefined,
  );
};

export const getExportReportLead = async (params: unknown) => {
  return await axiosInstance.get(
    `${urlDomainApi.msx_lead}/${typeQueryVersionApi.query}/lead/report/exploit/export-excel/download`,
    {
      params: params as Record<string, unknown> | undefined,
      responseType: 'arraybuffer',
    },
  );
};
