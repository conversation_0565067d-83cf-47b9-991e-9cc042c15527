import { RcFile } from 'antd/lib/upload';
import { getRequest, postRequest, deleteRequest, axiosInstance, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';
import { IDataSubmitCalculate, IDataSubmitUpdate, TFilterCommissionDebt } from '../../types/commissionDebt';

export const getAllCommissionDebt = async (params: TFilterCommissionDebt) => {
  return await getRequest(`${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v1}/debt/commission`, params);
};

export const createCommissionDebt = async (data: unknown) => {
  return await postRequest(`${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v1}/debt/commission`, data);
};

export const deleteRequestCommissionDebt = async (payload: { id: string; softDeleteReason: string }) => {
  const { id, softDeleteReason } = payload;
  return await deleteRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v1}/debt/commission/${id}`,
    { softDeleteReason },
  );
};

export const getByIdCommissionDebt = async (id: string, params?: { search: string }) => {
  return await getRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v1}/debt/commission/${id}`,
    params,
  );
};

export const postCommissionDebtCalculate = async (payload: IDataSubmitCalculate) => {
  return await postRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v1}/debt/commission-list/calculate`,
    payload,
  );
};

export const putChangeStatusCommissionDebt = async (payload: {
  id: string;
  status: string;
  adjustmentVersionId: string;
}) => {
  return await putRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v1}/debt/commission/update-status`,
    payload,
  );
};
export const putUpdateCommissionDebt = async (payLoad: IDataSubmitUpdate) => {
  return await putRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v1}/debt/commission-list`,
    payLoad,
  );
};

export const postUploadAdjustmentVersionDebt = async (payload: { file: RcFile; commissionId: string }) => {
  const { file, commissionId } = payload;
  const formData = new FormData();
  formData.append('files', file);
  return await axiosInstance.post(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v1}/debt/commission-list/import`,
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      params: {
        commissionId,
      },
    },
  );
};
