import { deleteRequest, getRequest, postRequest, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';
import { ParamsGetRequest } from '../../types/common/common';
import { FileSample } from '../../types/formReport';

export const getListFormReport = async (params?: ParamsGetRequest) => {
  const { id, ...query } = params || {};
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/project/${id}/getAllProjectForms`,
    { ...query },
  );
  return response;
};

export const getDetailFormReport = async (params?: ParamsGetRequest) => {
  const { projectId, formId, ...query } = params || {};
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/project/${projectId}/getDetailProjectForms/${formId}`,
    { ...query },
  );
  return response;
};

export const getListVariable = async (params?: ParamsGetRequest) => {
  const { projectId, formId, ...query } = params || {};
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/project/${projectId}/getListVariablesOfForm/${formId}`,
    { ...query },
  );
  return response;
};

export const createFileSample = async (payload?: FileSample) => {
  const response = await postRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/project/${payload?.projectId}/createFile/${payload?.formId}`,
    payload,
  );
  return response;
};

export const updateFileSample = async (payload?: FileSample) => {
  const response = await putRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/project/${payload?.projectId}/updateFile/${payload?.formId}`,
    payload,
  );
  return response;
};

export const deleteFileSample = async (payload?: FileSample) => {
  const response = await deleteRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/project/${payload?.projectId}/${payload?.formId}/deleteFile/${payload?.id}`,
    payload,
  );
  return response;
};
