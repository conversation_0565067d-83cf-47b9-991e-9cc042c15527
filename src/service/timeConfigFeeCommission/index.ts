import { deleteRequest, getRequest, postRequest, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';
import { TParamsTimeConfigFeeCommission, TSubmitTimeConfigFeeCommission } from '../../types/timeConfigFeeCommission';

export const getAllTimeConfigFeeCommission = async (
  params?: TParamsTimeConfigFeeCommission | { getUsable?: boolean },
) => {
  return await getRequest(`${urlDomainApi.msx_commission}/${typeQueryVersionApi.api_v1}/period`, params);
};

export const createTimeConfigFeeCommission = async (payload: TSubmitTimeConfigFeeCommission) => {
  return await postRequest(`${urlDomainApi.msx_commission}/${typeQueryVersionApi.api_v1}/period`, payload);
};

export const updateTimeConfigFeeCommission = async (payload: TSubmitTimeConfigFeeCommission) => {
  return await putRequest(`${urlDomainApi.msx_commission}/${typeQueryVersionApi.api_v1}/period`, payload);
};

export const changeStatusTimeConfigFeeCommission = async (payload: { id: string; isActive: number; name: string }) => {
  return await putRequest(`${urlDomainApi.msx_commission}/${typeQueryVersionApi.api_v1}/period`, payload);
};

export const getByIdTimeConfigFeeCommission = async (id?: string) => {
  return await getRequest(`${urlDomainApi.msx_commission}/${typeQueryVersionApi.api_v1}/period/${id}`);
};

export const softDeleteTimeConfigFeeCommission = async (payload: { id?: string; reasonDelete?: string }) => {
  return await deleteRequest(`${urlDomainApi.msx_commission}/${typeQueryVersionApi.api_v1}/period`, payload);
};

export const getTimeConfigFeeCommission = async (params: { year: string; orgcharts?: string }) => {
  return await getRequest(`${urlDomainApi.msx_commission}/${typeQueryVersionApi.api_v1}/period/gen-period`, params);
};
