import { deleteRequest, getRequest, postRequest, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';

export const getListDepositContract = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/deposit-contract`,
    params as Record<string, unknown> | undefined,
  );
};

export const deleteDepositContract = async (depositContractId: unknown) => {
  return await deleteRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/deposit-contract/${depositContractId}`,
  );
};

export const createGeneralInfo = async (payload: unknown) => {
  return await postRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/deposit-contract`,
    payload,
  );
};

export const updateProductDeposit = async (payload: unknown) => {
  return await putRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/propertyUnit/updateDepositContract`,
    payload,
  );
};

export const createProductDeposit = async (payload: unknown) => {
  return await postRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/deposit-contract/add-property-units`,
    payload,
  );
};

export const deleteProductDeposit = async (payload: unknown) => {
  return await deleteRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/deposit-contract/44aa9083-1ee6-498e-b493-dbd8d31d2f8f/delete-property-unit/e7220bde-0bdd-482e-9bab-7bc93b3fdd90`,
    payload,
  );
};

export const createPaymentDeposit = async (payload: unknown) => {
  return await postRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/deposit-contract/add-installments`,
    payload,
  );
};

export const deletePaymentDeposit = async (payload: unknown) => {
  return await deleteRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/deposit-contract/44aa9083-1ee6-498e-b493-dbd8d31d2f8f/delete-installment/fad55f5e-fcbf-4869-a6ad-a85127696f00`,
    payload,
  );
};

export const updatePaymentTracking = async (payload: unknown) => {
  return await putRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/deposit-contract/installment/transactionSuccessful`,
    payload,
  );
};
