import { getRequest, postRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';

export const createPaymentTransaction = async (params: unknown) => {
  return await postRequest(
    `${urlDomainApi.msx_transaction}/${typeQueryVersionApi.api_v2}/transaction`,
    params as Record<string, unknown> | undefined,
  );
};
export const getListTransaction = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_transaction}/${typeQueryVersionApi.api_v2}/transaction`,
    params as Record<string, unknown> | undefined,
  );
};
export const getListPaymentTransaction = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/propertyUnit/primary/tranx?sort=-createdDate&stage=2`,
    params as Record<string, unknown> | undefined,
  );
};
export const sendRegistration = async (payload: unknown) => {
  const response = await postRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/propertyUnit/registerUnit`,
    {
      ...(payload as Record<string, unknown> | undefined),
    },
  );
  return response;
};
export const sendRevokeRegistration = async (payload: unknown) => {
  const response = await postRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/primaryTransaction/revoke`,
    {
      ...(payload as Record<string, unknown> | undefined),
    },
  );
  return response;
};
export const sendLiquidateRegistration = async (payload: unknown) => {
  const response = await postRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/primaryTransaction/liquidate`,
    {
      ...(payload as Record<string, unknown> | undefined),
    },
  );
  return response;
};
