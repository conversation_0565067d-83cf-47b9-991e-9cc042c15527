import { getRequest, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';
import { ParamsGetRequest } from '../../types/common/common';

export const getListOffer = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_transaction}/${typeQueryVersionApi.api_v2}/transaction`,
    params as Record<string, unknown> | undefined,
  );
};

export const getDetailOffer = async (params?: ParamsGetRequest) => {
  const { id, ...query } = params || {};
  const response = await getRequest(`${urlDomainApi.msx_transaction}/${typeQueryVersionApi.api_v2}/transaction/${id}`, {
    ...query,
  });
  return response;
};

export const getListAccount = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_employee}/${typeQueryVersionApi.api_v2}/internal-employee`,
    params as Record<string, unknown> | undefined,
  );
};

export const getListOrgchartInternal = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_orgChart}/${typeQueryVersionApi.query_v2}/orgchart/allByQuery`,
    params as Record<string, unknown> | undefined,
  );
};

export const getListOrgchartExternal = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_orgChart}/${typeQueryVersionApi.query}/external/orgchart/getAll`,
    params as Record<string, unknown> | undefined,
  );
};

export const approveOffer = async (payload: unknown) => {
  return await putRequest(
    `${urlDomainApi.msx_transaction}/${typeQueryVersionApi.api_v2}/transaction/accountingList`,
    payload as Record<string, unknown> | undefined,
  );
};

export const rejectOffer = async (payload: { [key: string]: unknown }) => {
  return await putRequest(
    `${urlDomainApi.msx_transaction}/${typeQueryVersionApi.api_v2}/transaction/reject`,
    payload as Record<string, unknown> | undefined,
  );
};
