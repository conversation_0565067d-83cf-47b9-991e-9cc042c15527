import { getRequest, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';

export const sendGetInvestors = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.query}/salesProgram/getList`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};
export const sendGetDropdownList = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.query}/project/admin/dropdownList`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};

export const getPropertyUnits = async (params: unknown) => {
  const { idSaleProgram, ...payload } = params as Record<string, unknown>;
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/propertyUnit/getPropertyUnits/${idSaleProgram}`,
    payload as Record<string, unknown> | undefined,
  );
  return response;
};

export const sendTransferProducts = async (payload: { idSaleProgram?: string }) => {
  const response = await putRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/propertyUnit/propertyUnitTransfer/${payload.idSaleProgram}`,
    payload,
  );
  return response;
};
export const sendTransferProductsOpenForSale = async (payload: { idSaleProgram?: string }) => {
  const response = await putRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/propertyUnit/propertyUnitTransferAndComing/${payload.idSaleProgram}`,
    payload,
  );
  return response;
};
