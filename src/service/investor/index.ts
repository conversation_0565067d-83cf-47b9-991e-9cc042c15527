import { deleteRequest, getRequest, postRequest, putRequest } from '..';
import { AxiosHeaders, AxiosRequestConfig } from 'axios';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';
import { DetailInvestor, Investor } from '../../types/investor/investor';

export const getListInvestor = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/investor`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};

export const getDetailInvestor = async (id: string) => {
  const response = await getRequest(`${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/investor/${id}`);
  return response;
};

export const createInvestor = async (data: DetailInvestor) => {
  const response = await postRequest(`${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/investor`, {
    ...data,
  });
  return response;
};

export const updateInvestor = async (data: DetailInvestor) => {
  const response = await putRequest(`${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/investor/${data.id}`, {
    ...data,
  });
  return response;
};

export const softDeleteInvestor = async (payload: Investor) => {
  const response = await deleteRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/investor/soft-delete/${payload.id}`,
    payload as Record<string, unknown> | undefined,
  );
  return response;
};

export const importTemplateInvestor = async (data: FormData) => {
  const config: AxiosRequestConfig = {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  };
  const response = await postRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.domain}/investor/import`,
    data as unknown as Record<string, unknown>,
    config as AxiosHeaders,
  );
  return response;
};
