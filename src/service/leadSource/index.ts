import { deleteRequest, getRequest, postRequest, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';
import { ILead } from '../../types/leadSources';

// export const getUserProfile = async () => {
//   return await getRequest(`${urlDomainApi.msx_customer}/${typeQueryVersionApi.query}/customer/getProfile`);
// };

export const getListLeadSource = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_lead}/${typeQueryVersionApi.query}/source/list`,
    params as Record<string, unknown> | undefined,
  );
};

export const getDetailOfLeadSource = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_lead}/${typeQueryVersionApi.query}/source/get`,
    params as Record<string, unknown> | undefined,
  );
};

export const postCreateLeadSource = async (data: unknown) => {
  return await postRequest(
    `${urlDomainApi.msx_lead}/${typeQueryVersionApi.query}/source/create`,
    data as Record<string, unknown>,
  );
};

export const updateLeadSource = async (payload: ILead) => {
  return await putRequest(`${urlDomainApi.msx_lead}/${typeQueryVersionApi.query}/source/update`, payload);
};

export const deleteLeadSource = async (payload: { [key: string]: unknown }) => {
  return await deleteRequest(`${urlDomainApi.msx_lead}/${typeQueryVersionApi.query}/source/delete?${payload.id}`, {
    id: payload.id,
    softDeleteReason: payload.softDeleteReason,
  });
};
