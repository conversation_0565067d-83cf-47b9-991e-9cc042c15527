import { getRequest, postRequest, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';
import {
  CreateBookingTicket,
  CreateDemandBusiness,
  CreateDemandIndividual,
  UpdateCustomer,
  UpdateDemand,
} from '../../types/bookingRequest';
import { ParamsGetRequest } from '../../types/common/common';

export const getListBookingTicket = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/primaryTransaction`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};

export const getDetailBookingTicket = async (params?: ParamsGetRequest) => {
  const { id, ...query } = params || {};
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/primaryTransaction/${params?.id}`,
    { ...query },
  );
  return response;
};
export const updateBookingTicket = async (payload: CreateBookingTicket) => {
  return await putRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/primaryTransaction`,
    payload as unknown as Record<string, unknown> | undefined,
  );
};

export const getCustomerByIdentities = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_customer}/${typeQueryVersionApi.api_v2}/customer/getCustomerByIdentities`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};

export const getSalePrograms = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/salesProgram`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};

export const getProductUnits = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/propertyUnit/primary/assign`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};

export const getEmployees = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_employee}/${typeQueryVersionApi.api_v2}/internal-employee`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};

export const createBookingTicket = async (payload: CreateBookingTicket) => {
  return await postRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/primaryTransaction`,
    payload as unknown as Record<string, unknown> | undefined,
  );
};

export const createDemandIndividual = async (payload: CreateDemandIndividual) => {
  return await postRequest(
    `${urlDomainApi.msx_demand}/${typeQueryVersionApi.domain}/customer/create-demand`,
    payload as Record<string, unknown> | undefined,
  );
};
export const createDemandBusiness = async (payload: CreateDemandBusiness) => {
  return await postRequest(
    `${urlDomainApi.msx_demand}/${typeQueryVersionApi.domain}/customer/create-demand`,
    payload as Record<string, unknown> | undefined,
  );
};

export const updateDemand = async (payload: UpdateDemand) => {
  return await putRequest(
    `${urlDomainApi.msx_demand}/${typeQueryVersionApi.api_v2}/customer`,
    payload as Record<string, unknown> | undefined,
  );
};

export const updateCustomer = async (payload: UpdateCustomer) => {
  return await putRequest(
    `${urlDomainApi.msx_customer}/${typeQueryVersionApi.api_v2}/customer/updateAllCustomer`,
    payload as Record<string, unknown> | undefined,
  );
};

export const cloneTicket = async (payload: unknown) => {
  return await postRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/primaryTransaction/cloneTicket`,
    payload as Record<string, unknown> | undefined,
  );
};

export const getListStatusBookingTiket = async () => {
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/primaryTransaction/getAllStatus`,
  );
  return response;
};

export const getListOrgchart = async (params?: ParamsGetRequest) => {
  const { id, ...query } = params || {};
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/project/${params?.id}/orgchart`,
    { ...query },
  );
  return response;
};

export const createUrlPayment = async (payload: unknown) => {
  const response = await postRequest(
    `${urlDomainApi.msx_transaction}/${typeQueryVersionApi.api_v2}/payment`,
    payload as Record<string, unknown> | undefined,
  );
  return response;
};

export const adminApproveTicket = async (payload: unknown) => {
  const response = await postRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/primaryTransaction/adminApproveTicket`,
    payload as Record<string, unknown> | undefined,
  );
  return response;
};

export const csApproveTicket = async (payload: unknown) => {
  const response = await postRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/primaryTransaction/csApproveTicket`,
    payload as Record<string, unknown> | undefined,
  );
  return response;
};

export const adminRejectTicket = async (payload: unknown) => {
  const response = await postRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/primaryTransaction/adminApproveTicket`,
    payload as Record<string, unknown> | undefined,
  );
  return response;
};

export const csRejectTicket = async (payload: unknown) => {
  const response = await postRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/primaryTransaction/csApproveTicket`,
    payload as Record<string, unknown> | undefined,
  );
  return response;
};

// NVKD tạo đề nghị hủy YCDCH
export const cancelRequestTicket = async (payload: unknown) => {
  const response = await postRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/primaryTransaction/cancelRequest`,
    payload as Record<string, unknown> | undefined,
  );
  return response;
};

// GĐBH duyệt hủy YCDCH
export const adminApproveCancelRequestTicket = async (payload: unknown) => {
  const response = await postRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/primaryTransaction/adminApproveCancelRequest`,
    payload as Record<string, unknown> | undefined,
  );
  return response;
};

// DVKH duyệt hủy YCDCH
export const csApproveCancelRequestTicket = async (payload: unknown) => {
  const response = await postRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/primaryTransaction/csApproveCancelRequest`,
    payload as Record<string, unknown> | undefined,
  );
  return response;
};

// GĐBH từ chối
export const adminCancelRequestTicket = async (payload: unknown) => {
  const response = await postRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/primaryTransaction/adminCancelRequest`,
    payload as Record<string, unknown> | undefined,
  );
  return response;
};

// DVKH từ chối
export const csCancelRequestTicket = async (payload: unknown) => {
  const response = await postRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/primaryTransaction/csCancelRequest`,
    payload as Record<string, unknown> | undefined,
  );
  return response;
};
