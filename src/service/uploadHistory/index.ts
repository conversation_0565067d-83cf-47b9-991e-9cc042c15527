import { getRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';

export const getListImportHistory = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.query_v2}/importHistory/getImportHistory`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};
export const getListProjectHistory = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/project`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};

export const getListUploaderHistory = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.query_v2}/importHistory/getDropDownEmployeeByCreatedBy`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};

export const getListStatusImportProjectHistory = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.query_v2}/importHistory/getListStatus`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};
