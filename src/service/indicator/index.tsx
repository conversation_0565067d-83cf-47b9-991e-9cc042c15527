import { RcFile } from 'antd/lib/upload';
import { axiosInstance, deleteRequest, getRequest, postRequest, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';
import { IIndicator, TCreateIndicator } from '../../types/indicator';

export const getListIndicators = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.query}/indicator`,
    params as Record<string, unknown> | undefined,
  );
};

export const getOrgcharts = async (params: unknown) => {
  const { ...restParams } = params as Record<string, unknown>;
  const response = await getRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.query}/sales-policy/get-dropdown-orgcharts`,
    restParams as Record<string, unknown> | undefined,
  );
  return response;
};

export const createIndicator = async (data: TCreateIndicator) => {
  return await postRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.domain}/indicator`,
    data as Record<string, unknown>,
  );
};

export const getDetailIndicator = async (id: string) => {
  return await getRequest(`${urlDomainApi.msx_commission}/${typeQueryVersionApi.query}/indicator/${id}`);
};

export const importIndicatorList = async (file: RcFile, id: string) => {
  const formData = new FormData();
  formData.append(`files`, file);
  formData.append('id', id);

  try {
    const response = await axiosInstance.put(
      `${urlDomainApi.msx_commission}/api/domain/v1/indicator/import-indicator-list`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    );
    return response;
  } catch (error) {
    console.error('Lỗi khi tải lên:', error);
    throw error;
  }
};

export const downloadTemplate = async (params: unknown) => {
  return await axiosInstance.get(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.query}/indicator/download-template`,
    {
      params: params as Record<string, unknown> | undefined,
      responseType: 'arraybuffer',
    },
  );
};

export const cloneIndicator = async (data: { id: string }) => {
  return await postRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.domain}/indicator/copy`,
    data as Record<string, unknown>,
  );
};

export const softDeleteIndicator = async (payload: { [key: string]: unknown }) => {
  return await deleteRequest(`${urlDomainApi.msx_commission}/${typeQueryVersionApi.domain}/indicator`, {
    ...payload,
    reasonDelete: payload?.softDeleteReason,
  } as Record<string, unknown> | undefined);
};

export const changeStatusIndicator = async (data: { id: string; isActive: number }) => {
  return await putRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.domain}/indicator/change-status`,
    data as Record<string, unknown>,
  );
};

export const putUpdateIndicator = async (data: IIndicator) => {
  return await putRequest(`${urlDomainApi.msx_commission}/${typeQueryVersionApi.domain}/indicator`, data);
};

export const deleteIndicatorList = async (data: { id: string }) => {
  return await deleteRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.domain}/indicator-list`,
    data as Record<string, unknown>,
  );
};
