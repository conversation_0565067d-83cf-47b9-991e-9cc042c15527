import { getRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';

export const getProvinces = async () => {
  return await getRequest(`${urlDomainApi.url_masterData}/${typeQueryVersionApi.api_v1}/province`);
};

export const getIssuePlaces = async () => {
  return await getRequest(`${urlDomainApi.url_masterData}/${typeQueryVersionApi.api_v1}/province/issuePlace`);
};

export const getDistricts = async (provinceCode: string) => {
  return await getRequest(
    `${urlDomainApi.url_masterData}/${typeQueryVersionApi.api_v1}/district/districtByProvince/${provinceCode}`,
  );
};

export const getWards = async (id: string) => {
  return await getRequest(`${urlDomainApi.url_masterData}/${typeQueryVersionApi.api_v1}/ward/wardByDistrict/${id}`);
};
