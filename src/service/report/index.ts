import { axiosInstance, getRequest, postRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';
import { ParamsGetRequest } from '../../types/common/common';

export const getPdf = async (payload: unknown) => {
  const response = await postRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/policy/discountPolicy/previewForm`,
    payload as Record<string, unknown> | undefined,
  );

  return response;
};
export const getListTemplate = async (params: ParamsGetRequest) => {
  const { projectId, formId, ...query } = params || {};
  return await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/project/${projectId}/getDetailProjectForms/${formId}`,
    { ...query },
  );
};

export const getDiscountPolicyProposalNumber = async (formId?: string) => {
  const response = await getRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/policy/getPolicyProposalNumber/${formId}`,
  );
  return response;
};

export const createDiscountPolicyProposalNumber = async (payload: unknown) => {
  const response = await postRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/policy/createPolicyProposal`,
    payload as Record<string, unknown> | undefined,
  );
  return response;
};

export const previewPDF = async (): Promise<Blob> => {
  const fileUrl = `/assets/test.docx`;
  const fileData = await fetch(fileUrl);
  const blob = await fileData.blob();
  const file = new File([blob], 'example.docx');
  const formData = new FormData();
  formData.append('file', file);

  const response = await axiosInstance.post(
    'https://uat-api-crm.datxanh.com.vn/msx-primary-contract/api/v2/policy/discountPolicy/previewForm',
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      responseType: 'blob',
    },
  );
  console.log('response', response.data);
  return response.data;
};
