import { ADMINISTRATION_VN_Str } from '../data/templatesLocation';

export interface Province {
  n: string; // Name
  d: District[]; // Districts
  codeErp: string; //code
}
export interface District {
  n: string; // Name
  w: string[]; // Wards
  codeErp: string; //code
}

let ADMINISTRATION_VN_JSON = JSON.parse(ADMINISTRATION_VN_Str).sort((a: { n: string }, b: { n: string }) => {
  return a.n.localeCompare(b.n);
});

ADMINISTRATION_VN_JSON = ADMINISTRATION_VN_JSON.map((city: Province) => {
  city.d.sort((a, b) => {
    return a.n.localeCompare(b.n);
  });
  city.d = city.d.map((district: District) => {
    district.w.sort((c, d) => {
      return c.localeCompare(d);
    });
    return district;
  });
  return city;
});

export const ADMINISTRATION_VN = JSON.stringify(ADMINISTRATION_VN_JSON);
