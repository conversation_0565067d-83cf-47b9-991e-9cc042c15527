import { Dayjs } from 'dayjs';
import BigNumber from 'bignumber.js';
import { FORMAT_DATE_API } from '../constants/common';

// Hàm kiểm tra object có ít nhất một giá trị không rỗng
export const hasNonEmptyValues = (obj?: Record<string, unknown>): boolean => {
  return (
    !!obj &&
    Object.values(obj).some(value => {
      if (typeof value === 'string') return value.trim() !== '';
      if (Array.isArray(value)) return value.length > 0;
      if (value && typeof value === 'object') return Object.keys(value).length > 0;
      return !!value;
    })
  );
};

// hàm kiểm tra URL hợp lệ
export function validateProfileUrl(url: string) {
  if (!url) {
    return { isValid: true };
  }

  // Regex để kiểm tra URL hợp lệ
  const urlPattern = /^(https?:\/\/)?([\w\d-]+\.)+[\w\d]{2,}(\/[/\w\d-]*)*\/?$/;

  if (url.includes(' ')) {
    return {
      isValid: false,
      message: 'URL không được chứa khoảng trắng',
    };
  }

  if (!urlPattern.test(url)) {
    return {
      isValid: false,
      message: 'URL không hợp lệ. Vui lòng nhập URL đúng định dạng (ví dụ: https://example.com)',
    };
  }

  return { isValid: true };
}

// hàm kiểm tra email hợp lệ
export function validateEmail(email: string) {
  if (!email) return Promise.resolve();
  const emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return emailPattern.test(email) ? Promise.resolve() : Promise.reject('Địa chỉ email sai định dạng');
}

// Hàm định dạng số thành tiền tệ VND
export const formatCurrency = (value: string): string => {
  if (!value) return '';
  const numberValue = parseInt(value.replace(/\D/g, ''), 10);
  if (isNaN(numberValue)) return '';

  return numberValue.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

export const formatRangePikerToSubmit = (date: Dayjs[]) => {
  if (!date) return null;
  return { startDate: date[0].format(FORMAT_DATE_API), endDate: date[1].format(FORMAT_DATE_API) };
};

// Hàm tải file từ ArrayBuffer
export function downloadArrayBufferFile(props: { data: ArrayBuffer; fileName: string; type?: string }) {
  const { data, fileName, type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' } = props;
  try {
    // Tạo Blob từ ArrayBuffer
    const blob = new Blob([data], { type });

    // Tạo URL và tải file
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = fileName;
    document.body.appendChild(link);
    link.click();

    // Dọn dẹp
    URL.revokeObjectURL(link.href);
    document.body.removeChild(link);
  } catch (error) {
    console.error('Error downloading file:', error);
  }
}

export const hasNonNullValue = (addressObj: any): boolean => {
  // Nếu addressObj không tồn tại hoặc là null, trả về false
  if (!addressObj) return false;

  // Các trường bắt buộc phải kiểm tra
  const requiredFields = ['country', 'province', 'district', 'ward', 'address'];

  // Kiểm tra xem tất cả các trường bắt buộc có giá trị hợp lệ hay không
  return requiredFields.every(field => {
    if (field === 'address') {
      // Trường address là chuỗi, kiểm tra xem nó có tồn tại và không rỗng
      return addressObj[field] && addressObj[field].trim() !== '';
    } else {
      // Các trường country, province, district, ward là object, kiểm tra xem chúng có giá trị hợp lệ
      return (
        addressObj[field] &&
        typeof addressObj[field] === 'object' &&
        Object.values(addressObj[field]).some(value => value != null && value.toString().trim() !== '')
      );
    }
  });
};

export const formatBigNumber = (value: number | string | undefined): string => {
  if (value === null || value === undefined) return '';

  try {
    const bigNumberValue = new BigNumber(value);
    if (bigNumberValue.isNaN()) return '';

    return bigNumberValue.toFormat({
      decimalSeparator: '.',
      groupSeparator: ',',
      groupSize: 3,
      fractionGroupSeparator: '',
      fractionGroupSize: 0,
    });
  } catch (error) {
    console.error('Error formatting number:', error);
    return '';
  }
};

export const validateSpaceInput = (value: string) => {
  if (value.includes(' ')) {
    return {
      isValid: false,
      message: 'URL không được chứa khoảng trắng',
    };
  }
};

// Hàm chuyển đổi (vd:Tên sự kiện → ten-su-kien)
export const slugify = (value: string) => {
  return value
    .normalize('NFD') // bỏ dấu
    .replace(/[\u0300-\u036f]/g, '')
    .toLowerCase()
    .trim()
    .replace(/\s+/g, '-') // thay khoảng trắng bằng -
    .replace(/[^\w-]/g, '');
};
