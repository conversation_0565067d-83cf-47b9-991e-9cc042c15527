import { useMemo } from 'react';
import { useFetch } from '../query';

interface Props {
  params: object;
  apiQuery: (params: object) => void;
  queryKey: string[];
  labelField: string;
  valueField: string;
}
/**
 * Props interface for useEntitiesById hook
 * @template T - The type of entity being fetched
 * @param params - A comma-separated object used to fetch entities
 * @param apiQuery - Function that makes API request for entities by ID
 * @param queryKey - Array of strings used as React Query cache key
 * @param labelField - Field name from entity to use as label in returned data
 * @param valueField - Field name from entity to use as value in returned data
 */

export const useEntitiesById = ({ params, apiQuery, queryKey, labelField, valueField }: Props) => {
  const arrayIds = useMemo(() => {
    if (!params || typeof params !== 'object') return [];
    return Object.values(params).filter(Boolean);
  }, [params]);

  const { data, isLoading, isError } = useFetch({
    queryKeyArr: [queryKey, arrayIds],
    api: () => apiQuery(params),
    withFilter: false,
    enabled: !!arrayIds.length,
    cacheTime: Infinity,
  });
  const entitiesData = useMemo(() => data?.data?.data || [], [data]);

  const formatEntitiesData = useMemo(
    () =>
      Array.isArray(entitiesData) && !!Object.values(params).filter(Boolean).length
        ? entitiesData
            .map(item => {
              if (!item || typeof item !== 'object') return null;
              return {
                ...(item as Record<string, unknown>),
                label: (item as Record<string, unknown>)[labelField] as string,
                value: (item as Record<string, unknown>)[valueField] as string,
              };
            })
            .filter(Boolean)
        : [],
    [entitiesData, labelField, valueField, params],
  );

  return {
    isLoading: isLoading,
    isError: isError,
    entities: formatEntitiesData as Array<{ label: string; value: string }>,
    rawData: entitiesData,
  };
};
