import { keepPreviousD<PERSON>, Query<PERSON><PERSON>, useQuery, useQueryClient, UseQueryResult } from '@tanstack/react-query';
import useFilter from '../filter';
export interface TDataList<TData> {
  page: number;
  pageSize: number;
  rows: TData;
  total: number;
  totalPages: number;
  data: TData;
}

export interface FetchResponse<TData> {
  data: TData & {
    statusCode: string;
    message?: string;
    errors?: unknown;
    success?: boolean;
    timestamp?: string;
    data?: TData & {
      message?: string;
      statusCode?: string;
      page?: number;
      pageSize?: number;
      rows?: TData;
      total?: number;
      totalPages?: number;
    };
  };
}

interface IUseFetch<TData> {
  api: (params: object) => void;
  withFilter?: boolean;
  queryFn?: () => Promise<TData>;
  queryKeyArr?: QueryKey;
  moreParams?: object;
  defaultFilter?: object;
  cacheTime?: number;
  queryKeyArrWithFilter?: QueryKey;
  isPrevData?: boolean; // muốn nhớ dữ liệu cũ hay không ( default :true)
  enabled?: boolean;
  refetchInterval?: number;
}

export const useSubscribeQuery = <T>(key: QueryKey) => {
  const queryClient = useQueryClient();
  const data = queryClient.getQueryData(key);
  return data as FetchResponse<TDataList<T>>;
};

export const useSubscribeList = <T>(listKey?: QueryKey, defaultFilter?: object) => {
  const [filter] = useFilter({ defaultFilter });
  const data = useSubscribeQuery([filter, ...(listKey || 'unknown')]);
  return data as FetchResponse<TDataList<T>>;
};

export const useFetch = <TData>({
  api,
  withFilter = true,
  queryFn,
  queryKeyArrWithFilter, //dùng cho các Api get list và có phân trang
  queryKeyArr,
  moreParams,
  defaultFilter,
  cacheTime, // thời gian cache( mặc định 500000) nhưng khi call api detail cần truyền là 1 để dữ liệu khi update luôn là  mới nhất
  isPrevData = true, // muốn nhớ dữ liệu cũ hay không ( default :true)
  enabled = true,
  refetchInterval,
}: IUseFetch<TData>) => {
  const [filter] = useFilter({ defaultFilter });
  const getQueryKey = () => {
    if (queryKeyArrWithFilter) return [filter, ...queryKeyArrWithFilter];
    if (queryKeyArr) return queryKeyArr;
    if (filter) {
      return [filter];
    }
    return [];
  };

  const getQueryFn = () => {
    if (queryFn) return queryFn;
    if (withFilter)
      return () => {
        return api({
          ...filter,
          ...moreParams,
        });
      };
    if (!withFilter)
      return () => {
        return api({
          ...moreParams,
        });
      };
  };

  const query: UseQueryResult<FetchResponse<TData>> = useQuery({
    queryKey: [...getQueryKey()],
    queryFn: getQueryFn(),
    staleTime: cacheTime || 500000,
    placeholderData: isPrevData ? keepPreviousData : undefined,
    enabled,
    refetchInterval,
  });

  return query;
};
