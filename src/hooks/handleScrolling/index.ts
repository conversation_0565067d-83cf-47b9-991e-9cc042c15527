import { FetchNextPageOptions, InfiniteData, InfiniteQueryObserverResult } from '@tanstack/react-query';
import debounce from 'lodash/debounce';
import { useMemo } from 'react';
import { FetchResponse, TDataList } from '../query';
type TScrollHandlerLazyLoading = {
  hasNextPage: boolean; //kiểm tra có trang tiếp theo không
  isFetchingNextPage: boolean; //kiểm tra có đang fetch trang tiếp theo không
  fetchNextPage: (
    options?: FetchNextPageOptions,
  ) => Promise<InfiniteQueryObserverResult<InfiniteData<FetchResponse<TDataList<unknown[]>>, unknown>, Error>>; //hàm fetch trang tiếp theo
};
export const useScrollHandlerLazyLoading = (props: TScrollHandlerLazyLoading) => {
  const { hasNextPage, isFetchingNextPage, fetchNextPage } = props;
  const scrollHandlerLazy = useMemo(
    () =>
      debounce(async e => {
        const { scrollTop, scrollHeight, clientHeight } = e.target;

        if (scrollTop + clientHeight >= scrollHeight - 5 && hasNextPage && !isFetchingNextPage) {
          await fetchNextPage();
        }
      }, 300),
    [fetchNextPage, hasNextPage, isFetchingNextPage],
  );
  return scrollHandlerLazy;
};
