import { DEFAULT_PARAMS } from './../../constants/common';
import { createSearchParams, URLSearchParamsInit, useSearchParams } from 'react-router-dom';

export default function useFilter({ defaultFilter = {} } = {}) {
  const [urlParams, setUrlParams] = useSearchParams({
    ...DEFAULT_PARAMS,
    ...defaultFilter,
  });
  const filter = Object.fromEntries([...urlParams]);
  const setFilter = (newFilter: URLSearchParamsInit) => {
    const filteredFilter = Object.fromEntries(
      Object.entries(newFilter).filter(([, value]) => value !== null && value !== ''),
    );
    const params = createSearchParams(filteredFilter);
    setUrlParams(params);
  };

  return [filter, setFilter] as const;
}
