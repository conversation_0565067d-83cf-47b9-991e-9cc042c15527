import React, { useEffect, useState } from 'react';
import dayjs from 'dayjs';

interface CountUpTimerProps {
  startTime: string;
  endTime: string;
}

const CountUpTimer: React.FC<CountUpTimerProps> = ({ startTime, endTime }) => {
  const [currentTime, setCurrentTime] = useState(dayjs());
  const [isCompleted, setIsCompleted] = useState(false);

  const isInfiniteCounter = dayjs(startTime).isSame(endTime);
  const start = dayjs(startTime);
  const end = dayjs(endTime);

  useEffect(() => {
    if (!isInfiniteCounter && dayjs().isAfter(end)) {
      setIsCompleted(true);
      return;
    }

    const timer = setInterval(() => {
      const now = dayjs();
      setCurrentTime(now);

      if (!isInfiniteCounter && now.isAfter(end)) {
        setIsCompleted(true);
        clearInterval(timer);
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [startTime, endTime, isInfiniteCounter, end]);

  if (isCompleted && !isInfiniteCounter) return null;

  const elapsedSeconds = currentTime.isAfter(start) ? currentTime.diff(start, 'second') : 0;

  if (isInfiniteCounter) {
    const totalSeconds = elapsedSeconds % (24 * 3600);
    const hours = Math.floor(totalSeconds / 3600)
      .toString()
      .padStart(2, '0');
    const minutes = Math.floor((totalSeconds % 3600) / 60)
      .toString()
      .padStart(2, '0');

    return <span style={{ color: '#0958D9' }}>{`${hours}:${minutes}`}</span>;
  }

  const hours = Math.floor(elapsedSeconds / 3600)
    .toString()
    .padStart(2, '0');
  const minutes = Math.floor((elapsedSeconds % 3600) / 60)
    .toString()
    .padStart(2, '0');

  return <span style={{ color: '#0958D9' }}>{`${hours}:${minutes}`}</span>;
};

export default CountUpTimer;
