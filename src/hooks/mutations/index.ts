import { MutationFunction, QueryKey, useMutation, useQueryClient } from '@tanstack/react-query';
import { notification } from 'antd/lib';
import { useNavigate } from 'react-router-dom';
import { handleErrors } from '../../service/error/errorsService';
import { Response } from '../../types/common/common';
import useFilter from '../filter';
interface IApiQuery<T> {
  keyOfListQuery?: QueryKey; // key của api list
  keyOfDetailQuery?: QueryKey; // key của api detail
  apiQuery: MutationFunction | MutationFunction<unknown, T>;
  path?: string;
  label?: string;
  messageSuccess?: string;
  messageError?: string;
  isMessageSuccess?: boolean;
  isMessageError?: boolean;
  checkDuplicate?: boolean;
  useDefaultParams?: boolean; // Tham số này quyết định xem DEFAULT_PARAMS có được thêm vào queryKey không
  isShowMessage?: boolean; // Tham số này quyết định show message hay ko
  isDelete?: boolean; // Tham số này quyết định là delete hay update
}

type TDataMutation = {
  data: Response;
};

// Hàm chung xử lý mutation
const useFieldMutation = <T>({
  apiQuery,
  keyOfListQuery,
  keyOfDetailQuery,
  messageSuccess,
  messageError,
  path,
  label = '',
  isMessageSuccess = true,
  isMessageError = true,
  checkDuplicate = false,
  useDefaultParams = true, // Mặc định là true, nếu không muốn thêm DEFAULT_PARAMS thì set là false
  isShowMessage = true, // Mặc định là true, nếu không muốn hiển thị message thì set là false
  isDelete = false, // Mặc định là false, nếu là delete thì set là true
}: IApiQuery<T>) => {
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const [filter, setFilter] = useFilter();

  // Hàm hiển thị thông báo
  const showNotification = (type: 'success' | 'error', message: string) => {
    if (type === 'success' && isMessageSuccess) {
      notification.success({ message });
    } else if (type === 'error' && isMessageError) {
      notification.error({ message });
    }
  };

  return useMutation({
    mutationFn: async (payload: T) => apiQuery(payload) as Promise<TDataMutation>,
    onSuccess: async (data: TDataMutation | undefined) => {
      if (data?.data?.statusCode === '0') {
        // Invalidate query detail nếu có
        if (keyOfDetailQuery) {
          queryClient.invalidateQueries({ queryKey: keyOfDetailQuery, refetchType: 'all' });
        }
        // Xử lý invalidation cho keyOfListQuery
        if (keyOfListQuery) {
          const queryKey = useDefaultParams
            ? [
                { ...filter, page: isDelete ? `${Math.max(Number(filter?.page) - 1, 1)}` : '1' },
                ...(keyOfListQuery ?? []),
              ]
            : [filter, ...(keyOfListQuery ?? [])];
          await queryClient.invalidateQueries({
            queryKey,
          });
          setFilter(
            useDefaultParams
              ? { ...filter, page: isDelete ? `${Math.max(Number(filter?.page) - 1, 1)}` : '1' }
              : filter,
          );
        }
        // Hiển thị thông báo thành công
        const successMessage = messageSuccess || `Thao tác với ${label} thành công!`;
        showNotification('success', successMessage);

        // Điều hướng nếu có
        if (path) navigate(path);
      } else {
        data?.data && isShowMessage && !checkDuplicate
          ? handleErrors(data?.data)
          : showNotification('error', 'Thao tác không thành công!');
        messageError ? showNotification('error', messageError) : '';
      }
    },
    onError: () => {
      const errorMessage = messageError || `Thao tác với ${label} không thành công!`;
      showNotification('error', errorMessage);
    },
  });
};

// Hook cho các thao tác CRUD
export const useDeleteField = <T>(props: IApiQuery<T>) => {
  return useFieldMutation({
    ...props,
    label: props.label || 'Dữ liệu',
    messageSuccess: props.messageSuccess || `Xoá ${props.label || ''} thành công!`,
    messageError: props.messageError || `Xoá ${props.label || ''} không thành công!`,
  });
};

export const useUpdateField = <T>(props: IApiQuery<T>) => {
  return useFieldMutation({
    ...props,
    label: props.label || 'Dữ liệu',
    messageSuccess: props.messageSuccess || `Chỉnh sửa ${props.label || ''} thành công!`,
    messageError: props.messageError || `Chỉnh sửa ${props.label || ''} không thành công!`,
  });
};

export const useCreateField = <T>(props: IApiQuery<T>) => {
  return useFieldMutation({
    ...props,
    label: props.label || 'Dữ liệu',
    messageSuccess: props.messageSuccess || `Tạo mới ${props.label || ''} thành công!`,
    messageError: props.messageError || `Tạo mới ${props.label || ''} không thành công!`,
  });
};
