import { useAuth } from '../../context/AuthContext';

interface PermissionsObject {
  [key: string]: string[][];
}

/**
 * Hook that checks multiple permissions and returns an object with permission status
 * @param listPermissions - Object containing permission keys and values
 * @returns Record of permission keys mapped to boolean values indicating if user has each permission
 */

export const useCheckPermissions = <T extends PermissionsObject>(listPermissions: T) => {
  const { hasAuthority } = useAuth();

  // gọi ra tất cả các key của object listPermissions
  const permissions = Object.keys(listPermissions) as (keyof T)[];

  return permissions.reduce(
    (acc, permission) => {
      const permissionValue = listPermissions[permission as string];
      acc[permission] = hasAuthority(permissionValue);
      return acc;
    },
    {} as Record<keyof T, boolean>,
  );
};
