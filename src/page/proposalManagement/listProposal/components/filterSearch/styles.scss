.wrapper-dropdown-content {
  list-style-type: none;
  background-color: #ffffff;
  background-clip: padding-box;
  border-radius: 2px;
  outline: none;
  box-shadow:
    0 6px 16px 0 rgba(0, 0, 0, 0.08),
    0 3px 6px -4px rgba(0, 0, 0, 0.12),
    0 9px 28px 8px rgba(0, 0, 0, 0.05);
  padding: 16px;
  width: 325px;
  .ant-btn {
    float: right;
  }
  .ant-picker-range {
    width: 100%;
  }
}
.ant-input-search .ant-input-group .ant-input-affix-wrapper:not(:last-child) {
  border-start-start-radius: 2px;
  border-end-start-radius: 2px;
}

.ant-input-search > .ant-input-group > .ant-input-group-addon:last-child .ant-input-search-button {
  border-start-end-radius: 2px;
  border-end-end-radius: 2px;
}
.filter-search {
  min-width: 357px;
}
