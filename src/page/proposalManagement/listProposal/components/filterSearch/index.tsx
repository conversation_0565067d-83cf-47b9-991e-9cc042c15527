import { Col, DatePicker, Form, Row, Select } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { useEffect, useMemo, useState } from 'react';
import { useLocation } from 'react-router-dom';
import DropdownFilterSearch from '../../../../../components/dropdown/dropdownFilterSearch';
import MultiSelectLazy from '../../../../../components/select/mutilSelectLazy';
import { FORMAT_DATE, FORMAT_DATE_API, OPTIONS_APPROVAL_STATUS } from '../../../../../constants/common';
import { useFetch } from '../../../../../hooks';
import useFilter from '../../../../../hooks/filter';
import { getListEmployeeAll } from '../../../../../service/customers';
import { getProposalType } from '../../../../../service/proposal';
import { TEmployeeAll } from '../../../../../types/customers';
import { TProposalType } from '../../../../../types/proposal';
import './styles.scss';

type TFilter = {
  isActive?: number | null;
  startDate?: string | Dayjs | null;
  endDate?: string | Dayjs | null;
  status?: string;
  createdBy?: string;
  type?: string;
};

function FilterSearch() {
  const [form] = Form.useForm();
  const [isOpenFilter, setIsOpenFilter] = useState(false);
  const [initialValues, setInitialValues] = useState<TFilter>();
  const [filter, setFilter] = useFilter();
  const { search } = useLocation();
  const params = useMemo(() => new URLSearchParams(search), [search]);

  const { data: dataProposalType } = useFetch<TProposalType[]>({
    queryKeyArrWithFilter: ['get-proposal-type'],
    api: getProposalType,
  });

  useEffect(() => {
    const formatData = {
      status: params.get('status') || undefined,
      isActive: params.get('isActive') ? Number(params.get('isActive')) : undefined,
      startDate: params.get('startDate') ? dayjs(params.get('startDate')) : undefined,
      endDate: params.get('endDate') ? dayjs(params.get('endDate')) : undefined,
    };
    setInitialValues(formatData);
    form.setFieldsValue(formatData);
  }, [form, params]);

  const handleSubmitFilter = (values: TFilter) => {
    const filterType = dataProposalType?.data?.data?.filter(o => o?.code === values?.type)[0];
    const newFilter: Record<string, unknown> = {
      type: filterType?.name || null,
      status: values.status || null,
      isActive: values.isActive || null,
      startDate: values?.startDate ? dayjs(values?.startDate).format(FORMAT_DATE_API) : null,
      endDate: values?.endDate ? dayjs(values?.endDate).format(FORMAT_DATE_API) : null,
    };
    setFilter({ ...filter, page: '1', ...newFilter });
    setIsOpenFilter(false);
  };

  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };

  const handleSelectEmployee = (values: TEmployeeAll[]) => {
    console.log('values :', values);
  };

  const handleClearFilters = () => {
    setInitialValues(undefined);
    setIsOpenFilter(false);
  };
  return (
    <>
      <DropdownFilterSearch
        rootClassName="wrapper-filter-customers"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        showParams={true}
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        initialValues={initialValues}
        form={form}
        onClearFilters={handleClearFilters}
        extraFormItems={
          <>
            <Form.Item label="Loại tờ trình" name="type">
              <Select
                placeholder="Chọn loại tờ trình"
                allowClear
                options={dataProposalType?.data?.data?.map((item: TProposalType) => ({
                  value: item?.code,
                  label: item?.name,
                }))}
              />
            </Form.Item>
            <Form.Item label="Trạng thái" name="status">
              <Select placeholder="Chọn trạng thái" allowClear options={OPTIONS_APPROVAL_STATUS} />
            </Form.Item>

            <Form.Item label="Người tạo" name="createdBy">
              <MultiSelectLazy
                enabled={isOpenFilter}
                apiQuery={getListEmployeeAll}
                queryKey={['employee-dropdown']}
                keysLabel={['name', 'email']}
                handleListSelect={handleSelectEmployee}
                placeholder="Chọn nhân viên"
                keysTag={'email'}
              />
            </Form.Item>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="Từ ngày" name="startDate">
                  <DatePicker
                    format={FORMAT_DATE}
                    disabledDate={current => {
                      const endDate = form.getFieldValue('endDate');
                      return (
                        (current && current > dayjs().startOf('day')) ||
                        (endDate && current > dayjs(endDate).startOf('day')) // Disable ngày sau "Đến ngày"
                      );
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Đến ngày" name="endDate">
                  <DatePicker
                    format={FORMAT_DATE}
                    disabledDate={current => {
                      const startDate = form.getFieldValue('startDate');
                      return (
                        (current && current > dayjs().startOf('day')) ||
                        (startDate && current < dayjs(startDate).startOf('day')) // Disable ngày trước "Ngày tạo"
                      );
                    }}
                  />
                </Form.Item>
              </Col>
            </Row>
          </>
        }
      />
    </>
  );
}

export default FilterSearch;
