import { TableColumnsType, Typography } from 'antd';
import dayjs from 'dayjs';
import { FORMAT_DATE_TIME, getStatusObject } from '../../../constants/common';
import { IProposal } from '../../../types/proposal';

const { Text } = Typography;

export const columns: TableColumnsType = [
  {
    title: 'Mã tờ trình CRM',
    dataIndex: 'code',
    key: 'code',
    width: 160,
    fixed: 'left',
    render: (value: string) =>
      value ? (
        <div className="cell-name">
          {/* {record.status === 'CUST_DONE' ? (
            <Tag bordered={false} color="success">
              KHCT
            </Tag>
          ) : null} */}
          <Text>{value}</Text>
        </div>
      ) : (
        '-'
      ),
  },
  {
    title: 'Số E-Approve',
    dataIndex: 'eappNumber',
    key: 'eappNumber',
    width: 140,
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: '<PERSON>ại tờ trình',
    dataIndex: 'type',
    width: 140,
    key: 'type',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Tên phiếu',
    dataIndex: 'name',
    width: 140,
    key: 'name',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Trạng thái',
    dataIndex: 'status',
    width: 200,
    key: 'status',
    align: 'center',
    render: (value: string) => {
      const statusObject = getStatusObject(value);
      return <Text style={{ color: statusObject.value }}>{statusObject?.label} </Text>;
    },
  },
  {
    title: 'Ngày tạo',
    dataIndex: 'createdAt',
    key: 'createdAt',
    width: 200,

    render: (value: string, record: IProposal) => (
      <>
        <Text>{dayjs(value).format(FORMAT_DATE_TIME)}</Text>
        <br />
        <Text>{record?.createdBy ? `${record?.createdBy?.userName} - ${record?.createdBy?.fullName} ` : '-'}</Text>
      </>
    ),
  },
  {
    title: 'Ngày cập nhật',
    dataIndex: 'updatedAt',
    key: 'updatedAt',
    width: 200,

    render: (value: string, record: IProposal) => (
      <>
        <Text>{dayjs(value).format(FORMAT_DATE_TIME)}</Text> <br />
        <Text>{record?.updatedBy ? `${record?.updatedBy?.userName} - ${record?.updatedBy?.fullName} ` : '-'}</Text>
      </>
    ),
  },
];
