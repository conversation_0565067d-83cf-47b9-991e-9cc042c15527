import { Button, Col, Form, FormProps, Input, notification, Row, Table, Typography, Upload, UploadFile } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import BreadCrumbComponent from '../../../components/breadCrumb';
import {
  ALLOWED_ATTACHMENT_EXTENSIONS,
  ALLOWED_PROPOSAL_EXTENSIONS,
  FORMAT_DATE_TIME,
  getStatusObject,
} from '../../../constants/common';
import { useFetch, useUpdateField } from '../../../hooks';
import { IProposal, TAttachment, TProposalType } from '../../../types/proposal';
import './styles.scss';
import ButtonOfPageDetail from '../../../components/button/buttonOfPageDetail';
import { handleErrors } from '../../../service/error/errorsService';
import { getDetailProposal, getProposalType, updateProposal, uploadFile } from '../../../service/proposal';
import React from 'react';
import { DeleteOutlined, PlusOutlined, UploadOutlined } from '@ant-design/icons';
import { ColumnsType } from 'antd/lib/table';
import { RcFile } from 'antd/lib/upload';
import { AxiosError } from 'axios';
import { v4 as uuid } from 'uuid';

const { Item } = Form;
const { Title, Text } = Typography;
const { TextArea } = Input;

const DetailProposal = () => {
  const { id } = useParams();
  const [form] = Form.useForm();

  const [initialValues, setInitialValues] = useState<IProposal>();
  const [isModified, setIsModified] = useState(false);
  const [isLoadingFile, setIsLoadingFile] = useState(false);

  const { mutateAsync, isPending } = useUpdateField({
    apiQuery: updateProposal,
    keyOfListQuery: ['get-proposal'],
    keyOfDetailQuery: ['get-detail-proposal', id],
    checkDuplicate: true,
    isMessageError: false,
  });

  const { data: dataProposalType } = useFetch<TProposalType[]>({
    queryKeyArrWithFilter: ['get-proposal-type'],
    api: getProposalType,
  });

  const proposalType = dataProposalType?.data?.data;

  const { data: dataDetail } = useFetch<IProposal>({
    api: () => id && getDetailProposal(id),
    queryKeyArr: ['get-detail-proposal', id],
    enabled: !!id,
    cacheTime: 10,
  });
  const data = dataDetail?.data?.data;

  const [status, setStatus] = useState<string>('');

  const [attachments, setAttachments] = useState<TAttachment[]>([]);

  const [files, setFiles] = useState<TAttachment[]>(); //set tờ trình upload lên
  const [isLoadingUpload, setIsLoadingUpload] = useState<boolean>(false);
  const [fileList, setFileList] = useState<TAttachment[]>([]); //Use to check Error and LoadingTable

  const handleDeleteAttachment = React.useCallback(
    (attachment: TAttachment) => {
      const newAttachments = attachments;
      const findIndex = newAttachments?.findIndex(o => o?.id === attachment?.id);
      if (findIndex !== undefined && findIndex !== null) {
        setAttachments(
          newAttachments?.slice(0, findIndex)?.concat(newAttachments?.slice(findIndex + 1, newAttachments?.length)),
        );
      }
      setIsModified(true);
    },
    [attachments],
  );

  const handleChangeNameAttachment = React.useCallback((value: string, record: TAttachment) => {
    if (!record?.id) return;

    setAttachments(prev => {
      const index = prev.findIndex(item => item?.id === record.id);
      if (index === -1) return prev; // Không tìm thấy => không thay đổi

      const updated = [...prev];
      updated[index] = { ...updated[index], name: value };
      return updated;
    });

    setIsModified(true);
  }, []);

  const columnsAttachmentTable: ColumnsType<TAttachment> = React.useMemo(
    () => [
      {
        title: 'STT',
        dataIndex: 'index',
        key: 'index',
        render: (_stt, _record, index) => index + 1,
      },
      {
        title: 'Tên tài liệu',
        dataIndex: 'name',
        key: 'name',
        render: (value: string, record: TAttachment, index: number) => (
          <Form.Item
            name={['files', index, 'name']}
            rules={[
              {
                required: true,
                validator: (_, value) => {
                  if (!value || value.trim() === '') {
                    return Promise.reject(new Error('Vui lòng nhập tên!'));
                  }
                  return Promise.resolve();
                },
              },
            ]}
          >
            <Input
              onChange={e => handleChangeNameAttachment(e?.target?.value, record)}
              defaultValue={value}
              disabled={!['NEW', 'RETURNED', 'REJECTED']?.includes(data?.status as string)}
              maxLength={255}
            />
          </Form.Item>
        ),
      },
      {
        title: 'File đính kèm',
        dataIndex: 'url',
        key: 'url',
        render: (url: string, record: TAttachment) =>
          url ? (
            <a href={url} target="_blank" rel="noopener noreferrer">
              {record?.name}
            </a>
          ) : (
            <></>
          ),
      },
      {
        title: 'Tác vụ',
        width: '80px',
        key: 'action',
        align: 'center',
        render: (_action, record) => (
          <Button
            disabled={!['NEW', 'RETURNED', 'REJECTED']?.includes(data?.status as string)}
            style={{ background: 'none', border: 'none' }}
          >
            <DeleteOutlined onClick={() => handleDeleteAttachment(record)} style={{ color: 'red' }} />
          </Button>
        ),
      },
    ],
    [data?.status, handleChangeNameAttachment, handleDeleteAttachment],
  );

  // cảnh báo khi thoát trang khi chưa lưu dữ liệu
  useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  useEffect(() => {
    if (data) {
      const initialData = {
        ...data,
        fileProposal: [
          {
            name: data?.sheetName,
            fileName: data?.sheetName,
            url: data?.sheetUrl,
          },
        ],
      };
      const statusObject = getStatusObject(data?.status as string);
      setStatus(statusObject?.label);
      setFiles([
        {
          name: data?.sheetName as string,
          url: data?.sheetUrl,
          uid: '1',
        },
      ]);
      setAttachments(data?.files || []);
      setInitialValues(initialData as IProposal);
    }
  }, [data, form]);

  useEffect(() => {
    form.setFieldsValue({ fileProposal: files });
  }, [files, form]); // Cập nhật Form khi files thay đổi

  const onFinish: FormProps['onFinish'] = async (values: IProposal) => {
    if (!data?.hasTemplate) {
      let proposalUrl: { sheetName: string; url: string } = { sheetName: '', url: '' };

      proposalUrl = {
        url: files ? (files[0]?.url as string) : '',
        sheetName: files ? (files[0]?.name as string) : '',
      };

      const typeProposal = proposalType?.find(o => o?.name === values?.type);

      const newData = {
        ...values,
        id: id,
        eappNumber: '',
        ticketId: values?.ticketId ? values?.ticketId : 0,
        urlEapp: values?.urlEapp ? values?.urlEapp : '',
        url: values?.url ? values?.url : '',
        fileProposal: undefined,
        sheetUrl: proposalUrl?.url,
        sheetName: proposalUrl?.sheetName,
        files: [...attachments],
        type: typeProposal?.code,
      };
      const response = await mutateAsync({ ...newData });
      const responseData = response?.data;

      if (responseData) {
        switch (responseData?.statusCode) {
          case '0':
            setIsModified(false);
            break;
          default:
            handleErrors(responseData);
        }
      }
      // }
    } else {
      const newData = {
        id: id,
        ticketId: values?.ticketId ? values?.ticketId : 0,
        sheetUrl: values?.sheetUrl ? values?.sheetUrl : '',
        urlEapp: values?.urlEapp ? values?.urlEapp : '',
        url: values?.url ? values?.url : '',
        ...values,
      };

      const response = await mutateAsync({ ...newData });
      const responseData = response?.data;

      if (responseData) {
        switch (responseData?.statusCode) {
          case '0':
            setIsModified(false);
            break;
          default:
            handleErrors(responseData);
        }
      }
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setIsModified(false);
  };

  const handleGoEAPP = React.useCallback(() => {
    window.open(data?.urlEapp);
  }, [data?.urlEapp]);

  const validateForm = () => {
    setIsModified(true);
  };

  const handleBeforeUpload = async (file: RcFile) => {
    const fileName = file?.name.toLowerCase();

    const isAllowedType = ALLOWED_PROPOSAL_EXTENSIONS.some(ext => fileName.endsWith(ext));

    if (!isAllowedType) {
      notification.error({
        message: 'File không đúng định dạng. Vui lòng sử dụng file .doc, .docx, .pdf, .xls, hoặc .xlsx',
      });
      return Upload.LIST_IGNORE;
    }
    return true;
  };

  const handleBeforeUploadAttachment = async (file: RcFile, fileList: RcFile[]) => {
    if (fileList[0] === file) {
      setIsLoadingUpload(true);
    }
    const fileName = file.name.toLowerCase();
    const isAllowedType = ALLOWED_ATTACHMENT_EXTENSIONS.some(ext => fileName.endsWith(ext));

    // Tổng số lượng file sau khi upload
    const totalFiles = (attachments?.length || 0) + fileList?.length;

    if (totalFiles > 20) {
      if (fileList[fileList.length - 1] === file) {
        notification.error({
          message: `Tải lên vượt quá 20 file!`,
        });
      }
      setIsLoadingUpload(false);
      return Upload.LIST_IGNORE;
    }

    // Check tổng dung lượng tất cả file
    const currentTotalSize =
      (attachments?.reduce((acc, attachment) => acc + (attachment?.file?.size || 0), 0) || 0) +
      fileList.reduce((acc, file) => acc + (file?.size || 0), 0);

    const totalSizeMB = currentTotalSize / (1024 * 1024); // Byte -> MB
    if (totalSizeMB > 1024) {
      if (fileList[fileList.length - 1] === file) {
        notification.error({
          message: `Tổng dung lượng vượt quá 1GB!`,
        });
      }
      setIsLoadingUpload(false);
      return Upload.LIST_IGNORE;
    }

    // Check dung lượng từng file
    const fileSizeMB = file?.size / (1024 * 1024); // Byte -> MB

    if (fileSizeMB > 100) {
      if (fileList[fileList.length - 1] === file) {
        notification.error({
          message: `Kích thước file "${file?.name}" vượt quá 100MB!`,
        });
      }
      setIsLoadingUpload(false);
      return Upload.LIST_IGNORE;
    }

    if (!isAllowedType) {
      if (fileList[fileList.length - 1] === file) {
        notification.error({
          message:
            'File không đúng định dạng. Vui lòng sử dụng file .png, .jpg, .jpeg, .mp4, .avi, .mov, .wmv, .xls, .xlsx, .doc, .docx, .pdf, .ppt, .pptx, .jfif, .rar, .zip, .msg hoặc .txt',
        });
      }
      setIsLoadingUpload(false);
      return Upload.LIST_IGNORE;
    }

    if (fileList[fileList.length - 1] === file) {
      setFileList(fileList);
    }
    return true;
  };

  useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  return (
    <div className="wrapper-detail-personal-proposal">
      <BreadCrumbComponent titleBread={'Xem chi tiết'} />

      {initialValues ? (
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          onValuesChange={validateForm}
          initialValues={initialValues}
          className="space-y-6"
        >
          <Row gutter={{ md: 24, lg: 40, xl: 80, xxl: 126 }}>
            <Col span={24}></Col>
            <Col xs={24} md={12}>
              <Title level={5}>Thông tin tờ trình</Title>
              <Row gutter={{ md: 24, lg: 40 }}>
                <Col xs={24} md={12}>
                  <Item label="Mã tờ trình CRM" name="code">
                    <Input placeholder="Mã tờ trình CRM" disabled />
                  </Item>
                </Col>
                <Col xs={24} md={12}>
                  <Item name={'eappNumber'} label="Số E-Approve">
                    <Input className="input-link" onClick={handleGoEAPP} style={{ cursor: 'pointer' }} readOnly></Input>
                    {/* <Link /> */}
                  </Item>
                </Col>
                <Col xs={24} md={12}>
                  <Item label="Trạng thái">
                    <Input placeholder="Trạng thái" disabled value={status} />
                  </Item>
                </Col>
                <Col xs={24} md={12}>
                  <Item label="Loại tờ trình" name="type">
                    <Input placeholder="Loại tờ trình" disabled />
                  </Item>
                </Col>
                <Col xs={24} md={24}>
                  <Item
                    label="Tên phiếu"
                    name="name"
                    required
                    rules={[
                      {
                        required: true,
                        validator: (_, value) => {
                          if (!value || value.trim() === '') {
                            return Promise.reject(new Error('Vui lòng nhập tên phiếu'));
                          }
                          return Promise.resolve();
                        },
                      },
                    ]}
                  >
                    <Input
                      placeholder="Tên phiếu"
                      maxLength={255}
                      disabled={!['NEW', 'RETURNED', 'REJECTED']?.includes(data?.status as string)}
                    />
                  </Item>
                </Col>
                {!data?.hasTemplate && (
                  <Col xs={24} md={24}>
                    <Form.Item
                      name="fileProposal"
                      label="File tờ trình"
                      required
                      rules={[{ required: true, message: 'Vui lòng upload file!' }]}
                      valuePropName="fileList"
                      getValueFromEvent={e => {
                        return Array.isArray(e) ? e : e?.fileList;
                      }}
                    >
                      <Upload
                        showUploadList={{
                          showRemoveIcon: ['NEW', 'RETURNED', 'REJECTED']?.includes(data?.status as string),
                        }}
                        beforeUpload={handleBeforeUpload}
                        maxCount={1}
                        customRequest={async ({ file, onSuccess, onError }) => {
                          try {
                            setIsLoadingFile(true);
                            const fileData = file as RcFile;
                            const resp = await uploadFile([fileData], 'true');
                            const data = resp.data.data;

                            if (data?.length) {
                              // eslint-disable-next-line @typescript-eslint/no-explicit-any
                              const newFiles: UploadFile[] = data.map((item: any) => ({
                                uid: item?.uid, // fallback nếu backend không trả uid
                                name: item?.originalname,
                                url: item?.Location,
                                status: 'done', // ← quan trọng!
                              }));
                              setFiles([...newFiles]);
                              setIsModified(true);
                              setIsLoadingFile(false);
                            }

                            onSuccess?.('ok');
                            // Kiểm tra nếu đây là file cuối cùng
                          } catch (error: unknown) {
                            setIsLoadingFile(false);
                            onError?.(error as AxiosError);
                          }
                        }}
                        onRemove={() => {
                          setFiles(undefined);
                          setIsModified(true);
                          return true;
                        }}
                        fileList={files?.map(o => {
                          const newFile = { ...o, uid: o?.uid ?? '1' };
                          return newFile as RcFile;
                        })} // gán với fileList
                        name="file-upload"
                      >
                        <Button
                          icon={<UploadOutlined />}
                          disabled={
                            isLoadingFile ||
                            files?.length === 1 ||
                            !['NEW', 'RETURNED', 'REJECTED']?.includes(data?.status as string)
                          }
                        >
                          Upload
                        </Button>
                      </Upload>
                    </Form.Item>
                  </Col>
                )}
                {!data?.hasTemplate && ( //check ko tệp mẫu
                  <>
                    <Col span={24}>
                      <Text>Tài liệu đính kèm</Text>
                    </Col>
                    <Col xs={24} md={6} style={{ marginTop: '8px' }}>
                      <Upload
                        showUploadList={false}
                        multiple
                        maxCount={20}
                        customRequest={async ({ file, onSuccess, onError }) => {
                          try {
                            const fileData = file as RcFile;
                            const resp = await uploadFile([fileData], 'false');
                            const data = resp.data.data;

                            if (data?.length) {
                              // eslint-disable-next-line @typescript-eslint/no-explicit-any
                              const newAttachments = data?.map((item: any) => ({
                                name: item?.originalname,
                                url: item?.Location,
                                id: item?.uid || uuid(),
                              }));
                              setAttachments(prev => {
                                const updatedAttachments = [...prev, ...newAttachments];
                                form?.setFieldValue('files', updatedAttachments);
                                return updatedAttachments;
                              });
                            }
                            const lastFile = fileList[0];

                            if (fileData.uid === lastFile.uid) {
                              setIsLoadingUpload(false);
                            }
                            setIsModified(true);

                            onSuccess?.('ok');
                          } catch (error: unknown) {
                            setIsLoadingUpload(false);
                            onError?.(error as AxiosError);
                          }
                        }}
                        beforeUpload={handleBeforeUploadAttachment}
                      >
                        <Button
                          icon={<PlusOutlined />}
                          disabled={!['NEW', 'RETURNED', 'REJECTED']?.includes(data?.status as string)}
                        >
                          Thêm tài liệu
                        </Button>
                      </Upload>
                    </Col>
                    <Col xs={24} md={24} style={{ marginTop: '8px' }}>
                      <Table
                        className="attachment-file"
                        dataSource={attachments}
                        key={'id'}
                        columns={columnsAttachmentTable}
                        bordered
                        pagination={false}
                        loading={isLoadingUpload}
                      />
                    </Col>
                  </>
                )}
              </Row>
            </Col>
            <Col xs={24} md={12}>
              <Title level={5} style={{ marginBottom: '35px' }}>
                Lý do
              </Title>
              <Item name="eappReason">
                <TextArea rows={5} maxLength={500} placeholder="Lý do" disabled />
              </Item>
              <div className="info">
                <Row gutter={24}>
                  <Col lg={6} xs={8}>
                    <Text disabled>Ngày cập nhật: </Text>
                  </Col>
                  <Col lg={18} xs={16}>
                    <Text disabled>
                      {data?.updatedAt ? dayjs(data?.updatedAt).format(FORMAT_DATE_TIME) : ''}&nbsp;&nbsp;
                      {`${data?.updatedBy?.userName} - ${data?.updatedBy?.fullName}`}
                    </Text>
                  </Col>
                </Row>
                <Row gutter={24}>
                  <Col lg={6} xs={8}>
                    <Text disabled>Ngày tạo: </Text>
                  </Col>
                  <Col lg={18} xs={16}>
                    <Text disabled>
                      {data?.createdAt ? dayjs(data?.createdAt).format(FORMAT_DATE_TIME) : ''}&nbsp;&nbsp;
                      {`${data?.createdBy?.userName} - ${data?.createdBy?.fullName}`}
                    </Text>
                  </Col>
                </Row>
              </div>
            </Col>
          </Row>
        </Form>
      ) : (
        <></>
      )}
      {isModified && (
        <ButtonOfPageDetail handleSubmit={() => form.submit()} handleCancel={handleCancel} loadingSubmit={isPending} />
      )}
    </div>
  );
};

export default DetailProposal;
