import { Button, Col, Form, Input, Modal, notification, Row, Select, Table, Typography, Upload } from 'antd';
import { useEffect, useState } from 'react';
import { useCreateField, useFetch } from '../../../hooks';
import {
  IProposal,
  TCreateProposal,
  TProposalType,
  TResponseCreateProposal,
  TSubmitNoneSampleProposal,
} from '../../../types/proposal';
import './styles.scss';
import { DeleteOutlined, PaperClipOutlined, PlusOutlined, UploadOutlined } from '@ant-design/icons';
import { ColumnsType } from 'antd/lib/table';
import React from 'react';
import { TAttachment } from '../../../types/proposal';
import { createNonSampleProposal, createProposal, getProposalType, uploadFile } from '../../../service/proposal';
import { RcFile } from 'antd/es/upload';
import ModalComponent from '../../../components/modal';
import { QueryKey } from '@tanstack/react-query';
import { ALLOWED_ATTACHMENT_EXTENSIONS, ALLOWED_PROPOSAL_EXTENSIONS } from '../../../constants/common';
import { UploadFile } from 'antd/lib/upload';
import { AxiosError } from 'axios';

import { v4 as uuidv4 } from 'uuid';
import PdfViewer from '../component/previewPdf/pdf';
import { OptionTypeSelect } from '../../../types/common/common';
import { PROPOSAL } from '../../../configs/path';
import { handleKeyDownEnterNumber } from '../../../utilities/regex';

const { Item } = Form;
const { Title, Text } = Typography;

interface CreateModalProposalNoneTemplateProps {
  visible: boolean;
  onClose: () => void;
  keyQuery: QueryKey;
  attachments?: TAttachment[];
  setAttachments?: React.Dispatch<React.SetStateAction<TAttachment[]>>;
  initialValues?: TCreateProposal;
  listProposalTypeInterestCalculation?: OptionTypeSelect[];
  handleAfterSubmitProposal?: () => void;
  handleCancelProposal?: () => void;
}

const CreateModalProposalNoneTemplate = ({
  visible,
  onClose,
  keyQuery,
  attachments,
  setAttachments,
  initialValues,
  listProposalTypeInterestCalculation,
  handleAfterSubmitProposal,
  handleCancelProposal,
}: CreateModalProposalNoneTemplateProps) => {
  const [form] = Form.useForm();

  const typeProposal = Form.useWatch('type', form);

  const [isModified, setIsModified] = useState(false);
  const [files, setFiles] = useState<TAttachment[]>();

  const [isLoadingFile, setIsLoadingFile] = useState(false);

  const [isLoadingUpload, setIsLoadingUpload] = useState<boolean>(false);
  const [fileList, setFileList] = useState<TAttachment[]>([]); //Use to check Error and LoadingTable

  const [openPreviewProposalModal, setOpenPreviewProposalModal] = useState<boolean>(false);

  const sheetUrl = Form.useWatch('fileProposal', form);

  const dataAccount = sessionStorage.getItem('dataAccount');
  const parsedDataAccount = dataAccount ? JSON.parse(dataAccount) : null;

  const createProposalNoneTemplate = useCreateField<TCreateProposal>({
    keyOfDetailQuery: keyQuery,
    apiQuery: createProposal,
    label: 'tờ trình',
  });

  const handleDeleteAttachment = React.useCallback(
    (attachment: TAttachment) => {
      const newAttachments = attachments ? attachments : [];
      const findIndex = newAttachments?.findIndex(o => o?.id === attachment?.id);
      if (findIndex !== undefined && findIndex !== null) {
        // newAttachments?.splice(findIndex, 1);
        setAttachments &&
          setAttachments(
            newAttachments?.slice(0, findIndex)?.concat(newAttachments?.slice(findIndex + 1, newAttachments?.length)),
          );
      }
    },
    [attachments, setAttachments],
  );

  const handleChangeNameAttachment = React.useCallback(
    (value: string, record: TAttachment) => {
      if (!record?.id) return;
      setAttachments &&
        setAttachments(prev => {
          const index = prev.findIndex(item => item?.id === record.id);
          if (index === -1) return prev; // Không tìm thấy => không thay đổi

          const updated = [...prev];
          updated[index] = { ...updated[index], name: value };
          return updated;
        });

      setIsModified(true);
    },
    [setAttachments],
  );

  const columnsAttachmentTable: ColumnsType<TAttachment> = React.useMemo(
    () => [
      {
        title: 'STT',
        dataIndex: 'index',
        width: 80,
        key: 'index',
        align: 'center',
        render: (_stt, _record, index) => index + 1,
      },
      {
        title: 'Tên tài liệu',
        dataIndex: 'name',
        key: 'name',
        width: '35%',
        render: (value: string, record: TAttachment, index: number) => (
          <Form.Item
            name={['files', index, 'name']}
            rules={[
              {
                required: true,
                validator: (_, value) => {
                  if (!value || value.trim() === '') {
                    return Promise.reject(new Error('Vui lòng nhập tên!'));
                  }
                  return Promise.resolve();
                },
              },
            ]}
          >
            <Input
              onChange={e => handleChangeNameAttachment(e?.target?.value, record)}
              defaultValue={value}
              maxLength={255}
            />
          </Form.Item>
        ),
      },
      {
        title: 'File đính kèm',
        dataIndex: 'url',
        key: 'url',
        render: (url: string, record: TAttachment) =>
          url ? (
            <a href={url} download>
              <PaperClipOutlined />
              {record?.name}
            </a>
          ) : (
            <></>
          ),
      },
      {
        title: 'Tác vụ',
        width: '80px',
        key: 'action',
        align: 'center',
        render: (_action, record) => (
          <DeleteOutlined onClick={() => handleDeleteAttachment(record)} style={{ color: 'red' }} />
        ),
      },
    ],
    [handleChangeNameAttachment, handleDeleteAttachment],
  );

  const { data: dataProposalType } = useFetch<TProposalType[]>({
    queryKeyArrWithFilter: ['get-proposal-type'],
    api: getProposalType,
  });

  // cảnh báo khi thoát trang khi chưa lưu dữ liệu
  useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  const handleCancel = React.useCallback(() => {
    if (form.isFieldsTouched()) {
      Modal.confirm({
        title: 'Xác nhận hủy',
        content: 'Dữ liệu đang chưa được lưu, bạn có chắc muốn hủy dữ liệu đang nhập không?',
        cancelText: 'Quay lại',
        onOk: () => {
          setFiles([]);
          form.resetFields();
          onClose();
          handleCancelProposal && handleCancelProposal();
        },
        okButtonProps: {
          type: 'default',
        },
        cancelButtonProps: {
          type: 'primary',
        },
      });
      setIsModified(false);
    } else {
      setFiles([]);
      form.resetFields();
      setIsModified(false);
      onClose();
      handleCancelProposal && handleCancelProposal();
    }
  }, [form, handleCancelProposal, onClose]);

  const handleFinish = async (values: TCreateProposal) => {
    let proposalUrl: { sheetName: string; url: string } = { sheetName: '', url: '' };
    proposalUrl = {
      url: files ? (files[0]?.url as string) : '',
      sheetName: files ? (files[0]?.name as string) : '',
    };

    const transformedValues = {
      ...values,
      contract: initialValues?.contract,
      fileProposal: undefined,
      sheetUrl: proposalUrl?.url,
      sheetName: proposalUrl?.sheetName,
      files: attachments ? [...attachments] : [],
    };

    const res = await createProposalNoneTemplate.mutateAsync(transformedValues);
    const statusCode = res?.data?.statusCode;
    if (statusCode === '0') {
      form.resetFields();
      setFiles([]);
      setAttachments && setAttachments([]);
      setIsModified(false);
      onClose();
      handleAfterSubmitProposal && handleAfterSubmitProposal();
      handleAfterSubmitProposal &&
        window.open(
          `${PROPOSAL}/${(res?.data?.data as TResponseCreateProposal)?.proposal?.id}`,
          '_blank',
          'noopener noreferrer',
        );
    }
  };

  const validateForm = () => {
    setIsModified(true);
  };

  const handleBeforeUpload = async (file: RcFile) => {
    const fileName = file?.name.toLowerCase();

    const isAllowedType = ALLOWED_PROPOSAL_EXTENSIONS.some(ext => fileName.endsWith(ext));

    if (!isAllowedType) {
      notification.error({
        message: 'File không đúng định dạng. Vui lòng sử dụng file .doc, .docx, .pdf, .xls, hoặc .xlsx',
      });
      return Upload.LIST_IGNORE;
    }
    return true;
  };

  const handleBeforeUploadAttachment = async (file: RcFile, fileList: RcFile[]) => {
    if (fileList[0] === file) {
      setIsLoadingUpload(true);
    }

    const fileName = file.name.toLowerCase();
    const isAllowedType = ALLOWED_ATTACHMENT_EXTENSIONS.some(ext => fileName.endsWith(ext));

    // Tổng số lượng file sau khi upload
    const totalFiles = (attachments?.length || 0) + fileList?.length;

    if (totalFiles > 20) {
      if (fileList[fileList.length - 1] === file) {
        notification.error({
          message: `Tải lên vượt quá 20 file!`,
        });
      }
      setIsLoadingUpload(false);
      return Upload.LIST_IGNORE;
    }

    // Check tổng dung lượng tất cả file
    const currentTotalSize =
      (attachments?.reduce((acc, attachment) => acc + (attachment?.file?.size || 0), 0) || 0) +
      fileList.reduce((acc, file) => acc + (file?.size || 0), 0);

    const totalSizeMB = currentTotalSize / (1024 * 1024); // Byte -> MB
    if (totalSizeMB > 1024) {
      if (fileList[fileList.length - 1] === file) {
        notification.error({
          message: `Tổng dung lượng vượt quá 1GB!`,
        });
      }
      setIsLoadingUpload(false);
      return Upload.LIST_IGNORE;
    }

    // Check dung lượng từng file
    const fileSizeMB = file?.size / (1024 * 1024); // Byte -> MB

    if (fileSizeMB > 100) {
      if (fileList[fileList.length - 1] === file) {
        notification.error({
          message: `Kích thước file "${file?.name}" vượt quá 100MB!`,
        });
      }
      setIsLoadingUpload(false);
      return Upload.LIST_IGNORE;
    }

    if (!isAllowedType) {
      if (fileList[fileList.length - 1] === file) {
        notification.error({
          message:
            'File không đúng định dạng. Vui lòng sử dụng file .png, .jpg, .jpeg, .mp4, .avi, .mov, .wmv, .xls, .xlsx, .doc, .docx, .pdf, .ppt, .pptx, .jfif, .rar, .zip, .msg hoặc .txt',
        });
      }
      setIsLoadingUpload(false);
      return Upload.LIST_IGNORE;
    }

    if (fileList[fileList.length - 1] === file) {
      setFileList(fileList);
    }

    return true;
  };

  useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  useEffect(() => {
    form.setFieldsValue({ fileProposal: files });
  }, [files, form]); // Cập nhật Form khi files thay đổi

  const { mutateAsync: sendProposal } = useCreateField<unknown>({
    keyOfDetailQuery: ['get-detail-proposal'],
    apiQuery: createNonSampleProposal,
    isMessageError: false,
    messageSuccess: 'Gửi tờ trình thành công!',
  });

  const handleCancelPreviewProposal = React.useCallback(async () => {
    if (form.isFieldsTouched()) {
      Modal.confirm({
        title: 'Xác nhận hủy',
        content: 'Dữ liệu chưa được lưu, bạn có chắc chắn muốn thoát khỏi Trang không?',
        cancelText: 'Quay lại',
        onOk: async () => {
          setOpenPreviewProposalModal(false);
          // form.resetFields();
          // reload?.();
        },
        okButtonProps: { type: 'default' },
        cancelButtonProps: { type: 'primary' },
        destroyOnClose: true,
      });
    } else {
      setOpenPreviewProposalModal(false);
      // reload?.();
    }
  }, [form]);

  const handleSubmitProposal = async () => {
    try {
      await form.validateFields();

      //Tạo mới trước khi Gửi tờ trình
      const values = form?.getFieldsValue();

      const transformedValues = {
        ...values,
        fileProposal: undefined,
        sheetUrl: values?.fileProposal[0]?.url,
        sheetName: values?.fileProposal[0]?.name,
        files: attachments ? [...attachments] : [],
      };

      const res = await createProposalNoneTemplate.mutateAsync(transformedValues);
      const statusCode = res?.data?.statusCode;
      if (statusCode === '0') {
        //Tạo mới thành công => Gửi tờ trình
        const dataCreateProposalNoneTemplate: IProposal = (res?.data?.data as { proposal?: IProposal })
          ?.proposal as IProposal;
        const payload = {
          name: dataCreateProposalNoneTemplate?.name,
          sheetName: dataCreateProposalNoneTemplate?.sheetName,
          sheetUrl: dataCreateProposalNoneTemplate?.sheetUrl,
          files: dataCreateProposalNoneTemplate?.files?.map((file?: TAttachment) => ({
            name: file?.name,
            url: file?.url || '',
          })),
          account: parsedDataAccount?.username,
          proposalId: dataCreateProposalNoneTemplate?.id,
          proposalType: dataCreateProposalNoneTemplate?.type,
        };
        const resp = await sendProposal(payload);
        if (resp?.data?.statusCode === '0') {
          form.resetFields();
          setFiles([]);
          setAttachments && setAttachments([]);
          setIsModified(false);
          handleCancelPreviewProposal();
          onClose();
          window.open(
            (resp?.data?.data as TSubmitNoneSampleProposal)?.eapData?.data?.url,
            '_blank',
            'noopener,noreferrer',
          );
        }
      }
    } catch (error) {
      console.log('Validation failed:', error);
    }
  };

  return (
    <>
      <ModalComponent
        title="Tạo tờ trình"
        open={visible}
        onCancel={handleCancel}
        destroyOnClose
        footer={
          <>
            <Button type="primary" htmlType="submit" onClick={() => form.submit()}>
              Lưu
            </Button>
            {!(listProposalTypeInterestCalculation?.length && listProposalTypeInterestCalculation?.length > 0) && (
              <Button
                onClick={() => setOpenPreviewProposalModal(true)}
                style={{ marginRight: 12 }}
                // disabled={loading}
              >
                Soạn tờ trình
              </Button>
            )}
          </>
        }
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleFinish}
          onValuesChange={validateForm}
          initialValues={initialValues ? initialValues : undefined}
          className="space-y-6"
        >
          <Row gutter={{ md: 24, lg: 40, xl: 80, xxl: 126 }}>
            <Col span={24}>
              <Title level={5}>Thông tin tờ trình</Title>
            </Col>
            <Col xs={24} md={12}>
              <Row gutter={{ md: 24, lg: 40 }}>
                <Col xs={24} md={12}>
                  <Item
                    label="Tên phiếu"
                    name="name"
                    required
                    rules={[
                      {
                        required: true,
                        validator: (_, value) => {
                          if (!value || value.trim() === '') {
                            return Promise.reject(new Error('Vui lòng nhập tên phiếu'));
                          }
                          return Promise.resolve();
                        },
                      },
                    ]}
                  >
                    <Input placeholder="Nhập tên phiếu" maxLength={255} />
                  </Item>
                </Col>
                <Col xs={24} md={12}>
                  <Item
                    label="Loại tờ trình"
                    name="type"
                    required
                    rules={[{ required: true, message: 'Vui lòng chọn loại tờ trình' }]}
                  >
                    <Select
                      placeholder="Chọn loại tờ trình"
                      allowClear
                      options={
                        listProposalTypeInterestCalculation
                          ? listProposalTypeInterestCalculation
                          : dataProposalType?.data?.data?.map((item: TProposalType) => ({
                              key: item?.code,
                              label: item?.name,
                              value: item?.code,
                            }))
                      }
                      disabled={!!initialValues}
                    />
                  </Item>
                </Col>
                {typeProposal === 'PROPOSAL_FOR_INTEREST_WAIVER_OR_REDUCTION' && (
                  <Col xs={24} md={24}>
                    <Item
                      label="Số tiền lãi muốn giảm"
                      name="interestReductionAmountEap"
                      required
                      rules={[
                        {
                          required: true,
                          validator: (_, value) => {
                            if (!value || value.trim() === '') {
                              return Promise.reject(new Error('Vui lòng nhập số tiền'));
                            }
                            return Promise.resolve();
                          },
                        },
                      ]}
                    >
                      <Input
                        onKeyDown={handleKeyDownEnterNumber}
                        placeholder="Nhập số tiền lãi muốn giảm"
                        maxLength={255}
                      />
                    </Item>
                  </Col>
                )}
                <Col xs={24} md={24}>
                  <Form.Item
                    name="fileProposal"
                    label="File tờ trình"
                    required
                    rules={[{ required: true, message: 'Vui lòng upload file!' }]}
                    valuePropName="fileList"
                    getValueFromEvent={e => {
                      return Array.isArray(e) ? e : e?.fileList;
                    }}
                  >
                    <Upload
                      beforeUpload={handleBeforeUpload}
                      maxCount={1}
                      customRequest={async ({ file, onSuccess, onError }) => {
                        try {
                          setIsLoadingFile(true);
                          const fileData = file as RcFile;
                          const resp = await uploadFile([fileData], 'true');
                          const data = resp.data.data;

                          if (data?.length) {
                            // eslint-disable-next-line @typescript-eslint/no-explicit-any
                            const newFiles: UploadFile[] = data.map((item: any) => ({
                              uid: item?.uid || 1, // fallback nếu backend không trả uid
                              name: item?.originalname,
                              url: item?.Location,
                              status: 'done', // ← quan trọng!
                            }));
                            setFiles(newFiles);
                            setIsModified(true);
                            setIsLoadingFile(false);
                          }
                          onSuccess?.('ok');
                        } catch (error: unknown) {
                          setIsLoadingFile(false);
                          onError?.(error as AxiosError);
                        }
                      }}
                      onRemove={() => {
                        setFiles(undefined);
                        setIsModified(true);
                        return true;
                      }}
                      fileList={files?.map((o: TAttachment) => {
                        const newFile = { ...o, uid: o?.uid ?? '1' };
                        return newFile as RcFile;
                      })} // gán với fileList
                      name="file-upload"
                    >
                      <Button disabled={isLoadingFile || files?.length === 1} icon={<UploadOutlined />}>
                        Upload
                      </Button>
                    </Upload>
                  </Form.Item>
                </Col>
              </Row>
            </Col>
            <Col span={24}>
              <Text>Tài liệu đính kèm</Text>
            </Col>
            <Col xs={24} md={6} style={{ marginTop: '8px' }}>
              <Upload
                showUploadList={false}
                multiple
                maxCount={20}
                customRequest={async ({ file, onSuccess, onError }) => {
                  try {
                    const fileData = file as RcFile;
                    const resp = await uploadFile([fileData], 'false');
                    const data = resp.data.data;

                    if (data?.length) {
                      // eslint-disable-next-line @typescript-eslint/no-explicit-any
                      const newAttachments = data?.map((item: any) => ({
                        name: item?.originalname,
                        url: item?.Location,
                        id: item?.uid || uuidv4(),
                      }));

                      setAttachments &&
                        setAttachments(prev => {
                          const updatedAttachments = [...prev, ...newAttachments];
                          form?.setFieldValue('files', updatedAttachments);
                          return updatedAttachments;
                        });
                    }
                    const lastFile = fileList[0];

                    if (fileData.uid === lastFile.uid) {
                      setIsLoadingUpload(false);
                    }
                    setIsModified(true);
                    onSuccess?.('ok');
                  } catch (error: unknown) {
                    setIsLoadingUpload(false);
                    onError?.(error as AxiosError);
                  }
                }}
                beforeUpload={handleBeforeUploadAttachment}
              >
                <Button icon={<PlusOutlined />}>Thêm tài liệu</Button>
              </Upload>
            </Col>
            <Col xs={24} md={24} style={{ marginTop: '8px' }}>
              <Table
                className="attachment-file"
                dataSource={attachments}
                key={'id'}
                columns={columnsAttachmentTable}
                bordered
                pagination={false}
                loading={isLoadingUpload}
              />
            </Col>
          </Row>
        </Form>
      </ModalComponent>
      <ModalComponent
        title={'Tờ trình'}
        open={openPreviewProposalModal}
        onCancel={handleCancelPreviewProposal}
        footer={[
          <Button key="cancel" onClick={handleCancelPreviewProposal}>
            Hủy
          </Button>,
          <Button key="submit" type="primary" onClick={handleSubmitProposal} disabled={!sheetUrl}>
            Gửi tờ trình
          </Button>,
        ]}
      >
        <div style={{ marginTop: 33 }}>{sheetUrl && <PdfViewer pdfLink={sheetUrl[0]?.url} />}</div>
      </ModalComponent>
    </>
  );
};

export default CreateModalProposalNoneTemplate;
