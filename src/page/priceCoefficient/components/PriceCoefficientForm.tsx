import { Form, Button, Input, Row, Col, Typography, Select, InputNumber, FormListFieldData, Table } from 'antd';
import { FormInstance } from 'antd/lib';
import { useWatch } from 'antd/es/form/Form';
import { useMemo, useState, useCallback } from 'react';
import SingleSelectLazy from '../../../components/select/singleSelectLazy';
import { getListProject, getListProjectSaleProgram } from '../../../service/project';
import { OptionTypeSelect } from '../../../types/common/common';
import { getPropertyAttribute } from '../../../service/propertyAttribute';
import { IPropertyAttribute } from '../../../types/propertyAttribute';
import {
  CreatePriceCoefficientFormValues,
  EAttributeValueType,
  IPriceCoefficients,
} from '../../../types/priceCoefficient';

import './styles.scss';

const { Title } = Typography;

const VALUE_TYPE_OPTIONS = [
  { label: 'Khoảng giá trị', value: EAttributeValueType.RANGE },
  { label: 'Giá trị chính xác', value: EAttributeValueType.EXACT },
];

interface PriceCoefficientFormProps {
  onFinish: (values: CreatePriceCoefficientFormValues) => void;
  initialValues?: IPriceCoefficients;
  form: FormInstance<CreatePriceCoefficientFormValues>;
}

interface IPropertyAttributeOption extends IPropertyAttribute {
  label: string | JSX.Element | undefined;
  value: string | undefined;
}

const initialValues = {
  attributeCombinations: [
    {
      attributeId: undefined,
      value: {
        type: undefined,
        data: undefined,
      },
    },
  ],
};

export default function PriceCoefficientForm({
  form,
  initialValues: initialValuesProp,
  onFinish,
}: PriceCoefficientFormProps) {
  const projectId = useWatch('projectId', form);
  const saleProgramId = useWatch('saleProgramId', form);
  const [attributes, setAttributes] = useState<IPropertyAttributeOption[]>([]);

  const handleProjectSelect = useCallback(
    (value: OptionTypeSelect) => {
      form.setFieldsValue({
        projectId: value?.value as string,
        saleProgramId: undefined,
      });
    },
    [form],
  );

  const handleSaleProgramSelect = useCallback(
    (value: OptionTypeSelect) => {
      form.setFieldsValue({
        saleProgramId: value?.value as string,
      });
    },
    [form],
  );

  const handleAttributeSelect = useCallback(
    (fieldName: number, value: OptionTypeSelect) => {
      form.setFieldValue(['attributeCombinations', fieldName, 'attributeId'], value?.value);
      // Clear dependent fields when attribute changes
      form.setFieldValue(['attributeCombinations', fieldName, 'value', 'type'], undefined);
      form.setFieldValue(['attributeCombinations', fieldName, 'value', 'data'], undefined);
    },
    [form],
  );

  const handleValueTypeChange = useCallback(
    (fieldName: number, value: 'range' | 'exact') => {
      form.setFieldValue(['attributeCombinations', fieldName, 'value', 'type'], value);
      // Clear data field when type changes
      form.setFieldValue(['attributeCombinations', fieldName, 'value', 'data'], undefined);
    },
    [form],
  );

  const columns = useMemo(
    () => [
      {
        title: 'Hệ số',
        dataIndex: 'name',
        key: 'name',
        width: '30%',
        render: () => (
          <Form.Item name="name" rules={[{ required: true, message: 'Vui lòng nhập tên hệ số' }]}>
            <Input placeholder="Nhập tên hệ số" maxLength={50} />
          </Form.Item>
        ),
      },
      {
        title: 'Hệ số %',
        dataIndex: 'coefficient',
        key: 'coefficient',
        width: 80,
        render: () => (
          <Form.Item
            name="coefficient"
            className="price-coefficient__input-item"
            rules={[{ required: true, message: 'Nhập hệ số' }]}
          >
            <InputNumber placeholder="Hệ số" className="price-coefficient__input" min={0} />
          </Form.Item>
        ),
      },
      {
        title: 'Thuộc tính',
        dataIndex: 'attribute',
        key: 'attribute',
        render: () => (
          <Form.List name="attributeCombinations">
            {(fields, { add, remove }) => (
              <>
                <Button
                  type="dashed"
                  onClick={() => {
                    const newAttribute = {
                      attributeId: undefined,
                      value: {
                        type: undefined,
                        data: undefined,
                      },
                    };
                    add(newAttribute);
                  }}
                  className="price-coefficient__add-btn"
                  style={{ marginBottom: fields.length ? 12 : 0 }}
                >
                  + Thêm thuộc tính
                </Button>
                <div className="attribute__wrapper">
                  {fields.map((field: FormListFieldData, index) => {
                    const errors = form.getFieldError(['attributeCombinations', field.name]);

                    return (
                      <Form.Item
                        key={field.key}
                        style={{ marginBottom: 0 }}
                        help={
                          errors && errors.length > 0
                            ? errors.map((err, idx) => (
                                <div key={idx} style={{ color: '#ff4d4f' }}>
                                  {err}
                                </div>
                              ))
                            : null
                        }
                        validateStatus={errors && errors.length > 0 ? 'error' : ''}
                      >
                        <div className="price-coefficient__attribute-row">
                          <div className="price-coefficient__attribute-inputs-group">
                            <Row
                              gutter={12}
                              style={{
                                width: 413,
                              }}
                            >
                              <Col span={12}>
                                <Form.Item
                                  name={[field.name, 'attributeId']}
                                  style={{ marginBottom: 0 }}
                                  rules={[{ required: true, message: 'Vui lòng chọn thuộc tính' }]}
                                  dependencies={['projectId', 'saleProgramId']}
                                >
                                  <SingleSelectLazy
                                    apiQuery={getPropertyAttribute}
                                    queryKey={['property-attribute', projectId, saleProgramId]}
                                    moreParams={{ projectId, saleProgramId }}
                                    keysLabel={['name']}
                                    placeholder="Chọn thuộc tính"
                                    disabled={!projectId || !saleProgramId}
                                    getPopupContainer={() => document.body}
                                    onOptionsChange={options => {
                                      setAttributes(options as unknown as IPropertyAttributeOption[]);
                                    }}
                                    handleSelect={(value: OptionTypeSelect) => {
                                      handleAttributeSelect(field.name, value);
                                    }}
                                    defaultValues={{
                                      value: initialValuesProp?.attributeCombinations[index]?.attribute.id,
                                      label: initialValuesProp?.attributeCombinations[index]?.attribute.name,
                                    }}
                                  />
                                </Form.Item>
                              </Col>
                              <Col span={12}>
                                <Form.Item
                                  name={[field.name, 'value', 'type']}
                                  style={{ marginBottom: 0 }}
                                  rules={[{ required: true, message: 'Vui lòng chọn kiểu giá trị' }]}
                                >
                                  <Select
                                    className="price-coefficient__select"
                                    options={VALUE_TYPE_OPTIONS}
                                    placeholder="Kiểu giá trị"
                                    style={{ width: 180 }}
                                    onChange={value => {
                                      handleValueTypeChange(field.name, value);
                                    }}
                                  />
                                </Form.Item>
                              </Col>
                            </Row>
                            <Form.Item shouldUpdate noStyle>
                              {({ getFieldValue }) => {
                                const valueType = getFieldValue(['attributeCombinations', field.name, 'value', 'type']);
                                const selectedAttributeId = getFieldValue([
                                  'attributeCombinations',
                                  field.name,
                                  'attributeId',
                                ]);

                                if (valueType === 'range') {
                                  return (
                                    <Row gutter={18}>
                                      <Col span={12}>
                                        <Form.Item
                                          name={[field.name, 'value', 'data', 'min']}
                                          style={{ marginBottom: 0 }}
                                          rules={[{ required: true, message: 'Nhập giá trị min' }]}
                                        >
                                          <InputNumber
                                            className="price-coefficient__input price-coefficient__input--min"
                                            placeholder="55"
                                            suffix="m2"
                                            style={{ width: 92 }}
                                            min={0}
                                          />
                                        </Form.Item>
                                      </Col>
                                      <Col span={12}>
                                        <Form.Item
                                          name={[field.name, 'value', 'data', 'max']}
                                          style={{ marginBottom: 0 }}
                                          dependencies={[['attributeCombinations', field.name, 'value', 'data', 'min']]}
                                          rules={[
                                            { required: true, message: 'Nhập giá trị max' },
                                            ({ getFieldValue }) => ({
                                              validator(_, value) {
                                                const minValue = getFieldValue([
                                                  'attributeCombinations',
                                                  field.name,
                                                  'value',
                                                  'data',
                                                  'min',
                                                ]);
                                                if (!value || !minValue || minValue < value) {
                                                  return Promise.resolve();
                                                }
                                                return Promise.reject(new Error('Max lớn hơn min'));
                                              },
                                            }),
                                          ]}
                                        >
                                          <InputNumber
                                            className="price-coefficient__input price-coefficient__input--max"
                                            placeholder="60"
                                            suffix="m2"
                                            style={{ width: 92 }}
                                            min={0}
                                          />
                                        </Form.Item>
                                      </Col>
                                    </Row>
                                  );
                                }

                                if (valueType === 'exact') {
                                  // Safe attribute lookup
                                  const selectedAttribute = attributes.find(
                                    attr => attr?.value === selectedAttributeId,
                                  );
                                  const allowedValues = selectedAttribute?.allowedValues || [];

                                  return (
                                    <Form.Item
                                      name={[field.name, 'value', 'data']}
                                      style={{ marginBottom: 0 }}
                                      rules={[{ required: true, message: 'Vui lòng chọn giá trị chính xác' }]}
                                    >
                                      <Select
                                        className="price-coefficient__select"
                                        options={allowedValues.map(v => ({
                                          value: v,
                                          label: v,
                                        }))}
                                        placeholder="Giá trị chính xác"
                                        style={{ width: 198 }}
                                      />
                                    </Form.Item>
                                  );
                                }
                                return null;
                              }}
                            </Form.Item>
                          </div>
                          <Button
                            type="text"
                            onClick={() => {
                              remove(field.name);
                            }}
                            style={{
                              color: '#00000073',
                            }}
                          >
                            ✕
                          </Button>
                        </div>
                      </Form.Item>
                    );
                  })}
                </div>
              </>
            )}
          </Form.List>
        ),
      },
    ],
    [form, projectId, saleProgramId, attributes, handleAttributeSelect, handleValueTypeChange],
  );

  const handleFinish = useCallback(
    (values: CreatePriceCoefficientFormValues) => {
      onFinish(values);
    },
    [onFinish],
  );

  const projectDefaultValue = useMemo(
    () => ({
      value: initialValuesProp?.project.id,
      label: initialValuesProp?.project.name,
    }),
    [initialValuesProp],
  );

  const saleProgramDefaultValue = useMemo(
    () => ({
      value: initialValuesProp?.saleProgram.id,
      label: initialValuesProp?.saleProgram.name,
    }),
    [initialValuesProp],
  );

  return (
    <Form<CreatePriceCoefficientFormValues>
      form={form}
      layout="vertical"
      initialValues={initialValuesProp ?? initialValues}
      onFinish={handleFinish}
      className="price-coefficient"
    >
      <div className="price-coefficient__info-section">
        <Title level={5} className="price-coefficient__info-title">
          Thông tin thuộc tính
        </Title>
        <Row gutter={32} className="price-coefficient__info-row">
          <Col span={8}>
            <Form.Item label="Dự án" name="projectId" rules={[{ required: true, message: 'Vui lòng chọn dự án' }]}>
              <SingleSelectLazy
                apiQuery={getListProject}
                queryKey={['project']}
                keysLabel={['name']}
                handleSelect={handleProjectSelect}
                placeholder="Chọn dự án"
                getPopupContainer={() => document.body}
                defaultValues={projectDefaultValue}
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="Chương trình bán hàng"
              name="saleProgramId"
              rules={[{ required: true, message: 'Vui lòng chọn chương trình bán hàng' }]}
            >
              <SingleSelectLazy
                moreParams={{ projectId }}
                disabled={!projectId}
                apiQuery={getListProjectSaleProgram}
                queryKey={['project-sale-program', projectId]}
                keysLabel={['name']}
                placeholder="Chọn chương trình bán hàng"
                handleSelect={handleSaleProgramSelect}
                getPopupContainer={() => document.body}
                defaultValues={saleProgramDefaultValue}
              />
            </Form.Item>
          </Col>
        </Row>
      </div>
      <div className="price-coefficient__info-section">
        <Title
          level={5}
          className="price-coefficient__info-title"
          style={{
            marginBottom: 16,
          }}
        >
          Bảng tham số
        </Title>
        <Table columns={columns} dataSource={[{ key: 'row' }]} pagination={false} showHeader footer={() => <></>} />
      </div>
    </Form>
  );
}
