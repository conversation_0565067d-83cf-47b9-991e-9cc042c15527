import DropdownFilterSearch from '../../../components/dropdown/dropdownFilterSearch';
import { Form, Typography } from 'antd';
import { useState, useEffect } from 'react';
import useFilter from '../../../hooks/filter';
import { getListProject, getListProjectSaleProgram } from '../../../service/project';
import { useWatch } from 'antd/es/form/Form';
import MultiSelectLazy from '../../../components/select/mutilSelectLazy';
import { Project } from '../../../types/project/project';
import { pickBy } from 'lodash';

const { Text } = Typography;

const FilterSearch = () => {
  const [form] = Form.useForm();
  const [isOpenFilter, setIsOpenFilter] = useState(false);
  const [filter, setFilter] = useFilter();

  useEffect(() => {
    if (isOpenFilter) {
      form.setFieldsValue({
        projectId: filter.projectId ? (Array.isArray(filter.projectId) ? filter.projectId : [filter.projectId]) : [],
        saleProgramId: filter.saleProgramId
          ? Array.isArray(filter.saleProgramId)
            ? filter.saleProgramId
            : [filter.saleProgramId]
          : [],
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleReset = () => {
    form.resetFields();
    setFilter({});
  };

  const handleOpenChange = (open: boolean) => {
    setIsOpenFilter(open);
  };

  const handleSubmit = (values: Record<string, unknown>) => {
    const result = pickBy(
      {
        ...filter,
        ...values,
        page: '1',
      },
      v => v != null,
    );

    setFilter(result);
    setIsOpenFilter(false);
  };

  const projectIds = useWatch('projectId', form);

  const extraFormItems = (
    <>
      <Form.Item label={<Text strong>Dự án</Text>} name="projectId">
        <MultiSelectLazy
          enabled={isOpenFilter}
          apiQuery={getListProject}
          queryKey={['project']}
          keysLabel={['name']}
          handleListSelect={(values: Project[]) => {
            form.setFieldsValue({ projectId: values.map(item => item.id).join(',') });
            form.resetFields(['saleProgramId']);
          }}
          placeholder="Chọn dự án"
          keysTag="name"
        />
      </Form.Item>
      <Form.Item label={<Text strong>Chương trình bán hàng</Text>} name="salesProgramId">
        <MultiSelectLazy
          disabled={!projectIds}
          moreParams={{ projectIds }}
          enabled={isOpenFilter}
          apiQuery={getListProjectSaleProgram}
          queryKey={['project-sale-program', projectIds]}
          keysLabel={'name'}
          handleListSelect={(values: unknown) => {
            const salesProgramId = (values as { value: string }[]).map(item => item.value).join(',');
            form.setFieldsValue({ salesProgramId });
          }}
          placeholder="Chọn chương trình bán hàng"
          keysTag={'name'}
        />
      </Form.Item>
    </>
  );

  return (
    <DropdownFilterSearch
      form={form}
      extraFormItems={extraFormItems}
      handleOpenChange={handleOpenChange}
      isOpenFilter={isOpenFilter}
      onClearFilters={handleReset}
      submitFilter={handleSubmit}
      placeholder="Tìm kiếm"
      defaultValueSearch={filter?.search}
    />
  );
};

export default FilterSearch;
