.price-coefficient {
  &__attribute-add {
    flex: 1;
    display: flex;
    align-items: center;
    margin-left: 0;
  }

  &__add-btn {
    margin-bottom: 12px;
    display: block;
  }

  &__attribute-row {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    gap: 8px;
    min-height: 40px;
  }

  &__attribute-inputs-group {
    display: flex;
    gap: 8px;
    flex: 1;
    align-items: flex-start;
  }

  .ant-input-number-suffix {
    color: #00000073 !important;
  }

  .price-coefficient__footer {
    height: 46px;
    background-color: #00000005;
    border-bottom: 1px solid #0000000f;
  }

  &__info-section {
    .ant-table-tbody td {
      vertical-align: top !important;
    }
  }

  .attribute__wrapper {
    max-height: 240px;
    overflow-y: scroll;
  }

  .ant-table-wrapper {
    width: 100%;
  }
}

.price-coefficient__modal {
  .attribute__wrapper {
    max-height: 150px;
  }
}
