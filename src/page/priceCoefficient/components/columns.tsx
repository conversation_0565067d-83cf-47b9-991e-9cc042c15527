import { Link } from 'react-router-dom';
import { PRICE_COEFFICIENT } from '../../../configs/path';
import { IPriceCoefficients } from '../../../types/priceCoefficient';
import { TableColumnsType } from 'antd';

export const columns: TableColumnsType<IPriceCoefficients> = [
  {
    title: 'Tên hệ số',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: 'CTBH',
    dataIndex: 'saleProgram',
    key: 'saleProgram',
    render: saleProgram => saleProgram.name,
  },
  {
    title: 'Dự án',
    dataIndex: 'project',
    key: 'project',
    render: project => project.name,
  },
  {
    title: '<PERSON>ệ số',
    dataIndex: 'coefficient',
    key: 'coefficient',
    align: 'right',
    width: 100,
  },
  {
    title: '',
    dataIndex: 'action',
    key: 'action',
    align: 'right',
    width: 100,
    render: (_, record) => <Link to={`${PRICE_COEFFICIENT}/${record.id}`}><PERSON>em chi tiết</Link>,
  },
];
