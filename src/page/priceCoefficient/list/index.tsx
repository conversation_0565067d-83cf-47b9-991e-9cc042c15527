import { useState } from 'react';
import { Button, Flex } from 'antd';
import FilterSearch from '../components/FilterSearch';
import { columns } from '../components/columns';
import BreadCrumbComponent from '../../../components/breadCrumb';
import TableComponent from '../../../components/table';
import CreatePriceCoefficientModal from '../components/CreatePriceCoefficientModal';
import { useFetch } from '../../../hooks';
import { getPriceCoefficients } from '../../../service/priceCoefficient';
import { IPriceCoefficients } from '../../../types/priceCoefficient';

export default function PriceCoefficientList() {
  const [openCreate, setOpenCreate] = useState(false);

  const { data: { data: { data: { rows: data = [] } = {} } = {} } = {}, isFetching } = useFetch<IPriceCoefficients[]>({
    api: getPriceCoefficients,
    queryKeyArrWithFilter: ['price-coefficients'],
  });

  const handleOpenModalCreate = () => {
    setOpenCreate(true);
  };

  const handleCloseModalCreate = () => {
    setOpenCreate(false);
  };

  return (
    <div className="wrapper-list">
      <BreadCrumbComponent />
      <div className="header-content">
        <FilterSearch />
        <Flex justify="end">
          <Button type="primary" onClick={handleOpenModalCreate}>
            Tạo mới hệ số
          </Button>
        </Flex>
      </div>

      <div className="table-list">
        <TableComponent
          loading={isFetching}
          queryKeyArr={['price-coefficients']}
          columns={columns}
          dataSource={data}
          rowKey="id"
        />
      </div>
      {openCreate && <CreatePriceCoefficientModal open={openCreate} onCancel={handleCloseModalCreate} />}
    </div>
  );
}
