import { TableColumnsType, Typography } from 'antd';
import dayjs from 'dayjs';
import { useNavigate } from 'react-router-dom';
import { ProjectSaleProgram } from '../../types/project/project';
import { useFetch } from '../../hooks';
import { getListProjectSaleProgram } from '../../service/project';
import TableComponent from '../../components/table';
import BreadCrumbComponent from '../../components/breadCrumb';
import FilterSellProgramManagement from './filterSellProgram';
import { FORMAT_DATE } from '../../constants/common';
import { OPERATE_SELL_PROGRAM, PROJECTS_MANAGEMENT } from '../../configs/path';
import { useSellProgramStore } from '../Store';

const { Text } = Typography;

const statusColors = {
  active: '#52C41A',
  inactive: '#FF4D4F',
};
const getStatusProjectSaleProgram = (status: string) => (
  <p style={{ color: status === '01' ? statusColors.active : statusColors.inactive }}>
    {status === '01' ? 'Đang hoạt động' : 'Ngưng hoạt động'}
  </p>
);

const SellProgramManagement = () => {
  const setSalesProgramIds = useSellProgramStore(state => state.ArrSetSalesProgramIds);
  const selectedProgramNames = useSellProgramStore(state => state.selectedProgramNames);

  const navigate = useNavigate();
  const { data, isLoading } = useFetch<ProjectSaleProgram[]>({
    queryKeyArrWithFilter: ['list-sale-program'],
    api: getListProjectSaleProgram,
  });
  const dataSaleProgram = (data?.data?.data?.rows || []).map(item => ({
    ...item,
    key: item.id,
  }));

  const columns: TableColumnsType<ProjectSaleProgram> = [
    {
      title: 'Tên CTBH',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Mã CTBH',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: 'Tên dự án',
      dataIndex: ['project', 'name'],
      key: 'name',
    },
    {
      title: 'Thời gian áp dụng',
      dataIndex: '',
      key: '',
      render: (_: string, record: ProjectSaleProgram) => (
        <>
          {record?.openingTimeSale && <Text>{dayjs(record.openingTimeSale).format(FORMAT_DATE)}</Text>}
          {record?.openingTimeSale && record?.endTimeSale && ' - '}
          {record?.endTimeSale && <Text>{dayjs(record.endTimeSale).format(FORMAT_DATE)}</Text>}
        </>
      ),
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      render: getStatusProjectSaleProgram,
      align: 'center',
    },
    {
      title: '',
      dataIndex: 'action',
      key: 'action',
      align: 'center',
      render: (_, record) => {
        return (
          <Typography.Link
            onClick={() => {
              setSalesProgramIds([record.id]);
              selectedProgramNames[0] = record.name;
              navigate(`${PROJECTS_MANAGEMENT}${OPERATE_SELL_PROGRAM}/${record.project?.id}`, {
                state: { from: '/sell-program-management' },
              });
            }}
          >
            Chi tiết
          </Typography.Link>
        );
      },
    },
  ];

  return (
    <>
      <BreadCrumbComponent />
      <div className="header-content">
        <FilterSellProgramManagement />
      </div>
      <TableComponent
        dataSource={dataSaleProgram}
        columns={columns}
        queryKeyArr={['list-sale-program']}
        loading={isLoading}
      />
    </>
  );
};

export default SellProgramManagement;
