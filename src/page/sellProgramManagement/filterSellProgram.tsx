import { Form, Row } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import { useLocation } from 'react-router-dom';
import MultiSelectLazy from '../../components/select/mutilSelectLazy';
import { getListProjectHistory } from '../../service/uploadHistory';
import DropdownFilterSearch from '../../components/dropdown/dropdownFilterSearch';
import useFilter from '../../hooks/filter';
import MultiSelectStatic from '../../components/select/mutilSelectStatic';
import { dataStatusSellProgram } from '../../constants/common';

type TFilter = {
  statuses?: string[];
  projectIds?: string[];
};

function FilterSellProgramManagement() {
  const { search } = useLocation();
  const [form] = Form.useForm();
  const params = useMemo(() => new URLSearchParams(search), [search]);
  const [filter, setFilter] = useFilter();

  const [initialValues, setInitialValues] = useState<TFilter>();
  const [isOpenFilter, setIsOpenFilter] = useState<boolean>(false);

  useEffect(() => {
    if (params) {
      setInitialValues({
        statuses: params.get('statuses') ? params.get('statuses')?.split(',') : [],
        projectIds: params.get('projectIds') ? params.get('projectIds')?.split(',') : [],
      });
    }
  }, [params]);

  const handleSubmitFilter = (values: TFilter) => {
    const newImportFilter: Record<string, string> = {
      projectIds: values?.projectIds?.join(',') || '',
      statuses: values?.statuses?.join(',') || '',
      page: '1',
    };
    setFilter({ ...filter, page: '1', ...newImportFilter });
    setIsOpenFilter(false);
  };
  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };
  const handleSelectStatus = (values: unknown) => {
    const newStatusFilter = (values as { value: string }[]).map(item => item.value);
    form.setFieldsValue({ statuses: newStatusFilter });
  };

  const handleSelectProject = (values: unknown) => {
    const newProjectFilter = (values as { value: string }[]).map(item => item.value);
    form.setFieldsValue({ projectIds: newProjectFilter });
  };

  return (
    <>
      <DropdownFilterSearch
        rootClassName="wrapper-filter-customers"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm theo mã, tên CTBH"
        showParams={true}
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        initialValues={initialValues}
        form={form}
        onClearFilters={() => {
          handleSelectProject([]);
          handleSelectStatus([]);
          const clearedValues = {
            status: [],
            projectIds: [],
          };

          setInitialValues(clearedValues);
          form.setFieldsValue(clearedValues);
        }}
        extraFormItems={
          <>
            <Form.Item label="Dự án" name="projectIds">
              <MultiSelectLazy
                enabled={isOpenFilter}
                queryKey={['get-project']}
                apiQuery={getListProjectHistory}
                keysLabel={['name']}
                keysTag={['name']}
                handleListSelect={handleSelectProject}
                placeholder="Chọn dự án"
              />
            </Form.Item>
            <Form.Item label="Trạng thái" name="statuses">
              <MultiSelectStatic
                data={dataStatusSellProgram}
                handleListSelect={handleSelectStatus}
                placeholder="Chọn trạng thái"
                keysTag={'label'}
              />
            </Form.Item>
            <Row gutter={16}></Row>
          </>
        }
      />
    </>
  );
}
export default FilterSellProgramManagement;
