import { Button, Flex, notification } from 'antd';
import BreadCrumbComponent from '../../../components/breadCrumb';
import TableComponent from '../../../components/table';
import { useFetch } from '../../../hooks';
import FilterSearch from './components/filterSearch';
import './styles.scss';
import { columns } from './columns';
import { exportEVoucherHistory, getListEVoucherHistory } from '../../../service/eVoucherHistory';
import { IEVoucher } from '../../../types/eVoucher';
import { useStoreEVoucherHistory } from '../store';
import { ExportOutlined } from '@ant-design/icons';
import { useMutation } from '@tanstack/react-query';
import { downloadArrayBufferFile } from '../../../utilities/shareFunc';

function ListEVoucherHistory() {
  const { getCurrentFilter } = useStoreEVoucherHistory();
  const {
    data: listEVoucherHistory,
    isLoading,
    isPlaceholderData,
    isFetching,
  } = useFetch<IEVoucher[]>({
    queryKeyArrWithFilter: ['get-list-e-voucher-history', getCurrentFilter()],
    api: getListEVoucherHistory,
    moreParams: {
      ...(getCurrentFilter() as Record<string, string | undefined>),
    },
  });

  const exportHistoryMutation = useMutation({
    mutationFn: () => exportEVoucherHistory(),
  });

  const handleSubmitExport = async () => {
    try {
      const response = await exportHistoryMutation.mutateAsync();
      downloadArrayBufferFile({ data: response.data, fileName: `Lich_su_E_VOUCHER.xlsx` });
    } catch (error) {
      notification.error({ message: 'Xuất dữ liệu thất bại.' });
    }
  };

  return (
    <div className="wrapper-list-e-voucher">
      <BreadCrumbComponent />
      <div className="header-content">
        <Flex gap={17}>
          <FilterSearch />
        </Flex>
        <Flex gap={10}>
          <Button type="default" onClick={handleSubmitExport}>
            <ExportOutlined />
            Xuất excel
          </Button>
        </Flex>
      </div>
      <div className="table-e-voucher">
        <TableComponent
          queryKeyArr={['get-list-e-voucher-history', getCurrentFilter()]}
          columns={columns}
          loading={isLoading || isPlaceholderData || isFetching}
          dataSource={listEVoucherHistory?.data?.data?.rows ?? []}
          rowKey="id"
        />
      </div>
    </div>
  );
}

export default ListEVoucherHistory;
