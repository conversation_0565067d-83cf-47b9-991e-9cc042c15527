import { TableColumnsType } from 'antd';
import dayjs from 'dayjs';
import { FORMAT_DATE_TIME, OPTIONS_HISTORY_EVOUCHER_STATUS } from '../../../constants/common';

export const columns: TableColumnsType = [
  {
    title: 'Người thực hiện',
    dataIndex: ['user', 'fullName'],
    width: '50%',
    key: 'userName',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Trạng thái',
    dataIndex: 'type',
    width: '50%',
    key: 'type',
    render: (value: string) => {
      const status = OPTIONS_HISTORY_EVOUCHER_STATUS?.find(i => i?.value === value)?.label;
      return status ? status : '-';
    },
  },
  {
    title: 'Thời gian',
    dataIndex: 'createdDate',
    key: 'createdDate',
    width: '50%',
    render: (value: string) => (value ? dayjs(value).format(FORMAT_DATE_TIME) : '-'),
  },
  {
    title: 'Mã',
    dataIndex: 'code',
    width: '45%',
    key: 'code',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Đối tác áp dụng',
    dataIndex: 'merchant',
    width: '45%',
    key: 'merchant',
    render: (value: string) => (value ? value : '-'),
  },
];
