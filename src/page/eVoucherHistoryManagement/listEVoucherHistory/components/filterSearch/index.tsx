import { Form, Select } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { useEffect, useMemo, useState } from 'react';
import { useLocation } from 'react-router-dom';
import DropdownFilterSearch from '../../../../../components/dropdown/dropdownFilterSearch';
import { DEFAULT_PARAMS, FORMAT_DATE_API, OPTIONS_HISTORY_EVOUCHER_STATUS } from '../../../../../constants/common';
import useFilter from '../../../../../hooks/filter';
import './styles.scss';
import { useStoreEVoucherHistory } from '../../../store';
import DatePickerFilter from '../../../../../components/datePicker/DatePickerFilter';

type TFilter = {
  type?: string | null;
  createdDate?: string | Dayjs | null;
  endedDate?: string | Dayjs | null;
};

function FilterSearch() {
  const [form] = Form.useForm();
  const [initialValues, setInitialValues] = useState<TFilter>();
  const [isOpenFilter, setIsOpenFilter] = useState(false);

  const [, setFilter] = useFilter();
  const { search } = useLocation();
  const params = useMemo(() => new URLSearchParams(search), [search]);

  const { setFilter: setFilterParam, getCurrentFilter } = useStoreEVoucherHistory();

  useEffect(() => {
    setInitialValues({
      createdDate: params.get('startDate') ? dayjs(params.get('startDate')) : undefined,
      endedDate: params.get('endedDate') ? dayjs(params.get('endedDate')) : undefined,
    });
  }, [params]);

  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };

  const handleSubmitFilter = (values: TFilter) => {
    const newFilter: Record<string, unknown> = {
      createdDate: values?.createdDate ? dayjs(values?.createdDate).format(FORMAT_DATE_API) : null,
      endedDate: values?.endedDate ? dayjs(values?.endedDate).format(FORMAT_DATE_API) : null,
      type: values?.type,
    };
    setFilterParam({ ...getCurrentFilter(), ...newFilter } as Record<string, unknown>);
    setFilter({ ...DEFAULT_PARAMS });
  };

  const handleSearch = (value: string) => {
    const search = value ? value : '';
    setFilter({ ...DEFAULT_PARAMS });
    setFilterParam({ ...getCurrentFilter(), search } as Record<string, unknown>);
  };

  const handleClearFilters = () => {
    setInitialValues(undefined);
    setFilter({ search: getCurrentFilter()?.search });
    setTimeout(() => {
      setIsOpenFilter(false);
    }, 100);
  };

  return (
    <>
      <DropdownFilterSearch
        rootClassName="wrapper-filter-customers"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        showParams={false}
        onChangeSearch={handleSearch}
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        initialValues={initialValues}
        form={form}
        onClearFilters={handleClearFilters}
        extraFormItems={
          <>
            <Form.Item label="Đối tác" name="createdBy">
              {/* <MultiSelectLazy
                enabled={isOpenFilter}
                apiQuery={getListEmployeeAll}
                queryKey={['employee-dropdown']}
                keysLabel={['name', 'username']}
                handleListSelect={handleSelectEmployee}
                placeholder="Chọn nhân viên"
                keysTag={'username'}
                // defaultValues={defaultEmployee}
              /> */}
            </Form.Item>
            <Form.Item label="Trạng thái" name="type">
              <Select placeholder="Chọn trạng thái" allowClear options={OPTIONS_HISTORY_EVOUCHER_STATUS} />
            </Form.Item>
            <DatePickerFilter startDate="createdDate" endDate="endedDate" />
          </>
        }
      />
    </>
  );
}

export default FilterSearch;
