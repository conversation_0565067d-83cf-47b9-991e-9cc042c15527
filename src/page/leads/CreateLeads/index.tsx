import { Form, Input, Modal } from 'antd';
import { DEFAULT_PARAMS, REGEX_PHONE_VN } from '../../../constants/common';
import { useCreateField } from '../../../hooks';
import { createLeadCommonAll } from '../../../service/lead';
import { TLeadCommon } from '../../../types/leadCommon';
import { handleKeyDownEnterNumber } from '../../../utilities/regex';
import GroupSelect from '../component/GroupSelect';
import './styles.scss';
import { validateEmail, validateProfileUrl } from '../../../utilities/shareFunc';
import { useQueryClient } from '@tanstack/react-query';

const { Item } = Form;
const { TextArea } = Input;

interface ICreateLeadsProps {
  open: boolean;
  onCancel: () => void;
}

const CreateLeads = (props: ICreateLeadsProps) => {
  const { open, onCancel } = props;
  const [form] = Form.useForm();
  const queryClient = useQueryClient();

  const { mutateAsync: createLead, isPending } = useCreateField({
    apiQuery: createLeadCommonAll,
    keyOfListQuery: ['list-lead-common'],
    isMessageError: false,
  });

  const handleSubmit = async (values: TLeadCommon) => {
    const newValues = {
      ...values,
      name: values?.name.trim(),
      phone: values?.phone.trim(),
      email: values?.email?.trim() || '',
      profileUrl: values.profileUrl?.trim() || '',
      note: values?.note?.trim() || '',
    };

    const res = await createLead(newValues);
    if (res?.data?.statusCode === '0') {
      form.resetFields();
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: [DEFAULT_PARAMS, 'list-lead-assigned'] });
        queryClient.invalidateQueries({ queryKey: ['list-lead-dashboard'] });
        queryClient.invalidateQueries({ queryKey: [DEFAULT_PARAMS, 'get-assign-leads'] });
      }, 20000);
      onCancel();
    }
  };

  return (
    <Modal
      title="Tạo mới lead"
      wrapClassName="wrapper-modal-lead-common"
      open={open}
      okButtonProps={{ loading: isPending }}
      onOk={() => form.submit()}
      onCancel={() => {
        onCancel();
        form.resetFields();
      }}
      width={650}
      destroyOnClose
    >
      <Form layout="horizontal" form={form} onFinish={handleSubmit} labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
        <GroupSelect enabled={open} required />
        <Item label="Tên Lead" name="name" rules={[{ required: true, message: 'Vui lòng nhập tên lead' }]}>
          <Input placeholder="Nhập tên lead" maxLength={60} />
        </Item>
        <Item
          label="Số điện thoại"
          name="phone"
          rules={[
            { required: true, message: 'Vui lòng nhập số điện thoại' },
            {
              pattern: REGEX_PHONE_VN,
              message: 'Số điện thoại phải bắt đầu bằng số 0 và từ 9 đến 15 chữ số',
            },
          ]}
        >
          <Input placeholder="Nhập số điện thoại" maxLength={15} onKeyDown={handleKeyDownEnterNumber} />
        </Item>
        <Item
          label="Email"
          name="email"
          rules={[
            {
              validator: (_, value) => validateEmail(value),
            },
          ]}
        >
          <Input placeholder="Nhập email" maxLength={25} />
        </Item>
        <Item
          label="Link profile"
          name="profileUrl"
          rules={[
            {
              validator: (_, value) => {
                const result = validateProfileUrl(value);
                return result.isValid ? Promise.resolve() : Promise.reject(result.message);
              },
            },
          ]}
        >
          <Input placeholder="Nhập link profile" maxLength={50} />
        </Item>
        <Item label="Ghi chú" name="note">
          <TextArea placeholder="Nhập ghi chú" rows={4} maxLength={500} />
        </Item>
      </Form>
    </Modal>
  );
};

export default CreateLeads;
