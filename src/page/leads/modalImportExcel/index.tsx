import { UploadOutlined } from '@ant-design/icons';
import { App, Button, Form, Modal, Typography, Upload } from 'antd';
import { RcFile } from 'antd/es/upload';
import { UploadFile } from 'antd/lib';
import { useState } from 'react';
import { useCreateField } from '../../../hooks';
import { createLeadCommonByImport } from '../../../service/lead';
import GroupSelect from '../component/GroupSelect';
import './styles.scss';

const LeadTemplate = '/assets//LeadTemplate.xlsx';
const { Text, Link } = Typography;
const { Item } = Form;

interface IModalImportExcel {
  open: boolean;
  onCancel: () => void;
}

const ModalImportExcel = (props: IModalImportExcel) => {
  const { open, onCancel } = props;

  const [form] = Form.useForm();
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const { notification } = App.useApp();

  const { mutateAsync: createLead, isPending } = useCreateField({
    apiQuery: createLeadCommonByImport,
    keyOfListQuery: ['list-lead-common'],
  });

  const handleImport = async () => {
    form.validateFields().then(async values => {
      const newValues = { ...values, files: values.files?.file };
      await createLead(newValues);
      form.resetFields();
      setFileList([]);
      onCancel();
    });
  };

  const handleRemoveFile = () => {
    setFileList([]);
    form.setFieldValue('files', null);
  };

  const handleBeforeUpload = async (file: RcFile) => {
    const isAllowedType = file?.name.toLowerCase().endsWith('.xlsx');
    if (!isAllowedType) {
      notification.error({ message: 'File không đúng định dạng. Vui lòng sử dụng file .xlsx' });
      return Upload.LIST_IGNORE;
    }
    setFileList([file]);
    return false;
  };

  return (
    <Modal
      className="modal-import-excel-leads"
      title="Nhập biểu mẫu Excel"
      open={open}
      onCancel={() => {
        onCancel();
        form.resetFields();
      }}
      onOk={handleImport}
      cancelButtonProps={{ style: { display: 'none' } }}
      okText="Import"
      okButtonProps={{ loading: isPending }}
      destroyOnClose
    >
      <Text>
        Upload file theo mẫu để nhập dữ liệu Lead vào hệ thống. Bạn có thể download file mẫu tại{' '}
        <Link download="LeadTemplate.xlsx" href={LeadTemplate} target="_blank">
          đây
        </Link>
        .
      </Text>
      <Form form={form} layout="vertical">
        <GroupSelect enabled={open} hiddenTVV />
        <Item
          name={'files'}
          required={false}
          rules={[
            {
              validator: () => {
                if (fileList.length === 0) {
                  return Promise.reject('Vui lòng nhập file template cần import');
                }
                return Promise.resolve();
              },
            },
          ]}
          label={'Tải lên file'}
        >
          <Upload
            maxCount={1}
            onRemove={handleRemoveFile}
            fileList={fileList}
            beforeUpload={handleBeforeUpload}
            name="file-upload"
          >
            <Button icon={<UploadOutlined />}>Upload</Button>
          </Upload>
        </Item>
      </Form>
    </Modal>
  );
};

export default ModalImportExcel;
