import { ImportOutlined } from '@ant-design/icons';
import { Button, Space, Tag, Tooltip, Typography } from 'antd';
import { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { useState } from 'react';
import BreadCrumbComponent from '../../components/breadCrumb';
import TableComponent from '../../components/table';
import { FORMAT_DATE_TIME } from '../../constants/common';
import { PERMISSION_LEAD } from '../../constants/permissions/lead';
import { useCheckPermissions, useFetch } from '../../hooks';
import { getListOfLeadCommonAll } from '../../service/lead';
import { TListOfLeads, TPosition, TRepo, TRepoConfig } from '../../types/leadCommon';
import CreateLeads from './CreateLeads';
import FilterLeads from './FilterLeads';
import ModalImportExcel from './modalImportExcel';
import './styles.scss';

const { Paragraph, Text } = Typography;

const ListOfLeadCommon = () => {
  const [openCreate, setOpenCreate] = useState(false);
  const [openModalImport, setOpenModalImport] = useState(false);
  const { create: isPermissionCreateByManually, import: isPermissionCreateByImport } =
    useCheckPermissions(PERMISSION_LEAD);

  const { data, isFetching } = useFetch<TListOfLeads[]>({
    api: getListOfLeadCommonAll,
    queryKeyArrWithFilter: ['list-lead-common'],
  });

  const dataLeads = data?.data?.data?.rows || [];

  const columns: ColumnsType<TListOfLeads> = [
    {
      title: 'Mã Lead',
      dataIndex: 'code',
      key: 'code',
      width: 180,
      render: (value: string, record: TListOfLeads) =>
        value ? (
          <div className="cell-name">
            {record.isHot ? <Tag color="red">Hot</Tag> : null}
            {value}
          </div>
        ) : (
          '-'
        ),
    },
    {
      title: 'Nguồn lead',
      dataIndex: 'source',
      key: 'source',
      align: 'center',
      width: 200,
      render: value => (value ? value : '-'),
    },
    {
      title: 'Kho cấu hình',
      dataIndex: 'repo',
      key: 'repo',
      width: 200,
      render: (value: TRepo) => (value?.name ? value?.name : '-'),
    },
    {
      title: 'Cấu hình phân bổ',
      dataIndex: ['repo', 'config'],
      key: 'configs',
      width: 200,
      render: (value: TRepoConfig) => (value?.name ? value?.name : '-'),
    },
    {
      title: 'Họ và tên',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: value => (value ? value : '-'),
    },
    {
      title: 'Số điện thoại',
      dataIndex: 'phone',
      key: 'phone',
      width: 150,
      render: value => (value ? value : '-'),
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      width: 200,
      render: value => (value ? value : '-'),
    },
    {
      title: 'Link profile',
      dataIndex: 'profileUrl',
      key: 'profileUrl',
      width: 200,
      render: value => (
        <Tooltip title={value}>
          <Paragraph ellipsis style={{ margin: 0 }}>
            <a href={value} target="_blank">
              {value}
            </a>
          </Paragraph>
        </Tooltip>
      ),
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdDate',
      key: 'createdDate',
      width: 180,
      render: (value: string, record: TListOfLeads) => (
        <>
          <Text>{value ? dayjs(value).format(FORMAT_DATE_TIME) : '-'}</Text> <br />
          <Text>{record?.createdByObj?.fullName ? record.createdByObj.fullName : '-'}</Text>
        </>
      ),
    },
    {
      title: 'Ghi chú',
      dataIndex: 'note',
      key: 'note',
      width: 200,
      render: value => (value ? value : '-'),
    },
    {
      title: 'Đơn vị tiếp nhận',
      dataIndex: 'pos',
      key: 'pos',
      width: 200,
      render: (value: TPosition) => (value?.name ? value?.name : '-'),
    },
  ];

  const handleOpenModalCreate = () => {
    setOpenCreate(true);
  };
  const handleCancel = () => {
    setOpenCreate(false);
  };
  const handleCancelImport = () => {
    setOpenModalImport(false);
  };

  return (
    <div className="list-lead-common">
      <BreadCrumbComponent />
      <div className="header-filter-lead-common">
        <FilterLeads />
        <Space size={16}>
          {isPermissionCreateByImport && (
            <Button type="default" icon={<ImportOutlined />} onClick={() => setOpenModalImport(true)}>
              Nhập biểu mẫu Excel
            </Button>
          )}
          {isPermissionCreateByManually && (
            <Button type="primary" onClick={handleOpenModalCreate}>
              Tạo mới Lead
            </Button>
          )}
        </Space>
      </div>
      <TableComponent
        columns={columns}
        loading={isFetching}
        queryKeyArr={['list-lead-common']}
        rowKey={'id'}
        dataSource={dataLeads}
      />
      <CreateLeads open={openCreate} onCancel={handleCancel} />
      <ModalImportExcel open={openModalImport} onCancel={handleCancelImport} />
    </div>
  );
};

export default ListOfLeadCommon;
