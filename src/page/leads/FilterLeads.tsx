import { Col, DatePicker, Form, Row, Select } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useMemo, useState } from 'react';
import { useLocation } from 'react-router-dom';
import DropdownFilterSearch from '../../components/dropdown/dropdownFilterSearch';
import SingleSelectLazy from '../../components/select/singleSelectLazy';
import { FORMAT_DATE, FORMAT_DATE_API, TYPE_LEAD_SOURCE } from '../../constants/common';
import { useFetch } from '../../hooks';
import useFilter from '../../hooks/filter';
import { getDetailOfLeadSource, getListLeadSource } from '../../service/leadSource';
import { ILead } from '../../types/lead';
import { TFilterLeadCommon } from '../../types/leadCommon';

const FilterLeads = () => {
  const { search } = useLocation();
  const [form] = Form.useForm();
  const params = useMemo(() => new URLSearchParams(search), [search]);
  const [filter, setFilter] = useFilter();
  const [initialValues, setInitialValues] = useState<TFilterLeadCommon>();
  const [isOpenFilter, setIsOpenFilter] = useState(false);
  const [defaultSource, setDefaultSource] = useState<
    { label: string | React.ReactElement; value: string } | undefined
  >();

  const { data: source } = useFetch<ILead>({
    api: () => getDetailOfLeadSource({ name: params.get('source') }),
    queryKeyArr: ['source'],
    withFilter: false,
    enabled: !!isOpenFilter,
  });

  const dataSource = source?.data?.data;

  useEffect(() => {
    if (params) {
      const initialValue = {
        createdFrom: params.get('createdFrom') ? dayjs(params.get('createdFrom')) : undefined,
        createdTo: params.get('createdTo') ? dayjs(params.get('createdTo')) : undefined,
        isHot: params.get('isHot') || undefined,
        source: params.get('source') || undefined,
      };
      setInitialValues(initialValue);
      form.setFieldsValue(initialValue);
    }
  }, [form, params]);

  useEffect(() => {
    if (dataSource?.name && dataSource?.id) {
      setDefaultSource({ label: dataSource.name, value: dataSource.id });
    }
  }, [dataSource]);

  const handleSubmitFilter = (values: TFilterLeadCommon) => {
    const newFilter: Record<string, unknown> = {
      createdFrom: values?.createdFrom ? dayjs(values?.createdFrom).format(FORMAT_DATE_API) : null,
      createdTo: values?.createdTo ? dayjs(values?.createdTo).format(FORMAT_DATE_API) : null,
      isHot: values?.isHot || null,
      source: values?.source || null,
    };
    setFilter({ ...filter, page: '1', ...newFilter });
    setIsOpenFilter(false);
  };

  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };

  const handleSelectLeadSource = (value: ILead) => {
    form.setFieldsValue({ source: value?.name, idSource: value?.id });
  };

  const handleClearFilters = () => {
    setInitialValues(undefined);
    setDefaultSource(undefined);
    setTimeout(() => {
      setIsOpenFilter(false);
    }, 100);
  };

  return (
    <>
      <DropdownFilterSearch
        rootClassName="wrapper-filter"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        showParams={true}
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        initialValues={initialValues}
        form={form}
        onClearFilters={handleClearFilters}
        extraFormItems={
          <>
            <Form.Item label="Nguồn Lead" name="source">
              <SingleSelectLazy
                apiQuery={getListLeadSource}
                queryKey={['list-source']}
                enabled={isOpenFilter}
                placeholder="Chọn nguồn lead"
                keysLabel={['name']}
                handleSelect={handleSelectLeadSource}
                defaultValues={defaultSource}
              />
            </Form.Item>
            <Form.Item label="Loại Lead" name="isHot">
              <Select placeholder="Chọn loại Lead" allowClear options={TYPE_LEAD_SOURCE} />
            </Form.Item>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="Từ ngày" name="createdFrom">
                  <DatePicker
                    format={FORMAT_DATE}
                    disabledDate={current => {
                      const createdTo = form.getFieldValue('createdTo');
                      return (
                        (current && current > dayjs().startOf('day')) ||
                        (createdTo && current > dayjs(createdTo).startOf('day')) // Disable ngày sau "Đến ngày"
                      );
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Đến ngày" name="createdTo">
                  <DatePicker
                    format={FORMAT_DATE}
                    disabledDate={current => {
                      const createdFrom = form.getFieldValue('createdFrom');
                      return (
                        (current && current > dayjs().startOf('day')) ||
                        (createdFrom && current < dayjs(createdFrom).startOf('day')) // Disable ngày trước "Ngày tạo"
                      );
                    }}
                  />
                </Form.Item>
              </Col>
            </Row>
          </>
        }
      />
    </>
  );
};

export default FilterLeads;
