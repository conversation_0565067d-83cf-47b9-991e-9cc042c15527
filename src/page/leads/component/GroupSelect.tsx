import { Form, Select, Spin } from 'antd';
import { useMemo, useState } from 'react';
import SingleSelectLazy from '../../../components/select/singleSelectLazy';
import { useFetch } from '../../../hooks';
import { getByIdOrgChartDropdown, getDetailOfLeadAll, getListOfLeadAll } from '../../../service/lead';
import { getListLeadSource } from '../../../service/leadSource';
import { IConfig, ILead, IOrgChart, TStaffIds, TUnits } from '../../../types/lead';
import { useQueries } from '@tanstack/react-query';

const { Item } = Form;

interface IGroupSelect {
  enabled: boolean;
  required?: boolean;
  hiddenTVV?: boolean;
}
const GroupSelect = (props: IGroupSelect) => {
  const { enabled, required, hiddenTVV } = props;
  const form = Form.useFormInstance();
  const [idLeadRepo, setIdLeadRepo] = useState('');
  const [idOrgCharts, setIdOrgCharts] = useState<string[]>();
  const [takeCare, setTakeCare] = useState<boolean>();
  const [listStaffIds, setListStaffIds] = useState<string[] | TStaffIds[]>();

  const { data: configsLeads, isFetching: isFetchingConfigs } = useFetch<ILead>({
    queryKeyArr: ['get-lead-repo', idLeadRepo || ''],
    api: getDetailOfLeadAll,
    enabled: !!idLeadRepo,
    withFilter: false,
    moreParams: { id: idLeadRepo },
  });

  const resultOrgCharts = useQueries({
    queries:
      idOrgCharts?.map(id => ({
        queryKey: ['orgCharts', id],
        queryFn: () => getByIdOrgChartDropdown({ id }),
        staleTime: Infinity,
      })) || [],
  });

  const isFetchingOrgCharts = resultOrgCharts?.some(result => result?.isFetching);

  const listStaff = resultOrgCharts
    .map(item => item.data?.data?.data?.flatMap((item: TUnits) => item?.staffIds))
    ?.flat()
    .filter(item => listStaffIds?.includes(item?.id));

  const activeConfigsLeads = useMemo(() => {
    return configsLeads?.data?.data?.configs?.filter(item => item?.active === 1) || [];
  }, [configsLeads]);

  const handleSelectLeadRepo = (value: ILead) => {
    setIdLeadRepo(value?.id);
    form.setFieldsValue({ repoId: value?.id });
    form.resetFields(['repoConfigCode']);
  };

  const handleChangeConfig = (_: unknown, options: IConfig | IConfig[]) => {
    const config = Array.isArray(options) ? options[0] : options;
    if (config?.deliverType === 1) {
      setIdOrgCharts(config?.orgChartIds);
      const mergedStaffIds = Array.from(
        new Set(config?.orgCharts?.flatMap((item: IOrgChart) => item.staffIds as TStaffIds[])),
      );
      setListStaffIds(mergedStaffIds);
      setTakeCare(true);
    } else {
      setTakeCare(false);
      setListStaffIds([]);
      setIdOrgCharts([]);
    }
    form.setFieldsValue({ repoConfigCode: config?.code });
    form.resetFields(['takecare']);
  };

  const handleSelectLeadSource = (value: ILead) => {
    form.setFieldsValue({ source: value?.name });
  };

  const handleChangeAssignee = (_: unknown, options: TStaffIds | TStaffIds[]) => {
    const staff = Array.isArray(options) ? options[0] : options;
    form.setFieldsValue({ assignee: staff?.code });
  };

  return (
    <>
      <Item
        label="Nguồn Lead"
        name="source"
        required={!!required}
        rules={[{ required: true, message: 'Vui lòng chọn nguồn lead' }]}
      >
        <SingleSelectLazy
          apiQuery={getListLeadSource}
          queryKey={['list-source']}
          enabled={enabled}
          placeholder="Chọn nguồn lead"
          keysLabel={['name']}
          handleSelect={handleSelectLeadSource}
        />
      </Item>
      <Item
        label="Kho cấu hình"
        name={'repoId'}
        required={!!required}
        rules={[{ required: true, message: 'Vui lòng chọn kho lead' }]}
      >
        <SingleSelectLazy
          apiQuery={getListOfLeadAll}
          queryKey={['list-lead-repo']}
          enabled={enabled}
          placeholder="Chọn kho lead"
          keysLabel={['name']}
          handleSelect={handleSelectLeadRepo}
        />
      </Item>
      <Item
        label="Cấu hình phân bổ"
        name={'repoConfigCode'}
        rules={idLeadRepo ? [{ required: true, message: 'Vui lòng chọn cấu hình phân bổ' }] : []}
        required={!!required}
      >
        <Select
          loading={isFetchingConfigs}
          disabled={!idLeadRepo}
          allowClear
          options={activeConfigsLeads?.map(item => ({ ...item, value: item?.code, label: item?.name }))}
          placeholder={'Chọn cấu hình'}
          dropdownRender={menu =>
            isFetchingConfigs ? (
              <div className="wrapper-spin" style={{ textAlign: 'center' }}>
                <Spin />
              </div>
            ) : (
              <div>{menu}</div>
            )
          }
          onChange={handleChangeConfig}
        />
      </Item>
      {takeCare && !hiddenTVV && (
        <Item label="Nhân viên tư vấn" name={'assignee'}>
          <Select
            allowClear
            loading={isFetchingOrgCharts}
            options={listStaff?.map((item: TStaffIds) => ({
              ...item,
              value: item?.id,
              label: `${item?.name + ' - ' + item?.email}`,
            }))}
            placeholder={'Chọn cấu hình'}
            dropdownRender={menu =>
              isFetchingOrgCharts ? (
                <div className="wrapper-spin" style={{ textAlign: 'center' }}>
                  <Spin />
                </div>
              ) : (
                <div>{menu}</div>
              )
            }
            onChange={handleChangeAssignee}
          />
        </Item>
      )}
    </>
  );
};

export default GroupSelect;
