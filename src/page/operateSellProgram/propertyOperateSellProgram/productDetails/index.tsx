import { Button, Col, Row, Select, Space, Spin, Timeline, Typography } from 'antd';
import BreadCrumbComponent from '../../../../components/breadCrumb';
import './styles.scss';
import { useFetch } from '../../../../hooks';
import { getDetailProductUnit } from '../../../../service/project';
import { useNavigate, useParams } from 'react-router-dom';
import FPTLogo from '../../../../assets/images/Default_Logo_Project.png';
import { formatNumber } from '../../../../utilities/regex';
import { getPrimaryStatusLabel } from '../../../../constants/common';
import { useState } from 'react';
import ButtonOfPageDetail from '../../../../components/button/buttonOfPageDetail';

const { Title, Text } = Typography;
const { Option } = Select;
interface IDetailProductUnit {
  histories: {
    modifiedDate: string | null;
    primaryStatus: string | null;
    actionName: string | null;
    posName: string | null;
    modifiedByName: string | null;
    reason: string | null;
    prevPosName: string | null;
  }[];
  contractPrice: string | number | undefined;
  priceAboveVat: string | number | undefined;
  priceAbove: string | number | undefined;
  priceVat: string | number | undefined;
  id: string;
  code: string;
  name: string;
  status: string;
  projectId: string;
  projectName: string;
  projectCode: string;
  productId: string;
  project: {
    id: string;
    name: string;
    imageUrl: string;
  };
  price: number;
}
const priorities = [
  {
    priority: 0,
    bookingTicketCode: null,
    escrowTicketCode: 'FRA-YCDC-00106',
    ticketType: 'YCDC',
    customerId: null,
    customerName: 'LÊ ANH MINH',
    employeeName: 'Giám Đốc Ihouzz',
    employeeId: '1b4a9ec5-c644-42de-9e51-b20f5b554c1b',
    posName: 'IHOUZZ',
    status: 'POS_CONFIRM_LOCK',
    id: 'a47d8fc2-6e40-4ea2-b81a-043e71ee611e',
  },
];

const ViewProductDetails = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [selectedId1, setSelectedId1] = useState<string | null>(null);
  const selectedData1 = priorities.find(p => p.id === selectedId1);

  const { data: dataDetailProductUnit, isLoading } = useFetch<IDetailProductUnit>({
    queryKeyArr: ['detail-product-unit', id],
    api: () => getDetailProductUnit(id),
    cacheTime: 1,
  });
  const handleCancel = () => {
    navigate(-1);
  };

  const detailProduct: IDetailProductUnit = dataDetailProductUnit?.data?.data || ({} as IDetailProductUnit);

  const timelineData =
    detailProduct.histories?.map(history => ({
      time: history.modifiedDate ? new Date(history.modifiedDate).toLocaleString() : '-',
      primaryStatus: history.primaryStatus || '',
      actionName: history.actionName || '',
      posName: history.posName || '',
      modifiedByName: history.modifiedByName || '',
      content: history.actionName || '',
      reason: history.reason || '',
      prevPosName: history.prevPosName || '',
    })) || [];

  return (
    <Spin spinning={isLoading}>
      <BreadCrumbComponent
        customItems={[
          {
            label: 'Quản lý dự án',
          },
          {
            label: 'Chi tiết dự án',
          },
          {
            label: `Chi tiết sản phẩm ${detailProduct.code}`,
          },
        ]}
        noMenu={true}
      />
      <div className="project-card">
        <div className="project-info">
          <Title level={5}>Sản phẩm {detailProduct.code}</Title>
          <Text type="secondary">
            Dự án: <span className="text-type">{detailProduct?.project?.name}</span>
          </Text>
        </div>
        <div className="button-actions-control-project">
          <Button size="small">Thu hồi sản phẩm</Button>
          <Button size="small">Chuyển sang đơn vị khác</Button>
          <Button size="small">Chuyển sang CTBH khác</Button>
        </div>
        <div className="project-image">
          <img
            src={
              detailProduct?.project?.imageUrl
                ? `${import.meta.env.VITE_S3_IMAGE_URL}/${detailProduct?.project?.imageUrl}`
                : FPTLogo
            }
            alt="Project"
          />
        </div>
      </div>
      <Row style={{ marginBottom: 100 }}>
        <Col xs={24} md={12} style={{ paddingRight: 60 }}>
          <Col xs={24} md={24}>
            <div className="project_detail-page__content-left">
              <div>
                <div className="project_detail-page__title">
                  <Title level={5}>Thông tin sản phẩm</Title>
                </div>
                <div className="project_detail-page__content">
                  <p>
                    <strong>Số sản phẩm</strong> <span>{detailProduct.code || '-'}</span>
                  </p>
                  <p>
                    <strong>Giá bán chưa VAT</strong> <span>{formatNumber(detailProduct.price) || '-'}</span>
                  </p>
                  <p>
                    <strong>Giá bán có VAT</strong> <span>{formatNumber(detailProduct.priceVat) || '-'}</span>
                  </p>
                  <p>
                    <strong>Đơn giá chưa VAT</strong> <span>{formatNumber(detailProduct.priceAbove) || '-'}</span>
                  </p>
                  <p>
                    <strong>Đơn giá có VAT</strong> <span>{formatNumber(detailProduct.priceAboveVat) || '-'}</span>
                  </p>
                  <p>
                    <strong>Tổng giá trị hợp đồng</strong>
                    <span>{formatNumber(detailProduct.contractPrice) || '-'}</span>
                  </p>
                </div>
              </div>
            </div>
          </Col>
          <Col xs={24} md={24}>
            <div className="project_detail-page__content-left">
              <div className="project_detail-page__title">
                <Title level={5}>Chọn loại ưu tiên</Title>
              </div>
              <Space direction="vertical" size={10} style={{ width: '100%' }}>
                <Row gutter={16} align="middle">
                  <Col span={4}>
                    <Text strong>Ưu tiên 1</Text>
                  </Col>
                  <Col span={20}>
                    <Select
                      allowClear
                      style={{ width: '100%' }}
                      placeholder="Chọn ưu tiên 1"
                      onChange={value => setSelectedId1(value)}
                    >
                      {priorities.map(item => (
                        <Option key={item.id} value={item.id}>
                          {item.escrowTicketCode} ({item.ticketType})
                        </Option>
                      ))}
                    </Select>
                  </Col>
                </Row>
                {selectedData1 && (
                  <Row style={{ paddingLeft: '19%' }}>
                    <Col>
                      <div className="info-customer-priority">
                        <div>
                          <Text type="secondary">Khách hàng:</Text>
                          <Text className="text-right">{selectedData1.customerName}</Text>
                        </div>
                        <div>
                          <Text type="secondary">Tư vấn viên:</Text>
                          <Text className="text-right">{selectedData1.employeeName}</Text>
                        </div>
                        <div>
                          <Text type="secondary">ĐVBH:</Text>{' '}
                          <Text className="text-right">{selectedData1.posName}</Text>
                        </div>
                      </div>
                    </Col>
                  </Row>
                )}
                <Row gutter={16} align="middle">
                  <Col span={4}>
                    <Text strong>Ưu tiên 2</Text>
                  </Col>
                  <Col span={20}>
                    <Select style={{ width: '100%' }} />
                  </Col>
                </Row>

                <Row gutter={16} align="middle">
                  <Col span={4}>
                    <Text strong>Ưu tiên 3</Text>
                  </Col>
                  <Col span={20}>
                    <Select style={{ width: '100%' }} />
                  </Col>
                </Row>
              </Space>
            </div>
          </Col>
        </Col>
        <Col xs={24} md={12} style={{ paddingLeft: 60, height: '500' }}>
          <div className="project_detail-page__content-right">
            <div className="project_detail-page__title">
              <Title level={5}>Lịch sử</Title>
            </div>
            <Timeline
              style={{ height: '500px', overflowY: 'auto', padding: '20px 0' }}
              items={timelineData.map(item => ({
                children: (
                  <div>
                    <div className="text-history-time">{item.time}</div>
                    <div className="text-history">Trạng thái: {getPrimaryStatusLabel(item.primaryStatus)}</div>
                    <div className="text-history">Hành động: {item.actionName}</div>
                    <div className="text-history">Đơn vị giữ sản phẩm: {item.prevPosName}</div>
                    <div className="text-history">Người thực hiện: {item.modifiedByName}</div>
                    <div className="text-history">Đơn vị chuyển tới: {item.posName}</div>
                    <div className="text-history">Ghi chú: {item.reason}</div>
                  </div>
                ),
                color: 'blue',
              }))}
            />
          </div>
        </Col>
      </Row>
      <ButtonOfPageDetail handleCancel={handleCancel} isShowModal={false} handleSubmit={() => {}} />
    </Spin>
  );
};

export default ViewProductDetails;
