.project-card {
  .button-actions-control-project {
    display: flex;
    gap: 16px;
  }
}
@media (max-width: 425px) {
  .button-actions-control-project {
    margin-top: 12px;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  .project-card {
    display: flex;
    flex-direction: column;
    gap: 16px;
    align-items: center;
  }
}

.project_detail-page__content-left,
.project_detail-page__content-right {
  margin-top: 60px;

  .project_detail-page__title {
    margin-bottom: 24px;
  }

  .project_detail-page__content {
    p {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      font-size: 14px;
      text-align: left;
      width: 100%;

      strong {
        flex: 0 0 200px;
        color: #00000073;
        font-weight: 400;
      }

      span {
        flex: 1;
        font-weight: 600;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

.text-history-time,
.text-history {
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
}

.text-history-time {
  color: #00000073;
}

.text-history {
  color: #000000e0;
}
.project_detail-page__content-left {
  .info-customer-priority {
    div {
      display: flex;
      align-items: center;
      font-size: 14px;
      text-align: left;
      width: 100%;

      .ant-typography {
        flex: 0 0 120px;
        color: #00000073;
        font-weight: 400;
      }

      .text-right {
        color: #000000e0;
        flex: 1;
        font-weight: 400;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
}
