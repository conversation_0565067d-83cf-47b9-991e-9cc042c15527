import { Form, FormInstance } from 'antd';
import { useEffect, useState } from 'react';
import { getBlockBySalesProgramIds } from '../../../service/project';
import DropdownFilterSearch from '../../../components/dropdown/dropdownFilterSearch';
import MultiSelectLazy from '../../../components/select/mutilSelectLazy';
import { useSellProgramStore } from '../../Store';

type TFilter = {
  salesProgramIds?: string[];
};

function FilterRowTableOperate({ handleSubmit }: { handleSubmit: (values: Record<string, unknown>) => void }) {
  const [form] = Form.useForm();
  const [isOpenFilter, setIsOpenFilter] = useState(false);
  const [blockOptions, setBlockOptions] = useState<{ label: string; value: string }[]>([]);

  const salesProgramIds = useSellProgramStore(state => state.ArrSalesProgramIds);

  const fetchBlocks = async (
    salesProgramIds: string[],
    form: FormInstance,
    setBlockOptions: (options: { label: string; value: string }[]) => void,
  ) => {
    if (!salesProgramIds.length) {
      setBlockOptions([]);
      form.setFieldsValue({ block: [] });
      return;
    }

    try {
      const blocks = await getBlockBySalesProgramIds({ salesProgramIds });
      if (!blocks?.data?.data) return;

      const formattedBlocks = blocks.data.data.map((block: { id: string; block: string }) => ({
        label: block.block,
        value: block.id,
        block: block.block,
      }));

      setBlockOptions(formattedBlocks);
      form.setFieldsValue({ block: formattedBlocks.map((b: { value: unknown }) => b.value) });
    } catch (error) {
      console.error('Error fetching blocks by sales program ids:', error);
    }
  };
  useEffect(() => {
    fetchBlocks(salesProgramIds, form, setBlockOptions);
  }, [salesProgramIds, form]);

  const handleSubmitFilter = (values: TFilter) => {
    handleSubmit({ ...values });
    setIsOpenFilter(false);
  };

  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };

  const handleSelectBlock = (values: unknown[]) => {
    form.setFieldsValue({ block: values });
  };

  return (
    <>
      <DropdownFilterSearch
        onClearFilters={() => {
          form.resetFields(); // Xóa tất cả giá trị trong form
          setBlockOptions([]); // Xóa block đã chọn
        }}
        rootClassName="wrapper-filter-customers"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        showParams={true}
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        form={form}
        extraFormItems={
          <Form.Item
            label="Block"
            name="block"
            rules={[{ required: true, message: 'Vui lòng chọn ít nhất một block' }]}
          >
            <MultiSelectLazy
              enabled={isOpenFilter}
              moreParams={{ salesProgramIds }}
              apiQuery={getBlockBySalesProgramIds}
              queryKey={['getBlockBySalesProgramIds', salesProgramIds]}
              keysLabel={'block'}
              handleListSelect={handleSelectBlock}
              placeholder="Chọn block"
              keysTag={'block'}
              defaultValues={blockOptions}
              showSelectAll={true}
            />
          </Form.Item>
        }
      />
    </>
  );
}

export default FilterRowTableOperate;
