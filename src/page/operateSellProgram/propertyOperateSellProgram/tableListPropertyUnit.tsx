import { useNavigate, useParams } from 'react-router-dom';
import TableComponent from '../../../components/table';
import { IPropertyUnit } from '../../../types/project/project';

const style = {
  color: '#1677FF',
};
const linkStyle = {
  color: '#1677FF',
  cursor: 'pointer',
  textDecoration: 'underline',
  fontWeight: 'bold',
};
interface IPriority {
  priority: number;
  bookingTicketCode: string | null;
  escrowTicketCode: string;
  ticketType: string;
  customerId: string | null;
  customerName: string;
  employeeName: string;
  employeeId: string;
  posName: string;
  status: string;
  id: string;
}

interface IPropertyUnitWithPriorities extends IPropertyUnit {
  priorities?: IPriority[];
  id: string;
}

interface TableListPropertyUnit {
  data?: IPropertyUnitWithPriorities[];
}

const TableListPropertyUnit: React.FC<TableListPropertyUnit> = ({ data }) => {
  const { projectId } = useParams();
  const navigate = useNavigate();
  const getPriorityByOrder = (priorities: IPriority[] = [], order: number) => {
    return priorities.find(p => p.priority === order);
  };

  const countPriorityItems = (priorityOrder: number) => {
    if (!data) return 0;
    return data.filter(item => item.priorities?.some(p => p.priority === priorityOrder)).length;
  };

  const handleEscrowTicketClick = (ticketId: string) => {
    if (projectId && ticketId) {
      navigate(`/projects/operate-sell-program/${projectId}/${ticketId}`);
    }
  };

  return (
    <div>
      <TableComponent
        isPagination={false}
        queryKeyArr={[]}
        dataSource={data}
        columns={[
          {
            title: 'Thông tin sản phẩm',
            dataIndex: 'code',
            key: 'code',
            render: (text: string) => <span>{text}</span>,
          },
          {
            title: (
              <>
                Ưu tiên 1 <span>({countPriorityItems(1)})</span>
              </>
            ),
            dataIndex: 'priorities',
            key: 'priority1',
            render: (priorities: IPriority[]) => {
              const priority1 = getPriorityByOrder(priorities, 1);
              return (
                <div style={style}>
                  <span style={linkStyle} onClick={() => priority1?.id && handleEscrowTicketClick(priority1.id)}>
                    {priority1?.escrowTicketCode}
                  </span>
                </div>
              );
            },
          },
          {
            title: (
              <>
                Ưu tiên 2 <span>({countPriorityItems(2)})</span>
              </>
            ),
            dataIndex: 'priorities',
            key: 'priority2',
            render: (priorities: IPriority[]) => {
              const priority2 = getPriorityByOrder(priorities, 2);
              return (
                <div style={style}>
                  <span style={linkStyle} onClick={() => priority2?.id && handleEscrowTicketClick(priority2.id)}>
                    {priority2?.escrowTicketCode}
                  </span>
                </div>
              );
            },
          },
          {
            title: (
              <>
                Ưu tiên 3 <span>({countPriorityItems(3)})</span>
              </>
            ),
            dataIndex: 'priorities',
            key: 'priority3',
            render: (priorities: IPriority[]) => {
              const priority3 = getPriorityByOrder(priorities, 3);
              return (
                <div style={style}>
                  <span style={linkStyle} onClick={() => priority3?.id && handleEscrowTicketClick(priority3.id)}>
                    {priority3?.escrowTicketCode}
                  </span>
                </div>
              );
            },
          },
        ]}
      />
    </div>
  );
};

export default TableListPropertyUnit;
