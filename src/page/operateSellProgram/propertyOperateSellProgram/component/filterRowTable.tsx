import { Form, FormInstance } from 'antd';
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import DropdownFilterSearch from '../../../../components/dropdown/dropdownFilterSearch';
import MultiSelectLazy from '../../../../components/select/mutilSelectLazy';
import {
  getBlockBySalesProgramIds,
  getListProjectSaleProgram,
  getProjectSaleProgramById,
} from '../../../../service/project';
import { useQueries } from '@tanstack/react-query';
type TFilter = {
  salesProgramIds?: string[];
};

function FilterRowTable({
  handleSubmit,
  defaultSelectedIds,
}: {
  handleSubmit: (values: Record<string, unknown>) => void;
  defaultSelectedIds?: string[];
}) {
  const { id: projectId } = useParams<{ id: string }>();
  const [form] = Form.useForm();
  const [isOpenFilter, setIsOpenFilter] = useState(false);
  const [salesProgramsOptions, setSalesProgramsOptions] = useState<{ label: string; value: string; name: string }[]>(
    [],
  );
  const [blockOptions, setBlockOptions] = useState<{ label: string; value: string }[]>([]);

  const [salesProgramIds, setSalesProgramIds] = useState<string[]>([]);

  const queryResults = useQueries({
    queries: (defaultSelectedIds || []).map(id => ({
      queryKey: ['saleProgramById', id],
      queryFn: () => getProjectSaleProgramById(id),
      enabled: !!id, // Chỉ chạy nếu có id
    })),
  });
  const salesProgramsSelected = queryResults
    .filter(query => query.data) // Chỉ lấy những query đã có dữ liệu
    .map(query => query.data);
  const salesProgramsSelectedString = JSON.stringify(salesProgramsSelected);

  useEffect(() => {
    if (defaultSelectedIds?.length) {
      form.setFieldsValue({ salesProgramIds: defaultSelectedIds });
    }
  }, [defaultSelectedIds, form]);

  useEffect(() => {
    if (!salesProgramsOptions.length) return;

    const salesProgramIds = salesProgramsOptions.map(item => item.value);
    setSalesProgramIds(salesProgramIds);
  }, [salesProgramsOptions]);
  const fetchBlocks = async (
    salesProgramIds: string[],
    form: FormInstance,
    setBlockOptions: (options: { label: string; value: string }[]) => void,
  ) => {
    if (!salesProgramIds.length) {
      setBlockOptions([]);
      form.setFieldsValue({ block: [] });
      return;
    }

    try {
      const blocks = await getBlockBySalesProgramIds({ salesProgramIds });
      if (!blocks?.data?.data) return;

      const formattedBlocks = blocks.data.data.map((block: { id: string; block: string }) => ({
        label: block.block,
        value: block.id,
        block: block.block,
      }));

      setBlockOptions(formattedBlocks);
      form.setFieldsValue({ block: formattedBlocks.map((b: { value: unknown }) => b.value) });
    } catch (error) {
      console.error('Error fetching blocks by sales program ids:', error);
    }
  };
  useEffect(() => {
    if (!salesProgramIds.length) {
      setBlockOptions([]); // Làm sạch danh sách block khi không có salesProgramIds
      form.setFieldsValue({ block: [] });
      return;
    }
    fetchBlocks(salesProgramIds, form, setBlockOptions);
  }, [salesProgramIds, form]);

  const handleSubmitFilter = (values: TFilter) => {
    if (!values?.salesProgramIds) {
      return;
    }
    handleSubmit({ ...values });
    setIsOpenFilter(false);
  };

  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };

  const handleSelectIdSaleProgram = (values: unknown) => {
    const salesProgramIds = (values as { value: string }[]).map(item => item.value);
    form.setFieldsValue({ salesProgramIds });
    setSalesProgramIds(salesProgramIds); // Cập nhật salesProgramIds khi chọn chương trình bán hàng
  };
  const handleSelectBlock = (values: unknown[]) => {
    form.setFieldsValue({ block: values });
  };

  useEffect(() => {
    const convertData = JSON.parse(salesProgramsSelectedString);
    const options = convertData.map((item: { data: { data: { name: string; id: string } } }) => ({
      label: item?.data.data.name as string,
      value: item?.data.data.id as string,
      name: item?.data.data.name as string,
    }));
    setSalesProgramsOptions(options);
  }, [salesProgramsSelectedString]);

  return (
    <>
      <DropdownFilterSearch
        onClearFilters={() => {
          form.resetFields(); // Xóa tất cả giá trị trong form
          setSalesProgramsOptions([]); // Xóa danh sách đã chọn
          setBlockOptions([]); // Xóa block đã chọn
          setSalesProgramIds([]);
        }}
        rootClassName="wrapper-filter-customers"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        showParams={true}
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        form={form}
        extraFormItems={
          <>
            <Form.Item
              label="Chương trình bán hàng"
              name="salesProgramIds"
              rules={[{ required: true, message: 'Vui lòng chọn ít nhất một chương trình bán hàng' }]}
            >
              <MultiSelectLazy
                moreParams={{ projectId }}
                enabled={isOpenFilter}
                apiQuery={getListProjectSaleProgram}
                queryKey={['project-sale-program', projectId]}
                keysLabel={'name'}
                handleListSelect={handleSelectIdSaleProgram}
                placeholder="Chọn chương trình bán hàng"
                keysTag={'name'}
                defaultValues={salesProgramsOptions}
                showSelectAll={true}
              />
            </Form.Item>
            <Form.Item
              label="Block"
              name="block"
              rules={[{ required: true, message: 'Vui lòng chọn ít nhất một block' }]}
            >
              <MultiSelectLazy
                enabled={isOpenFilter}
                moreParams={{ salesProgramIds }}
                apiQuery={getBlockBySalesProgramIds}
                queryKey={['getBlockBySalesProgramIds', salesProgramIds]}
                keysLabel={'block'}
                handleListSelect={handleSelectBlock}
                placeholder="Chọn block"
                keysTag={'block'}
                defaultValues={blockOptions}
                showSelectAll={true}
              />
            </Form.Item>
          </>
        }
      />
    </>
  );
}

export default FilterRowTable;
