.product-detail-modal {
  .ant-modal-content {
    border-radius: 2px;
  }
  :where(.ant-modal-content) {
    padding: 0 !important;
  }

  .ant-modal-header {
    border-bottom: 1px solid #eaeaea;
    padding: 20px 24px;
  }

  .ant-modal-body {
    padding: 30px 20px;

    p {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      font-size: 14px;
      text-align: left;
      width: 100%;

      strong {
        flex: 0 0 200px;
        color: #00000073;
        font-weight: 400;
      }

      span {
        flex: 1;
        font-weight: 600;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}
