import React, { useState } from 'react';
import 'antd/dist/reset.css';
import './styled.scss';
import { IPropertyUnit } from '../../../../types/project/project';
import { useLocation, useNavigate } from 'react-router-dom';
import { PRODUCT_DETAIL, PROJECTS_MANAGEMENT } from '../../../../configs/path';
import { Modal, Tooltip } from 'antd';
import { formatBigNumber } from '../../../../utilities/shareFunc';
import PropertyTable from '../../../../components/propertyTable';
interface RoomTableProps {
  listBlocks: {
    block: string;
    floors: string[];
    rooms: string[];
  }[];
  isPendingCreate?: boolean;
  listPropertyUnit: IPropertyUnit[];
  isLoadingDataTable?: boolean;
}

const CustomTable: React.FC<RoomTableProps> = ({
  listBlocks,
  listPropertyUnit,
  isPendingCreate,
  isLoadingDataTable,
}) => {
  const [selectedProduct, setSelectedProduct] = useState<IPropertyUnit | null>(null);
  const navigate = useNavigate();
  const location = useLocation();
  const handleCellClick = (floor: string, room: string, block: string) => {
    const propertyUnit = listPropertyUnit.find(
      item => item.floor === floor && item.shortCode === room && item.block === block,
    );
    if (propertyUnit) {
      const currentUrl = location.pathname + location.search;
      const targetUrl = `${PROJECTS_MANAGEMENT}/${propertyUnit.project.id}?tab=5`;

      if (currentUrl === targetUrl) {
        setSelectedProduct(propertyUnit);
      } else {
        navigate(`${PROJECTS_MANAGEMENT}${PRODUCT_DETAIL}/${propertyUnit.id}`);
      }
    }
  };

  return (
    <>
      <PropertyTable
        listBlocks={listBlocks}
        listPropertyUnit={listPropertyUnit}
        onCellClick={handleCellClick}
        isPendingCreate={isPendingCreate}
        isLoadingDataTable={isLoadingDataTable}
      />
      <Modal
        className="product-detail-modal"
        open={!!selectedProduct}
        title={`Sản phẩm ${selectedProduct?.code}`}
        onCancel={() => setSelectedProduct(null)}
        footer={null}
        width={396}
      >
        {selectedProduct && (
          <div>
            <p>
              <strong>Số sản phẩm</strong>{' '}
              <Tooltip title={selectedProduct.code || '-'}>
                <span>{selectedProduct.code || '-'}</span>
              </Tooltip>
            </p>
            <p>
              <strong>Giá bán chưa VAT</strong>{' '}
              <Tooltip title={formatBigNumber(selectedProduct.price) || '-'}>
                <span>{formatBigNumber(selectedProduct.price) || '-'}</span>
              </Tooltip>
            </p>
            <p>
              <strong>Giá bán có VAT</strong>{' '}
              <Tooltip title={formatBigNumber(selectedProduct.priceVat) || '-'}>
                <span>{formatBigNumber(selectedProduct.priceVat) || '-'}</span>
              </Tooltip>
            </p>
            <p>
              <strong>Đơn giá chưa VAT</strong>{' '}
              <Tooltip title={formatBigNumber(selectedProduct.priceAbove) || '-'}>
                <span>{formatBigNumber(selectedProduct.priceAbove) || '-'}</span>
              </Tooltip>
            </p>
            <p>
              <strong>Đơn giá có VAT</strong>{' '}
              <Tooltip title={formatBigNumber(selectedProduct.priceAboveVat) || '-'}>
                <span>{formatBigNumber(selectedProduct.priceAboveVat) || '-'}</span>
              </Tooltip>
            </p>
            <p>
              <strong>Tổng giá trị hợp đồng</strong>{' '}
              <Tooltip title={formatBigNumber(selectedProduct.contractPrice) || '-'}>
                <span>{formatBigNumber(selectedProduct.contractPrice) || '-'}</span>
              </Tooltip>
            </p>
          </div>
        )}
      </Modal>
    </>
  );
};

export default CustomTable;
