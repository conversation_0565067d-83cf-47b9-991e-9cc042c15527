import { But<PERSON>, Col, Form, Modal, Row, Select, Table, Typography } from 'antd';
import { useCallback, useEffect, useState } from 'react';
import FilterAddProduct, { FilterValues } from './filterMoreProduct';
import TextArea from 'antd/es/input/TextArea';
import { ColumnsType } from 'antd/es/table';
import { ExclamationCircleFilled } from '@ant-design/icons';
import { useSellProgramStore } from '../../../Store';
import { useCreateField, useFetch } from '../../../../hooks';
import { getPropertyUnits, sendTransferProducts } from '../../../../service/projectSalesProgram';
import { getOrgChartSaleProgramId } from '../../../../service/project';
import ModalComponent from '../../../../components/modal';
import { useParams } from 'react-router-dom';

const { Title } = Typography;

interface Props {
  visible: boolean;
  onClose: () => void;
  productCode?: string;
}

interface UnitConversionData {
  key: string;
  productCode: string;
  currentUnit: string;
  productId: string;
  targetUnit: { id: string; nameVN: string; code: string } | null;
  orgChartTransfer: { id: string; nameVN: string }[];
  status: string;
}
type ApiCallFunction = (payload: {
  reason: string;
  units: {
    id: string;
    pos: {
      id: string;
      nameVN: string;
      code: string;
    };
  }[];
  idSaleProgram: string;
}) => Promise<unknown | undefined>;

const FormModalUnitConversion = ({ visible, onClose, productCode }: Props) => {
  const projectId = useParams<{ id: string }>().id;
  const selectedProgramNames = useSellProgramStore(state => state.selectedProgramNames);
  const [form] = Form.useForm();
  const [dataSource, setDataSource] = useState<UnitConversionData[]>([]);
  const [initialDataSource, setInitialDataSource] = useState<UnitConversionData[]>([]);
  const [dropdownOpen, setDropdownOpen] = useState<{ [key: string]: boolean }>({});
  const [highlightedValues, setHighlightedValues] = useState<{ [key: string]: string | null }>({});
  const [searchValue, setSearchValue] = useState<Record<string, string>>({});
  const [successMessage, setSuccessMessage] = useState<string>('Chuyển sản phẩm thành công');
  const salesProgramIds = useSellProgramStore(state => state.ArrSalesProgramIds);

  const sendTransferProductsToUnits = useCreateField({
    keyOfDetailQuery: ['getPropertyUnitByProjectIdAndSalesProgramIds', projectId, salesProgramIds],
    apiQuery: sendTransferProducts,
    isMessageError: false,
    messageSuccess: successMessage,
  });

  const { data: dataInternalOrgChart } = useFetch({
    queryKeyArrWithFilter: ['project-sale-program', projectId],
    api: getOrgChartSaleProgramId,
    moreParams: { idSaleProgram: salesProgramIds || '', pageSize: 1000 },
    cacheTime: 1,
    enabled: visible && !!salesProgramIds,
  });

  const dataInternalOrgChartList: Array<{ id: string; nameVN: string; code: string }> =
    dataInternalOrgChart?.data?.data?.rows && Array.isArray(dataInternalOrgChart.data.data?.rows)
      ? dataInternalOrgChart.data.data.rows.map(item => ({
          id: item.id,
          nameVN: item.nameVN,
          code: item.code,
        }))
      : [];

  const handleDeleteProduct = (key: string) => {
    const newData = dataSource.filter(item => item.key !== key);
    setDataSource(newData);
    form.setFieldsValue({
      [`targetUnit_${key}`]: undefined,
    });
  };

  const resetFormState = () => {
    setDataSource([]);
    setInitialDataSource([]);
    setDropdownOpen({});
    setHighlightedValues({});
    form.resetFields();
  };

  const handleSubmitGeneric = async (apiCall: ApiCallFunction, inEvent: boolean) => {
    try {
      const values = await form.validateFields();
      const payload = {
        reason: values.reason,
        units: dataSource.map(item => ({
          id: item.productId,
          pos: {
            id: item.targetUnit?.id || '',
            nameVN: item.targetUnit?.nameVN || '',
            code: item.targetUnit?.code || '',
          },
        })),
        idSaleProgram: salesProgramIds[0] || '',
        inEvent,
      };

      await apiCall(payload);
      onClose();
      setDataSource([]);
      setInitialDataSource([]);
      setDropdownOpen({});
      setHighlightedValues({});
      form.resetFields();
      resetFormState();
    } catch (error) {
      console.error('Lỗi khi gửi payload:', error);
    }
  };
  const handleSubmit = () => {
    setSuccessMessage('Chuyển sản phẩm thành công');
    handleSubmitGeneric(sendTransferProductsToUnits.mutateAsync, false);
  };

  const handleSubmitOpenForSale = () => {
    setSuccessMessage('Chuyển sản phẩm và mở bán thành công');
    handleSubmitGeneric(sendTransferProductsToUnits.mutateAsync, true);
  };

  const handleSubmitFilter = async (values: FilterValues) => {
    const formattedParams = {
      idSaleProgram: salesProgramIds[0] || '',
      internalOrgChartIds: Array.isArray(values.internalOrgChartIds)
        ? values.internalOrgChartIds.join(',')
        : values.internalOrgChartIds || '',
      statuses: Array.isArray(values.statues) ? values.statues.join(',') : values.statues || '',
      blocks: Array.isArray(values.blocks) ? values.blocks.join(',') : values.blocks || '',
      floors: Array.isArray(values.floors) ? values.floors.join(',') : values.floors || '',
      rooms: Array.isArray(values.rooms) ? values.rooms.join(',') : values.rooms || '',
    };
    const hasFilterValues = Object.values(formattedParams).some(value => value !== '' && value !== salesProgramIds[0]);

    if (!hasFilterValues) {
      return;
    }

    try {
      const response = await getPropertyUnits(formattedParams);
      const rows = Array.isArray(response.data.data) ? response.data.data : [];
      const formattedData = rows.map(
        (
          item: {
            id: string;
            code: string;
            pos?: { nameVN: string; id: string; code?: string };
            orgChartTransfer?: { id: string; nameVN: string }[];
            primaryStatus: string;
          },
          index: number,
        ) => ({
          key: index.toString(),
          productCode: item.code,
          currentUnit: item.pos?.nameVN || '',
          currentUnitId: item.pos?.id || '',
          targetUnit: null, // Khởi tạo targetUnit là null
          orgChartTransfer:
            item.orgChartTransfer?.map(org => ({
              id: org.id,
              nameVN: org.nameVN,
            })) || [],
          primaryStatus: item.primaryStatus,
          productId: item.id,
        }),
      );
      setDataSource(formattedData);
      setInitialDataSource(formattedData);
    } catch (error) {
      console.error('Lỗi khi gọi API:', error);
    }
  };

  const showConfirm = (onOk: () => void) => {
    Modal.confirm({
      title: 'Xác nhận chuyển và mở bán',
      icon: <ExclamationCircleFilled />,
      content: 'Hành động này sẽ chuyển sản phẩm và thay đổi trạng thái tất cả sản phẩm đã chọn sang mở bán',
      okText: 'Xác nhận',
      cancelText: 'Hủy',
      onOk,
    });
  };

  const handleCancel = useCallback(() => {
    const dataSourceChanged = JSON.stringify(dataSource) !== JSON.stringify(initialDataSource);
    const hasDataSource = dataSource.length > 0;

    if (dataSourceChanged || hasDataSource || form.isFieldsTouched()) {
      Modal.confirm({
        title: 'Xác nhận hủy',
        content: 'Dữ liệu đang chưa được lưu, bạn có chắc muốn hủy dữ liệu đang nhập không?',
        cancelText: 'Quay lại',
        onOk: () => {
          form.resetFields();
          setDataSource([]);
          setInitialDataSource([]);
          setDropdownOpen({});
          setHighlightedValues({});
          onClose();
        },
        okButtonProps: { type: 'default' },
        cancelButtonProps: { type: 'primary' },
      });
    } else {
      setInitialSearchValues(undefined);
      onClose();
    }
  }, [form, dataSource, initialDataSource, onClose]);

  const customComponents = {
    header: {
      row: (props: React.HTMLAttributes<HTMLTableRowElement>) => (
        <>
          <tr {...props} />
          <tr>
            <td colSpan={columns.length} className="ant-table-cell-total-product">
              <Typography.Text className="text-total-product">
                Tổng số sản phẩm đã chọn {dataSource.length}
              </Typography.Text>
            </td>
          </tr>
        </>
      ),
    },
  };

  const columns: ColumnsType<UnitConversionData> = [
    { title: 'Mã sản phẩm', dataIndex: 'productCode', key: 'productCode' },
    { title: 'Đơn vị đang sở hữu', dataIndex: 'currentUnit', key: 'currentUnit', width: 400 },
    {
      title: 'Đơn vị muốn chuyển tới',
      dataIndex: 'targetUnit',
      key: 'targetUnit',
      width: 400,
      render: (_, record) => (
        <Form.Item
          name={`targetUnit_${record.key}`}
          rules={[{ required: true, message: 'Vui lòng chọn đơn vị cần chuyển' }]}
        >
          <Select
            showSearch
            allowClear
            placeholder="Chọn đơn vị cần chuyển"
            style={{ width: '100%' }}
            open={dropdownOpen[record.key] ?? false}
            filterOption={false}
            onSearch={value => {
              setSearchValue(prev => ({ ...prev, [record.key]: value }));
            }}
            onDropdownVisibleChange={visible => {
              if (!visible) {
                setHighlightedValues(prev => ({ ...prev, [record.key]: null }));
                setSearchValue(prev => ({ ...prev, [record.key]: '' }));
              }
              setDropdownOpen(prev => ({ ...prev, [record.key]: visible }));
            }}
            onChange={value => {
              const selectedUnit = dataInternalOrgChartList.find(unit => unit.id === value);
              const newData = [...dataSource];
              const index = newData.findIndex(item => item.key === record.key);
              if (index !== -1 && selectedUnit) {
                newData[index].targetUnit = {
                  id: selectedUnit.id,
                  nameVN: selectedUnit.nameVN,
                  code: selectedUnit.code || '',
                };
                setDataSource(newData);
              }
              form.setFieldsValue({
                [`targetUnit_${record.key}`]: value,
              });
            }}
            dropdownRender={() => {
              const filteredUnits = dataInternalOrgChartList?.filter(unit =>
                unit.nameVN?.toLowerCase().includes((searchValue[record.key] || '').toLowerCase()),
              );

              return (
                <div>
                  <div
                    style={{ maxHeight: 200, overflowY: 'auto' }}
                    onClick={e => {
                      e.preventDefault();
                      e.stopPropagation();
                    }}
                  >
                    {filteredUnits.map(unit => (
                      <div
                        key={unit.id}
                        style={{
                          padding: '8px 12px',
                          cursor: 'pointer',
                          backgroundColor: highlightedValues[record.key] === unit.id ? '#e6f7ff' : 'transparent',
                          fontWeight: highlightedValues[record.key] === unit.id ? '600' : 'normal',
                        }}
                        onClick={() => {
                          setHighlightedValues(prev => ({
                            ...prev,
                            [record.key]: unit.id,
                          }));
                        }}
                      >
                        {unit.nameVN}
                      </div>
                    ))}
                  </div>
                  <div
                    className="dropdown-footer-buttons"
                    onClick={e => {
                      e.preventDefault();
                      e.stopPropagation();
                    }}
                  >
                    <Button
                      size="middle"
                      onClick={() => {
                        const highlightedValue = highlightedValues[record.key];
                        const selectedUnit = dataInternalOrgChartList.find(unit => unit.id === highlightedValue);
                        if (selectedUnit) {
                          const updatedData = dataSource.map(item => ({
                            ...item,
                            targetUnit: {
                              id: selectedUnit.id,
                              nameVN: selectedUnit.nameVN,
                              code: selectedUnit.code || '',
                            },
                          }));
                          setDataSource(updatedData);

                          const updatedFields = dataSource.reduce(
                            (acc, item) => ({
                              ...acc,
                              [`targetUnit_${item.key}`]: selectedUnit.nameVN,
                            }),
                            {},
                          );
                          form.setFieldsValue(updatedFields);

                          setDropdownOpen(prev => ({ ...prev, [record.key]: false }));
                          setHighlightedValues(prev => ({ ...prev, [record.key]: null }));
                        }
                      }}
                    >
                      Áp dụng cho tất cả sản phẩm
                    </Button>
                    <Button
                      type="primary"
                      size="middle"
                      onClick={() => {
                        const highlightedValue = highlightedValues[record.key];
                        const selectedUnit = dataInternalOrgChartList.find(unit => unit.id === highlightedValue);
                        if (selectedUnit) {
                          const newData = [...dataSource];
                          const index = newData.findIndex(item => item.key === record.key);
                          if (index !== -1) {
                            newData[index].targetUnit = {
                              id: selectedUnit.id,
                              nameVN: selectedUnit.nameVN,
                              code: selectedUnit.code || '',
                            };
                            setDataSource(newData);
                          }

                          form.setFieldsValue({
                            [`targetUnit_${record.key}`]: selectedUnit.nameVN,
                          });

                          setDropdownOpen(prev => ({ ...prev, [record.key]: false }));
                          setHighlightedValues(prev => ({ ...prev, [record.key]: null }));
                        }
                      }}
                    >
                      Chọn
                    </Button>
                  </div>
                </div>
              );
            }}
          />
        </Form.Item>
      ),
    },
    {
      title: 'Trạng thái',
      dataIndex: 'primaryStatus',
      key: 'primaryStatus',
      align: 'center',
      render: (value: string) => {
        const map = {
          LOCK: { label: 'Khóa đầu tư', color: '#FF4D4F' },
          CLOSE: { label: 'Chưa mở bán', color: '#1890FF' },
          DEFAULT: { label: 'Không xác định', color: '#8C8C8C' },
        };
        const { label, color } = map[value as keyof typeof map] || map.DEFAULT;
        return <Typography.Text style={{ color }}>{label}</Typography.Text>;
      },
    },
    {
      title: '',
      dataIndex: 'delete',
      key: 'delete',
      width: 25,
      render: (_, record) => (
        <Button type="text" style={{ color: '#FF4D4F' }} onClick={() => handleDeleteProduct(record.key)}>
          Xóa
        </Button>
      ),
    },
  ];

  const [initialSearchValues, setInitialSearchValues] = useState<FilterValues | undefined>();

  useEffect(() => {
    if (productCode) {
      const parts = productCode.split('-');
      if (parts.length >= 3) {
        const block = parts[0];
        const floor = parts[1];
        const room = parts[2];

        setInitialSearchValues({
          blocks: [block],
          floors: [`${block}-${floor}`],
          rooms: [`${block}-${room}`],
        });
      } else {
        setInitialSearchValues(undefined);
      }
    } else {
      setInitialSearchValues(undefined);
    }
  }, [productCode]);

  useEffect(() => {
    if (!visible) {
      setInitialSearchValues(undefined);
    }
  }, [visible]);

  return (
    <ModalComponent
      title={'Chuyển sản phẩm sang đơn vị khác'}
      open={visible}
      onCancel={handleCancel}
      destroyOnClose
      className="unit-conversion-modal"
      footer={
        <div className="modal-footer">
          <Button
            size="small"
            disabled={dataSource.length === 0}
            onClick={() => {
              showConfirm(() => {
                handleSubmitOpenForSale();
              });
            }}
          >
            Chuyển sản phẩm và mở bán
          </Button>
          <Button disabled={dataSource.length === 0} size="small" onClick={handleSubmit}>
            Chuyển sản phẩm
          </Button>
        </div>
      }
    >
      <Form form={form} layout="vertical" className="unit-conversion-modal">
        <Row gutter={[24, 24]}>
          <Col span={8}>
            <Row gutter={[24, 24]}>
              <Col span={24}>
                <Row gutter={[24, 20]} className="flex-column">
                  <Title level={5}>{selectedProgramNames}</Title>
                  <FilterAddProduct handleSubmit={handleSubmitFilter} initialSearchValues={initialSearchValues} />
                  <Form.Item
                    name="reason"
                    rules={[
                      {
                        required: true,
                        validator: (_, value) => {
                          if (!value || value.trim() === '') {
                            return Promise.reject(new Error('Vui lòng nhập lý do'));
                          }
                          return Promise.resolve();
                        },
                      },
                    ]}
                  >
                    <TextArea
                      onBlur={e => {
                        form.setFieldsValue({ reason: e.target.value.trim() });
                      }}
                      rows={4}
                      className="reason-textarea"
                      placeholder="Nhập lý do"
                      maxLength={255}
                    />
                  </Form.Item>
                </Row>
              </Col>
            </Row>
          </Col>
          <Table
            columns={columns}
            dataSource={dataSource}
            pagination={false}
            className="unit-conversion-table"
            components={customComponents}
          />
        </Row>
      </Form>
    </ModalComponent>
  );
};

export default FormModalUnitConversion;
