import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { Form } from 'antd';
import './styles.scss';
import {
  getBlockBySaleProgramId,
  getFloorAndRoomByBlockId,
  getListStatusSaleProgram,
  getOrgChartSaleProgramId,
} from '../../../../service/project';
import DropdownFilterSearch from '../../../../components/dropdown/dropdownFilterSearch';
import MultiSelectLazy from '../../../../components/select/mutilSelectLazy';
import MultiSelectStatic from '../../../../components/select/mutilSelectStatic';
import { useSellProgramStore } from '../../../Store';

export interface FilterValues {
  internalOrgChartIds?: string[];
  statues?: string[];
  blocks?: string[];
  floors?: string[];
  rooms?: string[];
  initialSearchValues?: FilterValues;
}
interface Block {
  id: string;
  block: string;
  [key: string]: unknown;
}

const FilterAddProduct = ({
  handleSubmit,
  initialSearchValues,
}: {
  handleSubmit: (values: FilterValues) => void;
  initialSearchValues?: FilterValues;
}) => {
  const { id: projectId } = useParams<{ id: string }>();
  const salesProgramIds = useSellProgramStore(state => state.ArrSalesProgramIds);

  const [form] = Form.useForm();
  const [isOpenFilter, setIsOpenFilter] = useState(false);
  const [floorOptions, setFloorOptions] = useState<{ value: string; label: string }[]>([]);
  const [roomOptions, setRoomOptions] = useState<{ value: string; label: string }[]>([]);
  const [blockMap, setBlockMap] = useState<Record<string, string>>({});

  const [selectedBlockOptions, setSelectedBlockOptions] = useState<
    Array<{
      option: any;
      value: string;
      label: string;
    }>
  >([]);

  useEffect(() => {
    if (initialSearchValues) {
      if (initialSearchValues.blocks?.length) {
        const fetchBlockData = async () => {
          try {
            const blocksResponse = await getBlockBySaleProgramId({
              idSaleProgram: salesProgramIds,
            });

            const blocksData = blocksResponse.data?.data || [];

            const blockItems = (blocksData as Block[]).filter((block: Block) =>
              initialSearchValues.blocks?.includes(block.block),
            );

            if (blockItems.length > 0) {
              const blockSelectItems = blockItems.map((block: Block) => ({
                option: block,
                value: block.id,
                label: block.block,
                block: block.block,
              }));
              setSelectedBlockOptions(blockSelectItems);

              await handleSelectBlockBySaleProgram(blockSelectItems);

              if (initialSearchValues.floors?.length) {
                form.setFieldsValue({
                  floors: initialSearchValues.floors,
                });
              }

              if (initialSearchValues.rooms?.length) {
                form.setFieldsValue({
                  rooms: initialSearchValues.rooms,
                });
              }
            }
          } catch (error) {
            console.error('Error fetching block data:', error);
          }
        };

        fetchBlockData();
      }
    }
  }, [initialSearchValues, salesProgramIds]);
  useEffect(() => {
    if (
      initialSearchValues?.blocks?.length &&
      initialSearchValues?.floors?.length &&
      initialSearchValues?.rooms?.length
    ) {
      setTimeout(() => {
        const currentValues = form.getFieldsValue();
        handleSubmitFilter(currentValues);
      }, 500);
    }
  }, [initialSearchValues, salesProgramIds]);

  const handleSubmitFilter = (values: FilterValues) => {
    const formattedValues = {
      ...values,
      blocks: values.blocks?.map(blockId => blockMap[blockId] || blockId),
      floors: values.floors?.map(floor => floor.split('-').pop()).filter((floor): floor is string => !!floor),
      rooms: values.rooms?.map(room => room.split('-').pop()).filter((room): room is string => !!room),
    };
    handleSubmit(formattedValues);
    setIsOpenFilter(false);
  };

  const handleSelectOrgChartIdSaleProgram = (values: { value: string }[]) => {
    form.setFieldsValue({ internalOrgChartIds: values.map(item => item.value) });
  };

  const handleSelectStatusTicket = (values: { value: string }[]) => {
    form.setFieldsValue({ statues: values.map(item => item.value) });
  };

  const handleSelectBlockBySaleProgram = async (values: { option: Record<string, unknown>; value: string }[]) => {
    const newValues = values.map(item => item.value); // Block IDs
    const blockLabels: string[] = values.map(item => {
      const blockValue = item.option?.block;
      return typeof blockValue === 'string' ? blockValue : '';
    });

    form.setFieldsValue({
      blocks: newValues,
    });

    const newBlockMap = newValues.reduce(
      (acc, id, index) => {
        acc[id] = blockLabels[index];
        return acc;
      },
      {} as Record<string, string>,
    );
    setBlockMap(newBlockMap);

    if (newValues.length > 0) {
      try {
        const floorOptions: { value: string; label: string }[] = [];
        const roomOptions: { value: string; label: string }[] = [];

        for (const blockId of newValues) {
          const response = await getFloorAndRoomByBlockId({
            saleProgramId: salesProgramIds,
            blockIdsInput: blockId,
          });

          const blockName = newBlockMap[blockId];

          const floors = response.data.data.floors || [];
          const rooms = response.data.data.rooms || [];

          floors.forEach((floor: string) => {
            const floorValue = `${blockName}-${floor}`;
            floorOptions.push({
              value: floorValue,
              label: `Block ${blockName} - Tầng ${floor}`,
            });
          });

          rooms.forEach((room: string) => {
            const roomValue = `${blockName}-${room}`;
            roomOptions.push({
              value: roomValue,
              label: `Block ${blockName} - Phòng ${room}`,
            });
          });
        }

        setFloorOptions(floorOptions);
        setRoomOptions(roomOptions);

        // Lấy giá trị hiện tại của floors và rooms từ form
        const currentFloors = form.getFieldValue('floors') || [];
        const currentRooms = form.getFieldValue('rooms') || [];

        // Lọc lại để chỉ giữ các giá trị hợp lệ
        const validFloors = currentFloors.filter((floor: string) =>
          floorOptions.some(option => option.value === floor),
        );
        const validRooms = currentRooms.filter((room: string) => roomOptions.some(option => option.value === room));

        // Cập nhật form với các giá trị đã lọc
        form.setFieldsValue({
          blocks: newValues,
          floors: validFloors,
          rooms: validRooms,
        });
      } catch (error) {
        console.error('Error fetching floors and rooms:', error);
        setFloorOptions([]);
        setRoomOptions([]);
        form.setFieldsValue({
          blocks: newValues,
          floors: [],
          rooms: [],
        });
      }
    } else {
      setFloorOptions([]);
      setRoomOptions([]);
      setBlockMap({});
      form.setFieldsValue({
        blocks: [],
        floors: [],
        rooms: [],
      });
    }
  };

  const handleSelectFloors = (values: { value: string }[]) => {
    form.setFieldsValue({
      floors: values.map(item => item.value),
      rooms: [],
    });
  };

  const handleSelectRooms = (values: { value: string }[]) => {
    form.setFieldsValue({ rooms: values.map(item => item.value) });
  };

  const handleClearFilters = () => {
    setFloorOptions([]);
    setRoomOptions([]);
    setBlockMap({});
    form.resetFields();
  };

  return (
    <div className="display-icon-search">
      <DropdownFilterSearch
        rootClassName="filter-more-product"
        searchButtonText="Thêm sản phẩm"
        onClearFilters={handleClearFilters}
        placeholder="Lựa chọn lọc nâng cao"
        submitFilter={handleSubmitFilter}
        handleOpenChange={setIsOpenFilter}
        isOpenFilter={isOpenFilter}
        showParams={true}
        form={form}
        isReadOnly={true}
        extraFormItems={
          <>
            <Form.Item label="Đơn vị bán hàng" name="internalOrgChartIds">
              <MultiSelectLazy
                enabled={isOpenFilter}
                apiQuery={params =>
                  getOrgChartSaleProgramId({
                    idSaleProgram: salesProgramIds,
                    ...params,
                  })
                }
                queryKey={['project-sale-program', projectId]}
                keysLabel={['nameVN']}
                handleListSelect={handleSelectOrgChartIdSaleProgram}
                placeholder="Chọn đơn vị bán hàng"
                keysTag={['nameVN']}
              />
            </Form.Item>
            <Form.Item label="Trạng thái" name="statues">
              <MultiSelectLazy
                enabled={isOpenFilter}
                queryKey={['get-status-ticket']}
                apiQuery={getListStatusSaleProgram}
                keysLabel={['name']}
                keysTag={['name']}
                handleListSelect={handleSelectStatusTicket}
                placeholder="Chọn trạng thái"
              />
            </Form.Item>
            <Form.Item label="Block" name="blocks">
              <MultiSelectLazy
                enabled={isOpenFilter}
                apiQuery={params =>
                  getBlockBySaleProgramId({
                    idSaleProgram: salesProgramIds,
                    ...params,
                  })
                }
                queryKey={['org-block-by-sale-program', salesProgramIds]}
                keysLabel={['block']}
                handleListSelect={handleSelectBlockBySaleProgram}
                placeholder="Chọn block"
                keysTag={['block']}
                defaultValues={selectedBlockOptions}
              />
            </Form.Item>
            <Form.Item label="Tầng" name="floors">
              <MultiSelectStatic
                data={floorOptions}
                handleListSelect={handleSelectFloors}
                value={form.getFieldValue('floors')} // Đồng bộ với form
                placeholder="Chọn tầng"
                showSelectAll
                disabled={!form.getFieldValue('blocks')?.length}
                keysTag={['value']}
              />
            </Form.Item>
            <Form.Item label="Phòng" name="rooms">
              <MultiSelectStatic
                data={roomOptions}
                handleListSelect={handleSelectRooms}
                value={form.getFieldValue('rooms')} // Đồng bộ với form
                placeholder="Chọn phòng"
                showSelectAll
                disabled={!form.getFieldValue('blocks')?.length}
                keysTag={['value']}
              />
            </Form.Item>
          </>
        }
      />
    </div>
  );
};

export default FilterAddProduct;
