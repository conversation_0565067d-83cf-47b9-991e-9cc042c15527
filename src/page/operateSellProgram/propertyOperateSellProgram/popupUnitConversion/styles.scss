.display-icon-search {
  .ant-input-group-wrapper {
    .ant-input-wrapper {
      width: 385px !important;
      .ant-input-group-addon {
        display: none;
      }
    }
  }
}
.filter-more-product .wrapper-dropdown-content {
  width: 385px;
  overflow-y: auto;
  max-height: 600px;
}

.unit-conversion-table {
  .ant-table {
    width: 100%;
  }
}
.unit-conversion-modal {
  .flex-column {
    display: flex;
    flex-direction: column;
    .reason-textarea {
      width: 385px;
    }
  }
  .modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
  }
}
.dropdown-footer-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding: 12px 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
}
.ant-table-cell-total-product {
  padding: 12px 12px;
  font-weight: 400 !important;
  .text-total-product {
    float: left;
    font-size: 12px;
    line-height: 20px;
  }
}
