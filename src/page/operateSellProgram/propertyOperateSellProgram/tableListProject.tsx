import TableComponent from '../../../components/table';

type MockDataItem = {
  productCode: string;
  priority1?: string;
  priority2?: string;
  priority3?: string;
};

const mockData: MockDataItem[] = [
  {
    productCode: '2251085215',
    priority1: 'GSW-YCDCH-00002',
    priority2: 'GSW-YCDCH-00002',
    priority3: 'GSW-YCDCH-00002',
  },
  {
    productCode: '2251085216',
    priority1: 'GSW-YCDCH-00002',
    priority2: 'GSW-YCDCH-00002',
    priority3: 'GSW-YCDCH-00002',
  },
  {
    productCode: '2251085217',
    priority1: 'GSW-YCDCH-00002',
    priority2: 'GSW-YCDCH-00002',
    priority3: 'GSW-YCDCH-00002',
  },
  {
    productCode: '2251085218',
    priority1: 'GSW-YCDCH-00002',
    priority2: 'GSW-YCDCH-00002',
    priority3: 'GSW-YCDCH-00002',
  },
  {
    productCode: '2251085220',
    priority1: 'GSW-YCDCH-00002',
    priority2: 'GSW-YCDCH-00002',
    priority3: 'GSW-YCDCH-00002',
  },
  {
    productCode: '2251085221',
    priority1: 'GSW-YCDCH-00002',
    priority2: 'GSW-YCDCH-00002',
    priority3: '',
  },
  {
    productCode: '2251085221',
    priority1: 'GSW-YCDCH-00002',
    priority2: 'GSW-YCDCH-00002',
    priority3: '',
  },
  {
    productCode: '2251085221',
    priority1: 'GSW-YCDCH-00002',
    priority2: 'GSW-YCDCH-00002',
    priority3: '',
  },
  {
    productCode: '2251085221',
    priority2: 'GSW-YCDCH-00002',
    priority3: '',
  },
];
const style = {
  color: '#1677FF',
};
const countPriorityItems = (key: keyof MockDataItem) => mockData.filter(item => item[key]).length;

const TableListProject = () => {
  return (
    <div>
      <TableComponent
        queryKeyArr={[]}
        dataSource={mockData}
        columns={[
          {
            title: 'Thông tin sản phẩm',
            dataIndex: 'productCode',
            key: 'productCode',
            render: (text: string) => <span>Mã sản phẩm {text}</span>,
          },
          {
            title: (
              <>
                Ưu tiên 1 <span>({countPriorityItems('priority1')})</span>
              </>
            ),
            dataIndex: 'priority1',
            key: 'priority1',
            render: (text: string) => <span style={style}>{text}</span>,
          },
          {
            title: (
              <>
                Ưu tiên 2 <span>({countPriorityItems('priority2')})</span>
              </>
            ),
            dataIndex: 'priority2',
            key: 'priority2',
            render: (text: string) => <span style={style}>{text}</span>,
          },
          {
            title: (
              <>
                Ưu tiên 3 <span>({countPriorityItems('priority3')})</span>
              </>
            ),
            dataIndex: 'priority3',
            key: 'priority3',
            render: (text: string) => <span style={style}>{text}</span>,
          },
        ]}
      />
    </div>
  );
};
export default TableListProject;
