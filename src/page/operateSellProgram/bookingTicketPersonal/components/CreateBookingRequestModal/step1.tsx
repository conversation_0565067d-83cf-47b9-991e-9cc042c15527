import React, { useState } from 'react';
import { Form, Select, Input, DatePicker, Button, Row, Col, Modal } from 'antd';
import DuplicateModal from './step2';
import { DuplicateResp, FormVerify } from '../../../../../types/bookingRequest';
import ModalComponent from '../../../../../components/modal';
import { useFetch } from '../../../../../hooks';
import { getProvinces } from '../../../../../service/address';
import { getCustomerByIdentities } from '../../../../../service/bookingTicket';
import { AddressType } from '../../../../../components/selectAddress';

const { Option } = Select;

interface CreateBookingRequestStep1Props {
  visible: boolean;
  onCancel: (hasFormChanged?: boolean, resetForm?: () => void) => void;
  onNext: (values: FormVerify, duplicateData?: DuplicateResp | null) => void;
}

const CreateBookingRequestStep1: React.FC<CreateBookingRequestStep1Props> = ({ visible, onCancel, onNext }) => {
  const [form] = Form.useForm<FormVerify>();
  const [duplicateModalVisible, setDuplicateModalVisible] = useState<boolean>(false);
  const [duplicateData, setDuplicateData] = useState<DuplicateResp | null>(null);
  const [customerType, setCustomerType] = useState<string>('individual'); // Mặc định là individual

  const { data: dataProvinces } = useFetch<AddressType[]>({
    queryKeyArrWithFilter: ['get-list-identity'],
    api: getProvinces,
  });
  const provinces = dataProvinces?.data?.data;

  const checkDuplicateIdentity = async (
    type?: string,
    identityType?: string,
    identityNo?: string,
  ): Promise<DuplicateResp> => {
    try {
      // Kiểm tra KHCT (official-customer-personal)
      const customerResp = await getCustomerByIdentities({
        type: type,
        identityNo: identityNo,
        identityType: identityType,
      });
      const customerData = customerResp?.data?.data || [];
      if (customerData.length > 0) {
        return {
          isDuplicate: true,
          customerType: customerData[0]?.customerType,
          data: customerData.map((item: DuplicateResp) => ({
            ...item,
            type: type,
            customerType: item.customerType,
          })),
          type: type,
        };
      }

      // Kiểm tra KHTN (demand-customer-personal)
      const demandResp = await getCustomerByIdentities({
        type: type,
        identityNo: identityNo,
        identityType: identityType,
      });
      const demandData = demandResp?.data?.data || [];
      if (demandData.length > 0) {
        return {
          isDuplicate: true,
          customerType: demandData[0]?.customerType,
          data: demandData.map((item: DuplicateResp) => ({
            ...item,
            type: type,
            customerType: item?.customerType,
          })),
          type: type,
        };
      }

      return {
        isDuplicate: false,
        customerType: 'initial',
        type: type,
        data: [],
      };
    } catch (error) {
      return {
        isDuplicate: false,
        customerType: 'initial',
        type: type,
        data: [],
      };
    }
  };

  const handleNext = async () => {
    try {
      const values = await form.validateFields();
      const { type, identityType, identityNo, taxCode } = values;

      const duplicateCheck = await checkDuplicateIdentity(
        type,
        identityType,
        type === 'business' ? taxCode : identityNo,
      );
      console.log('Duplicate check result:', duplicateCheck);

      if (duplicateCheck.isDuplicate) {
        setDuplicateData(duplicateCheck);
        setDuplicateModalVisible(true);
      } else {
        onNext(values, duplicateCheck); // Không trùng, chuyển sang bước 3
      }
    } catch (errorInfo) {
      console.log('Validation failed:', errorInfo);
    }
  };

  const handleDuplicateConfirm = (selectedCustomer: any) => {
    setDuplicateModalVisible(false);
    const updatedDuplicateData = duplicateData
      ? { ...duplicateData, data: selectedCustomer ? [selectedCustomer] : duplicateData.data }
      : null;
    onNext(form.getFieldsValue(), updatedDuplicateData);
  };

  const handleDuplicateCancel = () => {
    Modal.confirm({
      title: 'Xác nhận hủy',
      content: 'Dữ liệu chưa được lưu, bạn có chắc chắn muốn thoát khỏi trang không?',
      cancelText: 'Quay lại',
      okText: 'Đồng ý',
      onOk: () => {
        setDuplicateModalVisible(false);
      },
      okButtonProps: {
        type: 'default',
      },
      cancelButtonProps: {
        type: 'primary',
      },
    });
  };

  const handleCancel = () => {
    onCancel(form.isFieldsTouched(), () => form.resetFields());
  };

  // Cập nhật identityType và reset các field liên quan khi type thay đổi
  const handleCustomerTypeChange = (value: string) => {
    setCustomerType(value);
    form.resetFields(['identityType', 'identityNo', 'taxCode', 'issueDate', 'issueLocation']);
  };

  return (
    <>
      <ModalComponent
        title="Tạo mới yêu cầu đặt chỗ 1/2"
        open={visible}
        onCancel={handleCancel}
        destroyOnClose
        footer={
          <Button type="primary" htmlType="submit" onClick={handleNext}>
            Tiếp tục
          </Button>
        }
      >
        <Form form={form} layout="vertical" initialValues={{ type: 'individual' }}>
          <Row gutter={16}>
            <Col span={6}>
              <Form.Item
                label="Loại khách hàng"
                name="type"
                rules={[{ required: true, message: 'Vui lòng chọn loại khách hàng' }]}
              >
                <Select placeholder="Cá nhân" onChange={handleCustomerTypeChange}>
                  <Option value="individual">Cá nhân</Option>
                  <Option value="business">Doanh nghiệp</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <p style={{ marginBottom: 8 }}>
            Giấy tờ xác minh<span style={{ color: 'red' }}>*</span>
          </p>
          <Row
            gutter={[8, 8]}
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.02)',
              border: '1px solid var(--colorBorder, rgba(0, 0, 0, 0.15))',
              padding: 20,
              margin: 0,
            }}
          >
            <Col span={6}>
              <Form.Item
                label="Loại giấy tờ"
                name="identityType"
                rules={[{ required: true, message: 'Vui lòng chọn loại giấy tờ' }]}
              >
                <Select placeholder="Chọn loại giấy tờ">
                  {customerType === 'individual' ? (
                    <>
                      <Option value="CCCD">Căn cước công dân</Option>
                      <Option value="CMND">CMT</Option>
                      <Option value="Hộ chiếu">Hộ chiếu</Option>
                    </>
                  ) : (
                    <Option value="MST">Mã số thuế</Option>
                  )}
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                label="Số giấy tờ"
                name={customerType === 'business' ? 'taxCode' : 'identityNo'}
                rules={[
                  {
                    required: true,
                    message: 'Vui lòng nhập số giấy tờ',
                  },
                ]}
              >
                <Input placeholder="Nhập số giấy tờ" maxLength={60} />
              </Form.Item>
            </Col>

            <Col span={6}>
              <Form.Item
                label="Ngày cấp"
                name="issueDate"
                rules={[{ required: true, message: 'Vui lòng chọn ngày cấp' }]}
              >
                <DatePicker placeholder="Chọn ngày cấp" format="DD/MM/YYYY" />
              </Form.Item>
            </Col>
            <Col span={6}>
              {customerType === 'individual' ? (
                <Form.Item
                  label="Nơi cấp"
                  name="issueLocation"
                  rules={[{ required: true, message: 'Vui lòng chọn nơi cấp' }]}
                >
                  <Select
                    filterOption={(input, option) =>
                      typeof option?.label === 'string'
                        ? option.label.toLowerCase().includes(input.toLowerCase())
                        : false
                    }
                    allowClear
                    options={provinces?.map(item => ({
                      value: item.code,
                      label: item.nameVN,
                    }))}
                    labelInValue
                    showSearch
                    placeholder="Chọn nơi cấp"
                    onChange={value => {
                      form.setFieldsValue({
                        issueLocation: value || undefined,
                      });
                    }}
                  />
                </Form.Item>
              ) : (
                <Form.Item
                  label="Nơi cấp"
                  name="issueLocation"
                  rules={[{ required: true, message: 'Vui lòng nhập nơi cấp' }]}
                >
                  <Input placeholder="Nhập nơi cấp" maxLength={255} />
                </Form.Item>
              )}
            </Col>
          </Row>
        </Form>
      </ModalComponent>

      <DuplicateModal
        visible={duplicateModalVisible}
        duplicateData={duplicateData}
        onConfirm={handleDuplicateConfirm}
        onCancel={handleDuplicateCancel}
      />
    </>
  );
};

export default CreateBookingRequestStep1;
