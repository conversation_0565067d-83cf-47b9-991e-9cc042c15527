import { Button, TableColumnsType, Typography } from 'antd';
import FilterSearch from '../components/FilterSearch';
import CreateBookingRequestModal from '../components/CreateBookingRequestModal';
import { useState } from 'react';
import { useCreateField, useFetch } from '../../../../hooks';
import { cloneTicket, getListBookingTicket } from '../../../../service/bookingTicket';
import { ActionsColumns } from '../../../../components/table/components/ActionsColumns';
import TableComponent from '../../../../components/table';
import { BookingTicket, Reciept } from '../../../../types/bookingRequest';
import { formatCurrency } from '../../../../utilities/shareFunc';
import { BOOKING_TICKET_STATUS_COLOR, BOOKING_TICKET_STATUS_NAME } from '../../../../constants/common';
import './styles.scss';
import { OPERATE_SELL_PROGRAM, PROJECTS_MANAGEMENT } from '../../../../configs/path';
import { useNavigate, useParams } from 'react-router-dom';
const { Text } = Typography;

const BookingTicketPersonal: React.FC<any> = ({}) => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [isOpoenBookingRequestModal, setBookingRequestModal] = useState(false);

  const { data, isPlaceholderData, isFetching, isLoading } = useFetch<BookingTicket[]>({
    queryKeyArrWithFilter: ['get-booking-ticket', id],
    api: getListBookingTicket,
    moreParams: { projectId: id, type: 'YCDCH' },
  });

  const bookingTickets = data?.data?.data?.rows;

  const { mutateAsync: _cloneTicket } = useCreateField({
    apiQuery: cloneTicket,
    keyOfListQuery: ['get-booking-ticket'],
    isMessageError: false,
    messageSuccess: 'Tao bản sao thành công',
  });

  const columns: TableColumnsType<BookingTicket> = [
    {
      title: 'Mã phiếu',
      dataIndex: 'bookingTicketCode',
      key: 'bookingTicketCode',
      width: 160,
      render: (value: string) => {
        return <Text>{value || ''}</Text>;
      },
    },
    {
      title: 'Mã phiếu thu',
      dataIndex: 'code',
      key: 'code',
      width: 130,
      render: (_: string, record: BookingTicket) => {
        if (!record?.reciept || record.reciept.length === 0) {
          return <Text>{'-'}</Text>;
        }
        return record?.reciept?.map((reciept: Reciept) => (
          <p
            style={{
              ...((record.reciept?.length ?? 0) > 1 ? {} : { marginBottom: 0 }),
            }}
          >
            {reciept?.code || ''}
          </p>
        ));
      },
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      width: 130,
      render: (_: string, record: BookingTicket) => {
        return (
          <Text
            style={{
              color: BOOKING_TICKET_STATUS_COLOR[record?.status ?? ''],
            }}
          >
            {BOOKING_TICKET_STATUS_NAME[record?.status ?? ''] || ''}
          </Text>
        );
      },
    },
    {
      title: 'Số tiền',
      dataIndex: 'amountRegistration',
      key: 'amountRegistration',
      width: 130,
      render: (_: string, record: BookingTicket) => {
        return <Text>{formatCurrency((record?.amountRegistration ?? 0).toString()) || ''}</Text>;
      },
    },

    {
      title: 'Khách hàng',
      dataIndex: 'name',
      key: 'name',
      width: 180,
      render: (_: any, record: BookingTicket) => {
        return <Text>{record?.customer?.personalInfo?.name || ''}</Text>;
      },
    },

    {
      title: 'Số điện thoại',
      dataIndex: 'phone',
      key: 'phone',
      width: 120,
      render: (_: any, record: BookingTicket) => {
        return <Text>{record?.customer?.personalInfo?.phone || ''}</Text>;
      },
    },

    {
      title: 'CCCD',
      dataIndex: 'identites',
      key: 'identites',
      width: 120,
      render: (_: any, record: BookingTicket) => {
        return <Text>{record?.customer?.identities?.[0]?.value || ''}</Text>;
      },
    },

    {
      title: 'Nhân viên chăm sóc',
      dataIndex: 'staff',
      key: 'staff',
      width: 175,
      render: (_: any, record: BookingTicket) => {
        return <Text>{record?.employee?.name || ''}</Text>;
      },
    },

    {
      title: '',
      dataIndex: 'action',
      key: 'action',
      align: 'center',
      width: 80,
      render: (_: any, record: BookingTicket) => {
        const handleDelete = () => {
          // handle delete logic here
        };
        const handleView = () => {
          navigate(`${PROJECTS_MANAGEMENT}${OPERATE_SELL_PROGRAM}/${id}/${record?.id}`);
          // handle view logic here
        };

        const handleClone = () => {
          _cloneTicket({
            id: record?.id,
          });
          // handle duplicate logic here
        };
        return (
          <ActionsColumns handleDelete={handleDelete} handleCloneRole={handleClone} handleViewDetail={handleView} />
        );
      },
    },
  ];

  return (
    <div>
      <div className="header-content">
        <FilterSearch />
        <Button type="default" onClick={() => setBookingRequestModal(true)}>
          Tạo phiếu YCĐC
        </Button>
      </div>
      <TableComponent
        dataSource={bookingTickets}
        columns={columns}
        queryKeyArr={['get-booking-ticket', id]}
        className="table-booking-request"
        rowKey="id"
        loading={isFetching || isPlaceholderData || isLoading}
      />
      <CreateBookingRequestModal
        isVisible={isOpoenBookingRequestModal}
        onCancel={() => setBookingRequestModal(false)}
      />
    </div>
  );
};

export default BookingTicketPersonal;
