import React, { useEffect, useState } from 'react';
import {
  Form,
  Input,
  Select,
  DatePicker,
  Checkbox,
  Row,
  Col,
  Radio,
  Typography,
  Button,
  UploadFile,
  Timeline,
} from 'antd';
import {
  BOOKING_TICKET_STATUS_NAME,
  FORMAT_DATE,
  FORMAT_DATE_TIME,
  OPTIONS_GENDER,
} from '../../../../constants/common';
import { useCreateField, useFetch, useUpdateField } from '../../../../hooks';
import {
  createUrlPayment,
  getDetailBookingTicket,
  getSalePrograms,
  updateBookingTicket,
} from '../../../../service/bookingTicket';
import { useParams } from 'react-router-dom';
import BreadCrumbComponent from '../../../../components/breadCrumb';
import { BookingTicketInfo, SaleProgram } from '../../../../types/bookingRequest';
import './styles.scss';
import dayjs from 'dayjs';
import SelectAddress from '../../../../components/selectAddress';
import UploadFileBookingTicket from '../components/UploadFileBookingTicket';
import { formatCurrency } from '../../../../utilities/shareFunc';
import FPTLogo from '../../../../assets/images/FPT_logo.png';
import SingleSelectLazy from '../../../../components/select/singleSelectLazy';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;
interface ExtendedUploadFile extends UploadFile {
  key?: string;
  absoluteUrl?: string;
}

const BookingTicketDetail: React.FC<any> = () => {
  const { id, bookingTicketId } = useParams();
  const [form] = Form.useForm();
  const type = Form.useWatch(['type'], form);
  const [yearOnly, setYearOnly] = useState(false);
  const [fileList, setFileList] = useState<ExtendedUploadFile[]>([]);
  const [urlPayment, setUrlPayment] = useState<string>('');

  const { mutateAsync: _updateBookingTicket } = useUpdateField({
    // keyOfListQuery: ['get-booking-ticket', id],
    keyOfDetailQuery: ['get-detail-booking-ticket', bookingTicketId],
    apiQuery: updateBookingTicket,
    isMessageError: false,
  });

  const { mutateAsync: _createUrlPayment } = useCreateField({
    apiQuery: createUrlPayment,
    keyOfDetailQuery: ['create-url-payment'],
    isMessageError: false,
    isMessageSuccess: false,
    isShowMessage: false,
  });

  const { data: bookingTicketData } = useFetch<BookingTicketInfo>({
    api: () => bookingTicketId && getDetailBookingTicket({ id: bookingTicketId }),
    queryKeyArr: ['get-detail-booking-ticket', bookingTicketId],
    enabled: !!bookingTicketId,
    cacheTime: 10,
  });

  const bookingTicket = bookingTicketData?.data?.data;

  useEffect(() => {
    if (bookingTicket) {
      const bankInfoDefault = `${bookingTicket?.customer?.bankInfo.name} - ${bookingTicket?.customer?.bankInfo.accountNumber} - ${bookingTicket?.customer?.bankInfo.beneciary}`;
      const transformedBookingTicket = {
        type: bookingTicket.customer?.type || 'business',
        customer: { code: bookingTicket?.customer?.code || '' },
        identityType: bookingTicket.customer?.identityType || '',
        taxCode: bookingTicket.customer?.taxCode || '',
        identityNumber: bookingTicket.customer?.identityNumber || '',
        identityDate: bookingTicket.customer?.identityDate
          ? dayjs(bookingTicket.customer.identityDate, FORMAT_DATE)
          : null,
        identityPlace: bookingTicket.customer?.identityPlace || '',
        company: {
          name: bookingTicket.customer?.company?.name || '',
        },
        personalInfo: {
          name: bookingTicket.customer?.personalInfo?.name || '',
          phone: bookingTicket.customer?.personalInfo?.phone || '',
          email: bookingTicket.customer?.personalInfo?.email || '',
        },
        info: {
          gender: bookingTicket.customer?.info?.gender,
          onlyYear: bookingTicket.customer?.info?.onlyYear || false,
          birthday: bookingTicket.customer?.info?.birthday
            ? dayjs(bookingTicket.customer?.info?.birthday, FORMAT_DATE)
            : null,
          birthdayYear: bookingTicket.customer?.info?.birthdayYear
            ? dayjs(bookingTicket.customer?.info?.birthdayYear)
            : null,
          cloneAddress: bookingTicket.customer?.info?.useResidentialAddress || false,
        },
        employeeTakeCareId: bookingTicket.employee?.name || '',
        companyAdress: bookingTicket.customer?.company?.address || '',
        address: bookingTicket.customer?.info?.address || '',
        rootAddress: bookingTicket.customer?.info?.rootAddress || '',
        bankInfo: bankInfoDefault || '',
        salesProgram: {
          id: bookingTicket?.salesProgram?.id || '',
          name: bookingTicket?.salesProgram?.name || '',
        },
        salesProgramId: bookingTicket?.salesProgramId || '',
        propertyUnit: { code: bookingTicket.propertyUnit?.code || '' },
        amountRegistration: formatCurrency((bookingTicket?.amountRegistration ?? 0).toString()),
        note: bookingTicket.note || '',
        files: bookingTicket.files || [],
      };
      // Cập nhật giá trị form
      form.setFieldsValue(transformedBookingTicket);
      setFileList((bookingTicket?.files || []) as ExtendedUploadFile[]);
      setYearOnly(bookingTicket.customer?.info?.onlyYear || false);
    }
  }, [bookingTicket, form]);

  const handleCreateUrlPayment = async () => {
    const payload = {
      amount: bookingTicket?.amountRegistration,
      orderId: bookingTicket?.code,
      bankCode: bookingTicket?.customer?.bankInfo?.bankCode,
    };
    const resp = await _createUrlPayment(payload);
    setUrlPayment(String(resp?.data?.data ?? ''));
  };

  const handleUpdateBookingTicket = async () => {
    const values = await form.validateFields();
    const payload = {
      id: bookingTicket?.id,
      salesProgramId: values?.salesProgram?.id,
      note: values?.note,
    };
    await _updateBookingTicket(payload);
  };

  const handleSelectSaleProgram = (values: SaleProgram) => {
    form.setFieldsValue({
      salesProgram: {
        id: values?.id,
        name: values?.name,
      },
      salesProgramId: values?.id,
    });
  };

  return (
    <div className="booking-ticket-detail">
      <BreadCrumbComponent
        customItems={[
          { label: 'Dự án và sản phẩm' },
          { label: 'Danh sách dự án', path: '/projects' },
          { label: `Thông tin dự án - ${bookingTicket?.project?.name || ''}`, path: `/projects/${id}` },
          { label: `Vận hành bán hàng dự án ${bookingTicket?.project?.name || ''}`, path: '#' },
        ]}
        titleBread={bookingTicket?.bookingTicketCode}
        noMenu={true}
      />
      <div className="project-card">
        <div className="project-info">
          <Title level={5}>{`Phiếu đặt chỗ ${bookingTicket?.project?.name}`}</Title>
          <Text type="secondary">
            Dự án: <span className="text-type">{bookingTicket?.project?.name}</span>
          </Text>
        </div>
        <div className="project-actions">
          <Button disabled={bookingTicket?.status === 'CS_APPROVED_TICKET'} onClick={handleUpdateBookingTicket}>
            Lưu
          </Button>
          <Button>Gửi Email</Button>
          <Button>Xác nhận</Button>
          <Button>Từ chối</Button>
          <Button>Tải xuống</Button>
        </div>
        <div className="project-image">
          <img
            src={
              bookingTicket?.project?.imageUrl
                ? `${import.meta.env.VITE_S3_IMAGE_URL}/${bookingTicket?.project?.imageUrl}`
                : FPTLogo
            }
            alt="Project"
          />
        </div>
      </div>
      <Form form={form} layout="vertical" style={{ marginTop: 32 }}>
        <Row gutter={32}>
          <Col span={16}>
            {/* Thông tin khách hàng */}
            <Title level={5}>Thông tin khách hàng</Title>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label="Loại khách hàng"
                  name="type"
                  rules={[{ required: true, message: 'Vui lòng chọn loại khách hàng!' }]}
                >
                  <Select placeholder="Chọn loại khách hàng" disabled>
                    <Option value="individual">Cá nhân</Option>
                    <Option value="business">Doanh nghiệp</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <p style={{ marginBottom: 8 }}>
              Giấy tờ xác minh <span style={{ color: 'red' }}>*</span>
            </p>
            <Row
              gutter={[8, 8]}
              style={{
                backgroundColor: 'rgba(0, 0, 0, 0.02)',
                border: '1px solid var(--colorBorder, rgba(0, 0, 0, 0.15))',
                padding: 20,
                margin: 0,
                marginBottom: 16,
              }}
            >
              <Col span={6}>
                <Form.Item
                  label="Loại giấy tờ"
                  name="identityType"
                  rules={[{ required: true, message: 'Vui lòng chọn loại giấy tờ' }]}
                >
                  <Select placeholder="Chọn loại giấy tờ" disabled>
                    {type === 'individual' ? (
                      <>
                        <Option value="CCCD">Căn cước công dân</Option>
                        <Option value="CMND">CMT</Option>
                        <Option value="PASSPORT">Hộ chiếu</Option>
                      </>
                    ) : (
                      <Option value="MST">Mã số thuế</Option>
                    )}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label="Số giấy tờ"
                  name={type === 'business' ? 'taxCode' : 'identityNumber'}
                  rules={[
                    {
                      required: true,
                      message: 'Vui lòng nhập số giấy tờ',
                    },
                  ]}
                >
                  <Input placeholder="Nhập số giấy tờ" maxLength={60} disabled />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label="Ngày cấp"
                  name="identityDate"
                  rules={[{ required: true, message: 'Vui lòng chọn ngày cấp' }]}
                >
                  <DatePicker placeholder="Chọn ngày cấp" format={FORMAT_DATE} disabled />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label="Nơi cấp"
                  name="identityPlace"
                  rules={[{ required: true, message: 'Vui lòng chọn nơi cấp!' }]}
                >
                  <Input placeholder="Nhập số giấy tờ" maxLength={60} disabled />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label={type === 'business' ? 'Tên công ty' : 'Tên khách hàng'}
                  name={type === 'business' ? ['company', 'name'] : ['personalInfo', 'name']}
                  rules={[
                    {
                      required: true,
                      message: type === 'business' ? 'Vui lòng nhập tên công ty' : 'Vui lòng nhập tên khách hàng',
                    },
                    {
                      validator(_, value) {
                        // Nếu giá trị chỉ chứa khoảng trống (sau khi trim còn rỗng)
                        if (value && value.trim() === '') {
                          return Promise.reject('Không được nhập khoảng trống');
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <Input
                    placeholder={type === 'business' ? 'Nhập tên công ty' : 'Nhập tên khách hàng'}
                    maxLength={type === 'business' ? 120 : 60}
                    onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                      e.target.value = e.target.value.toUpperCase(); // Chuyển đổi trực tiếp
                    }}
                    disabled
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Mã khách hàng"
                  name={['customer', 'code']}
                  required
                  // rules={[{ required: true, message: 'Vui lòng mã khách hàng' }]}
                >
                  <Input placeholder="Mã khách hàng" disabled maxLength={14} />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              {type === 'business' && (
                <Col span={12}>
                  <Form.Item
                    label="Tên người đại diện"
                    name={['personalInfo', 'name']}
                    rules={[{ required: true, message: 'Vui lòng nhập tên người đại diện' }]}
                  >
                    <Input
                      placeholder="Nhập tên người đại diện"
                      maxLength={60}
                      onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                        e.target.value = e.target.value.toUpperCase(); // Chuyển đổi trực tiếp
                      }}
                      disabled
                    />
                  </Form.Item>
                </Col>
              )}

              <Col span={12}>
                <Form.Item label="Số điện thoại" name={['personalInfo', 'phone']}>
                  <Input placeholder="Nhập số điện thoại" maxLength={15} disabled />
                </Form.Item>
              </Col>
              {type === 'individual' && (
                <Col span={12}>
                  <Form.Item
                    label="Địa chỉ email"
                    name={['personalInfo', 'email']}
                    rules={[{ type: 'email', message: 'Địa chỉ email sai định dạng' }]}
                  >
                    <Input placeholder="Nhập địa chỉ email" maxLength={25} disabled />
                  </Form.Item>
                </Col>
              )}
            </Row>

            <Row gutter={16}>
              {type === 'individual' && (
                <Col span={24}>
                  <Form.Item
                    name={['info', 'gender']}
                    label="Giới tính"
                    rules={[{ required: true, message: 'Vui lòng nhập giới tính' }]}
                    layout="horizontal"
                  >
                    <Radio.Group options={OPTIONS_GENDER} style={{ marginLeft: 30 }} disabled />
                  </Form.Item>
                </Col>
              )}

              <Col span={24}>
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      layout="horizontal"
                      label="Ngày sinh"
                      name={['info', 'onlyYear']}
                      valuePropName="checked"
                    >
                      <Checkbox
                        checked={yearOnly}
                        style={{ marginLeft: 30 }}
                        onChange={e => setYearOnly(e.target.checked)}
                        disabled
                      >
                        Chỉ năm sinh
                      </Checkbox>
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    {yearOnly ? (
                      <Form.Item name={['info', 'birthdayYear']}>
                        <DatePicker picker="year" format="YYYY" placeholder="YYYY" disabled />
                      </Form.Item>
                    ) : (
                      <Form.Item name={['info', 'birthday']}>
                        <DatePicker format={FORMAT_DATE} placeholder={FORMAT_DATE} disabled />
                      </Form.Item>
                    )}
                  </Col>
                </Row>
              </Col>
            </Row>

            <Form.Item label="Nhân viên chăm sóc" name="employeeTakeCareId">
              <Input placeholder="Nhập địa chỉ cụ thể" maxLength={155} disabled />
            </Form.Item>

            {/* Địa chỉ công ty */}
            {type === 'business' && (
              <>
                <Title level={5}>Địa chỉ công ty</Title>
                <Row gutter={16}>
                  <Col span={24}>
                    <Form.Item label="Địa chỉ" name={'companyAdress'} className="input-address">
                      <SelectAddress
                        placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
                        parentName={'companyAdress'}
                        address={form.getFieldValue('companyAdress')}
                        isDisable
                      />
                    </Form.Item>
                  </Col>
                  <Col span={24} className="address">
                    <Form.Item name={['companyAdress', 'address']}>
                      <Input placeholder="Nhập địa chỉ cụ thể" maxLength={155} disabled />
                    </Form.Item>
                  </Col>
                </Row>
              </>
            )}

            {/* Địa chỉ thường trú */}
            {type === 'business' ? (
              <Title level={5}>Địa chỉ thường trú người đại diện</Title>
            ) : (
              <Title level={5}>Địa chỉ thường trú</Title>
            )}
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="Địa chỉ" name={['info', 'address']} className="input-address">
                  <SelectAddress
                    placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
                    parentName={'address'}
                    address={form.getFieldValue('address')}
                    isDisable
                  />
                </Form.Item>
              </Col>
              <Col span={24} className="address">
                <Form.Item name={['address', 'address']}>
                  <Input placeholder="Nhập địa chỉ cụ thể" maxLength={155} disabled />
                </Form.Item>
              </Col>
            </Row>

            {/* Địa chỉ liên lạc */}
            {type === 'business' ? (
              <Title level={5}>Địa chỉ liên lạc người đại diện</Title>
            ) : (
              <Title level={5}>Địa chỉ liên lạc</Title>
            )}
            <Form.Item name={['info', 'cloneAddress']} valuePropName="checked">
              <Checkbox disabled>Sử dụng địa chỉ thường trú</Checkbox>
            </Form.Item>

            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="Địa chỉ" name={['info', 'rootAddress']} className="input-address">
                  <SelectAddress
                    placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
                    parentName={'rootAddress'}
                    address={form.getFieldValue('rootAddress')}
                    isDisable
                  />
                </Form.Item>
              </Col>
              <Col span={24} className="address">
                <Form.Item name={['rootAddress', 'address']}>
                  <Input placeholder="Nhập địa chỉ cụ thể" maxLength={155} disabled />
                </Form.Item>
              </Col>
            </Row>

            {/* Thông tin thanh toán */}
            <Title level={5}>Thông tin thanh toán</Title>

            <p style={{ marginBottom: 8 }}>
              Tài khoản giao dịch chính (default) <span style={{ color: 'red' }}>*</span>
            </p>
            <div
              style={{
                backgroundColor: 'rgba(0, 0, 0, 0.02)',
                border: '1px solid var(--colorBorder, rgba(0, 0, 0, 0.15))',
                padding: 20,
                margin: 0,
                marginBottom: 16,
              }}
            >
              <Form.Item
                name={'bankInfo'}
                rules={[{ required: true, message: 'Vui lòng chọn tài khoản giao dịch chính' }]}
              >
                <Select placeholder="Chọn tài khoản giao dịch chính" disabled></Select>
              </Form.Item>
            </div>

            {/* Thông tin dự án */}
            <Title level={5}>Thông tin dự án</Title>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item
                  label="Chương trình bán hàng"
                  name={['salesProgram', 'id']}
                  rules={[{ required: true, message: 'Vui lòng chọn chương trình bán hàng!' }]}
                >
                  <SingleSelectLazy
                    enabled={!!bookingTicketId}
                    apiQuery={getSalePrograms}
                    queryKey={['get-list-sale-programs']}
                    keysLabel={'name'}
                    placeholder="Chọn chương trình bán hàng"
                    handleSelect={handleSelectSaleProgram}
                    moreParams={{
                      projectId: id,
                      allowBookingPriority: true,
                    }}
                    disabled={bookingTicket?.status === 'CS_APPROVED_TICKET'}
                    defaultValues={
                      bookingTicket?.salesProgram
                        ? {
                            value: bookingTicket.salesProgram.id,
                            label: bookingTicket.salesProgram.name,
                          }
                        : undefined
                    }
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Mã sản phẩm" name={['propertyUnit', 'code']}>
                  <Input placeholder="Nhập mã sản phẩm" maxLength={50} disabled />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Số tiền đăng ký"
                  name="amountRegistration"
                  rules={[{ required: true, message: 'Vui lòng nhập số tiền đăng ký' }]}
                >
                  <Input placeholder="Nhập số tiền đăng ký" maxLength={155} disabled />
                </Form.Item>
              </Col>
            </Row>
            {/* Thông tin gửi email */}
            <Title level={5}>Thông tin gửi email</Title>
            <Text>Thời gian gửi email lần cuối: </Text>

            {/* Thông tin thanh toán online */}
            <Title level={5}>Thanh toán online</Title>
            <Text>
              Mã phiếu thu: <Text style={{ marginLeft: 15 }}>{'FakeData'}</Text>
              <Button
                type="default"
                style={{ marginLeft: 30 }}
                onClick={handleCreateUrlPayment}
                disabled={bookingTicket?.status === 'CS_APPROVED_TICKET'}
              >
                Tạo link thanh toán
              </Button>
            </Text>
            {urlPayment && (
              <a
                href={urlPayment}
                target="_blank"
                rel="noopener noreferrer"
                style={{
                  marginTop: 10,
                  overflowWrap: 'break-word',
                  wordBreak: 'break-all',
                  display: 'inline-block',
                  maxWidth: '100%',
                }}
              >
                {urlPayment}
              </a>
            )}
          </Col>
          <Col span={8}>
            {/* Thông tin khác */}
            <Title level={5}>Thông tin khác</Title>
            <Form.Item label="Ghi chú" name="note">
              <TextArea
                placeholder="Nhập ghi chú"
                maxLength={250}
                rows={4}
                disabled={bookingTicket?.status === 'CS_APPROVED_TICKET'}
              />
            </Form.Item>
            <Form.Item name="files">
              <UploadFileBookingTicket
                fileList={fileList}
                setFileList={setFileList}
                uploadPath="property/primarty-transaction"
                size={25}
                isDetail
              />
            </Form.Item>

            <Title level={5}>Lịch sử</Title>
            <Timeline
              style={{ height: '500px', overflowY: 'auto', padding: '20px 0' }}
              items={bookingTicket?.historyStatus?.map(item => ({
                children: (
                  <div>
                    <div className="text-history-time">{dayjs(item?.date).format(FORMAT_DATE_TIME)}</div>
                    <div className="text-history">{item?.status ? BOOKING_TICKET_STATUS_NAME[item.status] : ''}</div>
                  </div>
                ),
                color: 'blue',
              }))}
            />
          </Col>
        </Row>
      </Form>
    </div>
  );
};

export default BookingTicketDetail;
