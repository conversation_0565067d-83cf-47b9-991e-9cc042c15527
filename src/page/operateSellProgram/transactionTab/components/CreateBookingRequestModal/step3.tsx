import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Form,
  Input,
  Select,
  DatePicker,
  Checkbox,
  Button,
  InputNumber,
  Row,
  Col,
  Radio,
  UploadFile,
  Typography,
} from 'antd';
import { CloseOutlined, PlusOutlined } from '@ant-design/icons';
import { useCreateField, useFetch } from '../../../../../hooks';
import {
  Bank,
  BankAccount,
  BankOption,
  CreateBookingTicket,
  DuplicateResp,
  Employee,
  PropertyUnit,
  SaleProgram,
} from '../../../../../types/bookingRequest';
import { getBanks } from '../../../../../service/bank';
import ModalComponent from '../../../../../components/modal';
import SelectAddress, { AddressType } from '../../../../../components/selectAddress';
import dayjs from 'dayjs';
import { FORMAT_DATE, OPTIONS_GENDER, REGEX_PHONE_VN } from '../../../../../constants/common';
import { CheckboxChangeEvent } from 'antd/es/checkbox';
import SingleSelectLazy from '../../../../../components/select/singleSelectLazy';
import { formatNumber, handleKeyDownEnterNumber } from '../../../../../utilities/regex';
import { createBookingTicket, getEmployees, getProductUnits } from '../../../../../service/bookingTicket';
import { useParams } from 'react-router-dom';
import { getProvinces } from '../../../../../service/address';
import { hasNonNullValue } from '../../../../../utilities/shareFunc';
import UploadFileBookingTicket from '../../../bookingTicketPersonal/components/UploadFileBookingTicket';
import { getListBookingApproved } from '../../../../../service/depositRequired';
import { getListProjectSaleProgram } from '../../../../../service/project';

const { Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;

interface ExtendedUploadFile extends UploadFile {
  key?: string;
  absoluteUrl?: string;
}

interface CreateBookingRequestStep3Props {
  visible: boolean;
  onCancel: (hasFormChanged?: boolean, resetForm?: () => void) => void;
  onSave: (values: unknown) => void;
  initialData?: DuplicateResp;
  duplicateData?: DuplicateResp | null;
  selectedPropertyId?: string;
}

const CreateBookingRequestStep3: React.FC<CreateBookingRequestStep3Props> = ({
  visible,
  onCancel,
  onSave,
  initialData,
  duplicateData,
  selectedPropertyId,
}) => {
  const { id: projectId } = useParams<{ id: string }>();
  const [form] = Form.useForm();

  // Form watch
  const cloneAddress = Form.useWatch(['info', 'cloneAddress'], form);
  const issueDate = Form.useWatch('issueDate', form);
  const issueLocation = Form.useWatch('issueLocation', form);
  const address = Form.useWatch(['address'], form);
  const rootAddress = Form.useWatch(['rootAddress'], form);
  const type = Form.useWatch(['type'], form);
  const bankInfoOptions = Form.useWatch('bankAccount', form) || [];

  // Tính toán các giá trị phụ thuộc
  const isAddressNonNull = hasNonNullValue(address);
  const isRootAddressNonNull = hasNonNullValue(rootAddress);
  const isKHCT = duplicateData?.customerType === 'customer';

  // Local state
  const [fileList, setFileList] = useState<ExtendedUploadFile[]>([]);
  const [propertyUnitParams, setPropertyUnitParams] = useState({ salesProgramIds: '', projectId: '' });
  const [yearOnly, setYearOnly] = useState(false);
  const [isModified, setIsModified] = useState(false);

  // Tính toán options cho bank từ bankInfoOptions
  const defaultBankOptions: BankOption[] = useMemo(
    () =>
      bankInfoOptions
        .filter((bank: Bank) => bank?.bankCode && bank?.bankName)
        .map((bank: Bank, index: number) => ({
          value: `${bank.bankCode}-${index}`,
          label: `${bank?.bankName || 'N/A'} - ${bank?.accountNumber || ''} - ${bank?.beneciary || ''}`,
          originalBankCode: bank.bankCode,
        })),
    [bankInfoOptions],
  );

  // API calls
  const { data: dataProvinces } = useFetch<AddressType[]>({
    queryKeyArrWithFilter: ['get-list-identity'],
    api: getProvinces,
  });

  const { data: dataBanks, isLoading: isLoadingBanks } = useFetch<Bank[]>({
    queryKeyArrWithFilter: ['get-list-banks'],
    api: getBanks,
  });

  const { mutateAsync: _createBookingTicket } = useCreateField<CreateBookingTicket>({
    apiQuery: createBookingTicket,
    keyOfDetailQuery: ['get-booking-ticket'],
    keyOfListQuery: ['get-request-deposit', projectId],
    isMessageError: false,
  });

  const provinces = dataProvinces?.data?.data;
  const banks = dataBanks?.data?.data || [];

  // Handler khi lưu form
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      const { mainBank, employeeTakeCareId } = values;

      const payloadCreateTicket: CreateBookingTicket = {
        ticketType: 'YCDC',
        employeeTakeCareId,
        id: values?.idBookingTicketCode,
        projectId,
        demandPropertyId: selectedPropertyId || '',
        bookingTicketCode: values?.bookingTicketCode,
        customer: {
          useResidentialAddress: values?.info?.cloneAddress,
          code: values?.code,
          gender: values?.info?.gender,
          onlyYear: values?.info?.onlyYear,
          birthday: !yearOnly ? (values?.info?.birthday ? dayjs(values?.info?.birthday).format(FORMAT_DATE) : '') : '',
          birthdayYear: yearOnly && values?.info?.birthdayYear ? dayjs(values?.info?.birthdayYear).format('YYYY') : '',
          name: values.personalInfo?.name,
          phone: values.personalInfo?.phone,
          email: values.personalInfo?.email,
          identityType: values?.identityType,
          identityNumber: values?.type === 'business' ? values?.taxCode : values?.identityNo,
          identityIssueDate: values?.issueDate ? dayjs(values?.issueDate).format(FORMAT_DATE) : '',
          identityIssueLocation:
            typeof values?.issueLocation === 'object' ? values?.issueLocation?.value : values?.issueLocation,
          rootAddress: { ...values.rootAddress },
          address: { ...values?.address },
          taxCode: values?.taxCode,
          bankInfo: {
            name: mainBank?.name || '',
            accountNumber: mainBank?.accountNumber || '',
            beneciary: mainBank?.beneciary || '',
          },
          position: values?.position,
          type: values?.type,
          company: { ...values?.company, address: values?.companyAddress },
        },
        files: fileList.map(file => ({
          uid: file.uid || '',
          name: file.name,
          url: file.key || '',
          absoluteUrl: file?.absoluteUrl || '',
          uploadName: file.name,
        })),
        amountRegistration: values?.registrationAmount,
        note: values?.description,
        isCreateAtStage2: true,
      };

      const ticketResp = await _createBookingTicket(payloadCreateTicket);
      if (ticketResp?.data?.statusCode === '0') {
        onSave(payloadCreateTicket);
      }
    } catch (error) {
      console.error('Form validation failed:', error);
    }
  };

  // Đánh dấu form đã thay đổi
  const validateForm = () => setIsModified(true);

  // Xử lý xóa tài khoản ngân hàng
  const handleRemoveBankAccount = (name: number) => {
    const currentMainBankId = form.getFieldValue('mainBankId');
    const bankAccount = form.getFieldValue('bankAccount');
    if (!bankAccount || !currentMainBankId) return;

    const removedBank = bankAccount[name];
    const isMainBankRemoved = currentMainBankId === `${removedBank?.bankCode}-${name}`;

    if (isMainBankRemoved) {
      form.setFieldsValue({
        mainBankId: undefined,
        mainBank: { name: '', accountNumber: '', beneciary: '' },
      });
    } else if (currentMainBankId) {
      const [bankCode, currentIndex] = currentMainBankId.split('-');
      const currentIndexNum = parseInt(currentIndex);
      if (currentIndexNum > name) {
        form.setFieldsValue({
          mainBankId: `${bankCode}-${currentIndexNum - 1}`,
        });
      }
    }
  };

  // Xử lý khi chọn tài khoản ngân hàng mặc định
  const handleSelectBankInfo = (value: string) => {
    const selectedOption = defaultBankOptions.find(option => option.value === value);
    const selectedBank = bankInfoOptions.find((bank: Bank) => bank.bankCode === selectedOption?.originalBankCode);

    form.setFieldsValue({
      mainBankId: selectedBank ? value : undefined,
      mainBank: selectedBank
        ? {
            name: selectedBank.bankName || '',
            accountNumber: selectedBank.accountNumber || '',
            beneciary: selectedBank.beneciary || '',
          }
        : { name: '', accountNumber: '', beneciary: '' },
    });
  };

  // Xử lý khi chọn sao chép địa chỉ
  const handleCloneAddress = useCallback(
    (e: CheckboxChangeEvent) => {
      if (e.target.checked) {
        form.setFieldsValue({
          rootAddress: form.getFieldValue('address'),
        });
      }
    },
    [form],
  );

  // Các hàm xử lý khi chọn giá trị từ select
  const handleSelectSaleProgram = (value: SaleProgram) => {
    const newParams = {
      salesProgramIds: value?.id || '',
      projectId: value?.project?.id || '',
    };

    setPropertyUnitParams(newParams);
    form.setFieldsValue({ salesProgramId: value?.id });
    form.validateFields(['salesProgramId']);
  };

  const handleSelectProductCode = (value: PropertyUnit) => {
    form.setFieldsValue({ demandPropertyId: value?.id });
    form.validateFields(['demandPropertyId']);
  };

  const handleSelectEmployee = (value: Employee) => {
    form.setFieldsValue({ employeeTakeCareId: value?.id });
    form.validateFields(['employeeTakeCareId']);
  };

  const handleSelectBookingTicketCode = (value: { code: string; id: string }) => {
    form.setFieldsValue({
      bookingTicketCode: value?.code,
      idBookingTicketCode: value?.id,
    });
    form.validateFields(['bookingTicketCode']);
  };

  // Nạp dữ liệu ban đầu
  useEffect(() => {
    if (initialData) {
      form.setFieldsValue(initialData);
    }

    if (duplicateData?.data?.length) {
      const customerData = duplicateData.data[0];
      const identity = customerData.personalInfo?.identities?.[0];
      const bankInfo = customerData.bankInfo || [];
      const mainBankInfo = bankInfo[0] || {};

      form.setFieldsValue({
        type: customerData.type,
        code: customerData.code,
        identityType: customerData?.type === 'business' ? 'MST' : identity?.type,
        identityNo: identity?.value,
        issueDate:
          customerData.type === 'business'
            ? customerData?.company?.issueDate
              ? dayjs(customerData?.company?.issueDate, FORMAT_DATE)
              : null
            : identity?.date
              ? dayjs(identity?.date, FORMAT_DATE)
              : null,
        issueLocation: customerData.type === 'business' ? customerData?.company?.issueLocation : identity?.place,
        employeeTakeCareId: customerData.employee?.id,
        address: customerData.info?.address,
        rootAddress: customerData.info?.rootAddress,
        company: {
          name: customerData.company?.name,
        },
        personalInfo: {
          name: customerData.personalInfo?.name,
          phone: customerData.personalInfo?.phone,
          email: customerData.personalInfo?.email,
        },
        info: {
          birthday: customerData?.info?.birthday ? dayjs(customerData.info.birthday, FORMAT_DATE) : null,
          birthdayYear: customerData?.info?.birthdayYear ? dayjs(customerData.info.birthdayYear, 'YYYY') : null,
          gender: customerData.info?.gender,
          onlyYear: customerData?.info?.onlyYear,
          cloneAddress: customerData.info?.cloneAddress,
        },
        bankAccount: bankInfo.map((bank: BankAccount) => ({
          bankName: bank.name || '',
          bankCode: bank.code || '',
          accountNumber: bank.accountNumber || '',
          beneciary: bank.beneficiary || '',
        })),
        mainBank: {
          name: mainBankInfo.name || '',
          accountNumber: mainBankInfo.accountNumber || '',
          beneciary: mainBankInfo.beneciary || '',
        },
        description: customerData.description,
      });

      setYearOnly(!!customerData.info?.onlyYear);
    }
  }, [duplicateData, form, initialData]);

  // Kiểm tra tài khoản ngân hàng chính có hợp lệ
  useEffect(() => {
    const currentMainBankId = form.getFieldValue('mainBankId');
    if (!currentMainBankId) return;

    const [bankCode, index] = currentMainBankId.split('-');
    const bankAccount = form.getFieldValue('bankAccount') || [];
    const selectedBank = bankAccount[parseInt(index)];

    // Nếu tài khoản chính không còn tồn tại hoặc bankCode không khớp thì reset
    if (!selectedBank || selectedBank.bankCode !== bankCode) {
      form.setFieldsValue({
        mainBankId: undefined,
        mainBank: { name: '', accountNumber: '', beneciary: '' },
      });
    }
  }, [bankInfoOptions, form]);

  // Xử lý đồng bộ địa chỉ khi checkbox cloneAddress được chọn
  useEffect(() => {
    if (cloneAddress) {
      form.setFieldsValue({
        rootAddress: form.getFieldValue('address'),
      });
    }
  }, [cloneAddress, form, address]);

  // Xử lý cảnh báo khi thoát trang mà form đã thay đổi
  useEffect(() => {
    if (!isModified) return;

    const handleBeforeunload = (e: BeforeUnloadEvent) => {
      e.preventDefault();
      e.returnValue = '';
    };

    window.addEventListener('beforeunload', handleBeforeunload);
    return () => window.removeEventListener('beforeunload', handleBeforeunload);
  }, [isModified]);

  // Xử lý đóng modal
  const handleCancel = () => {
    onCancel(form.isFieldsTouched(), () => form.resetFields());
  };

  return (
    <ModalComponent
      title="Tạo mới yêu cầu đặt cọc 2/2"
      open={visible}
      onCancel={handleCancel}
      footer={[
        <Button key="save" type="primary" onClick={handleSave}>
          Lưu
        </Button>,
      ]}
    >
      <Form form={form} layout="vertical" initialValues={initialData} onValuesChange={validateForm}>
        <Row gutter={123}>
          <Col span={16}>
            <Title level={5}>Thông tin nhân viên</Title>
            <Row gutter={24}>
              <Col span={24}>
                <Form.Item label="Nhân viên chăm sóc" name="employeeTakeCareId">
                  <SingleSelectLazy
                    apiQuery={getEmployees}
                    queryKey={['get-list-employee']}
                    keysLabel={'name'}
                    placeholder="Chọn mã nhân viên chăm sóc"
                    handleSelect={handleSelectEmployee}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Title level={5}>Thông tin khách hàng</Title>
            <Row gutter={24}>
              <Col span={24}>
                <Form.Item label="Mã yêu cầu đặt chỗ" name="bookingTicketCode">
                  <SingleSelectLazy
                    apiQuery={getListBookingApproved}
                    moreParams={{ idProject: projectId }}
                    queryKey={['get-request-deposit']}
                    keysLabel={'code'}
                    placeholder="Nhập mã yêu cầu đặt chỗ"
                    handleSelect={handleSelectBookingTicketCode}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={24}>
                <Form.Item
                  label="Loại khách hàng"
                  name="type"
                  rules={[{ required: true, message: 'Vui lòng chọn loại khách hàng!' }]}
                >
                  <Select placeholder="Chọn loại khách hàng" disabled>
                    <Option value="individual">Cá nhân</Option>
                    <Option value="business">Doanh nghiệp</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <p style={{ marginBottom: 8 }}>
              Giấy tờ xác minh <span style={{ color: 'red' }}>*</span>
            </p>
            <Row
              gutter={[8, 8]}
              style={{
                backgroundColor: 'rgba(0, 0, 0, 0.02)',
                border: '1px solid var(--colorBorder, rgba(0, 0, 0, 0.15))',
                padding: 20,
                margin: 0,
                marginBottom: 16,
              }}
            >
              <Col span={6}>
                <Form.Item
                  label="Loại giấy tờ"
                  name="identityType"
                  rules={[{ required: true, message: 'Vui lòng chọn loại giấy tờ' }]}
                >
                  <Select placeholder="Chọn loại giấy tờ" disabled>
                    {type === 'individual' ? (
                      <>
                        <Option value="CCCD">Căn cước công dân</Option>
                        <Option value="CMND">CMT</Option>
                        <Option value="Hộ chiếu">Hộ chiếu</Option>
                      </>
                    ) : (
                      <Option value="MST">Mã số thuế</Option>
                    )}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label="Số giấy tờ"
                  name={type === 'business' ? 'taxCode' : 'identityNo'}
                  rules={[
                    {
                      required: true,
                      validator: (_, value) => {
                        if (!value || value.trim() === '') {
                          return Promise.reject(new Error('Vui lòng nhập số giấy tờ'));
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <Input
                    placeholder="Nhập số giấy tờ"
                    maxLength={60}
                    disabled={isKHCT}
                    onBlur={e => {
                      form.setFieldsValue({ taxCode: e.target.value.trim() });
                      form.setFieldsValue({ identityNo: e.target.value.trim() });
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label="Ngày cấp"
                  name="issueDate"
                  rules={[{ required: true, message: 'Vui lòng chọn ngày cấp' }]}
                >
                  <DatePicker placeholder="Chọn ngày cấp" format={FORMAT_DATE} disabled={isKHCT && issueDate != null} />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label="Nơi cấp"
                  name="issueLocation"
                  rules={[{ required: true, message: 'Vui lòng chọn nơi cấp!' }]}
                >
                  <Select
                    filterOption={(input, option) =>
                      typeof option?.label === 'string'
                        ? option.label.toLowerCase().includes(input.toLowerCase())
                        : false
                    }
                    allowClear
                    options={provinces?.map(item => ({
                      value: item.code,
                      label: item.nameVN,
                    }))}
                    showSearch
                    placeholder="Chọn nơi cấp"
                    onChange={value => {
                      form.setFieldsValue({
                        issueLocation: value || undefined,
                      });
                    }}
                    disabled={isKHCT && issueLocation != null}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label={type === 'business' ? 'Tên công ty' : 'Tên khách hàng'}
                  name={type === 'business' ? ['company', 'name'] : ['personalInfo', 'name']}
                  rules={[
                    {
                      required: true,
                      message: type === 'business' ? 'Vui lòng nhập tên công ty' : 'Vui lòng nhập tên khách hàng',
                    },
                    {
                      validator(_, value) {
                        if (value && value.trim() === '') {
                          return Promise.reject('Không được để khoảng trống');
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <Input
                    placeholder={type === 'business' ? 'Nhập tên công ty' : 'Nhập tên khách hàng'}
                    maxLength={type === 'business' ? 120 : 60}
                    disabled={isKHCT}
                    onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                      e.target.value = e.target.value.toUpperCase(); // Chuyển đổi trực tiếp
                    }}
                    onBlur={e => {
                      const fieldPath = type === 'business' ? ['company', 'name'] : ['personalInfo', 'name'];
                      form.setFieldValue(fieldPath, e.target.value.trim());
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Mã khách hàng" name="code">
                  <Input placeholder="Mã khách hàng" disabled maxLength={14} />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              {type === 'business' && (
                <Col span={12}>
                  <Form.Item
                    label="Tên người đại diện"
                    name={['personalInfo', 'name']}
                    rules={[{ required: true, message: 'Vui lòng nhập tên người đại diện' }]}
                  >
                    <Input
                      placeholder="Nhập tên người đại diện"
                      maxLength={60}
                      disabled={isKHCT}
                      onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                        e.target.value = e.target.value.toUpperCase(); // Chuyển đổi trực tiếp
                      }}
                    />
                  </Form.Item>
                </Col>
              )}

              <Col span={12}>
                <Form.Item
                  label="Số điện thoại"
                  name={['personalInfo', 'phone']}
                  rules={[
                    {
                      required: true,
                      message: 'Vui lòng nhập số điện thoại',
                    },
                    {
                      pattern: REGEX_PHONE_VN,
                      message: 'Số điện thoại phải bắt đầu bằng số 0 và từ 9 đến 15 chữ số',
                    },
                  ]}
                >
                  <Input
                    placeholder="Nhập số điện thoại"
                    maxLength={15}
                    onKeyDown={handleKeyDownEnterNumber}
                    disabled={isKHCT}
                  />
                </Form.Item>
              </Col>
              {type === 'individual' && (
                <Col span={12}>
                  <Form.Item
                    label="Địa chỉ email"
                    name={['personalInfo', 'email']}
                    rules={[{ type: 'email', message: 'Địa chỉ email sai định dạng' }]}
                  >
                    <Input placeholder="Nhập địa chỉ email" maxLength={25} />
                  </Form.Item>
                </Col>
              )}
            </Row>

            <Row gutter={16}>
              <Col span={14}>
                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item
                      layout="horizontal"
                      label="Ngày sinh"
                      name={['info', 'onlyYear']}
                      valuePropName="checked"
                    >
                      <Checkbox
                        checked={yearOnly}
                        style={{ marginLeft: 30 }}
                        onChange={e => setYearOnly(e.target.checked)}
                      >
                        Chỉ năm sinh
                      </Checkbox>
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    {yearOnly ? (
                      <Form.Item name={['info', 'birthdayYear']}>
                        <DatePicker picker="year" format="YYYY" placeholder="YYYY" />
                      </Form.Item>
                    ) : (
                      <Form.Item name={['info', 'birthday']}>
                        <DatePicker format={FORMAT_DATE} placeholder={FORMAT_DATE} />
                      </Form.Item>
                    )}
                  </Col>
                </Row>
              </Col>

              <Col span={10}>
                <Form.Item
                  name={['info', 'gender']}
                  label="Giới tính"
                  rules={[{ required: true, message: 'Vui lòng chọn giới tính' }]}
                  layout="horizontal"
                >
                  <Radio.Group options={OPTIONS_GENDER} style={{ marginLeft: 30 }} />
                </Form.Item>
              </Col>
            </Row>
            {/* Địa chỉ công ty */}

            <Title level={5}>Địa chỉ thường trú</Title>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="Địa chỉ" name={['address']} className="input-address">
                  <SelectAddress
                    placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
                    parentName={'address'}
                    address={form.getFieldValue('address')}
                    handleAddressChange={validateForm}
                    isDisable={isKHCT && isAddressNonNull}
                  />
                </Form.Item>
              </Col>
              <Col span={24} className="address">
                <Form.Item name={['address', 'address']}>
                  <Input placeholder="Nhập địa chỉ cụ thể" maxLength={155} disabled={isKHCT && isAddressNonNull} />
                </Form.Item>
              </Col>
            </Row>

            {/* Địa chỉ liên lạc */}

            <Title level={5}>Địa chỉ liên lạc</Title>
            <Form.Item name={['info', 'cloneAddress']} valuePropName="checked">
              <Checkbox onChange={handleCloneAddress}>Sử dụng địa chỉ thường trú</Checkbox>
            </Form.Item>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="Địa chỉ" name={['rootAddress']} className="input-address">
                  <SelectAddress
                    placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
                    parentName="rootAddress"
                    address={rootAddress}
                    handleAddressChange={validateForm}
                    isDisable={(isKHCT && isRootAddressNonNull) || cloneAddress}
                  />
                </Form.Item>
              </Col>
              <Col span={24} className="address">
                <Form.Item name={['rootAddress', 'address']}>
                  <Input
                    placeholder="Nhập địa chỉ cụ thể"
                    maxLength={155}
                    disabled={(isKHCT && isRootAddressNonNull) || cloneAddress}
                  />
                </Form.Item>
              </Col>
            </Row>

            {/* Thông tin thanh toán */}
            <Title level={5}>Thông tin thanh toán</Title>
            <Form.Item label="Tài khoản giao dịch">
              <div
                style={{
                  backgroundColor: 'rgba(0, 0, 0, 0.02)',
                  border: '1px solid var(--colorBorder, rgba(0, 0, 0, 0.15))',
                  padding: 20,
                  margin: 0,
                  marginBottom: 16,
                }}
              >
                <Form.List name="bankAccount">
                  {(fields, { add, remove }) => (
                    <>
                      {fields.map(({ key, name, ...restField }) => (
                        <Row align="middle" gutter={[8, 8]} key={key}>
                          <Col span={10}>
                            <Form.Item
                              {...restField}
                              name={[name, 'bankCode']}
                              label="Ngân hàng"
                              rules={[{ required: false }]}
                            >
                              <Select
                                placeholder="Chọn ngân hàng"
                                allowClear
                                filterOption={(input, option) =>
                                  typeof option?.label === 'string'
                                    ? option.label.toLowerCase().includes(input.toLowerCase())
                                    : false
                                }
                                showSearch
                                loading={isLoadingBanks}
                                options={banks.map(item => ({
                                  value: item?.bankCode,
                                  label: item?.bankName,
                                }))}
                                onChange={(value, option) => {
                                  form.setFieldsValue({
                                    bankAccount: {
                                      [name]: {
                                        bankCode: value,
                                        bankName: Array.isArray(option) ? '' : option?.label || '',
                                        accountNumber: form.getFieldValue(['bankAccount', name, 'accountNumber']) || '',
                                        beneciary: form.getFieldValue(['bankAccount', name, 'beneciary']) || '',
                                      },
                                    },
                                  });
                                }}
                              />
                            </Form.Item>
                          </Col>
                          <Col span={6}>
                            <Form.Item
                              {...restField}
                              name={[name, 'accountNumber']}
                              label="Số tài khoản"
                              rules={[
                                {
                                  message: 'Vui lòng nhập số tài khoản',
                                  validator: (_, value) => {
                                    const bankCode = form.getFieldValue(['bankAccount', name, 'bankCode']);
                                    if (bankCode && !value?.trim()) {
                                      return Promise.reject('Vui lòng nhập số tài khoản');
                                    }
                                    return Promise.resolve();
                                  },
                                },
                              ]}
                            >
                              <Input
                                placeholder="Nhập số tài khoản"
                                maxLength={20}
                                onBlur={e => {
                                  const fieldPath = ['bankAccount', name, 'accountNumber'];
                                  form.setFieldValue(fieldPath, e.target.value.trim());
                                }}
                              />
                            </Form.Item>
                          </Col>
                          <Col span={7}>
                            <Form.Item
                              {...restField}
                              name={[name, 'beneciary']}
                              label="Tên người thụ hưởng"
                              rules={[
                                {
                                  message: 'Vui lòng nhập tên người thụ hưởng',
                                  validator: (_, value) => {
                                    const bankCode = form.getFieldValue(['bankAccount', name, 'bankCode']);
                                    if (bankCode && !value?.trim()) {
                                      return Promise.reject('Vui lòng nhập tên người thụ hưởng');
                                    }
                                    return Promise.resolve();
                                  },
                                },
                              ]}
                            >
                              <Input placeholder="Nhập tên người thụ hưởng" maxLength={255} />
                            </Form.Item>
                          </Col>
                          <Col span={1}>
                            <CloseOutlined
                              style={{ marginTop: 15, textAlign: 'center', cursor: 'pointer' }}
                              onClick={() => {
                                remove(name);
                                handleRemoveBankAccount(name);
                              }}
                            />
                          </Col>
                        </Row>
                      ))}

                      {fields.length < 10 ? (
                        <Col span={23}>
                          <Button
                            type="dashed"
                            onClick={() => {
                              add({ bankCode: undefined, bankName: '', accountNumber: '', beneciary: '' });
                            }}
                            style={{ padding: 0 }}
                            block
                            icon={<PlusOutlined />}
                          >
                            Thêm tài khoản giao dịch
                          </Button>
                        </Col>
                      ) : null}
                    </>
                  )}
                </Form.List>
              </div>
            </Form.Item>
            <p style={{ marginBottom: 8 }}>
              Tài khoản giao dịch chính (default) <span style={{ color: 'red' }}>*</span>
            </p>
            <div
              style={{
                backgroundColor: 'rgba(0, 0, 0, 0.02)',
                border: '1px solid var(--colorBorder, rgba(0, 0, 0, 0.15))',
                padding: 20,
                margin: 0,
                marginBottom: 16,
              }}
            >
              <Form.Item
                name="mainBankId"
                rules={[{ required: true, message: 'Vui lòng chọn tài khoản giao dịch chính' }]}
              >
                <Select
                  placeholder="Chọn tài khoản giao dịch chính"
                  options={defaultBankOptions}
                  onChange={handleSelectBankInfo}
                  showSearch
                  optionFilterProp="label"
                  filterOption={(input, option) =>
                    typeof option?.label === 'string' ? option.label.toLowerCase().includes(input.toLowerCase()) : false
                  }
                />
              </Form.Item>
              {/* Trường ẩn để lưu trữ đối tượng MainBank */}
              <Form.Item name="mainBank" hidden>
                <Input type="hidden" />
              </Form.Item>
            </div>

            {/* Thông tin dự án */}
            <Title level={5}>Thông tin dự án</Title>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="Chương trình bán hàng" name="salesProgramId">
                  <SingleSelectLazy
                    apiQuery={getListProjectSaleProgram}
                    queryKey={['project-sale-program', projectId]}
                    keysLabel={'name'}
                    placeholder="Chọn chương trình bán hàng"
                    handleSelect={handleSelectSaleProgram}
                    moreParams={{ projectId: projectId }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Mã sản phẩm" name="demandPropertyId">
                  <SingleSelectLazy
                    apiQuery={getProductUnits}
                    queryKey={['get-list-product-units']}
                    keysLabel={'code'}
                    placeholder="Chọn mã sản phẩm"
                    handleSelect={handleSelectProductCode}
                    moreParams={
                      propertyUnitParams.salesProgramIds || propertyUnitParams.projectId
                        ? propertyUnitParams
                        : undefined
                    }
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Số tiền đăng ký"
                  name="registrationAmount"
                  rules={[{ required: true, message: 'Vui lòng nhập số tiền đăng ký' }]}
                >
                  <InputNumber
                    placeholder="Nhập số tiền đăng ký"
                    style={{ width: '100%' }}
                    formatter={formatNumber}
                    maxLength={15}
                    addonAfter="VND"
                    min={0}
                    precision={0}
                  />
                </Form.Item>
              </Col>
            </Row>
          </Col>
          <Col span={8}>
            {/* Thông tin khác */}
            <Title level={5}>Thông tin khác</Title>
            <Form.Item label="Ghi chú" name="description">
              <TextArea placeholder="Nhập ghi chú" maxLength={250} rows={4} />
            </Form.Item>
            <Form.Item name="files">
              <UploadFileBookingTicket
                fileList={fileList}
                setFileList={setFileList}
                uploadPath={type === 'business' ? 'deposit-request/business' : 'deposit-request/personal'}
                size={25}
              />
            </Form.Item>
          </Col>
          <Form.Item name="idBookingTicketCode" hidden>
            <Input type="hidden" />
          </Form.Item>
        </Row>
      </Form>
    </ModalComponent>
  );
};

export default CreateBookingRequestStep3;
