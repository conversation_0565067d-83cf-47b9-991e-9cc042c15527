import { useState } from 'react';
import CreateBookingRequestStep1 from './step1';
import CreateBookingRequestStep3 from './step3';
import { Modal } from 'antd';
import { DuplicateResp, FormVerify } from '../../../../../types/bookingRequest';

interface CreateBookingRequestModalProps {
  isVisible: boolean;
  onCancel: () => void;
  selectedPropertyId?: string;
}

const CreateBookingRequestFreeModal: React.FC<CreateBookingRequestModalProps> = ({
  isVisible,
  onCancel,
  selectedPropertyId,
}) => {
  const [step, setStep] = useState(1);
  const [step1Data, setStep1Data] = useState<FormVerify | null>(null);
  const [duplicateData, setDuplicateData] = useState<DuplicateResp | null>(null);

  const handleNext = (values: FormVerify, duplicate?: DuplicateResp | null) => {
    setStep1Data(values);
    setDuplicateData(duplicate || null); // Ensure `null` is set if `undefined` is passed
    setStep(3);
  };

  const handleSave = () => {
    // TODO: Gọi API để cập nhật KHCT/KHTN dựa trên duplicateData
    onCancel(); // Đóng modal và quay về màn danh sách
    setStep(1);
    setStep1Data(null);
    setDuplicateData(null);
  };

  const handleCancel = (hasFormChanged: boolean = false, resetForm?: () => void) => {
    if (hasFormChanged) {
      Modal.confirm({
        title: 'Xác nhận hủy',
        content: 'Dữ liệu chưa được lưu, bạn có chắc chắn muốn thoát khỏi trang không?',
        cancelText: 'Quay lại',
        okText: 'Đồng ý',
        onOk: () => {
          resetForm?.(); // Reset form ngay cả khi không có thay đổi
          onCancel(); // Thông báo parent component để quay về màn danh sách
          setStep(1);
          setStep1Data(null);
          setDuplicateData(null);
        },
        okButtonProps: {
          type: 'default',
        },
        cancelButtonProps: {
          type: 'primary',
        },
      });
    } else {
      resetForm?.(); // Reset form ngay cả khi không có thay đổi
      onCancel();
      setStep(1);
      setStep1Data(null);
      setDuplicateData(null);
    }
  };

  return (
    <div>
      {step === 1 && <CreateBookingRequestStep1 visible={isVisible} onCancel={handleCancel} onNext={handleNext} />}
      {step === 3 && (
        <CreateBookingRequestStep3
          visible={isVisible}
          onCancel={handleCancel}
          onSave={handleSave}
          initialData={step1Data as DuplicateResp | undefined}
          duplicateData={duplicateData}
          selectedPropertyId={selectedPropertyId}
        />
      )}
    </div>
  );
};

export default CreateBookingRequestFreeModal;
