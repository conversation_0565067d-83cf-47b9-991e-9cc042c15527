import React, { useEffect, useState } from 'react';
import InputSearch from '../../../components/input/InputSearch';
import TableComponent from '../../../components/table';
import { useCreateField, useFetch } from '../../../hooks';
import { Button, Empty, notification, Radio, Space } from 'antd';
import './styles.scss';
import {
  ArrowDownOutlined,
  ArrowRightOutlined,
  DeleteOutlined,
  EditOutlined,
  FileDoneOutlined,
  FormOutlined,
  RedoOutlined,
  SelectOutlined,
} from '@ant-design/icons';
import { ITransaction } from '../../../types/transaction';
import {
  getListPaymentTransaction,
  sendLiquidateRegistration,
  sendRegistration,
  sendRevokeRegistration,
} from '../../../service/transaction';
import dayjs from 'dayjs';
import CreateBookingRequestFreeModal from './components/CreateBookingRequestModal';
import { useParams } from 'react-router-dom';
import { useSellProgramStore } from '../../Store';
import { formatNumber } from '../../../utilities/regex';
import { getTransactionStatusInfo } from '../../../constants/common';
import CountUpTimer from '../../../hooks/countUpTimer';
import FormModalUnitConversion from '../propertyOperateSellProgram/popupUnitConversion/modalUnitConversion';

enum ProductListType {
  FREE = 'FREE',
  NAMED = 'NAMED',
}
const formatDate = (date: string) => {
  const formattedTime = dayjs(date).format('HH:mm');
  const formattedDate = dayjs(date).format('DD/MM/YY');
  return (
    <>
      <div className="text-time">{formattedTime}</div>
      <div>{formattedDate}</div>
    </>
  );
};

const TransactionManagementTab = () => {
  const { id: projectId } = useParams<{ id: string }>();
  const [productListType, setProductListType] = useState<ProductListType>(ProductListType.FREE);
  const [selectedRowKey, setSelectedRowKey] = useState<React.Key | undefined>(undefined);
  const [isOpenBookingRequestModal, setBookingRequestModal] = useState(false);
  const salesProgramIds = useSellProgramStore(state => state.ArrSalesProgramIds);
  const [initialLoadCompleted, setInitialLoadCompleted] = useState(false);
  const [isUnitConversionModalVisible, setIsUnitConversionModalVisible] = useState(false);
  const [selectedProductCode, setSelectedProductCode] = useState<string | undefined>();
  const setSalesProgramIds = useSellProgramStore(state => state.ArrSetSalesProgramIds);

  const toggleModal = (visible: boolean, productCode?: string) => {
    setIsUnitConversionModalVisible(visible);
    if (visible && productCode) {
      setSelectedProductCode(productCode);
    } else {
      setSelectedProductCode(undefined);
    }
  };

  const {
    data: transaction,
    isLoading,
    isFetching,
  } = useFetch<ITransaction[]>({
    queryKeyArrWithFilter: ['get-list-payment-transaction', projectId, salesProgramIds],
    api: getListPaymentTransaction,
    enabled: productListType === ProductListType.FREE,
    moreParams: {
      projectId: projectId || '',
      salesProgramIds: salesProgramIds.join(',') || '',
    },
    refetchInterval: 60000,
  });

  useEffect(() => {
    if (transaction?.data?.data && !initialLoadCompleted) {
      setInitialLoadCompleted(true);
    }
  }, [transaction, initialLoadCompleted]);

  useEffect(() => {
    setInitialLoadCompleted(false);
  }, [productListType]);

  const isLoadingTable = isLoading || (isFetching && !initialLoadCompleted);

  const updateRegistration = useCreateField({
    apiQuery: sendRegistration,
    messageSuccess: 'Cập nhật thành công!',
    messageError: 'Cập nhật thất bại!',
    keyOfListQuery: ['get-list-payment-transaction', projectId, salesProgramIds],
    keyOfDetailQuery: ['getPropertyUnitByProjectIdAndSalesProgramIds', projectId, salesProgramIds],
  });

  const sendRevoke = useCreateField({
    apiQuery: sendRevokeRegistration,
    messageSuccess: 'Thu hồi thành công!',
    messageError: 'Thu hồi thất bại!',
    keyOfListQuery: ['get-list-payment-transaction', projectId, salesProgramIds],
    keyOfDetailQuery: ['getPropertyUnitByProjectIdAndSalesProgramIds', projectId, salesProgramIds],
  });
  const sendLiquidate = useCreateField({
    apiQuery: sendLiquidateRegistration,
    messageSuccess: 'Thanh lý thành công!',
    messageError: 'Thanh lý thất bại!',
    keyOfListQuery: ['get-list-payment-transaction', projectId, salesProgramIds],
    keyOfDetailQuery: ['getPropertyUnitByProjectIdAndSalesProgramIds', projectId, salesProgramIds],
  });

  const dataPaymentTransaction = transaction?.data?.data || [];

  // Khai báo cột cho tab "Danh sách sản phẩm tự do"
  const freeProductsColumns = [
    {
      title: 'Số sản phẩm',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: 'Giá chưa VAT',
      dataIndex: 'price',
      key: 'price',
      render: (price: string | number | undefined) => (price ? <span>{formatNumber(price)} VNĐ</span> : null),
    },
    {
      title: 'Giá có VAT',
      dataIndex: 'priceAboveVat',
      key: 'priceAboveVat',
      render: (priceAboveVat: string | number | undefined) =>
        priceAboveVat ? <span>{formatNumber(priceAboveVat)} VNĐ</span> : null,
    },
    {
      title: 'Khách hàng',
      dataIndex: ['priorities'],
      key: 'customerName',
      render: (priorities?: { customerName: string }[]) =>
        priorities?.[0]?.customerName ? <span>{priorities[0].customerName}</span> : null,
    },
    {
      title: 'Trạng thái',
      dataIndex: 'primaryStatus',
      key: 'primaryStatus',
      render: (primaryStatus: string) => {
        const statusInfo = getTransactionStatusInfo(primaryStatus);
        return <span style={{ color: statusInfo.color }}>{statusInfo.label}</span>;
      },
    },
    {
      title: 'Mã sản phẩm',
      dataIndex: 'attributes',
      key: 'productCode',
      render: (attributes?: Array<{ id: string; attributeName: string; value: string }>) => {
        const productCodeAttr = attributes?.find(attr => attr.attributeName === 'View 1');
        return productCodeAttr?.value ? <span>{productCodeAttr.value}</span> : null;
      },
    },
    {
      title: 'Ngày cập nhật',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      className: 'text-time-td',
      render: formatDate,
    },
    {
      title: 'Đơn vị bán hàng',
      dataIndex: ['pos', 'name'],
      key: 'posName',
      render: (_: unknown, record: { pos?: { name?: string } }) => record?.pos?.name || null,
    },
    {
      title: 'Thời gian',
      dataIndex: 'registeredDate',
      key: 'registeredDate',
      render: (_: unknown, record: ITransaction) => {
        const isValidDate = !!record?.registeredDate && dayjs(record.registeredDate).isValid();

        if (!isValidDate) return null;

        const dwellTimeMinutes = record?.salesProgram?.dwellTime || 0;

        const endTime =
          dwellTimeMinutes === 0
            ? record.registeredDate
            : dayjs(record.registeredDate).add(dwellTimeMinutes, 'minute').toISOString();

        return <CountUpTimer startTime={record.registeredDate} endTime={endTime} />;
      },
    },
  ];

  const handleRowSelectionChange = (selectedRowKeys: React.Key[]) => {
    const selectedKey = selectedRowKeys[0];
    setSelectedRowKey(selectedKey);

    if (selectedKey) {
      const selectedRecord = dataPaymentTransaction.find(item => getRowKey(item) === selectedKey);

      if (selectedRecord?.salesProgram?.id) {
        setSalesProgramIds([selectedRecord.salesProgram.id]);
      }
    }
  };

  const getRowKey = (record: ITransaction) => {
    if (record.id) return record.id;
    if (record.code) return record.code;
    if (record.escrowTicketCode) return record.escrowTicketCode;
    return JSON.stringify(record);
  };
  const handleRegistration = () => {
    if (!selectedRowKey) return;

    const selectedRecord = dataPaymentTransaction.find(item => getRowKey(item) === selectedRowKey);

    if (selectedRecord && selectedRecord.id && selectedRecord.primaryStatus === 'COMING') {
      updateRegistration.mutate({
        projectId: projectId || '',
        propertyUnitId: selectedRecord.id,
        unRegister: false,
      });
    }
  };

  const isProductRegistered = () => {
    if (!selectedRowKey) return false;

    const selectedRecord = dataPaymentTransaction.find(item => getRowKey(item) === selectedRowKey);
    return selectedRecord && selectedRecord.primaryStatus !== 'COMING';
  };

  const canRegister = () => {
    if (!selectedRowKey) return false;

    const selectedRecord = dataPaymentTransaction.find(item => getRowKey(item) === selectedRowKey);
    return selectedRecord && selectedRecord.primaryStatus === 'COMING';
  };
  // Thu hồi
  const canRecall = () => {
    if (!selectedRowKey) return false;

    const selectedRecord = dataPaymentTransaction.find(item => getRowKey(item) === selectedRowKey);
    if (!selectedRecord) return false;

    const recallableStatuses = [
      'COMING', // Đang mở bán
      'PROCESSING', // ĐVBH đã đăng ký GD
      'CONFIRM', // ĐVBH đã xác nhận
      'LOCK_COMFIRM', // Đang kiểm tra hồ sơ
    ];
    return recallableStatuses.includes(selectedRecord.primaryStatus);
  };

  const handleRecall = () => {
    if (!canRecall()) return;

    const selectedRecord = dataPaymentTransaction.find(item => getRowKey(item) === selectedRowKey);
    if (!selectedRecord || !selectedRecord.id) return;
    sendRevoke.mutate({
      projectId: projectId || '',
      propertyIds: [selectedRecord.id],
    });
  };
  // Thanh lý
  const canLiquidate = () => {
    if (!selectedRowKey) return false;

    const selectedRecord = dataPaymentTransaction.find(item => getRowKey(item) === selectedRowKey);
    if (!selectedRecord) return false;

    const recallableStatuses = [
      'SUCCESS', // Đã thành công
    ];
    return recallableStatuses.includes(selectedRecord.primaryStatus);
  };

  const handleLiquidate = () => {
    if (!canRecall()) return;

    const selectedRecord = dataPaymentTransaction.find(item => getRowKey(item) === selectedRowKey);
    if (!selectedRecord || !selectedRecord.id) return;
    sendLiquidate.mutate({
      projectId: projectId || '',
      propertyIds: selectedRecord.id,
    });
  };
  // Chuyển
  const canTransfer = () => {
    if (!selectedRowKey) return false;

    const selectedRecord = dataPaymentTransaction.find(item => getRowKey(item) === selectedRowKey);
    if (!selectedRecord) return false;

    const transferableStatuses = [
      'COMING', // Đang mở bán
      'PROCESSING', // ĐVBH đã đăng ký GD
      'CONFIRM', // ĐVBH đã xác nhận
      'LOCK_COMFIRM', // Đang kiểm tra hồ sơ
    ];

    return transferableStatuses.includes(selectedRecord.primaryStatus);
  };

  return (
    <div className="transaction-management-tab">
      <Space className="transaction-management-radio-group">
        <Radio.Group
          value={productListType}
          onChange={e => setProductListType(e.target.value)}
          optionType="button"
          className="rounded-radio-group"
        >
          <Radio.Button value={ProductListType.NAMED}>Danh sách sản phẩm ráp đích danh</Radio.Button>
          <Radio.Button value={ProductListType.FREE}>Danh sách sản phẩm tự do</Radio.Button>
        </Radio.Group>
      </Space>

      {/* Tab Danh sách sản phẩm tự do */}
      {productListType === ProductListType.FREE && (
        <>
          <div className="header-content">
            <InputSearch placeholder="Tìm kiếm" keySearch="search" />
            <div className="header-content__button">
              <Button size="small" onClick={handleRegistration} disabled={!canRegister()}>
                <EditOutlined /> Đăng ký
              </Button>
              <Button size="small" disabled={!isProductRegistered()}>
                <RedoOutlined /> Trả về
              </Button>
              <Button size="small" onClick={() => setBookingRequestModal(true)} disabled={!isProductRegistered()}>
                <SelectOutlined /> Chọn khách hàng
              </Button>
              <Button size="small" disabled={!isProductRegistered()}>
                <FormOutlined /> Xác nhận
              </Button>
              <Button size="small" disabled={!isProductRegistered()}>
                <FileDoneOutlined /> Xác nhận GD
              </Button>
              <Button
                size="small"
                disabled={!canTransfer()}
                onClick={() => {
                  const selectedRecord = dataPaymentTransaction.find(item => getRowKey(item) === selectedRowKey);
                  if (!selectedRecord) return;
                  if (selectedRecord.salesProgram?.id) {
                    setSalesProgramIds([selectedRecord.salesProgram.id]);
                  }
                  if (selectedRecord.code) {
                    toggleModal(true, selectedRecord.code);
                  } else {
                    notification.warning({
                      message: 'Không tìm thấy thông tin mã sản phẩm',
                    });
                  }
                }}
              >
                <ArrowRightOutlined /> Chuyển
              </Button>
              <Button size="small" disabled={!canRecall()} onClick={handleRecall}>
                <ArrowDownOutlined /> Thu hồi
              </Button>
              <Button size="small" disabled={!canLiquidate()} onClick={handleLiquidate}>
                <DeleteOutlined /> Thanh lý
              </Button>
            </div>
          </div>
          <TableComponent
            dataSource={dataPaymentTransaction}
            columns={freeProductsColumns}
            queryKeyArr={['get-list-payment-transaction', projectId, salesProgramIds]}
            loading={isLoadingTable}
            rowKey={getRowKey}
            onRow={record => ({
              onClick: () => {
                const key = getRowKey(record);
                setSelectedRowKey(prevKey => (prevKey === key ? undefined : key));
              },
            })}
            rowSelection={{
              type: 'radio',
              selectedRowKeys: selectedRowKey ? [selectedRowKey] : [],
              onChange: handleRowSelectionChange,
            }}
            isPagination={false}
          />
          <CreateBookingRequestFreeModal
            isVisible={isOpenBookingRequestModal}
            onCancel={() => setBookingRequestModal(false)}
            selectedPropertyId={
              selectedRowKey ? dataPaymentTransaction.find(item => getRowKey(item) === selectedRowKey)?.id : undefined
            }
          />
          <FormModalUnitConversion
            visible={isUnitConversionModalVisible}
            onClose={() => toggleModal(false)}
            productCode={selectedProductCode}
          />
        </>
      )}

      {productListType === ProductListType.NAMED && (
        <div>
          <Empty description={<span>Tính năng danh sách sản phẩm ráp đích danh đang được phát triển</span>} />
        </div>
      )}
    </div>
  );
};

export default TransactionManagementTab;
