// import './styles.scss';
import { <PERSON>ton, Spin, Tabs, TabsProps, Typography } from 'antd';
import { useLocation, useParams, useSearchParams } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { DownSquareOutlined } from '@ant-design/icons';
import { Dropdown } from 'antd/lib';
import BookingTicketPersonal from './bookingTicketPersonal/List';
import { useFetch } from '../../hooks';
import { getDetailProject, getListProjectSaleProgram } from '../../service/project';
import { DetailProject } from '../../types/project/project';
import PropertyOperateSellProgram from './propertyOperateSellProgram';
import BreadCrumbComponent from '../../components/breadCrumb';
import { PROJECTS_MANAGEMENT } from '../../configs/path';
import FPTLogo from '../../assets/images/Default_Logo_Project.png';
import { useSellProgramStore } from '../Store';
import DepositRequiredManagementTab from './depositRequiredTab/List';
import TransactionManagementTab from './transactionTab';

const { Title, Text } = Typography;

const OperateSellProgram = () => {
  const salesProgramIds = useSellProgramStore(state => state.ArrSalesProgramIds);

  const { id: projectId } = useParams<{ id: string }>();
  const setSalesProgramIds = useSellProgramStore(state => state.ArrSetSalesProgramIds);
  const selectedProgramNames = useSellProgramStore(state => state.selectedProgramNames);
  const setSelectedProgramNames = useSellProgramStore(state => state.setSelectedProgramNames);
  const [salesProgramsOptions, setSalesProgramsOptions] = useState<{ label: string; value: string }[]>([]);
  const location = useLocation() as unknown as Location & { state: { activeTab?: string; from: string } };
  const { state } = location;
  const { from } = state || {};
  const pathname = location.pathname;
  const [searchParams, setSearchParams] = useSearchParams();

  const getInitialTab = () => {
    const tabFromUrl = searchParams.get('tab');
    const tabFromState = location.state?.activeTab;
    return tabFromUrl || tabFromState || '1';
  };
  const [activeTab, setActiveTab] = useState(getInitialTab());

  const { data: dataDetailProjects, isLoading } = useFetch<DetailProject>({
    queryKeyArr: ['detail-project', projectId],
    api: () => getDetailProject(projectId),
    withFilter: false,
    enabled: !!projectId,
    cacheTime: 10,
  });
  const dataDetailProject = dataDetailProjects?.data?.data;

  const handleTabChange = (key: string) => {
    setActiveTab(key);

    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set('tab', key);
    setSearchParams(newSearchParams, { replace: true });
  };
  useEffect(() => {
    const tabFromUrl = searchParams.get('tab');
    if (tabFromUrl && tabFromUrl !== activeTab) {
      setActiveTab(tabFromUrl);
    }
  }, [searchParams]);

  useEffect(() => {
    const tabFromUrl = searchParams.get('tab');
    if (!tabFromUrl) {
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.set('tab', activeTab);
      setSearchParams(newSearchParams, { replace: true });
    }
  }, []);

  useEffect(() => {
    if (salesProgramIds && salesProgramsOptions.length > 0) {
      const selectedPrograms = salesProgramsOptions.filter(program => salesProgramIds.includes(program.value));
      setSelectedProgramNames(selectedPrograms.map(program => program.label));
    }
  }, [salesProgramIds, salesProgramsOptions, setSelectedProgramNames]);
  useEffect(() => {
    const fetchSalesPrograms = async () => {
      try {
        const response = await getListProjectSaleProgram({ projectId: projectId });
        const programs = response?.data?.data?.rows?.map((program: { id: string; name: string }) => ({
          label: program.name,
          value: program.id,
        }));
        setSalesProgramsOptions(programs || []);

        // Cập nhật Zustand Store với saleProgramId từ URL
        if (salesProgramIds) {
          setSalesProgramIds(salesProgramIds); // Lưu saleProgramId vào Zustand Store
          const selectedProgram = programs?.find((program: { value: string }) =>
            salesProgramIds.includes(program.value),
          );
          if (selectedProgram) {
            setSelectedProgramNames([selectedProgram.label]); // Lưu tên chương trình đã chọn vào Zustand Store
          }
        }
      } catch (error) {
        console.error('Error fetching sales programs:', error);
      }
    };
    fetchSalesPrograms();
  }, [projectId, setSalesProgramIds, setSelectedProgramNames]);

  const handleMenuClick = ({ key }: { key: string }) => {
    const selectedProgram = salesProgramsOptions.find(option => option.value === key);
    if (selectedProgram) {
      const updatedNames = [...selectedProgramNames];
      const updatedIds = [...useSellProgramStore.getState().ArrSalesProgramIds]; // Lấy danh sách salesProgramIds hiện tại

      if (updatedNames.includes(selectedProgram.label)) {
        if (updatedNames.length === 1) {
          return;
        }
        const index = updatedNames.indexOf(selectedProgram.label);
        updatedNames.splice(index, 1);
        updatedIds.splice(updatedIds.indexOf(selectedProgram.value), 1); // Bỏ id khỏi danh sách
      } else {
        // Nếu chưa chọn, thêm vào
        updatedNames.push(selectedProgram.label);
        updatedIds.push(selectedProgram.value); // Thêm id vào danh sách
      }

      setSelectedProgramNames(updatedNames); // Cập nhật Zustand store cho selectedProgramNames
      useSellProgramStore.getState().ArrSetSalesProgramIds(updatedIds); // Cập nhật Zustand store cho salesProgramIds
    }
  };

  const menuItems = salesProgramsOptions.map(option => ({
    key: option.value,
    label: (
      <>
        <input type="checkbox" checked={selectedProgramNames.includes(option.label)} readOnly />
        {option.label}
      </>
    ),
  }));

  const menu = {
    items: menuItems,
    onClick: handleMenuClick,
  };

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: 'Bảng hàng',
      children: <PropertyOperateSellProgram dataProject={dataDetailProject} />,
    },
    {
      key: '2',
      label: 'Danh sách yêu cầu đặt chỗ',
      children: <BookingTicketPersonal />,
    },
    {
      key: '3',
      label: 'Danh sách yêu cầu đặt cọc',
      children: <DepositRequiredManagementTab />,
    },
    {
      key: '4',
      label: 'Cấu hình',
      children: '',
    },
    {
      key: '5',
      label: 'Danh sách giao dịch',
      children: <TransactionManagementTab />,
    },
    {
      key: '6',
      label: 'Biểu mẫu',
      children: '',
    },
  ];

  const breadcrumbItems = (() => {
    if (from === '/sell-program-management') {
      return [
        { label: 'Dự án và sản phẩm' },
        { label: 'Chương trình bán hàng', path: '/sell-program-management' },
        { label: `Vận hành bán hàng dự án ${dataDetailProject?.name || ''}` },
      ];
    } else if (pathname.startsWith('/projects/')) {
      return [
        { label: 'Dự án và sản phẩm' },
        { label: 'Danh sách dự án', path: '/projects' },
        { label: `Thông tin dự án - ${dataDetailProject?.name || ''}`, path: `/projects/${projectId}` },
        { label: `Vận hành bán hàng dự án ${dataDetailProject?.name || ''}`, path: '#' },
      ];
    } else if (pathname === '/project') {
      return [
        { label: 'Dự án và sản phẩm' },
        { label: 'Danh sách dự án', path: '/projects' },
        { label: `Vận hành bán hàng dự án ${dataDetailProject?.name || ''}`, path: '#' },
      ];
    } else {
      return [{ label: `${dataDetailProject?.name}`, path: `${PROJECTS_MANAGEMENT}/${projectId}` }];
    }
  })();

  return (
    <Spin spinning={isLoading}>
      <BreadCrumbComponent customItems={breadcrumbItems} noMenu={true} />
      <div className="project-card">
        <div className="project-info">
          <Title level={5}>
            <Dropdown menu={menu} trigger={['click']}>
              <DownSquareOutlined style={{ cursor: 'pointer', marginRight: 8 }} />
            </Dropdown>
            {selectedProgramNames.join(', ')}
          </Title>
          <Text type="secondary">
            Dự án: <span className="text-type">{dataDetailProject?.name}</span>
          </Text>
        </div>
        <div className="project-actions">
          <Button>Điều khiển dự án</Button>
        </div>
        <div className="project-image">
          <img
            src={
              dataDetailProject?.imageUrl
                ? `${import.meta.env.VITE_S3_IMAGE_URL}/${dataDetailProject?.imageUrl}`
                : FPTLogo
            }
            alt="Project"
          />
        </div>
      </div>
      <div className="project_detail-page">
        <Tabs activeKey={activeTab} onChange={handleTabChange} items={items} tabPosition="top" />
      </div>
    </Spin>
  );
};

export default OperateSellProgram;
