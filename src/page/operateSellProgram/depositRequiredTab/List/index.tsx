import { TableColumnsType } from 'antd';
import { useFetch } from '../../../../hooks';
import TableComponent from '../../../../components/table';
import { formatCurrency } from '../../../../utilities/shareFunc';
import { STATUS_COLORS, STATUS_LABELS, TXN_STATUS } from '../../../../constants/common';
import { getListDepositRequired } from '../../../../service/depositRequired';
import { useNavigate, useParams } from 'react-router-dom';
import { ActionsColumns } from '../../../../components/table/components/ActionsColumns';
import { DepositRequiredManagement } from '../../../../types/depositRequired';
import InputSearch from '../../../../components/input/InputSearch';
import { OPERATE_SELL_PROGRAM, PROJECTS_MANAGEMENT } from '../../../../configs/path';

const DepositRequiredManagementTab = () => {
  const navigate = useNavigate();
  const idProject = useParams().id;
  const { data, isLoading } = useFetch<DepositRequiredManagement[]>({
    queryKeyArrWithFilter: ['get-request-deposit', idProject],
    api: getListDepositRequired,
    moreParams: {
      idProject: idProject,
      ticketType: 'YCDC',
      sort: '-createdDate',
    },
  });
  const dataDepositRequired = data?.data?.data?.rows;

  const columns: TableColumnsType<DepositRequiredManagement> = [
    {
      title: 'Mã phiếu',
      dataIndex: 'escrowTicketCode',
      key: 'escrowTicketCode',
    },
    {
      title: 'Số sản phẩm',
      dataIndex: ['propertyUnit', 'code'],
      key: 'code',
    },
    {
      title: 'Số hợp đồng',
      dataIndex: ['contract', 'code'],
      key: 'contractCode',
      render: (value: string) => <a>{value}</a>,
    },
    {
      title: 'Mã phiếu thu',
      dataIndex: 'receipt',
      key: 'receiptCode',
      render: (_, record: DepositRequiredManagement) => {
        const receiptArray = record?.reciept || record?.reciept || [];
        return Array.isArray(receiptArray) && receiptArray.length > 0 ? <span>{receiptArray[0]?.code}</span> : '';
      },
    },
    {
      title: 'Số tiền',
      dataIndex: 'amountRegistration',
      key: 'amountRegistration',
      render: (money: number) => {
        return <span>{formatCurrency(money?.toString())}</span>;
      },
    },
    {
      title: 'Trạng thái phiếu thu',
      render: (_, record: DepositRequiredManagement) => {
        const receiptArray = record?.reciept || record?.reciept || [];

        if (Array.isArray(receiptArray) && receiptArray.length > 0) {
          const status = receiptArray[0]?.status;
          const statusText = STATUS_LABELS[status];
          return <span style={{ color: STATUS_COLORS[status] || 'black' }}>{statusText}</span>;
        }
      },
    },
    {
      title: 'Tên khách hàng',
      key: 'customerName',
      render: record => {
        return record.customer?.type === 'business'
          ? record.customer?.company?.name
          : record.customer?.personalInfo?.name;
      },
    },
    {
      title: 'Số điện thoại',
      dataIndex: ['customer', 'personalInfo', 'phone'],
      key: 'phonePersonalInfo',
    },
    {
      title: 'Số giấy tờ',
      dataIndex: ['customer', 'identityNumber'],
      key: 'identityNumber',
    },
    {
      title: 'Nhân viên chăm sóc',
      dataIndex: ['employee', 'name'],
      key: 'nameEmployee',
    },
    {
      title: 'Trạng thái YCĐCO',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => <span>{TXN_STATUS[status]}</span>,
    },

    {
      title: '',
      dataIndex: 'action',
      key: 'action',
      align: 'center',
      width: 50,
      render: (_, record: DepositRequiredManagement) => {
        return (
          <ActionsColumns
            handleViewDetail={() => {
              navigate(`${PROJECTS_MANAGEMENT}${OPERATE_SELL_PROGRAM}/deposit/${record?.id}`);
            }}
            handleEdit={() => {}}
          />
        );
      },
    },
  ];

  return (
    <div>
      <div className="header-content">
        <InputSearch placeholder="Tìm kiếm" keySearch="search" />
      </div>
      <TableComponent
        dataSource={dataDepositRequired}
        columns={columns}
        queryKeyArr={['get-request-deposit', idProject]}
        loading={isLoading}
      />
    </div>
  );
};

export default DepositRequiredManagementTab;
