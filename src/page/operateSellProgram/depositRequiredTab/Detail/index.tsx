import React, { useEffect, useState } from 'react';
import { Form, Input, Select, DatePicker, Checkbox, Row, Col, Radio, Typography, Button, UploadFile } from 'antd';
import { FORMAT_DATE, OPTIONS_GENDER_STRING } from '../../../../constants/common';
import BreadCrumbComponent from '../../../../components/breadCrumb';
import SelectAddress from '../../../../components/selectAddress';
import FPTLogo from '../../../../assets/images/FPT_logo.png';
import { useFetch } from '../../../../hooks';
import { getDetailDepositRequired } from '../../../../service/depositRequired';
import { useParams } from 'react-router-dom';
import { BookingTicketInfo } from '../../../../types/bookingRequest';
import UploadFileBookingTicket from '../../bookingTicketPersonal/components/UploadFileBookingTicket';
import { formatCurrency } from '../../../../utilities/shareFunc';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;
interface ExtendedUploadFile extends UploadFile {
  key?: string;
  absoluteUrl?: string;
}

const DepositRequiredDetail = () => {
  const { depositId } = useParams<{ depositId: string }>();
  const [form] = Form.useForm();
  const type = Form.useWatch(['type'], form);
  const [yearOnly, setYearOnly] = useState(false);
  const [fileList, setFileList] = useState<ExtendedUploadFile[]>([]);

  const { data: DepositRequiredData } = useFetch<BookingTicketInfo>({
    api: () => depositId && getDetailDepositRequired(depositId),
    queryKeyArr: ['detail-deposit-required', depositId],
    moreParams: { depositId: depositId },
    enabled: !!depositId,
  });
  const depositRequired = DepositRequiredData?.data?.data;

  useEffect(() => {
    if (depositRequired) {
      const bankInfoDefault = `${depositRequired?.customer?.bankInfo.name} - ${depositRequired?.customer?.bankInfo.accountNumber} - ${depositRequired?.customer?.bankInfo.beneciary}`;
      const transformeddepositRequired = {
        employeeTakeCareId: depositRequired.employee?.name || '',
        depositRequiredCode: depositRequired.bookingTicketCode || '',
        type: depositRequired.customer?.type || 'business',
        customer: {
          code: depositRequired?.customer?.code || '',
        },
        identityType: depositRequired.customer?.identityType || '',
        taxCode: depositRequired.customer?.taxCode || '',
        identityNumber: depositRequired.customer?.identityNumber || '',
        identityDate: depositRequired.customer?.identityDate
          ? dayjs(depositRequired.customer.identityDate, FORMAT_DATE)
          : null,
        identityPlace: depositRequired.customer?.identityPlace || '',
        company: {
          name: depositRequired.customer?.company?.name || '',
        },
        personalInfo: {
          name: depositRequired.customer?.personalInfo?.name || '',
          phone: depositRequired.customer?.personalInfo?.phone || '',
          email: depositRequired.customer?.personalInfo?.email || '',
        },
        info: {
          gender: depositRequired.customer?.info?.gender,
          onlyYear: depositRequired.customer?.info?.onlyYear || false,
          birthday: depositRequired.customer?.info?.birthday
            ? dayjs(depositRequired.customer?.info?.birthday, FORMAT_DATE)
            : null,
          birthdayYear: depositRequired.customer?.info?.birthdayYear
            ? dayjs(depositRequired.customer?.info?.birthdayYear)
            : null,
          cloneAddress: depositRequired.customer?.info?.useResidentialAddress || false,
        },
        companyAdress: depositRequired.customer?.company?.address || '',
        address: depositRequired.customer?.info?.address || '',
        rootAddress: depositRequired.customer?.info?.rootAddress || '',
        bankInfo: bankInfoDefault || '',
        salesProgram: { ...depositRequired?.salesProgram },
        propertyUnit: { code: depositRequired.propertyUnit?.code || '' },
        amountRegistration: formatCurrency((depositRequired?.amountRegistration ?? 0).toString()),
        note: depositRequired.note || '',
        files: depositRequired.files || [],
      };
      // Cập nhật giá trị form
      form.setFieldsValue(transformeddepositRequired);
      setFileList((depositRequired?.files || []) as ExtendedUploadFile[]);
      setYearOnly(depositRequired.customer?.info?.onlyYear || false);
    }
  }, [depositRequired, form, type]);

  return (
    <div className="booking-ticket-detail">
      <BreadCrumbComponent
        customItems={[
          { label: 'Dự án và sản phẩm' },
          { label: 'Danh sách dự án', path: '/projects' },
          { label: `Thông tin dự án - ${depositRequired?.project?.name || ''}`, path: `/projects` },
          { label: `Vận hành bán hàng dự án ${depositRequired?.project?.name || ''}`, path: '#' },
        ]}
        titleBread={depositRequired?.code}
        noMenu={true}
      />
      <div className="project-card">
        <div className="project-info">
          <Title level={5}>{`Phiếu yêu cầu đặt cọc ${depositRequired?.code}`}</Title>
          <Text type="secondary">
            Dự án: <span className="text-type">{depositRequired?.project?.name}</span>
          </Text>
        </div>
        <div className="project-actions">
          <Button>Tạo hợp đồng cọc</Button>
          <Button>Tạo đơn đề nghị</Button>
          <Button>Đề nghị hủy</Button>
        </div>
        <div className="project-image">
          <img
            src={
              depositRequired?.project?.imageUrl
                ? `${import.meta.env.VITE_S3_IMAGE_URL}/${depositRequired?.project?.imageUrl}`
                : FPTLogo
            }
            alt="Project"
          />
        </div>
      </div>
      <Form form={form} layout="vertical" style={{ marginTop: 32 }} initialValues={depositRequired}>
        <Row gutter={32}>
          <Col span={16}>
            <Title level={5}>Thông tin nhân viên</Title>
            <Row gutter={24}>
              <Col span={24}>
                <Form.Item label="Nhân viên chăm sóc" name="employeeTakeCareId">
                  <Input placeholder="Chọn mã nhân viên chăm sóc" disabled />
                </Form.Item>
              </Col>
            </Row>
            <Title level={5}>Thông tin khách hàng</Title>
            <Row gutter={24}>
              <Col span={24}>
                <Form.Item label="Mã yêu cầu đặt chỗ" name="depositRequiredCode">
                  <Input placeholder="Chọn mã yêu cầu đặt chỗ" disabled />
                </Form.Item>
              </Col>
            </Row>
            <Title level={5}>Thông tin khách hàng</Title>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item
                  label="Loại khách hàng"
                  name="type"
                  rules={[{ required: true, message: 'Vui lòng chọn loại khách hàng!' }]}
                >
                  <Select placeholder="Chọn loại khách hàng" disabled>
                    <Option value="individual">Cá nhân</Option>
                    <Option value="business">Doanh nghiệp</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <p style={{ marginBottom: 8 }}>
              Giấy tờ xác minh <span style={{ color: 'red' }}>*</span>
            </p>
            <Row
              gutter={[8, 8]}
              style={{
                backgroundColor: 'rgba(0, 0, 0, 0.02)',
                border: '1px solid var(--colorBorder, rgba(0, 0, 0, 0.15))',
                padding: 20,
                margin: 0,
                marginBottom: 16,
              }}
            >
              <Col span={6}>
                <Form.Item
                  label="Loại giấy tờ"
                  name="identityType"
                  rules={[{ required: true, message: 'Vui lòng chọn loại giấy tờ' }]}
                >
                  <Select placeholder="Chọn loại giấy tờ" disabled>
                    {type === 'individual' ? (
                      <>
                        <Option value="CCCD">Căn cước công dân</Option>
                        <Option value="CMND">CMT</Option>
                        <Option value="PASSPORT">Hộ chiếu</Option>
                      </>
                    ) : (
                      <Option value="MST">Mã số thuế</Option>
                    )}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label="Số giấy tờ"
                  name={type === 'business' ? 'taxCode' : 'identityNumber'}
                  rules={[
                    {
                      required: true,
                      message: 'Vui lòng nhập số giấy tờ',
                    },
                  ]}
                >
                  <Input placeholder="Nhập số giấy tờ" maxLength={60} disabled />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label="Ngày cấp"
                  name="identityDate"
                  rules={[{ required: true, message: 'Vui lòng chọn ngày cấp' }]}
                >
                  <DatePicker placeholder="Chọn ngày cấp" format={FORMAT_DATE} disabled />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label="Nơi cấp"
                  name="identityPlace"
                  rules={[{ required: true, message: 'Vui lòng chọn nơi cấp!' }]}
                >
                  <Input placeholder="Nhập số giấy tờ" maxLength={60} disabled />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label={type === 'business' ? 'Tên công ty' : 'Tên khách hàng'}
                  name={type === 'business' ? ['company', 'name'] : ['personalInfo', 'name']}
                  rules={[
                    {
                      required: true,
                      message: type === 'business' ? 'Vui lòng nhập tên công ty' : 'Vui lòng nhập tên khách hàng',
                    },
                    {
                      validator(_, value) {
                        // Nếu giá trị chỉ chứa khoảng trống (sau khi trim còn rỗng)
                        if (value && value.trim() === '') {
                          return Promise.reject('Không được nhập khoảng trống');
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <Input
                    placeholder={type === 'business' ? 'Nhập tên công ty' : 'Nhập tên khách hàng'}
                    maxLength={type === 'business' ? 120 : 60}
                    onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                      e.target.value = e.target.value.toUpperCase(); // Chuyển đổi trực tiếp
                    }}
                    disabled
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Mã khách hàng"
                  name={['customer', 'code']}
                  required
                  // rules={[{ required: true, message: 'Vui lòng mã khách hàng' }]}
                >
                  <Input placeholder="Mã khách hàng" disabled maxLength={14} />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              {type === 'business' && (
                <Col span={12}>
                  <Form.Item
                    label="Tên người đại diện"
                    name={['personalInfo', 'name']}
                    rules={[{ required: true, message: 'Vui lòng nhập tên người đại diện' }]}
                  >
                    <Input
                      placeholder="Nhập tên người đại diện"
                      maxLength={60}
                      onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                        e.target.value = e.target.value.toUpperCase(); // Chuyển đổi trực tiếp
                      }}
                      disabled
                    />
                  </Form.Item>
                </Col>
              )}

              <Col span={12}>
                <Form.Item label="Số điện thoại" name={['personalInfo', 'phone']}>
                  <Input placeholder="Nhập số điện thoại" maxLength={15} disabled />
                </Form.Item>
              </Col>
              {type === 'individual' && (
                <Col span={12}>
                  <Form.Item
                    label="Địa chỉ email"
                    name={['personalInfo', 'email']}
                    rules={[{ type: 'email', message: 'Địa chỉ email sai định dạng' }]}
                  >
                    <Input placeholder="Nhập địa chỉ email" maxLength={25} disabled />
                  </Form.Item>
                </Col>
              )}
            </Row>

            <Row gutter={16}>
              <Col span={14}>
                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item
                      layout="horizontal"
                      label="Ngày sinh"
                      name={['info', 'onlyYear']}
                      valuePropName="checked"
                    >
                      <Checkbox
                        checked={yearOnly}
                        style={{ marginLeft: 30 }}
                        onChange={e => setYearOnly(e.target.checked)}
                        disabled
                      >
                        Chỉ năm sinh
                      </Checkbox>
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    {yearOnly ? (
                      <Form.Item name={['info', 'birthdayYear']}>
                        <DatePicker picker="year" format="YYYY" placeholder="YYYY" disabled />
                      </Form.Item>
                    ) : (
                      <Form.Item name={['info', 'birthday']}>
                        <DatePicker format={FORMAT_DATE} placeholder={FORMAT_DATE} disabled />
                      </Form.Item>
                    )}
                  </Col>
                </Row>
              </Col>
              <Col span={10}>
                <Form.Item
                  name={['info', 'gender']}
                  label="Giới tính"
                  rules={[{ required: true, message: 'Vui lòng chọn giới tính' }]}
                  layout="horizontal"
                >
                  <Radio.Group options={OPTIONS_GENDER_STRING} style={{ marginLeft: 30 }} disabled />
                </Form.Item>
              </Col>
            </Row>

            {/* Địa chỉ thường trú */}
            <Title level={5}>Địa chỉ thường trú</Title>

            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="Địa chỉ" name={['info', 'address']} className="input-address">
                  <SelectAddress
                    placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
                    parentName={'address'}
                    address={form.getFieldValue('address')}
                    isDisable
                  />
                </Form.Item>
              </Col>
              <Col span={24} className="address">
                <Form.Item name={['address', 'address']}>
                  <Input placeholder="Nhập địa chỉ cụ thể" maxLength={155} disabled />
                </Form.Item>
              </Col>
            </Row>

            {/* Địa chỉ liên lạc */}
            <Title level={5}>Địa chỉ liên lạc</Title>

            <Form.Item name={['info', 'cloneAddress']} valuePropName="checked">
              <Checkbox disabled>Sử dụng địa chỉ thường trú</Checkbox>
            </Form.Item>

            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="Địa chỉ" name={['info', 'rootAddress']} className="input-address">
                  <SelectAddress
                    placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
                    parentName={'rootAddress'}
                    address={form.getFieldValue('rootAddress')}
                    isDisable
                  />
                </Form.Item>
              </Col>
              <Col span={24} className="address">
                <Form.Item name={['rootAddress', 'address']}>
                  <Input placeholder="Nhập địa chỉ cụ thể" maxLength={155} disabled />
                </Form.Item>
              </Col>
            </Row>

            {/* Thông tin thanh toán */}
            <Title level={5}>Thông tin thanh toán</Title>

            <p style={{ marginBottom: 8 }}>
              Tài khoản giao dịch chính (default) <span style={{ color: 'red' }}>*</span>
            </p>
            <div
              style={{
                backgroundColor: 'rgba(0, 0, 0, 0.02)',
                border: '1px solid var(--colorBorder, rgba(0, 0, 0, 0.15))',
                padding: 20,
                margin: 0,
                marginBottom: 16,
              }}
            >
              <Form.Item
                name={'bankInfo'}
                rules={[{ required: true, message: 'Vui lòng chọn tài khoản giao dịch chính' }]}
              >
                <Select placeholder="Chọn tài khoản giao dịch chính" disabled></Select>
              </Form.Item>
            </div>
            {/* Thông tin dự án */}
            <Title level={5}>Thông tin dự án</Title>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="Chương trình bán hàng" name={['salesProgramId']}>
                  <Select placeholder="Chọn chương trình bán hàng" disabled>
                    <Option value="program1">Chương trình 1</Option>
                    <Option value="program2">Chương trình 2</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Mã sản phẩm" name={['propertyUnit', 'code']}>
                  <Input placeholder="Nhập mã sản phẩm" maxLength={50} disabled />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Số tiền đăng ký" name="amountRegistration">
                  <Input placeholder="Nhập số tiền đăng ký" maxLength={155} disabled />
                </Form.Item>
              </Col>
            </Row>
          </Col>

          <Col span={8}>
            {/* Thông tin khác */}
            <Title level={5}>Thông tin khác</Title>
            <Form.Item label="Ghi chú" name="note">
              <TextArea placeholder="Nhập ghi chú" maxLength={250} rows={4} disabled />
            </Form.Item>
            <Form.Item name="files">
              <UploadFileBookingTicket
                fileList={fileList}
                setFileList={setFileList}
                uploadPath={type === 'business' ? 'deposit-request/business' : 'deposit-request/personal'}
                size={25}
                isDetail
              />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </div>
  );
};

export default DepositRequiredDetail;
