import { DEFAULT_PARAMS } from '../../constants/common';
import { create } from 'zustand';
import { URLSearchParamsInit } from 'react-router-dom';
import { IOfferRefundMoneyAccountancy, TabOfferRefundMoneyAccountancy } from '../../types/offer';

type OfferRefundMoneyAccountancyData = {
  WAITING_TRANSFER: IOfferRefundMoneyAccountancy[]; // Replace `any` with your real type
  TRANSFERED: IOfferRefundMoneyAccountancy[];
};

interface OfferRefundMoneyAccountancyDataStore {
  tab: TabOfferRefundMoneyAccountancy;
  dataOfferRefund: OfferRefundMoneyAccountancyData;
  loading?: boolean;
  tabsFilters: Record<string, URLSearchParamsInit>;
  initialValue?: IOfferRefundMoneyAccountancy;
  isModified: boolean;
  setIsModified: (isModified: boolean) => void;
  setTab: (value: TabOfferRefundMoneyAccountancy) => void;
  setDataForTab: (tab: TabOfferRefundMoneyAccountancy, data: IOfferRefundMoneyAccountancy[]) => void;
  setTabFilter: (tab: string, filter: URLSearchParamsInit) => void;
  setLoading: (loading: boolean) => void;
  getCurrentFilter: () => URLSearchParamsInit;
  setInitialValue: (data: IOfferRefundMoneyAccountancy) => void;
}

export const useStoreOfferRefundMoneyAccountancy = create<OfferRefundMoneyAccountancyDataStore>((set, get) => ({
  tab: 'WAITING_TRANSFER',
  dataOfferRefund: {
    WAITING_TRANSFER: [],
    TRANSFERED: [],
  },
  tabsFilters: {
    WAITING_TRANSFER: DEFAULT_PARAMS,
    TRANSFERED: DEFAULT_PARAMS,
  },
  loading: false,
  initialValue: undefined,
  isModified: false,
  setTab: (value: TabOfferRefundMoneyAccountancy) => set({ tab: value }),
  setDataForTab: (tab: TabOfferRefundMoneyAccountancy, data: IOfferRefundMoneyAccountancy[]) =>
    set(state => ({ dataOfferRefund: { ...state.dataOfferRefund, [tab]: data } })),
  setLoading: (loading: boolean) => set({ loading }),
  setTabFilter: (tab, filter) =>
    set(state => ({
      tabsFilters: {
        ...state.tabsFilters,
        [tab]: filter,
      },
    })),
  getCurrentFilter: () => {
    const state = get();
    const tabFilter = state.tabsFilters[state.tab] || {};
    return typeof tabFilter === 'object' ? { ...tabFilter, tab: state.tab } : { tab: state.tab };
  },
  setInitialValue: (data: IOfferRefundMoneyAccountancy) => set({ initialValue: data }),
  setIsModified: (isModified: boolean) => set({ isModified }),
}));
