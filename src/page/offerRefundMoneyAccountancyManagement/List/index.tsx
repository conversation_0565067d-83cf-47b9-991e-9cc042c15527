import { Tabs } from 'antd';
import BreadCrumbComponent from '../../../components/breadCrumb';
import useFilter from '../../../hooks/filter';
import { useFetch } from '../../../hooks';
import { getListOffer } from '../../../service/offer';
import { useEffect } from 'react';
import { IOfferRefundMoneyAccountancy, TabOfferRefundMoneyAccountancy } from '../../../types/offer';
import { useStoreOfferRefundMoneyAccountancy } from '../storeOfferRefundMoney';
import { DEFAULT_PARAMS } from '../../../constants/common';
import OfferRefundMoneyAccountancy from './OfferRefundMoneyAccountantcy';

const OfferRefundMoneyAccountancyList: React.FC<any> = ({}) => {
  const [filter, setFilter] = useFilter();
  const { setTab, tab, setDataForTab, setLoading, tabsFilters, getCurrentFilter, setTabFilter } =
    useStoreOfferRefundMoneyAccountancy();

  const { data, isFetching } = useFetch<IOfferRefundMoneyAccountancy[]>({
    queryKeyArrWithFilter: ['get-offer-refund', tab],
    api: getListOffer,
    moreParams: {
      type: 'REFUND',
      status: tab === 'WAITING_TRANSFER' ? 'WAITING_TRANSFER' : null,
      ...(tab !== 'WAITING_TRANSFER' && {
        statuses: tab === 'TRANSFERED' ? filter?.statuses || 'TRANSFERED,PROCESSING,REFUND,CANCELED' : '',
      }),
    },
  });
  const offerRefunds = data?.data?.data?.rows || [];

  useEffect(() => {
    setTab((filter.tab || 'WAITING_TRANSFER') as TabOfferRefundMoneyAccountancy);
  }, [filter.tab, setTab]);

  useEffect(() => {
    if (offerRefunds) {
      setDataForTab(tab, offerRefunds);
      setLoading(isFetching);
    }
  }, [data, isFetching, setDataForTab, setLoading, tab]);

  const handleChangeTab = (key: string) => {
    const tabValue = key as TabOfferRefundMoneyAccountancy;
    setTab(tabValue as TabOfferRefundMoneyAccountancy);
    const savedFilter = tabsFilters[tabValue] || DEFAULT_PARAMS;
    setTabFilter(tabValue, savedFilter);
    setFilter(getCurrentFilter() as Record<string, string>);
  };

  const items = [
    {
      label: 'Đề nghị hoàn tiền chờ duyệt',
      key: 'WAITING_TRANSFER',
      children: <OfferRefundMoneyAccountancy tabActive="WAITING_TRANSFER" />,
    },

    {
      label: 'Đề nghị hoàn tiền đã xử lý',
      key: 'TRANSFERED',
      children: <OfferRefundMoneyAccountancy tabActive="TRANSFERED" />,
    },
  ];
  return (
    <div className="offer_refund-list">
      <BreadCrumbComponent titleBread={''} />
      <Tabs onChange={handleChangeTab} activeKey={tab} items={items} />
    </div>
  );
};

export default OfferRefundMoneyAccountancyList;
