import { TableColumnsType, Typography } from 'antd';
import TableComponent from '../../../components/table';
import { useStoreOfferRefundMoneyAccountancy } from '../storeOfferRefundMoney';
import FilterSearch from '../component/FilterSearch';
import OfferModalComponent from '../component/offerModal';
import { IOfferRefundMoneyAccountancy, TabOfferRefundMoneyAccountancy } from '../../../types/offer';
import { useState } from 'react';
import './styles.scss';
import dayjs from 'dayjs';
import { FORMAT_DATE_TIME, OFFER_STATUS_NAME } from '../../../constants/common';
import { formatCurrency } from '../../../utilities/shareFunc';
import { Link } from 'react-router-dom';
import { OFFER_REFUND_MONEY_ACCOUNTANCY_MANAGEMENT } from '../../../configs/path';
import { ActionsColumns } from '../../../components/table/components/ActionsColumns';

const { Text } = Typography;
interface OfferRefundMoneyAccountancyProps {
  tabActive: TabOfferRefundMoneyAccountancy;
}

const OfferRefundMoneyAccountancy = (props: OfferRefundMoneyAccountancyProps) => {
  const { tabActive } = props;
  const { dataOfferRefund, loading } = useStoreOfferRefundMoneyAccountancy();
  const [initialOffer, setInitialOffer] = useState<IOfferRefundMoneyAccountancy>();
  const [isOpenOfferModal, setOfferModal] = useState<boolean>(false);

  const columns: TableColumnsType<IOfferRefundMoneyAccountancy> = [
    {
      title: 'Mã đề nghị',
      dataIndex: 'code',
      key: 'code',
      width: 110,
      render: (value: string, record?: IOfferRefundMoneyAccountancy) => (
        <Link to={`${OFFER_REFUND_MONEY_ACCOUNTANCY_MANAGEMENT}/${record?.id}`} target="_blank">
          {value || '-'}
        </Link>
      ),
    },
    {
      title: 'Tên khách hàng',
      dataIndex: 'customer',
      key: 'customer',
      width: 153,
      render: (_: any, record: IOfferRefundMoneyAccountancy) => (
        <Text>{record?.propertyTicket?.customer?.personalInfo?.name || ''}</Text>
      ),
    },
    {
      title: 'Đơn vị bán hàng',
      dataIndex: 'pos',
      key: 'pos',
      width: 183,
      render: (_: any, record: IOfferRefundMoneyAccountancy) => <Text>{record?.pos?.name || ''}</Text>,
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 106,
      render: (value: string) => <Text>{dayjs(value).format(FORMAT_DATE_TIME) || ''}</Text>,
    },
    {
      title: 'Mã YC/HĐ',
      dataIndex: 'bookingTicketCode',
      key: 'bookingTicketCode',
      width: 145,
      render: (_: any, record: IOfferRefundMoneyAccountancy) => (
        <Text>
          {record?.propertyTicket?.ticketType === 'YCDCH'
            ? record?.propertyTicket?.bookingTicketCode
            : record?.propertyTicket?.escrowTicketCode || ''}
        </Text>
      ),
    },
    {
      title: 'Mã chứng từ',
      dataIndex: 'receiptNum',
      key: 'receiptNum',
      width: 132,
      render: (_: any, record: IOfferRefundMoneyAccountancy) => <Text>{record?.receiptNum || ''}</Text>,
    },
    {
      title: 'Số SP',
      dataIndex: 'propertyUnit',
      key: 'propertyUnit',
      width: 139,
      render: (_: any, record: IOfferRefundMoneyAccountancy) => (
        <Text>{record?.propertyTicket?.propertyUnit?.code || ''}</Text>
      ),
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      width: 110,
      align: 'center',
      render: (value: string) => <Text>{OFFER_STATUS_NAME[value] || ''}</Text>,
    },
    {
      title: 'Số tiền',
      dataIndex: 'money',
      key: 'money',
      width: 149,
      align: 'center',
      render: (_: any, record: IOfferRefundMoneyAccountancy) => (
        <Text>{record?.money ? formatCurrency(record?.money.toString()) : ''}</Text>
      ),
    },
    {
      title: '',
      dataIndex: 'action',
      key: 'action',
      align: 'center',
      width: 80,
      render: (_: any, record: IOfferRefundMoneyAccountancy) => {
        const handleEdit = () => {};
        const handleAdjust = () => {};
        const handlePrint = () => {};
        const handleApprove = () => {
          setOfferModal(true);
          setInitialOffer(record);
        };
        const handlePrintWT = () => {};
        const handleCancel = () => {};
        return tabActive === 'WAITING_TRANSFER' ? (
          <ActionsColumns handleApprove={handleApprove} handlePrint={handlePrintWT} handleCancel={handleCancel} />
        ) : (
          <ActionsColumns handleEdit={handleEdit} handleAdjust={handleAdjust} handlePrint={handlePrint} />
        );
      },
    },
  ];

  return (
    <>
      <div className="header-filter" style={{ marginBottom: 16 }}>
        <FilterSearch />
      </div>
      <TableComponent
        className="table-offer-refund"
        columns={columns}
        queryKeyArr={['get-offer-refund', tabActive]}
        dataSource={dataOfferRefund[tabActive]}
        loading={loading}
        rowKey={'id'}
      />
      <OfferModalComponent
        title="Đề nghị hoàn tiền"
        visible={isOpenOfferModal}
        onCancel={() => setOfferModal(false)}
        onOk={() => setOfferModal(false)}
        onValuesChange={() => {}}
        initialValues={initialOffer}
      />
    </>
  );
};

export default OfferRefundMoneyAccountancy;
