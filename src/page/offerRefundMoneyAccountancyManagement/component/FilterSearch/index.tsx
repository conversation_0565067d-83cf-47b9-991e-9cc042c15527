import { DatePicker, Form, Radio, Row } from 'antd';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useLocation } from 'react-router-dom';
import dayjs, { Dayjs } from 'dayjs';
import useFilter from '../../../../hooks/filter';
import { DEFAULT_PARAMS } from '../../../../constants/common';
import DropdownFilterSearch from '../../../../components/dropdown/dropdownFilterSearch';
import MultiSelectLazy from '../../../../components/select/mutilSelectLazy';
import { getListAccount, getListOrgchartExternal, getListOrgchartInternal } from '../../../../service/offer';
import { getListProjectHistory } from '../../../../service/uploadHistory';
import { useStoreOfferRefundMoneyAccountancy } from '../../storeOfferRefundMoney';
import './styles.scss';
import MultiSelectStatic from '../../../../components/select/mutilSelectStatic';

const { RangePicker } = DatePicker;

type TFilter = {
  createdFrom?: string | Dayjs | null;
  createdTo?: string | Dayjs | null;
  statuses?: string[];
  orgchartIds?: string[];
  projectIds?: string[];
  createdByIds?: string[];
  dates?: [Dayjs | null, Dayjs | null];
};

function FilterSearch() {
  const { search } = useLocation();
  const [form] = Form.useForm();
  const params = useMemo(() => new URLSearchParams(search), [search]);
  const [filter, setFilter] = useFilter();
  const [initialValues, setInitialValues] = useState<TFilter>();
  const [isOpenFilter, setIsOpenFilter] = useState<boolean>(false);
  const [orgType, setOrgType] = useState<'internal' | 'external'>('internal');
  const rangePickerRef = useRef<React.ComponentRef<typeof DatePicker.RangePicker>>(null);
  const [dates, setDates] = useState<[Dayjs | null, Dayjs | null]>([null, null]);
  const { setTabFilter, tab } = useStoreOfferRefundMoneyAccountancy();

  useEffect(() => {
    if (params) {
      const createdFrom = params.get('createdFrom') ? dayjs(params.get('createdFrom')) : null;
      const createdTo = params.get('createdTo') ? dayjs(params.get('createdTo')) : null;
      const initialValue = {
        createdFrom: createdFrom,
        createdTo: createdTo,
        statuses: params.get('statuses') ? params.get('statuses')?.split(',') : undefined,
        orgchartIds: params.get('orgchartIds') ? params.get('orgchartIds')?.split(',') : undefined,
        projectIds: params.get('projectIds') ? params.get('projectIds')?.split(',') : undefined,
        createdByIds: params.get('createdByIds') ? params.get('createdByIds')?.split(',') : undefined,
      };
      setDates([createdFrom, createdTo]);
      setInitialValues(initialValue);
      form.setFieldsValue(initialValue);
    }
  }, [params, form]);

  const handleSubmitFilter = (values: TFilter) => {
    const [createdFrom, createdTo] = values.dates || [null, null];
    const newFilter: Record<string, string> = {
      createdFrom: createdFrom ? dayjs(createdFrom)?.startOf('day').toISOString() : '',
      createdTo: createdTo ? dayjs(createdTo).endOf('day').toISOString() : '',
      statuses: values?.statuses?.join(',') || '',
      createdByIds: values?.createdByIds?.join(',') || '',
      orgchartIds: values?.orgchartIds?.join(',') || '',
      projectIds: values?.projectIds?.join(',') || '',
      page: '1',
    };
    setFilter({ ...filter, page: '1', ...newFilter });
    setTabFilter(tab, { ...filter, page: '1', ...newFilter });
    setIsOpenFilter(false);
  };

  const handleClearFilters = () => {
    setInitialValues(undefined);
    setDates([null, null]);
    setFilter({ ...DEFAULT_PARAMS, search: params.get('search') || '', tab });
    setTabFilter(tab, { ...DEFAULT_PARAMS, search: params.get('search') || '', tab });
    setOrgType('internal');
    setTimeout(() => {
      setIsOpenFilter(false);
    }, 100);
  };

  const handleSearch = (value: string) => {
    const search = value ? value : '';
    setFilter({ ...filter, page: '1', search });
    setTabFilter(tab, { ...filter, page: '1', search });
  };

  const handleSelectStatuses = (values: unknown) => {
    const newTypeFilter = (values as { value: string }[]).map(item => item.value);
    form.setFieldsValue({ statuses: newTypeFilter });
  };

  const handleSelectProject = (values: unknown) => {
    const newProjectFilter = (values as { value: string }[]).map(item => item.value);
    form.setFieldsValue({ projectIds: newProjectFilter });
  };

  const handleSelectAccount = (values: unknown) => {
    console.log(values);
    const newAccountFilter = (values as { code: string }[]).map(item => item.code);
    console.log(newAccountFilter);
    form.setFieldsValue({ createdByIds: newAccountFilter });
  };

  const handleSelectOrgchart = (values: unknown) => {
    const newOrgchartFilter = (values as { value: string }[]).map(item => item.value);
    form.setFieldsValue({ orgchartIds: newOrgchartFilter });
  };

  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };

  const handleCalendarChange = useCallback(
    (value: [Dayjs | null, Dayjs | null] | null) => {
      if (!value) {
        if (dates[0] !== null || dates[1] !== null) {
          form.setFieldsValue({ dates: [null, null] });
        }
        return;
      }

      const newStart = value[0];
      const newEnd = value[1];

      if (
        (newStart?.isSame(dates[0], 'day') || (!newStart && !dates[0])) &&
        (newEnd?.isSame(dates[1], 'day') || (!newEnd && !dates[1]))
      ) {
        return;
      }

      form.setFieldsValue({ dates: [newStart, newEnd] });
    },
    [dates, form],
  );

  const handleChangeRangePicker = useCallback(
    (value: [Dayjs | null, Dayjs | null] | null) => {
      if (!value) {
        if (dates[0] !== null || dates[1] !== null) {
          form.setFieldsValue({ dates: [null, null] });
        }
        return;
      }

      const newStart = value[0];
      const newEnd = value[1];

      if (
        (newStart?.isSame(dates[0], 'day') || (!newStart && !dates[0])) &&
        (newEnd?.isSame(dates[1], 'day') || (!newEnd && !dates[1]))
      ) {
        return;
      }

      form.setFieldsValue({ dates: [newStart, newEnd] });
    },
    [dates, form],
  );

  const handleBlur = useCallback(
    (e: FocusEvent, index: 0 | 1) => {
      const target = e.target as HTMLInputElement;
      const value = target.value;

      if (!value && dates[index] !== null) {
        const newDates = [...dates] as [Dayjs | null, Dayjs | null];
        newDates[index] = null;
        setDates(newDates);
        form.setFieldsValue({ dates: newDates });
      }
    },
    [dates, form],
  );

  useEffect(() => {
    // Khai báo effect để thêm sự kiện blur
    const pickerNode = rangePickerRef.current?.nativeElement as HTMLElement | null; // Lấy DOM node của RangePicker
    const inputs = pickerNode?.querySelectorAll<HTMLInputElement>('input'); // Lấy tất cả input trong RangePicker

    if (inputs?.length === 2) {
      // Kiểm tra có đúng 2 input không
      const input0 = inputs[0]; // Lấy input ngày bắt đầu
      const input1 = inputs[1]; // Lấy input ngày kết thúc

      const blurHandler0 = (e: FocusEvent) => handleBlur(e, 0); // Tạo hàm xử lý blur cho input 0
      const blurHandler1 = (e: FocusEvent) => handleBlur(e, 1); // Tạo hàm xử lý blur cho input 1

      input0.addEventListener('blur', blurHandler0); // Thêm sự kiện blur cho input 0
      input1.addEventListener('blur', blurHandler1); // Thêm sự kiện blur cho input 1

      return () => {
        // Trả về cleanup function
        input0.removeEventListener('blur', blurHandler0); // Xóa sự kiện blur của input 0
        input1.removeEventListener('blur', blurHandler1); // Xóa sự kiện blur của input 1
      };
    }
  }, [handleBlur]); // Dependency array, effect chạy lại khi handleBlur thay đổi

  return (
    <>
      <DropdownFilterSearch
        rootClassName="wrapper-filter-offer-order"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        showParams={true}
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        initialValues={initialValues}
        form={form}
        onClearFilters={handleClearFilters}
        onChangeSearch={handleSearch}
        extraFormItems={
          <>
            {tab === 'TRANSFERED' && (
              <Form.Item label="Trạng thái" name="statuses">
                <MultiSelectStatic
                  data={[
                    { label: 'Đã duyệt', value: 'TRANSFERED' },
                    { label: 'Đã hủy', value: 'CANCELED' },
                    { label: 'Bị từ chối', value: 'PROCESSING' },
                  ]}
                  handleListSelect={handleSelectStatuses}
                  placeholder="Chọn trạng thái"
                  keysTag={'label'}
                />
              </Form.Item>
            )}
            <Form.Item label="Người tạo" name="createdByIds">
              <MultiSelectLazy
                enabled={isOpenFilter}
                queryKey={['get-account']}
                apiQuery={getListAccount}
                handleListSelect={handleSelectAccount}
                keysLabel={['code', 'name']}
                keysTag={['username', 'name']}
                placeholder="Chọn người tạo"
              />
            </Form.Item>

            <Form.Item label="Loại đơn vị">
              <Radio.Group onChange={e => setOrgType(e.target.value)} value={orgType}>
                <Radio value="internal">Đơn vị</Radio>
                <Radio value="external">Đối tác hợp tác</Radio>
              </Radio.Group>
            </Form.Item>
            {orgType === 'internal' ? (
              <Form.Item label="Đơn vị bán hàng" name="orgchartIds">
                <MultiSelectLazy
                  enabled={isOpenFilter}
                  queryKey={['get-orgchart-internal']}
                  apiQuery={getListOrgchartInternal}
                  handleListSelect={handleSelectOrgchart}
                  keysLabel={['code', 'nameVN']}
                  keysTag={['code', 'nameVN']}
                  placeholder="Chọn đơn vị bán hàng"
                />
              </Form.Item>
            ) : (
              <Form.Item label="Đơn vị bán hàng" name="orgchartIds">
                <MultiSelectLazy
                  enabled={isOpenFilter}
                  queryKey={['get-orgchart-external']}
                  apiQuery={getListOrgchartExternal}
                  handleListSelect={handleSelectOrgchart}
                  keysLabel={['partnershipCode', 'partnershipName']}
                  keysTag={['partnershipCode', 'partnershipName']}
                  placeholder="Chọn đơn vị bán hàng"
                />
              </Form.Item>
            )}

            <Form.Item label="Dự án" name="projectIds">
              <MultiSelectLazy
                enabled={isOpenFilter}
                queryKey={['get-project']}
                apiQuery={getListProjectHistory}
                keysLabel={['code', 'name']}
                keysTag={['name']}
                handleListSelect={handleSelectProject}
                placeholder="Chọn dự án"
              />
            </Form.Item>

            <Form.Item label="Khoảng thời gian tạo" name="dates">
              <RangePicker
                ref={rangePickerRef}
                value={dates}
                onCalendarChange={handleCalendarChange}
                onChange={handleChangeRangePicker}
                allowClear
                placeholder={['Từ ngày', 'Đến ngày']}
                format="DD/MM/YYYY"
              />
            </Form.Item>
            <Row gutter={16}></Row>
          </>
        }
      />
    </>
  );
}
export default FilterSearch;
