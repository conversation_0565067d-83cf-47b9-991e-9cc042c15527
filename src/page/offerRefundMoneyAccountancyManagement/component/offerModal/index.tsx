import React, { useEffect, useState, useMemo } from 'react';
import { Modal, Input, Radio, Checkbox, Button, Form, Row, Col, Select, DatePicker } from 'antd';
import './styles.scss';
import { MutationFunction } from '@tanstack/react-query';
import ConfirmDeleteModal from '../../../../components/modal/specials/ConfirmDeleteModal';
import { approveOffer, rejectOffer } from '../../../../service/offer';
import { IOfferRefundMoneyAccountancy } from '../../../../types/offer';
import { useUpdateField } from '../../../../hooks';
import { useStoreOfferRefundMoneyAccountancy } from '../../storeOfferRefundMoney';
import dayjs from 'dayjs';

interface State {
  label: string;
  value: string;
}

interface FooterButton {
  label: string;
  onClick: () => void;
  type?: 'default' | 'primary' | 'dashed' | 'link' | 'text';
}

interface OfferModalComponentProps {
  visible: boolean;
  onOk: () => void;
  onCancel: () => void;
  title?: string;
  initialValues?: IOfferRefundMoneyAccountancy;
  onValuesChange: (values: IOfferRefundMoneyAccountancy) => void;
  footerButtons?: FooterButton[];
  states?: State[];
}

const OfferModalComponent: React.FC<OfferModalComponentProps> = ({
  visible,
  onOk,
  onCancel,
  title = 'Điều chỉnh đề nghị hoàn tiền',
  initialValues = {},
  onValuesChange,
  states = [
    { label: 'Chuyển khoản', value: 'TRANSFER' },
    { label: 'Tiền mặt', value: 'CASH' },
    { label: 'Cà thẻ', value: 'DVBH' },
    { label: 'Đã hoàn CĐT', value: 'INVESTOR' },
    { label: 'Đã hoàn ĐVBH', value: 'POS' },
  ],
}) => {
  const [form] = Form.useForm();
  const state = Form.useWatch(['state'], form);
  const payer = Form.useWatch(['payer'], form);
  const receiptDate = Form.useWatch(['receiptDate'], form);
  const description = Form.useWatch(['description'], form);
  const bank = Form.useWatch(['bank'], form);
  const [isOpenModalReject, setIsOpenModalReject] = useState<boolean>(false);
  const { tab } = useStoreOfferRefundMoneyAccountancy();

  const isApproveDisabled = useMemo(() => {
    const isTransferInvalid = state === 'TRANSFER' && !bank;
    return !payer || !receiptDate || !description || !state || isTransferInvalid;
  }, [payer, receiptDate, description, state, bank]);

  // Reset form và set giá trị mới mỗi khi initialValues thay đổi
  useEffect(() => {
    if (visible) {
      form.resetFields();
      const defaultDescription =
        initialValues.description == null
          ? `Hoàn tiền khách hàng ${initialValues?.propertyTicket?.customer?.personalInfo?.name || ''} (${
              initialValues?.propertyTicket?.customer?.personalInfo?.code || ''
            })`
          : initialValues.description;

      const defaultState = initialValues.state == null ? 'TRANSFER' : initialValues.state;

      form.setFieldsValue({
        payer: initialValues?.propertyTicket?.customer?.personalInfo?.name || '',
        receiptDate: initialValues?.receiptDate ? dayjs(initialValues?.receiptDate) : null,
        description: defaultDescription,
        state: defaultState,
        receiptNum: initialValues?.receiptNum || undefined,
        reason: initialValues?.reason || undefined,
        unCheckAmount: initialValues?.unCheckAmount || false,
      });
    }
  }, [initialValues, form, visible]);

  useEffect(() => {
    if (state !== 'TRANSFER') {
      form.setFieldsValue({ bank: undefined });
    }
  }, [state, form]);

  const { mutateAsync: approve } = useUpdateField<IOfferRefundMoneyAccountancy>({
    keyOfListQuery: ['get-offer-refund', tab],
    apiQuery: approveOffer,
    messageSuccess: 'Duyệt đề nghị hoàn tiền thành công!',
    isMessageError: false,
  });

  const approveOfferOder = async () => {
    const values = await form.validateFields();

    const payload = {
      payBy: 'customer',
      id: initialValues?.id,
      reason: values?.reason,
      payer: values?.payer,
      receiptDate: values?.receiptDate,
      bankInfo: values.state === 'TRANSFER' ? values?.bank?.value || '' : '',
      bankName: values.state === 'TRANSFER' ? values?.bank?.label || '' : '',
      receiptNum: values?.receiptNum,
      description: values?.description,
      state: values?.state,
      unCheckAmount: values?.unCheckAmount,
    };
    const resp = await approve(payload);
    if (resp?.data.statusCode === '0') {
      await destroyAll();
    }
  };

  const destroyAll = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <>
      <Modal
        width={650}
        title={<div className="modal-title">{`${title} ${initialValues?.code || ''}`}</div>}
        open={visible}
        onOk={() => {
          form.validateFields().then(() => {
            onOk();
          });
        }}
        onCancel={destroyAll}
        footer={
          <div className="modal-footer">
            <Button
              type="default"
              onClick={() => {
                setIsOpenModalReject(true);
              }}
            >
              Từ chối
            </Button>
            <Button type="primary" onClick={approveOfferOder} disabled={isApproveDisabled}>
              Duyệt
            </Button>
          </div>
        }
        destroyOnClose
        className="custom-modal"
      >
        <Form
          form={form}
          layout="vertical"
          onValuesChange={(_, allValues) => onValuesChange(allValues)}
          className="form-offer-refund"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="Người nhận tiền"
                name={'payer'}
                rules={[{ required: true, message: 'Vui lòng nhập người nhận tiền' }]}
              >
                <Input placeholder="Nhập người nhận tiền" maxLength={255} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Ngày giờ hoàn tiền"
                name="receiptDate"
                rules={[{ required: true, message: 'Vui lòng nhập ngày giờ hoàn tiền' }]}
              >
                <DatePicker showTime format="DD-MM-YYYY HH:mm:ss" />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                label="Nhập ghi chú"
                name="description"
                rules={[{ required: true, message: 'Vui lòng nhập ghi chú' }]}
              >
                <Input placeholder="Nhập ghi chú" maxLength={255} />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="Số chứng từ" name="receiptNum">
                <Input placeholder="Nhập số chứng từ" maxLength={255} />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="Lý do thanh toán" name="reason">
                <Input placeholder="Nhập lý do thanh toán" maxLength={255} />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                label="Phương thức giao dịch"
                name="state"
                rules={[{ required: true, message: 'Vui lòng chọn phương thức giao dịch' }]}
              >
                <Radio.Group>
                  {states.map(state => (
                    <Radio key={state.value} value={state.value}>
                      {state.label}
                    </Radio>
                  ))}
                </Radio.Group>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                name="bank"
                rules={state === 'TRANSFER' ? [{ required: true, message: 'Vui lòng chọn ngân hàng' }] : []}
              >
                <Select
                  placeholder="Chọn ngân hàng"
                  allowClear
                  filterOption={(input, option) =>
                    typeof option?.label === 'string' ? option.label.toLowerCase().includes(input.toLowerCase()) : false
                  }
                  showSearch
                  options={initialValues?.propertyTicket?.project?.banks?.map(item => ({
                    value: item?.accountNumber,
                    label: item?.name,
                  }))}
                  onChange={(_, option) => {
                    form.setFieldsValue({
                      bank: option,
                    });
                  }}
                  disabled={state !== 'TRANSFER'}
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item name="unCheckAmount" valuePropName="checked">
                <Checkbox>Thu tiền tại sự kiện</Checkbox>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
      <ConfirmDeleteModal
        label="khách hàng"
        open={isOpenModalReject}
        apiQuery={rejectOffer as MutationFunction<unknown, unknown>}
        keyOfListQuery={['get-offer-refund', tab]}
        onCancel={() => {
          setIsOpenModalReject(false);
        }}
        idDetail={initialValues?.id}
        title="Xác nhận từ chối đề nghị hoàn tiền"
        description="Vui lòng nhập lý do từ chối"
        isTitlePlaceholder
        labelConfirm="Từ chối"
        fieldNameReason="reason"
        isUpdate={true}
        isMessageSuccess="Từ chối đề nghị hoàn tiền thành công!"
        setHideModal={() => {
          setIsOpenModalReject(false);
          destroyAll();
        }}
      />
    </>
  );
};

export default OfferModalComponent;
