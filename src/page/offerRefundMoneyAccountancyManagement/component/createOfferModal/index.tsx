import {
  <PERSON><PERSON>,
  Col,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Modal,
  Radio,
  Row,
  Select,
  Typography,
  UploadFile,
} from 'antd';
import { useCallback, useEffect, useState } from 'react';
import {
  createOfferRefund,
  getListBookingTicket,
  getListContractRefundMoney,
  getListProject,
} from '../../../../service/offer';
import { useCreateField } from '../../../../hooks';
import { useStoreOfferRefundMoneyAccountancy } from '../../storeOfferRefundMoney';
import UploadFileCreateOffer from '../uploadFileCreateOffer';
import SingleSelectLazy from '../../../../components/select/singleSelectLazy';
import { BookingTicket } from '../../../../types/bookingRequest';
import dayjs from 'dayjs';
import { formatNumber } from '../../../../utilities/regex';
import { Bank, ContractLiquidation, ICreateOfferRefundMoneyAccountancy, Installment } from '../../../../types/offer';
import { FORMAT_DATE, FORMAT_DATE_API } from '../../../../constants/common';
import ModalComponent from '../../../../components/modal';
import './styles.scss';
import { Project } from '../../../../types/project/project';
import { getFullAddress } from '../../../../utilities/shareFunc';

const { Title } = Typography;
const { Option } = Select;

interface ExtendedUploadFile extends UploadFile {
  key?: string;
  absoluteUrl?: string;
}

interface OfferCreateModalProps {
  visible: boolean;
  onClose: () => void;
  states?: { label: string; value: string }[];
}

const OfferCreateModal = ({
  visible,
  onClose,
  states = [
    { label: 'Chuyển khoản', value: 'TRANSFER' },
    { label: 'Tiền mặt', value: 'CASH' },
  ],
}: OfferCreateModalProps) => {
  const [form] = Form.useForm();
  const state = Form.useWatch(['state'], form);
  const [fileList, setFileList] = useState<ExtendedUploadFile[]>([]);
  const [proposalType, setProposalType] = useState<string>('YCDCH');
  const [banks, setBanks] = useState<Bank[]>([]);
  const [propertyTicketId, setPropertyTicketId] = useState<string | undefined>('');
  const [totalMoney, setTotalMoney] = useState<number>(0);
  const [projectId, setProjectId] = useState<string | undefined>('');
  const { tab } = useStoreOfferRefundMoneyAccountancy();

  const { mutateAsync: create } = useCreateField<ICreateOfferRefundMoneyAccountancy>({
    keyOfListQuery: ['get-offer-refund', tab],
    apiQuery: createOfferRefund,
    isMessageError: false,
    messageSuccess: 'Tạo đề nghị hoàn tiền thành công!',
  });

  useEffect(() => {
    if (state === 'TRANSFER') {
      form.setFieldsValue({
        bankName: undefined,
        bankNumber: undefined,
      });
    }
  }, [banks, form, state]);

  const handleSelectProposalType = (value: string) => {
    setProposalType(value);
    form.resetFields();
    setFileList([]);
    setBanks([]);
    setTotalMoney(0);
    setProjectId('');
    form.setFieldsValue({
      proposalType: value,
      state: 'TRANSFER',
      collectMoneyDate: dayjs(),
    });
  };

  const handleSelectProject = (values: Project) => {
    setProjectId(values?.id as string | undefined);
    setBanks(values?.banks || []);
    setPropertyTicketId('');
    setTotalMoney(0);
    form.resetFields([
      'contractId',
      ['propertyTicket', 'id'],
      'productCode',
      'name',
      'identityNumber',
      'address',
      'money',
      'bankName',
      'bankNumber',
      'paymentBatch',
    ]);

    form.setFieldsValue({
      projectId: values?.id,
      bankName: undefined,
      bankNumber: undefined,
    });
  };

  const handleSelectContract = (values: ContractLiquidation) => {
    const companyAddress = getFullAddress(values?.customer?.company?.address);
    const personalAddress = getFullAddress(values?.customer?.info?.rootAddress);

    setPropertyTicketId(values?.contract?.primaryTransaction?.id || '');
    const totalTransferedList =
      values?.contract?.policyPayment?.schedule?.installments?.map((item: Installment) => item?.totalTransfered) || [];

    // Tính tổng hoặc set null nếu không có giá trị
    const calculatedTotalMoney =
      totalTransferedList.length > 0 ? totalTransferedList.reduce((sum: number, value: number) => sum + value, 0) : 0;
    setTotalMoney(calculatedTotalMoney);

    form.setFieldsValue({
      contractId: values?.contract?.id,
      productCode: values?.contract?.primaryTransaction?.propertyUnit?.code || '',
      name:
        values?.customer?.type === 'bussiness'
          ? values?.customer?.company?.name
          : values?.customer?.personalInfo?.name || '',
      identityNumber:
        values?.customer?.type === 'business'
          ? values?.customer?.taxCode
          : values?.customer?.identities?.[0]?.value || '',
      address: values?.customer?.type === 'business' ? companyAddress : personalAddress,
      money: calculatedTotalMoney || 0,
    });
  };

  const handleSelectBookingTicket = (values: BookingTicket) => {
    const companyAddress = getFullAddress(values?.customer?.company?.address);
    const personalAddress = getFullAddress(values?.customer?.info?.rootAddress);
    setPropertyTicketId(values?.id || '');
    form.setFieldsValue({
      productCode: values?.propertyUnit?.code || '',
      propertyTicket: {
        id: values?.id,
      },
      money: values?.amount || 0,
      name:
        values?.customer?.type === 'bussiness'
          ? values?.customer?.company?.name
          : values?.customer?.personalInfo?.name || '',
      identityNumber:
        values?.customer?.type === 'business'
          ? values?.customer?.taxCode
          : values?.customer?.identities?.[0]?.value || '',
      address: values?.customer?.type === 'business' ? companyAddress : personalAddress,
    });
  };

  const handleSelectBank = (value: string) => {
    const selectedBank = banks?.find((bank: Bank) => bank?.code === value);
    form.setFieldsValue({
      bankName: selectedBank?.name || '',
      bankNumber: selectedBank?.accountNumber || '',
    });
  };

  const handleCancel = useCallback(async () => {
    if (form.isFieldsTouched()) {
      Modal.confirm({
        title: 'Xác nhận hủy',
        content: 'Dữ liệu chưa được lưu, bạn có chắc chắn muốn thoát khỏi trang không?',
        cancelText: 'Quay lại',
        onOk: async () => {
          await form.resetFields();
          setFileList([]);
          onClose();
        },
        okButtonProps: { type: 'default' },
        cancelButtonProps: { type: 'primary' },
      });
    } else {
      await form.resetFields();
      setFileList([]);
      onClose();
    }
  }, [form, onClose]);

  const handleCreate = async (values: ICreateOfferRefundMoneyAccountancy) => {
    const payload = {
      bankName: values?.bankName,
      bankNumber: values?.bankNumber,
      collectMoneyDate: values?.collectMoneyDate
        ? dayjs(values?.collectMoneyDate).format(FORMAT_DATE_API)
        : dayjs().format(FORMAT_DATE_API),
      contentBank: values?.contentBank,
      contractId: values?.contractId,
      description: values?.description,
      money: values?.money,
      files: fileList.map(file => ({
        uid: file.uid || '',
        name: file.name || '',
        url: file.key || '',
        absoluteUrl: file?.absoluteUrl || '',
        uploadName: file.name,
      })),
      propertyTicket: {
        id: propertyTicketId,
      },
      state: values?.state,
      type: 'REFUND',
    };

    try {
      const resp = await create(payload);
      if (resp?.data?.statusCode === '0' && resp?.data?.success) {
        await form.resetFields();
        onClose();
      }
    } catch (error) {
      console.error('Create error:', error);
    }
  };

  return (
    <ModalComponent
      className="create-offer-modal"
      title="Tạo mới đề nghị hoàn tiền"
      open={visible}
      onCancel={handleCancel}
      footer={null}
      destroyOnClose
    >
      <Form form={form} name="offer-refund-create" layout="vertical" onFinish={handleCreate}>
        <Row gutter={[64, 24]}>
          <Col span={12}>
            <Title level={5} style={{ marginBottom: 16 }}>
              Thông tin chi tiền
            </Title>
            <Row gutter={24}>
              <Col span={24}>
                <Form.Item
                  label="Loại đề nghị"
                  name="proposalType"
                  rules={[{ required: true, message: 'Vui lòng chọn loại đề nghị' }]}
                  initialValue="YCDCH"
                >
                  <Select placeholder="Chọn loại đề nghị" onChange={handleSelectProposalType}>
                    <Option value="YCDCH">Yêu cầu đặt chỗ</Option>
                    <Option value="TLHD">Thanh lý hợp đồng</Option>
                  </Select>
                </Form.Item>
              </Col>

              <Col span={24}>
                <Form.Item label="Dự án" name="projectId" rules={[{ required: true, message: 'Vui lòng chọn dự án' }]}>
                  <SingleSelectLazy
                    queryKey={['get-projects']}
                    apiQuery={getListProject}
                    placeholder="Chọn dự án"
                    keysLabel={'name'}
                    handleSelect={handleSelectProject}
                    moreParams={{ status: '01' }}
                  />
                </Form.Item>
              </Col>

              <Col span={24}>
                {proposalType === 'YCDCH' ? (
                  <Form.Item
                    label="YCĐCHO"
                    name={['propertyTicket', 'id']}
                    rules={[{ required: true, message: 'Vui lòng chọn YCĐCHO' }]}
                  >
                    <SingleSelectLazy
                      moreParams={{
                        id: projectId,
                        ticketType: 'YCDCH',
                        status: 'CS_APPROVED_CANCEL_REQUESTED',
                        _fields:
                          'propertyUnit.code,customer.type,surveys,customer2,_id,id,code,ticketType,demandCategory,bookingTicketCode,systemnoErp,reciept.code,reciept.status,reciept.id,customer.id,customer.info,customer.personalInfo,customer.identities,customer.bankInfo,customer.taxCode,customer.codeDx,customer.code,employee.name,pos.name,pos.id,employee.pos.name,createdDate,status,employee.id,escrowTicketCode,contract.id,contract.code,project.id,project.name,amount',
                      }}
                      queryKey={['get-booking-tickets']}
                      apiQuery={getListBookingTicket}
                      placeholder="Chọn YCĐCHO"
                      keysLabel={'bookingTicketCode'}
                      handleSelect={handleSelectBookingTicket}
                      enabled={proposalType === 'YCDCH' && !!projectId}
                    />
                  </Form.Item>
                ) : (
                  <Form.Item
                    label="Hợp đồng"
                    name="contractId"
                    rules={[{ required: true, message: 'Vui lòng chọn hợp đồng' }]}
                  >
                    <SingleSelectLazy
                      queryKey={['get-contract']}
                      apiQuery={getListContractRefundMoney}
                      placeholder="Chọn hợp đồng"
                      keysLabel={'name'}
                      handleSelect={handleSelectContract}
                      enabled={proposalType !== 'YCDCH' && !!projectId}
                      moreParams={{ projectIds: projectId, statuses: 'approved', type: 'termination' }}
                    />
                  </Form.Item>
                )}
              </Col>

              <Col span={24}>
                <Form.Item label="Số sản phẩm" name="productCode">
                  <Input placeholder="Số sản phẩm" disabled />
                </Form.Item>
              </Col>
              <Col span={14}>
                <Form.Item label="Tên khách hàng" name="name">
                  <Input placeholder="Tên khách hàng" disabled />
                </Form.Item>
              </Col>
              <Col span={10}>
                <Form.Item label="Số giấy tờ" name="identityNumber">
                  <Input placeholder="Số giấy tờ" disabled />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item label="Địa chỉ liên lạc" name="address">
                  <Input placeholder="Địa chỉ liên lạc" disabled />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item
                  label="Phương thức giao dịch"
                  name="state"
                  rules={[{ required: true, message: 'Vui lòng chọn phương thức giao dịch' }]}
                  layout="horizontal"
                  initialValue={'TRANSFER'}
                >
                  <Radio.Group style={{ width: '100%' }}>
                    <Row>
                      {states.map(state => (
                        <Col span={12} style={{ display: 'flex', justifyContent: 'flex-end' }} key={state.value}>
                          <Radio key={state.value} value={state.value}>
                            {state.label}
                          </Radio>
                        </Col>
                      ))}
                    </Row>
                  </Radio.Group>
                </Form.Item>
                {state === 'TRANSFER' && (
                  <div
                    style={{
                      backgroundColor: 'rgba(0, 0, 0, 0.02)',
                      border: '1px solid rgba(0, 0, 0, 0.15)',
                      borderRadius: 4,
                      padding: 16,
                      marginTop: 8,
                    }}
                  >
                    <Row gutter={[16, 16]}>
                      <Col span={12}>
                        <Form.Item
                          label="Ngân hàng"
                          name="bankName"
                          rules={[{ required: true, message: 'Vui lòng chọn ngân hàng' }]}
                        >
                          <Select
                            placeholder="Chọn ngân hàng"
                            allowClear
                            filterOption={(input, option) =>
                              typeof option?.label === 'string'
                                ? option.label.toLowerCase().includes(input.toLowerCase())
                                : false
                            }
                            showSearch
                            options={banks.map(item => ({
                              value: item.code,
                              label: item.name,
                            }))}
                            onChange={handleSelectBank}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          label="Số tài khoản"
                          name="bankNumber"
                          rules={[{ required: true, message: 'Vui lòng nhập số tài khoản' }]}
                        >
                          <Input placeholder="Số tài khoản" disabled />
                        </Form.Item>
                      </Col>
                    </Row>
                    <Row gutter={[16, 16]}>
                      <Col span={24}>
                        <Form.Item name="contentBank">
                          <Input.TextArea placeholder="Nhập nội dung KH chuyển khoản" maxLength={255} />
                        </Form.Item>
                      </Col>
                    </Row>
                  </div>
                )}
              </Col>
            </Row>
          </Col>
          <Col span={12}>
            <Title level={5} style={{ marginBottom: 16 }}>
              Thông tin chi phí
            </Title>
            <Row gutter={24}>
              <Col span={24}>
                <Form.Item
                  label="Số tiền"
                  name="money"
                  rules={[
                    {
                      required: true,
                      message: 'Vui lòng nhập số tiền',
                    },
                    {
                      validator: (_, value) => {
                        if (proposalType !== 'YCDCH' && totalMoney !== null && value > totalMoney) {
                          return Promise.reject(new Error('Vui lòng nhập vào giá trị nhỏ hơn số tiền phải thanh toán'));
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <InputNumber
                    placeholder="Nhập số tiền"
                    style={{ width: '100%' }}
                    formatter={formatNumber}
                    maxLength={15}
                    precision={0}
                    min={1}
                    onKeyPress={e => {
                      const char = e.key;
                      if (!/[0-9]/.test(char)) {
                        e.preventDefault();
                      }
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item
                  label="Lý do thanh toán"
                  name="description"
                  rules={[{ required: true, message: 'Vui lòng nhập lý do thanh toán', whitespace: true }]}
                >
                  <Input.TextArea placeholder="Nhập lý do thanh toán" maxLength={255} />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item
                  label="Ngày nộp tiền"
                  name="collectMoneyDate"
                  initialValue={dayjs()}
                  rules={[{ required: true, message: 'Vui lòng chọn ngày nộp tiền' }]}
                >
                  <DatePicker format={FORMAT_DATE} placeholder={FORMAT_DATE} />
                </Form.Item>
              </Col>
              {proposalType === 'YCDCH' && (
                <Col span={24}>
                  <Form.Item label="Tải tệp định kèm" name="files">
                    <UploadFileCreateOffer
                      fileList={fileList}
                      setFileList={setFileList}
                      uploadPath="transaction/create/bookingTicketCode"
                    />
                  </Form.Item>
                </Col>
              )}
            </Row>
          </Col>
        </Row>

        <div className="create-footer">
          <div className="button-create">
            <Button type="primary" htmlType="submit">
              Lưu
            </Button>
          </div>
        </div>
      </Form>
    </ModalComponent>
  );
};

export default OfferCreateModal;
