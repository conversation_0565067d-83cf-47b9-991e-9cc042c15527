//đ<PERSON>nh nghĩa các action có thể thực hiện được đang để test, sửa lại theo yêu cầu

import { EmployeeDetailState } from './state';

// payload là dữ liệu truyền vào để update state, đang để string để test
export type EmployeeDetailAction = { type: 'increment'; payload: string } | { type: 'decrement' };

export const reducer = (state: EmployeeDetailState, action: EmployeeDetailAction): EmployeeDetailState => {
  switch (action.type) {
    case 'increment':
      return state;
    default:
      return state;
  }
};
