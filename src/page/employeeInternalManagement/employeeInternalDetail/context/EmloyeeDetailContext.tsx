import { createContext, useReducer, ReactNode, Dispatch } from 'react';
import { EmployeeDetailAction, reducer } from './reducer';
import { initialEmployeeDetailState, EmployeeDetailState } from './state';

interface EmployeeDetailContextProps {
  state: EmployeeDetailState;
  dispatch: Dispatch<EmployeeDetailAction>;
}
interface EmployeeDetailProviderProps {
  children: ReactNode;
}

export const EmployeeDetailContext = createContext<EmployeeDetailContextProps | undefined>(undefined);

export const EmployeeDetailProvider = ({ children }: EmployeeDetailProviderProps) => {
  const [state, dispatch] = useReducer(reducer, initialEmployeeDetailState);

  return <EmployeeDetailContext.Provider value={{ state, dispatch }}>{children}</EmployeeDetailContext.Provider>;
};
