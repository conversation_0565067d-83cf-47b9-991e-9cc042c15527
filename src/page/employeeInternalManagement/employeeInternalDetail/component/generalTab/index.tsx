import { Col, Flex, Form, Input, Row, Select, Switch, Typography } from 'antd';
import dayjs from 'dayjs';
import { useEffect } from 'react';
import { FORMAT_DATE, OPTIONS_GENDER, OPTIONS_LINEMANAGER } from '../../../../../constants/common';
import { DetailEmployeeInternal } from '../../../../../types/employee/employee';
import './styles.scss';

const { Text } = Typography;

interface CommonFormProps {
  data?: DetailEmployeeInternal;
}

const GeneralTab: React.FC<CommonFormProps> = ({ data }) => {
  const [form] = Form.useForm();
  const isActive = Form.useWatch('isActive', form);

  useEffect(() => {
    if (data) {
      form.setFieldsValue({
        ...data,
        dob: dayjs(data.dob).isValid() ? dayjs(data.dob).format(FORMAT_DATE) : '',
      });
    }
  }, [data, form]);
  return (
    <Form layout="vertical" className="wrapper-generalTab" form={form}>
      <Row gutter={48}>
        <Col md={12} xs={24}>
          <Row gutter={24}>
            <Col xl={12} xs={24}>
              <Form.Item
                className="form-item-status"
                label="Trạng thái"
                labelAlign="left"
                labelCol={{ xs: 6, md: 10 }}
                layout="horizontal"
                name="isActive"
                valuePropName="checked"
              >
                <Flex justify="space-between">
                  <Switch value={isActive} disabled />
                  <Text style={{ color: isActive ? '#389E0D' : '#CF1322' }}>
                    {isActive ? 'Đang làm việc' : 'Đã nghỉ việc'}
                  </Text>
                </Flex>
              </Form.Item>
            </Col>
            <Col xl={12} xs={0}></Col>
            <Col span={12}>
              <Form.Item label="Tên nhân viên" name="name">
                <Input disabled />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Mã nhân viên" name="code">
                <Input disabled />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Địa chỉ email" name="email">
                <Input disabled />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Số điện thoại" name="phone">
                <Input disabled />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="CMND" name="identityCode">
                <Input disabled />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="CCCD" name="identityCode2">
                <Input disabled />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Giới tính" name="gender">
                <Select
                  options={OPTIONS_GENDER.map(item => {
                    return {
                      value: item.value,
                      label: item.label,
                    };
                  })}
                  disabled
                  suffixIcon={null}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Ngày sinh" name="dob">
                <Input disabled />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={24}>
              <Form.Item label="Địa chỉ" name="city">
                <Input disabled />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item name="street">
                <Input disabled placeholder="Địa chỉ chi tiết" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item label="Chức danh" name={['jobTitle', 'name']}>
                <Input disabled />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Chức vụ" name={['position', 'name']}>
                <Input disabled />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Vị trí" name="isLinemanager">
                <Select
                  placeholder="Vị trí"
                  disabled
                  suffixIcon={null}
                  options={OPTIONS_LINEMANAGER.map(item => {
                    return {
                      value: item.value,
                      label: item.label,
                    };
                  })}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Khối" name="level2Name">
                <Input disabled />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Phòng ban" name="level3Name">
                <Input disabled />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Bộ phận" name="level4Name">
                <Input disabled />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Nhóm" name="level5Name">
                <Input disabled />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Tổ đội" name="level6Name">
                <Input disabled />
              </Form.Item>
            </Col>
          </Row>
        </Col>
      </Row>
    </Form>
  );
};

export default GeneralTab;
