import { App, notification, Select, Table, TableProps, Tag, Typography } from 'antd';

import { PlusOutlined } from '@ant-design/icons';
import { Button, Col, Form, Input, Row } from 'antd';
import { useEffect, useState } from 'react';
import { modalConfirm } from '../../../../../components/modal/specials/ModalConfirm';
import TableComponent from '../../../../../components/table';
import { ActionsColumns } from '../../../../../components/table/components/ActionsColumns';
import { OPTIONS_POSITION_ACCOUNT, SOURCE_ROLE_DATA_TYPE } from '../../../../../constants/common';
import { useFetch, useUpdateField } from '../../../../../hooks';
import { getDetailAccount, putDataRolesAccount } from '../../../../../service/account';
import { DropdownRole } from './components/dropdownRole/DropdownRole';
import ModalDataRoles from './components/modalDataRoles/ModalInternal';

import ButtonOfPageDetail from '../../../../../components/button/buttonOfPageDetail';
import './styles.scss';
import { Account, AccountTabProps, Area, DataRole, GroupedDataRole, Role, selectObject } from './type';
import { checkDataRoles, groupByType, transformRoles } from './utilities';

const { Text } = Typography;

export const AccountTab = (props: AccountTabProps) => {
  const { id } = props;
  const { modal } = App.useApp();
  const [form] = Form.useForm();
  const [roles, setRoles] = useState<Role[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const [isModified, setIsModified] = useState(false);
  const [isModalOpenDataRoles, setIsModalOpenDataRoles] = useState(false);
  const [selectedDefaultRoles, setSelectedDefaultRoles] = useState<selectObject>();
  const [selectedRole, setSelectedRole] = useState<GroupedDataRole | null>(null);
  const [typeModal, setTypeModal] = useState<'create' | 'update'>('create');
  const [confirmBackData, setConfirmBackData] = useState(false);

  const {
    data: dataAccount,
    isLoading,
    isFetching,
  } = useFetch<Account>({
    queryKeyArr: ['tab-account', id],
    cacheTime: 100,
    api: () => id && getDetailAccount(id),
  });
  const initialValues = dataAccount?.data?.data;

  const updateDataRole = useUpdateField<Account>({
    apiQuery: putDataRolesAccount,
    keyOfListQuery: ['tab-account'],
    keyOfDetailQuery: ['tab-account', id],
  });

  // Cập nhật expandedKeys khi roles thay đổi
  useEffect(() => {
    const newExpandedKeys = roles.map(item => item.key).filter(key => key !== undefined) as string[];
    setExpandedKeys(newExpandedKeys);
  }, [roles]);

  useEffect(() => {
    const roles = initialValues?.roles;
    form.setFieldsValue(initialValues);
    if (roles) {
      setRoles(transformRoles(roles));
    }
  }, [form, initialValues, confirmBackData]);

  useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  // xử lý modal chọn vùng dữ liệu
  const handleOpenModal = (record: GroupedDataRole, typeModal: 'create' | 'update', type: string | string[]) => {
    setSelectedDefaultRoles({ type: type || '', listCheck: record.area || [] });
    setSelectedRole(record);
    setIsModalOpenDataRoles(true);
    setTypeModal(typeModal);
  };

  const handleCancelModalDataRoles = () => {
    setIsModalOpenDataRoles(false);
  };

  const handleClickRole = (role: { label: string; key: string }, index: number) => {
    if (role) {
      setRoles(
        roles.map((item, i) => (i === index ? { ...item, name: '', nameRole: role.label, code: role.key } : item)),
      );
      setIsModified(true);
    }
  };

  const handleSubmitUpdateModalDataRoles = (values: selectObject) => {
    setRoles(prevData =>
      prevData.map(item => {
        if (item.key === selectedRole?.parentCode) {
          const filterData = item?.dataRoles?.filter(newItem => newItem.type !== selectedRole?.type);
          const mergedResult = [...values.listCheck, ...(filterData ? filterData : [])];
          return {
            ...item,
            dataRoles: mergedResult,
            children: groupByType(mergedResult, selectedRole?.parentCode).sort((a, b) => a.type.localeCompare(b.type)),
          };
        } else {
          return item;
        }
      }),
    );
    setSelectedDefaultRoles(undefined);
    setSelectedRole(null);
    setIsModalOpenDataRoles(false);
    setIsModified(true);
  };

  const handleSubmitCreateModalDataRoles = (values: selectObject) => {
    setRoles(prevData =>
      prevData.map(item => {
        if (item.key === selectedRole?.key) {
          const filterData = item?.dataRoles?.filter(newItem => newItem.type !== selectedRole?.type);
          const mergedResult = [...values.listCheck, ...(filterData ? filterData : [])];
          return {
            ...item,
            dataRoles: mergedResult,
            children: groupByType(mergedResult, selectedRole?.key).sort((a, b) => a.type.localeCompare(b.type)),
          };
        } else {
          return item;
        }
      }),
    );
    setSelectedDefaultRoles(undefined);
    setSelectedRole(null);
    setIsModalOpenDataRoles(false);
    setIsModified(true);
  };

  // thêm role mới
  const handleAddNewRole = () => {
    const newData = [...roles];
    newData.push({
      name: '',
      code: '',
      dataRoles: [],
      key: Math.random().toString(),
      isDelete: true,
    });
    setRoles(newData);
    setIsModified(true);
  };

  const columns: TableProps['columns'] = [
    {
      title: 'Tên vai trò',
      dataIndex: 'name',
      width: '15%',
      key: 'name',
      render: (_, record: Role, index: number) => {
        if (!record.isAdmSource) {
          return 'name' in record ? (
            <DropdownRole onClick={handleClickRole} index={index} roles={roles} defaultValue={record?.name} />
          ) : (
            ''
          );
        } else {
          return record.name;
        }
      },
    },
    {
      title: 'Mã vai trò',
      width: '15%',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: 'Loại vùng dữ liệu',
      width: '15%',
      dataIndex: 'type',
      key: 'type',
      render: (value: string) => {
        const foundItem = SOURCE_ROLE_DATA_TYPE?.find(item => item?.value === value);
        const labelButton = foundItem && 'label' in foundItem ? foundItem.label : '';
        return labelButton;
      },
    },
    {
      title: 'Vùng dữ liệu',
      dataIndex: 'area',
      key: 'area',
      render: (value: Area[], record: GroupedDataRole) => {
        const selectedList = value?.map(item => item.labelDataRole).join(', ');
        switch (record.type) {
          case 'INTERNAL_ORGCHART':
            return (
              <a onClick={() => handleOpenModal(record, 'update', record.type)}>
                {selectedList ? selectedList : ' Chọn vùng dữ liệu'}
              </a>
            );
          case 'EXTERNAL_ORGCHART':
            return (
              <a onClick={() => handleOpenModal(record, 'update', record.type)}>
                {selectedList ? selectedList : ' Chọn vùng dữ liệu'}
              </a>
            );
          case 'PROJECT':
            return <p>{selectedList}</p>;
          default:
            return (
              Number(record?.type) === 2 && (
                <Tag style={{ float: 'right', cursor: 'pointer', background: '#00000005' }}>Kiêm nhiệm</Tag>
              )
            );
        }
      },
    },
    {
      title: 'Hành động',
      key: 'action',
      dataIndex: 'actions',
      width: '10%',
      align: 'center',
      render: (_, record: DataRole & GroupedDataRole) => {
        if (record.isChildren) {
          const dataRoleRow = roles.find(item => item.key === record.parentCode);
          const handleDeleteAreaRole = () => {
            const newData = roles.map(item =>
              item.key === record.parentCode
                ? {
                    ...item,
                    dataRoles: item.dataRoles?.filter(role => role.type !== record.type),
                    children: item.children?.filter(child => JSON.stringify(child) !== JSON.stringify(record)),
                  }
                : item,
            );
            setRoles(newData);
            setIsModified(true);
          };
          const modalDelete = () => {
            modalConfirm({
              modal,
              title: 'Xóa Vùng dữ liệu',
              content: 'Bạn chắc chắn muốn xóa vùng dữ liệu này?',
              handleConfirm: handleDeleteAreaRole,
            });
          };
          return (
            <Button
              type="text"
              onClick={modalDelete}
              danger
              disabled={
                !record?.isDelete || record?.type === 'PROJECT' || dataRoleRow?.children?.length === 1 ? true : false
              }
            >
              Xoá
            </Button>
          );
        } else {
          const type = record?.children?.map(item => item.type);
          const handleDeleteRole = () => {
            const newData = roles.filter(item => JSON.stringify(item) !== JSON.stringify(record));
            setRoles(newData);
            setIsModified(true);
          };

          const handleAddDataRole = () => {
            handleOpenModal(record, 'create', type ? type : []);
          };
          const modalDelete = () => {
            modalConfirm({
              modal,
              title: 'Xóa vai trò',
              content: 'Bạn chắc chắn muốn xóa vai trò này?',
              handleConfirm: handleDeleteRole,
            });
          };
          return (
            <ActionsColumns
              overlayClassName="action-column-data-role"
              moreActions={[
                {
                  label: 'Thêm vùng dữ liệu',
                  key: 'addDataRole',
                  onClick: handleAddDataRole,
                  disabled: (type && type.length > 1) || !record?.code ? true : false,
                },
                { label: 'Xóa', key: 'delete', onClick: modalDelete, disabled: record?.isAdmSource ? true : false },
              ]}
            />
          );
        }
      },
    },
  ];

  const handleSubmitAccount = async () => {
    const dataSubmit = {
      id: form.getFieldValue('id'),
      roles: roles.map(item => ({
        name: item.name || item.nameRole,
        code: item.code,
        dataRoles: item.dataRoles,
      })),
    };
    if (checkDataRoles(dataSubmit)) {
      updateDataRole.mutate(dataSubmit);
      setIsModified(false);
    } else {
      notification.error({ message: 'Vui lòng chọn vùng dữ liệu cho tất cả các vai trò' });
    }
  };
  const handleCancel = () => {
    form.setFieldsValue(initialValues);
    setConfirmBackData(prev => !prev);
    setIsModified(false);
  };

  return (
    <div className="wrapper-account-tab">
      <Form className="account-internal" layout="vertical" form={form} onFinish={handleSubmitAccount}>
        <h3 className="title-info">Thông tin tài khoản</h3>
        <Row gutter={48}>
          <Col md={12} xs={24}>
            <Row
              gutter={{
                xs: 8,
                md: 12,
              }}
            >
              <Col md={12} xs={20}>
                <Form.Item label="Loại người dùng" name="isAdmin">
                  <Select options={OPTIONS_POSITION_ACCOUNT} disabled />
                </Form.Item>
              </Col>
              <Col md={12} xs={20}>
                <Form.Item label="Tên tài khoản" name="username">
                  <Input disabled />
                </Form.Item>
              </Col>
            </Row>
          </Col>
        </Row>
        <Form.Item
          className="status-account-internal"
          label="Trạng thái"
          layout="horizontal"
          labelCol={{ span: 2 }}
          labelAlign="left"
          shouldUpdate
        >
          {() => {
            const isActive = form.getFieldValue('isActive');
            return <Text type={isActive ? 'success' : 'danger'}>{isActive ? 'Hoạt động' : 'Vô hiệu'}</Text>;
          }}
        </Form.Item>

        <div className="table-roles">
          <h3 className="title-info">Vai trò vùng dữ liệu</h3>
          <TableComponent
            columns={columns}
            dataSource={roles}
            isPagination={false}
            loading={isLoading || isFetching}
            queryKeyArr={[]}
            scroll={{ y: 'max-content' }}
            expandable={{
              expandedRowKeys: expandedKeys,
              expandIcon: () => null,
            }}
            summary={() => (
              <Table.Summary fixed="top">
                <Table.Summary.Row>
                  <Table.Summary.Cell index={0} colSpan={columns.length}>
                    <Button type="dashed" className="btn-add-roles" icon={<PlusOutlined />} onClick={handleAddNewRole}>
                      Thêm vai trò
                    </Button>
                  </Table.Summary.Cell>
                </Table.Summary.Row>
              </Table.Summary>
            )}
          />
        </div>
      </Form>
      <ModalDataRoles
        handleCancel={handleCancelModalDataRoles}
        isOpen={isModalOpenDataRoles}
        handleOk={typeModal === 'create' ? handleSubmitCreateModalDataRoles : handleSubmitUpdateModalDataRoles}
        selectedDefaultRoles={selectedDefaultRoles}
        typeModal={typeModal}
      />
      {isModified && <ButtonOfPageDetail handleSubmit={handleSubmitAccount} handleCancel={handleCancel} />}
    </div>
  );
};
