import { Tabs, TabsProps } from 'antd';
import { useParams } from 'react-router-dom';
import BreadCrumbComponent from '../../../components/breadCrumb';
import { useFetch } from '../../../hooks';
import { getDetailEmployeeInternal } from '../../../service/employee';
import { DetailEmployeeInternal } from '../../../types/employee/employee';
import { AccountTab } from './component/accountTab';
import GeneralTab from './component/generalTab';
import { EmployeeDetailProvider } from './context/EmloyeeDetailContext';

const EmployeeDetail = () => {
  const id = useParams();

  const { data: dataDetailEmployeeInternal } = useFetch<DetailEmployeeInternal>({
    queryKeyArr: ['detail-employee-internal', id?.id],
    api: () => getDetailEmployeeInternal(id?.id),
  });
  const detailEmployeeInternal = dataDetailEmployeeInternal?.data?.data;

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: 'Thông tin chung',
      tabKey: '1',
      children: <GeneralTab data={detailEmployeeInternal} />,
    },
    {
      key: '2',
      label: 'Tài khoản',
      tabKey: '2',
      children: <AccountTab id={detailEmployeeInternal?.accountId} />,
    },
  ];

  return (
    <EmployeeDetailProvider>
      <BreadCrumbComponent titleBread={detailEmployeeInternal?.email} />
      <Tabs defaultActiveKey="1" items={items} />
    </EmployeeDetailProvider>
  );
};

export default EmployeeDetail;
