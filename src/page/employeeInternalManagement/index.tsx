import { Button, Col, Row, TableColumnsType } from 'antd';
import dayjs from 'dayjs';
import BreadCrumbComponent from '../../components/breadCrumb';
import InputSearch from '../../components/input/InputSearch';
import TableComponent from '../../components/table';
import { EMPLOYEE_INTERNAL_MANAGEMENT } from '../../configs/path';
import { useFetch } from '../../hooks';
import { getListEmployeeInternal } from '../../service/employee';
import { EmployeeInternal } from '../../types/employee/employee';
import './styles.scss';

const statusColors = {
  active: '#389E0D',
  inactive: '#CF1322',
};

const getStatusEmployeeInternal = (status: boolean) => (
  <p style={{ color: status ? statusColors.active : statusColors.inactive }}>
    {status ? '<PERSON><PERSON> làm việc' : 'Đ<PERSON> nghỉ việc'}
  </p>
);
const formatDate = (date: string) => {
  const formattedDate = dayjs(date).format('DD/MM/YY');
  const formattedTime = dayjs(date).format('HH:mm:ss');
  return (
    <>
      <div>{formattedDate}</div>
      <div>{formattedTime}</div>
    </>
  );
};

const ListEmployeeInternal = () => {
  const { data, isLoading } = useFetch<EmployeeInternal[]>({
    queryKeyArrWithFilter: ['employee-internal'],
    api: getListEmployeeInternal,
  });
  const dataEmployeeInternal =
    (data?.data?.data as unknown as { rows: EmployeeInternal[] })?.rows?.map(user => ({
      ...user,
      key: user.id,
    })) ?? [];

  const columns: TableColumnsType<EmployeeInternal> = [
    { title: 'Mã nhân viên', dataIndex: 'code', key: 'code' },
    {
      title: 'Nhân viên',
      dataIndex: 'name',
      key: 'name',
      render: (_, record) => (
        <>
          <div>{record?.name}</div>
          <div>{record?.email}</div>
        </>
      ),
    },
    { title: 'Chức danh', dataIndex: ['jobTitle', 'name'], key: 'jobTitle' },
    { title: 'Số điện thoại', dataIndex: 'phone', key: 'phone' },
    { title: 'Công ty', dataIndex: 'level1Name', key: 'level1Name' },
    { title: 'Phòng ban', dataIndex: 'level3Name', key: 'level3Name' },
    { title: 'Ngày tạo', dataIndex: 'createdDate', key: 'createdDate', render: formatDate },
    { title: 'Ngày cập nhật', dataIndex: 'modifiedDate', key: 'modifiedDate', render: formatDate },
    {
      title: 'Trạng thái',
      dataIndex: 'isActive',
      key: 'isActive',
      align: 'center',
      width: '10%',
      render: getStatusEmployeeInternal,
    },
    {
      title: 'Hành động',
      key: 'action',
      align: 'center',
      render: (_, record) => (
        <a onClick={() => window.open(`${EMPLOYEE_INTERNAL_MANAGEMENT}/${record.id}`, '_blank')}>Xem chi tiết</a>
      ),
    },
  ];

  return (
    <>
      <BreadCrumbComponent />
      <div className="header-content-employee">
        <div className="header-content">
          <InputSearch
            className="input-search"
            placeholder="Tìm kiếm theo tên, email, mã nhân viên, sđt"
            keySearch="search"
          />
          <Row gutter={[16, 8]}>
            <Col>
              <Button type="primary">Xuất dữ liệu</Button>
            </Col>
          </Row>
        </div>
      </div>
      <TableComponent
        columns={columns}
        dataSource={dataEmployeeInternal}
        queryKeyArr={['employee-internal']}
        loading={isLoading}
      />
    </>
  );
};

export default ListEmployeeInternal;
