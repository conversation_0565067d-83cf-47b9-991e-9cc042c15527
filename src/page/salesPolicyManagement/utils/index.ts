import { v4 as uuid } from 'uuid';
import { TItemBonusByMargin, TItemBonusByQuantity, TListBonus, TPaymentBatch } from '../../../types/salesPolicy';
import { formatCurrency } from '../../../utilities/shareFunc';

/**
 * Format API response bonus data for display in forms
 */
export const formatBonusListForDisplay = (listBonus: TListBonus) => {
  if (!listBonus) return {};

  return {
    byQuantity: (listBonus.byQuantity || []).map((soldItem: TItemBonusByQuantity) => ({
      key: uuid(),
      minProductsSold: soldItem?.minProductsSold?.toString(),
      maxProductsSold: soldItem?.maxProductsSold?.toString(),
      bonusPercentage: soldItem?.bonusPercentage?.toString(),
      bonusAmount:
        soldItem?.bonusAmount &&
        typeof soldItem?.bonusAmount === 'number' &&
        formatCurrency(soldItem.bonusAmount.toString()),
      noteProductsSold: soldItem?.noteProductsSold,
    })),
    byMargin: (listBonus.byMargin || []).map((percentageItem: TItemBonusByMargin) => ({
      key: uuid(),
      minSalesPercentage: percentageItem?.minSalesPercentage?.toString(),
      maxSalesPercentage: percentageItem?.maxSalesPercentage?.toString(),
      bonusPercentageOnRate: percentageItem?.bonusPercentageOnRate?.toString(),
      bonusAmountOnRate:
        percentageItem?.bonusAmountOnRate &&
        typeof percentageItem?.bonusAmountOnRate === 'number' &&
        formatCurrency(percentageItem.bonusAmountOnRate.toString()),
      noteSalesPercentage: percentageItem?.noteSalesPercentage,
    })),
  };
};

/**
 * Format form data for submission to API
 */
export const formatBonusListForSubmit = (listBonus: TListBonus) => {
  if (!listBonus) return {};

  return {
    byQuantity: Array.isArray(listBonus?.byQuantity)
      ? listBonus.byQuantity.map(
          ({
            key,
            minProductsSold,
            maxProductsSold,
            bonusPercentage,
            bonusAmount,
            noteProductsSold,
          }: TItemBonusByQuantity) => ({
            key,
            minProductsSold: Number(minProductsSold),
            maxProductsSold: Number(maxProductsSold),
            bonusPercentage: Number(Number(bonusPercentage).toFixed(2)),
            bonusAmount: typeof bonusAmount === 'string' ? Number(bonusAmount.replace(/,/g, '')) : bonusAmount,
            noteProductsSold: noteProductsSold?.trim(),
          }),
        )
      : [],
    byMargin: Array.isArray(listBonus?.byMargin)
      ? listBonus.byMargin.map(
          ({
            key,
            minSalesPercentage,
            maxSalesPercentage,
            bonusPercentageOnRate,
            bonusAmountOnRate,
            noteSalesPercentage,
          }: TItemBonusByMargin) => ({
            key,
            minSalesPercentage: Number(Number(minSalesPercentage).toFixed(2)),
            maxSalesPercentage: Number(Number(maxSalesPercentage).toFixed(2)),
            bonusPercentageOnRate: Number(Number(bonusPercentageOnRate).toFixed(2)),
            bonusAmountOnRate:
              typeof bonusAmountOnRate === 'string' ? Number(bonusAmountOnRate.replace(/,/g, '')) : bonusAmountOnRate,
            noteSalesPercentage: noteSalesPercentage?.trim(),
          }),
        )
      : [],
  };
};

/**
 *  Format form data for submission to API
 */
export const formatListPaymentForSubmit = (listPaymentBatch: TPaymentBatch[]) =>
  Array.isArray(listPaymentBatch)
    ? listPaymentBatch.map(({ note, status, phase, transactionRate }: TPaymentBatch) => ({
        note: note?.trim(),
        status,
        phase: Number(Number(phase).toFixed(2)),
        transactionRate: Number(Number(transactionRate).toFixed(2)),
      }))
    : [];

/**
 * Format API response payment data for display in forms
 */
export const formatListPaymentForDisplay = (listPaymentBatch: TPaymentBatch[]) =>
  Array.isArray(listPaymentBatch)
    ? listPaymentBatch.map(({ note, status, phase, transactionRate }) => ({
        key: uuid(),
        note,
        status,
        phase: phase ?? 0,
        transactionRate: transactionRate ?? 0,
      }))
    : [];
