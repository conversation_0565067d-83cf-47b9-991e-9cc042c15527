import { Col, DatePicker, Form, Row } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useMemo, useState } from 'react';
import { useLocation } from 'react-router-dom';
import DropdownFilterSearch from '../../../components/dropdown/dropdownFilterSearch';
import SingleSelectLazy from '../../../components/select/singleSelectLazy';
import { FORMAT_DATE, FORMAT_DATE_API } from '../../../constants/common';
import { useFetch } from '../../../hooks';
import useFilter from '../../../hooks/filter';
import { sendGetDetailOfDropdownProject, sendGetListOfDropdownProjects } from '../../../service/salesPolicy';
import { TFilterSalesPolicy } from '../../../types/salesPolicy';

const FilterSalesPolicy = () => {
  const { search } = useLocation();
  const [form] = Form.useForm();
  const params = useMemo(() => new URLSearchParams(search), [search]);
  const [filter, setFilter] = useFilter();
  const [initialValues, setInitialValues] = useState<TFilterSalesPolicy>();
  const [isOpenFilter, setIsOpenFilter] = useState(false);
  const [defaultProject, setDefaultProject] = useState<
    { label: string | React.ReactElement; value: string } | undefined
  >();
  const id = params.get('projectID');

  const { data: project } = useFetch<{ id: string; name: string }>({
    api: () => sendGetDetailOfDropdownProject({ id }),
    queryKeyArr: ['project-detail', id],
    withFilter: false,
    enabled: !!isOpenFilter && !!id,
  });

  const dataProject = project?.data?.data;

  useEffect(() => {
    if (params) {
      const initialValue = {
        startCreatedDate: params.get('startCreatedDate') ? dayjs(params.get('startCreatedDate')) : undefined,
        endCreatedDate: params.get('endCreatedDate') ? dayjs(params.get('endCreatedDate')) : undefined,
        projectID: params.get('projectID') || undefined,
      };
      setInitialValues(initialValue);
      form.setFieldsValue(initialValue);
    }
  }, [form, params]);

  useEffect(() => {
    if (dataProject?.name && dataProject?.id) {
      setDefaultProject({ label: dataProject.name, value: dataProject.id });
    }
  }, [dataProject]);

  const handleSubmitFilter = (values: TFilterSalesPolicy) => {
    const newFilter: Record<string, unknown> = {
      startCreatedDate: values?.startCreatedDate ? dayjs(values?.startCreatedDate).format(FORMAT_DATE_API) : null,
      endCreatedDate: values?.endCreatedDate ? dayjs(values?.endCreatedDate).format(FORMAT_DATE_API) : null,
      projectID: values?.projectID || null,
    };
    setFilter({ ...filter, page: '1', ...newFilter });
    setIsOpenFilter(false);
  };

  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };

  const handleClearFilters = () => {
    setInitialValues(undefined);
    setDefaultProject(undefined);
    setTimeout(() => {
      setIsOpenFilter(false);
    }, 100);
  };
  const handleSelectProject = (value: { name: string; id: string }) => {
    form.setFieldsValue({ projectID: value?.id });
  };

  return (
    <>
      <DropdownFilterSearch
        rootClassName="wrapper-filter"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        showParams={true}
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        initialValues={initialValues}
        form={form}
        onClearFilters={handleClearFilters}
        extraFormItems={
          <>
            <Form.Item label="Chọn dự án" name="projectID">
              <SingleSelectLazy
                apiQuery={sendGetListOfDropdownProjects}
                queryKey={['list-source']}
                enabled={isOpenFilter}
                placeholder="Chọn dự án"
                keysLabel={['name']}
                handleSelect={handleSelectProject}
                defaultValues={defaultProject}
              />
            </Form.Item>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="Từ ngày" name="startCreatedDate">
                  <DatePicker
                    format={FORMAT_DATE}
                    disabledDate={current => {
                      const endCreatedDate = form.getFieldValue('endCreatedDate');
                      return (
                        (current && current > dayjs().startOf('day')) ||
                        (endCreatedDate && current > dayjs(endCreatedDate).startOf('day')) // Disable ngày sau "Đến ngày"
                      );
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Đến ngày" name="endCreatedDate">
                  <DatePicker
                    format={FORMAT_DATE}
                    disabledDate={current => {
                      const startCreatedDate = form.getFieldValue('startCreatedDate');
                      return (
                        (current && current > dayjs().startOf('day')) ||
                        (startCreatedDate && current < dayjs(startCreatedDate).startOf('day')) // Disable ngày trước "Ngày tạo"
                      );
                    }}
                  />
                </Form.Item>
              </Col>
            </Row>
          </>
        }
      />
    </>
  );
};

export default FilterSalesPolicy;
