import { Typography } from 'antd';
import { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { FORMAT_DATE } from '../../../constants/common';
import { TListOfSalesPolicy } from '../../../types/salesPolicy';

const { Text } = Typography;

export const columns: ColumnsType<TListOfSalesPolicy> = [
  {
    title: 'Mã chính sách',
    dataIndex: 'code',
    key: 'code',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Tên chính sách',
    dataIndex: 'name',
    key: 'name',
    render: (value: string) => (value ? value : '-'),
  },

  {
    title: 'Dự án',
    dataIndex: 'projectName',
    key: 'projectName',
    render: (value: string) => (value ? value : '-'),
  },

  {
    title: '<PERSON><PERSON><PERSON> bắt đầu',
    dataIndex: 'startDate',
    key: 'startDate',
    render: (value: string) => (value ? dayjs(value).format(FORMAT_DATE) : '-'),
  },
  {
    title: '<PERSON><PERSON><PERSON> kết thúc',
    dataIndex: 'endDate',
    key: 'endDate',
    render: (value: string) => (value ? dayjs(value).format(FORMAT_DATE) : '-'),
  },
  {
    title: 'Trạng thái',
    dataIndex: 'isActive',
    key: 'isActive',
    render: (value: number) =>
      value === 1 ? <Text type="success">Đang hoạt động</Text> : <Text type="danger">Vô hiệu hoá</Text>,
  },
  {
    title: 'Ngày tạo',
    dataIndex: 'createdDate',
    key: 'createdDate',

    render: (value: string, record: TListOfSalesPolicy) =>
      value ? (
        <>
          <Text>{dayjs(value).format(FORMAT_DATE)}</Text>
          <br />
          <Text>{record?.createdBy?.fullName ? record.createdBy.fullName : '-'}</Text>
        </>
      ) : (
        '-'
      ),
  },
  {
    title: 'Ngày cập nhật',
    dataIndex: 'modifiedDate',
    key: 'modifiedDate',
    render: (value: string, record: TListOfSalesPolicy) =>
      value ? (
        <>
          <Text>{dayjs(value).format(FORMAT_DATE)}</Text>
          <br />
          <Text>{record?.modifiedBy?.fullName ? record.modifiedBy.fullName : '-'}</Text>
        </>
      ) : (
        '-'
      ),
  },
];
