import { Button, Checkbox, Col, Form, Row, Space, Spin, Typography } from 'antd';
import dayjs from 'dayjs';
import { useEffect } from 'react';
import ModalComponent from '../../../components/modal';
import { ARRAY_FIELD_BY_MARGIN, ARRAY_FIELD_BY_QUANTITY } from '../../../constants/common';
import { useCreateField, useFetch } from '../../../hooks';
import { createSalesPolicy, getClonePolicy } from '../../../service/salesPolicy';
import { TItemBonusByMargin, TItemBonusByQuantity, TSalesPolicy } from '../../../types/salesPolicy';
import { formatRangePikerToSubmit } from '../../../utilities/shareFunc';
import AnalyticalInfo from '../components/AnalyticalInfo';
import { columnPercentage, columnSold } from '../components/columns';
import DetailedInfo from '../components/DetailedInfo';
import GeneralInfo from '../components/GeneralInfo';
import ListBonus from '../components/ListBonus';
import PaymentBatch from '../components/PaymentBatch';
import { useSalesPolicyStore } from '../store';
import {
  formatBonusListForDisplay,
  formatBonusListForSubmit,
  formatListPaymentForDisplay,
  formatListPaymentForSubmit,
} from '../utils';
import './styles.scss';

const { Title, Text } = Typography;

const CreateSalePolicy = () => {
  const [form] = Form.useForm();
  const { actionModal, setActionModal, setInitialValue } = useSalesPolicyStore();

  const { data, isLoading } = useFetch<TSalesPolicy>({
    api: () => getClonePolicy({ id: actionModal?.id }),
    queryKeyArr: ['get-clone-policy', actionModal?.id],
    enabled: !!actionModal?.id,
  });
  const dataClonePolicy = data?.data?.data;

  const { mutateAsync: createSalePolicy, isPending } = useCreateField<TSalesPolicy>({
    apiQuery: createSalesPolicy,
    keyOfListQuery: ['list-of-sales-policy'],
    isMessageError: false,
  });

  useEffect(() => {
    if (dataClonePolicy && actionModal?.type === 'clone') {
      const formatListBonus = formatBonusListForDisplay(dataClonePolicy?.listBonus);
      const formatListPayment = formatListPaymentForDisplay(dataClonePolicy?.listPayment);

      const formatData = {
        ...dataClonePolicy,
        code: `${dataClonePolicy?.code}-COPY`,
        active: Number(dataClonePolicy?.active),
        revenueRate: dataClonePolicy?.revenueRate?.toString(),
        amount: dataClonePolicy?.amount?.toString(),
        comamount: dataClonePolicy?.comamount?.toString(),
        comrate: dataClonePolicy?.comrate?.toString(),
        date: [dayjs(dataClonePolicy.startDate || undefined), dayjs(dataClonePolicy.endDate || undefined)],
        listPayment: formatListPayment,
        listBonus: formatListBonus,
      };
      form.setFieldsValue(formatData);
      setInitialValue(formatData as TSalesPolicy);
    }
  }, [actionModal?.type, dataClonePolicy, form, setInitialValue]);

  const handleSubmit = async () => {
    const values = await form.validateFields();
    const listPaymentBatch = form.getFieldValue('listPayment');
    const listBonus = form.getFieldValue('listBonus');
    const periodObj = form.getFieldValue('periodObj');

    const formatListPaymentBatch = formatListPaymentForSubmit(listPaymentBatch);
    const formatListBonus = formatBonusListForSubmit(listBonus);

    const newData = {
      ...values,
      ...periodObj,
      year: Number(values?.year),
      name: values?.name?.trim() || '',
      code: values?.code?.trim() || '',
      analysisBonus: values?.analysisBonus?.trim() || '',
      analysisIndicator: values?.analysisIndicator?.trim() || '',
      analysisFees: values?.analysisFees?.trim() || '',
      active: values?.active,
      revenueRate: Number(values?.revenueRate),
      amount: Number(values?.amount),
      comamount: Number(values?.comamount),
      comrate: Number(values?.comrate),
      ...formatRangePikerToSubmit(values.date),
      date: undefined,
      listPayment: formatListPaymentBatch,
      listBonus: {
        isProgressiveByQuantity: values?.listBonus?.isProgressiveByQuantity ? true : false,
        isProgressiveByMargin: values?.listBonus?.isProgressiveByMargin ? true : false,
        ...formatListBonus,
      },
    };

    const res = await createSalePolicy(newData);
    if (res?.data?.statusCode === '0') {
      form.resetFields();
      setActionModal({ isOpen: false, type: undefined, id: undefined });
      setInitialValue(undefined);
    }
  };

  return (
    <Spin spinning={isLoading}>
      <ModalComponent
        rootClassName="wrapper-create-sale-policy"
        title="Tạo mới chính sách phí"
        open={actionModal?.isOpen}
        onCancel={() => {
          setActionModal({ isOpen: false, type: undefined, id: undefined });
          form.resetFields();
          setInitialValue(undefined);
        }}
        destroyOnClose
        footer={[
          <Button key="submit" type="primary" onClick={handleSubmit} loading={!!isPending}>
            Lưu
          </Button>,
        ]}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            active: 1,
          }}
        >
          <Row gutter={24}>
            <Col xs={24} md={12}>
              <GeneralInfo />
              <DetailedInfo enabled={actionModal?.isOpen} />
              <AnalyticalInfo />
            </Col>
            <Col span={24}>
              <PaymentBatch />
            </Col>
            <Col span={24}>
              <div className="list-bonus">
                <Space direction="vertical" size={'middle'}>
                  <Title level={5} style={{ margin: 0 }}>
                    Thưởng vượt chỉ tiêu
                  </Title>
                  <Space direction="horizontal" align="baseline" size={'large'} style={{ marginLeft: 24 }}>
                    <Text>Thưởng theo số lượng sản phẩm</Text>
                    <Form.Item name={['listBonus', 'isProgressiveByQuantity']} valuePropName="checked">
                      <Checkbox>Tính lũy tiến</Checkbox>
                    </Form.Item>
                  </Space>
                  <ListBonus<TItemBonusByQuantity>
                    labelAdd="Thêm thưởng SLSP"
                    nameField="byQuantity"
                    columns={columnSold}
                    arrayFieldTable={ARRAY_FIELD_BY_QUANTITY}
                  />

                  <Space direction="horizontal" align="baseline" size={'large'} style={{ marginLeft: 24 }}>
                    <Text>Thưởng theo tỷ lệ bán được/ số sản phẩm ký quỹ </Text>
                    <Form.Item name={['listBonus', 'isProgressiveByMargin']} valuePropName="checked">
                      <Checkbox>Tính lũy tiến</Checkbox>
                    </Form.Item>
                  </Space>
                  <ListBonus<TItemBonusByMargin>
                    labelAdd="Thêm thưởng SLKQ"
                    nameField="byMargin"
                    columns={columnPercentage}
                    arrayFieldTable={ARRAY_FIELD_BY_MARGIN}
                  />
                </Space>
              </div>
            </Col>
          </Row>
        </Form>
      </ModalComponent>
    </Spin>
  );
};

export default CreateSalePolicy;
