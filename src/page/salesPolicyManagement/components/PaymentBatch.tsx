import { Form, Typography } from 'antd';
import React, { useEffect, useState } from 'react';
import { v4 as uuid } from 'uuid';
import { TPaymentBatch } from '../../../types/salesPolicy';
import { Editable } from './editTable';
import { EditableCell } from './editTable/EditableCell';
import { EditableRow } from './editTable/EditableRow';
import { columnPayment } from './columns';
import { useSalesPolicyStore } from '../store';

const { Title } = Typography;

const PaymentBatch = () => {
  const form = Form.useFormInstance();
  const [dataSource, setDataSource] = useState<TPaymentBatch[]>([]);
  const { initialValue } = useSalesPolicyStore();
  const defaultListPayment = initialValue?.listPayment;

  useEffect(() => {
    if (Array.isArray(defaultListPayment) && defaultListPayment?.length > 0) {
      setDataSource(defaultListPayment);
    }
  }, [defaultListPayment, initialValue?.listPayment]);

  const handleDelete = (key: React.Key) => {
    const newData = dataSource.filter(item => item.key !== key);
    setDataSource(newData);
    form.setFieldsValue({ listPayment: newData });
  };

  // Add a new row with auto-incrementing "Đót"
  const handleAdd = () => {
    const newData: TPaymentBatch = {
      key: uuid(),
      phase: '',
      transactionRate: '',
      status: '',
      note: '',
      isNew: true,
    };
    setDataSource([...dataSource, newData]);
  };

  const handleSave = (row: TPaymentBatch) => {
    const newData = [...dataSource];
    const index = newData.findIndex(item => row.key === item.key);
    const item = newData[index];
    newData.splice(index, 1, { ...item, ...row });
    setDataSource(newData);
    form.setFieldsValue({ listPayment: newData });
  };

  const components = {
    body: {
      row: EditableRow,
      cell: EditableCell,
    },
  };

  return (
    <>
      <Title level={5}>Đợt thanh toán</Title>
      <Editable<TPaymentBatch>
        columns={columnPayment}
        dataSource={dataSource}
        handleAdd={handleAdd}
        handleDelete={handleDelete}
        handleSave={handleSave}
        components={components}
        labelAdd="Thêm đợt TT"
      />
    </>
  );
};

export default PaymentBatch;
