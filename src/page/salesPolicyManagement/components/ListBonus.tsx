import { Form } from 'antd';
import React, { useEffect, useState } from 'react';
import { v4 as uuid } from 'uuid';
import { ColumnTypes, Editable, EditableRecord, EditColumns } from './editTable';
import { EditableCell } from './editTable/EditableCell';
import { EditableRow } from './editTable/EditableRow';
import { useSalesPolicyStore } from '../store';

interface ListBonusProps<T> {
  labelAdd: string;
  nameField: string;
  columns: (ColumnTypes<T>[number] & EditColumns)[];
  arrayFieldTable: string[];
}

const ListBonus = <T extends EditableRecord>({ labelAdd, nameField, columns, arrayFieldTable }: ListBonusProps<T>) => {
  const form = Form.useFormInstance();
  const [dataSource, setDataSource] = useState<T[]>([]);
  const { initialValue } = useSalesPolicyStore();
  const defaultListBonus = initialValue?.listBonus;

  useEffect(() => {
    if (defaultListBonus) {
      const fieldBonus = nameField === 'byQuantity' ? defaultListBonus?.byQuantity : defaultListBonus?.byMargin;
      if (Array.isArray(fieldBonus) && fieldBonus?.length > 0) {
        setDataSource(fieldBonus as T[]);
      }
    }
  }, [defaultListBonus, nameField]);

  const handleDelete = (key: React.Key) => {
    const newData = dataSource.filter(item => item.key !== key);
    setDataSource(newData);
    form.setFieldsValue({ listBonus: { [nameField]: newData } });
  };

  const handleAdd = () => {
    const newData = {
      ...arrayFieldTable.reduce((acc, field) => ({ ...acc, [field]: '' }), {}),
      key: uuid(),
      isNew: true,
    } as unknown as T;
    setDataSource([...dataSource, newData]);
  };

  const handleSave = (row: T) => {
    const newData = [...dataSource];
    const index = newData.findIndex((item: T) => row.key === item.key);
    const item = newData[index];
    newData.splice(index, 1, { ...item, ...row });
    setDataSource(newData);
    form.setFieldsValue({
      listBonus: {
        [`${nameField}`]: newData,
      },
    });
  };

  const components = {
    body: {
      row: EditableRow,
      cell: EditableCell,
    },
  };

  return (
    <>
      <Editable<T>
        columns={columns}
        dataSource={dataSource}
        handleAdd={handleAdd}
        handleDelete={handleDelete}
        handleSave={handleSave}
        components={components}
        labelAdd={labelAdd}
      />
    </>
  );
};

export default ListBonus;
