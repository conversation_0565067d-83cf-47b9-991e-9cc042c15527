import { Col, Form, Input, Row, Typography } from 'antd';
import { useSalesPolicyStore } from '../store';

const { Item } = Form;
const { Title } = Typography;

const AnalyticalInfo = () => {
  const { disabled } = useSalesPolicyStore();

  return (
    <div>
      <Title level={4}>Thông tin phân tích</Title>
      <Row gutter={16}>
        <Col span={24}>
          <Item label="Thưởng nóng nhân viên" name="analysisBonus">
            <Input maxLength={50} placeholder="Nhập thưởng nóng nhân viên" disabled={disabled} />
          </Item>
        </Col>
        <Col span={24}>
          <Item label="Loại thưởng vượt chỉ tiêu" name="analysisIndicator">
            <Input maxLength={50} placeholder="Nhập loại thưởng vượt chỉ tiêu" disabled={disabled} />
          </Item>
        </Col>
        <Col span={24}>
          <Item label="Loại phí dịch vụ nhận" name="analysisFees">
            <Input maxLength={50} placeholder="Nhập loại phí dịch vụ nhận" disabled={disabled} />
          </Item>
        </Col>
      </Row>
    </div>
  );
};

export default AnalyticalInfo;
