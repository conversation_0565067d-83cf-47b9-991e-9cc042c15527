import CurrencyInput from '../../../components/input/CurrencyInput';
import PercentInput from '../../../components/input/PercentInput';
import { OPTIONS_STATUS_TRANSACTIONS } from '../../../constants/common';
import { TItemBonusByMargin, TItemBonusByQuantity, TPaymentBatch } from '../../../types/salesPolicy';
import { handleKeyDownEnterNumber } from '../../../utilities/regex';
import { formatCurrency } from '../../../utilities/shareFunc';
import { ColumnTypes, EditColumns } from './editTable';

export const columnPayment: (ColumnTypes<TPaymentBatch>[number] & EditColumns)[] = [
  {
    title: 'Đợt',
    dataIndex: 'phase',
    key: 'phase',
    editable: true,
    align: 'center',
    width: '20%',
    inputType: 'text',
    inputProps: { maxLength: 13, onKeyDown: handleKeyDownEnterNumber, inputMode: 'numeric', placeholder: 'Đợt' },
  },
  {
    title: 'Tỉ lệ giao dịch',
    dataIndex: 'transactionRate',
    key: 'transactionRate',
    editable: true,
    width: '20%',
    inputType: 'custom',
    renderEditComponent: ({ onChange, save, disabled }) => (
      <PercentInput
        placeholder="Nhập tỉ lệ giao dịch"
        onPressEnter={save}
        onBlur={save}
        onChange={val => onChange(val)}
        suffix=""
        disabled={disabled}
      />
    ),
  },
  {
    title: 'Tình trạng giao dịch',
    dataIndex: 'status',
    key: 'status',
    editable: true,
    width: '20%',
    inputType: 'select',
    optionsSelect: OPTIONS_STATUS_TRANSACTIONS,
    selectProps: { placeholder: 'Chọn tình trang giao dịch' },
  },
  {
    title: 'Ghi chú',
    dataIndex: 'note',
    key: 'note',
    width: '30%',
    editable: true,
    inputType: 'text',
    inputProps: { maxLength: 500, placeholder: 'Nhập ghi chú' },
  },
];

export const columnSold: (ColumnTypes<TItemBonusByQuantity>[number] & EditColumns)[] = [
  {
    title: 'Từ',
    dataIndex: 'minProductsSold',
    key: 'minProductsSold',
    editable: true,
    align: 'center',
    width: '15%',
    inputType: 'text',
    inputProps: { maxLength: 13, onKeyDown: handleKeyDownEnterNumber, placeholder: 'Nhập giá trị từ' },
    dependencies: ['maxProductsSold'],
    rules: () => [
      ({ getFieldValue }) => ({
        validator: (_, value) => {
          const maxProductsSold = getFieldValue('maxProductsSold');
          if (maxProductsSold && Number(value) > Number(maxProductsSold)) {
            return Promise.reject('Giá trị "Từ" phải nhỏ hơn giá trị "Đến"');
          }
          return Promise.resolve();
        },
        validateTrigger: ['onChange', 'onBlur'],
      }),
    ],
  },
  {
    title: 'Đến',
    dataIndex: 'maxProductsSold',
    key: 'maxProductsSold',
    editable: true,
    align: 'center',
    width: '15%',
    inputType: 'text',
    inputProps: { maxLength: 13, onKeyDown: handleKeyDownEnterNumber, placeholder: 'Nhập giá trị đến' },
    dependencies: ['minProductsSold'],
    rules: () => [
      ({ getFieldValue }) => ({
        validator: (_, value) => {
          const minProductsSold = getFieldValue('minProductsSold');
          if (minProductsSold && Number(value) < Number(minProductsSold)) {
            return Promise.reject('Giá trị "Đến" phải lớn hơn giá trị "Từ"');
          }
          return Promise.resolve();
        },
        validateTrigger: ['onChange', 'onBlur'],
      }),
    ],
  },
  {
    title: 'Tỉ lệ ghi nhận',
    dataIndex: 'bonusPercentage',
    key: 'bonusPercentage',
    editable: true,
    width: '15%',
    inputType: 'custom',
    renderEditComponent: ({ onChange, save, disabled }) => (
      <PercentInput
        placeholder="Nhập tỉ lệ giao dịch"
        onPressEnter={save}
        onBlur={save}
        onChange={val => onChange(val)}
        suffix=""
        disabled={disabled}
      />
    ),
  },
  {
    title: 'Số tiền',
    dataIndex: 'bonusAmount',
    key: 'bonusAmount',
    editable: true,
    width: '15%',
    inputType: 'custom',
    renderEditComponent: ({ onChange, save, disabled }) => (
      <CurrencyInput
        onChange={val => onChange(val || '')}
        onBlur={save}
        onPressEnter={save}
        placeholder={'Nhập số tiền phí'}
        suffix=""
        disabled={disabled}
      />
    ),
  },
  {
    title: 'Ghi chú',
    dataIndex: 'noteProductsSold',
    key: 'noteProductsSold',
    width: '30%',
    editable: true,
    inputType: 'text',
    inputProps: { maxLength: 500, placeholder: 'Nhập ghi chú' },
  },
];

export const columnPercentage: (ColumnTypes<TItemBonusByMargin>[number] & EditColumns)[] = [
  {
    title: 'Từ',
    dataIndex: 'minSalesPercentage',
    key: 'minSalesPercentage',
    editable: true,
    align: 'center',
    width: '15%',
    inputType: 'custom',
    renderEditComponent: ({ onChange, save, disabled }) => (
      <PercentInput
        placeholder="Nhập giá trị từ"
        onPressEnter={save}
        onBlur={save}
        onChange={val => onChange(val)}
        suffix=""
        disabled={disabled}
      />
    ),
    dependencies: ['maxSalesPercentage'],
    rules: () => [
      ({ getFieldValue }) => ({
        validator: async (_, value) => {
          const maxSalesPercentage = getFieldValue('maxSalesPercentage');
          if (maxSalesPercentage && Number(value) > Number(maxSalesPercentage)) {
            return Promise.reject('Giá trị "Từ" phải nhỏ hơn giá trị "Đến"');
          }
          return Promise.resolve();
        },
      }),
    ],
  },
  {
    title: 'Đến',
    dataIndex: 'maxSalesPercentage',
    key: 'maxSalesPercentage',
    editable: true,
    align: 'center',
    width: '15%',
    inputType: 'custom',
    renderEditComponent: ({ onChange, save, disabled }) => (
      <PercentInput
        placeholder="Nhập giá trị đến"
        onPressEnter={save}
        onBlur={save}
        onChange={val => onChange(val)}
        suffix=""
        disabled={disabled}
      />
    ),
    dependencies: ['minSalesPercentage'],
    rules: () => [
      ({ getFieldValue }) => ({
        validator: async (_, value) => {
          const minSalesPercentage = getFieldValue('minSalesPercentage');
          if (minSalesPercentage && Number(value) < Number(minSalesPercentage)) {
            return Promise.reject('Giá trị "Từ" phải nhỏ hơn giá trị "Đến"');
          }
          return Promise.resolve();
        },
      }),
    ],
  },
  {
    title: 'Tỉ lệ ghi nhận',
    dataIndex: 'bonusPercentageOnRate',
    key: 'bonusPercentageOnRate',
    editable: true,
    width: '15%',
    inputType: 'custom',
    renderEditComponent: ({ onChange, save, disabled }) => (
      <PercentInput
        placeholder="Nhập tỉ lệ giao dịch"
        onPressEnter={save}
        onBlur={save}
        onChange={val => onChange(val)}
        suffix=""
        disabled={disabled}
      />
    ),
  },
  {
    title: 'Số tiền',
    dataIndex: 'bonusAmountOnRate',
    key: 'bonusAmountOnRate',
    editable: true,
    width: '15%',
    inputType: 'custom',
    renderEditComponent: ({ onChange, save, disabled }) => (
      <CurrencyInput
        onChange={val => onChange(formatCurrency(val))}
        onBlur={save}
        onPressEnter={save}
        placeholder={'Nhập số tiền phí'}
        suffix=""
        disabled={disabled}
      />
    ),
  },
  {
    title: 'Ghi chú',
    dataIndex: 'noteSalesPercentage',
    key: 'noteSalesPercentage',
    width: '30%',
    editable: true,
    inputType: 'text',
    inputProps: { maxLength: 500, placeholder: 'Nhập ghi chú' },
  },
];
