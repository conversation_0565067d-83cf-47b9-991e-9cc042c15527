import { Checkbox, Col, Form, Row, Space, Spin, Typography } from 'antd';
import dayjs from 'dayjs';
import { useEffect } from 'react';
import { useParams } from 'react-router-dom';
import BreadCrumbComponent from '../../../components/breadCrumb';
import ButtonOfPageDetail from '../../../components/button/buttonOfPageDetail';
import { ARRAY_FIELD_BY_MARGIN, ARRAY_FIELD_BY_QUANTITY, FORMAT_DATE_TIME } from '../../../constants/common';
import { PERMISSION_SALES_POLICY } from '../../../constants/permissions/commission';
import { useCheckPermissions, useFetch, useUpdateField } from '../../../hooks';
import { getDetailOfSalesPolicy, updateSalesPolicy } from '../../../service/salesPolicy';
import { TItemBonusByMargin, TItemBonusByQuantity, TSalesPolicy } from '../../../types/salesPolicy';
import { formatRangePikerToSubmit } from '../../../utilities/shareFunc';
import AnalyticalInfo from '../components/AnalyticalInfo';
import { columnPercentage, columnSold } from '../components/columns';
import DetailedInfo from '../components/DetailedInfo';
import GeneralInfo from '../components/GeneralInfo';
import ListBonus from '../components/ListBonus';
import PaymentBatch from '../components/PaymentBatch';
import { useSalesPolicyStore } from '../store';
import {
  formatBonusListForDisplay,
  formatBonusListForSubmit,
  formatListPaymentForDisplay,
  formatListPaymentForSubmit,
} from '../utils';
import './styles.scss';

const { Title, Text } = Typography;

const DetailOfSalesPolicy = () => {
  const { id } = useParams();
  const [form] = Form.useForm();
  const { update: permissionUpdate } = useCheckPermissions(PERMISSION_SALES_POLICY);
  const { setDisabled, setInitialValue, isModified, setIsModified, disabled, initialValue } = useSalesPolicyStore();

  const { data, isLoading } = useFetch<TSalesPolicy>({
    api: getDetailOfSalesPolicy,
    queryKeyArr: ['detail-of-sales-policy', id],
    withFilter: false,
    moreParams: { id },
  });
  const dataSalesPolicy = data?.data?.data;

  useEffect(() => {
    if (dataSalesPolicy) {
      const formatListBonus = formatBonusListForDisplay(dataSalesPolicy?.listBonus);
      const formatListPayment = formatListPaymentForDisplay(dataSalesPolicy?.listPayment);

      const formatData = {
        ...dataSalesPolicy,
        active: Number(dataSalesPolicy?.active),
        revenueRate: dataSalesPolicy?.revenueRate,
        amount: dataSalesPolicy?.amount?.toString(),
        comamount: dataSalesPolicy?.comamount?.toString(),
        comrate: dataSalesPolicy?.comrate?.toString(),
        date: [dayjs(dataSalesPolicy.startDate || undefined), dayjs(dataSalesPolicy.endDate || undefined)],
        listPayment: formatListPayment,
        listBonus: { ...dataSalesPolicy?.listBonus, ...formatListBonus },
        periodObj: {
          periodFrom: dataSalesPolicy?.periodFrom,
          periodTo: dataSalesPolicy?.periodTo,
          periodName: dataSalesPolicy?.periodName,
        },
      };
      setDisabled(!dataSalesPolicy?.isUpdateStatus ? true : false);
      form.setFieldsValue(formatData);
      setInitialValue(formatData as TSalesPolicy);
    }
  }, [dataSalesPolicy, form, setDisabled, setInitialValue]);

  // cảnh báo khi thoát trang khi chưa lưu dữ liệu
  useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  const { mutateAsync: updateSalePolicy, isPending } = useUpdateField<TSalesPolicy>({
    apiQuery: updateSalesPolicy,
    keyOfDetailQuery: ['detail-of-sales-policy', id],
    isMessageError: false,
  });

  const handleSubmit = async () => {
    const values = await form.validateFields();
    const listPaymentBatch = form.getFieldValue('listPayment');
    const listBonus = form.getFieldValue('listBonus');
    const periodObj = form.getFieldValue('periodObj');

    const formatListPaymentBatch = formatListPaymentForSubmit(listPaymentBatch);
    const formatListBonus = formatBonusListForSubmit(listBonus);

    const newData = {
      ...values,
      ...periodObj,
      id,
      year: Number(values?.year),
      name: values?.name?.trim() || '',
      code: values?.code?.trim() || '',
      analysisBonus: values?.analysisBonus?.trim() || '',
      analysisIndicator: values?.analysisIndicator?.trim() || '',
      analysisFees: values?.analysisFees?.trim() || '',
      active: values?.active,
      revenueRate: Number(values?.revenueRate),
      amount: Number(values?.amount),
      comamount: Number(values?.comamount),
      comrate: Number(values?.comrate),
      ...formatRangePikerToSubmit(values.date),
      date: undefined,
      listPayment: formatListPaymentBatch,
      listBonus: {
        isProgressiveByQuantity: values?.listBonus?.isProgressiveByQuantity ? true : false,
        isProgressiveByMargin: values?.listBonus?.isProgressiveByMargin ? true : false,
        ...formatListBonus,
      },
    };
    const res = await updateSalePolicy(newData);
    if (res?.data?.statusCode === '0') {
      setIsModified(false);
    }
  };

  const handleCancel = () => {
    form.setFieldsValue(initialValue);
    setIsModified(false);
  };

  const validateForm = () => {
    setIsModified(true);
  };

  return (
    <div className="wrapper-detail-of-sales-policy">
      <Spin spinning={isLoading}>
        <BreadCrumbComponent titleBread={dataSalesPolicy && dataSalesPolicy.name ? dataSalesPolicy.name : ''} />
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          disabled={!permissionUpdate}
          onValuesChange={validateForm}
        >
          <Row gutter={[24, 24]}>
            <Col xs={24} md={12}>
              <GeneralInfo />
              <DetailedInfo enabled />
              <AnalyticalInfo />
            </Col>
            <Col xs={24} md={12}>
              <Col span={24}>
                <div className="update-time">
                  <Row gutter={24}>
                    <Col lg={6} xs={8}>
                      <Text disabled>Ngày cập nhật: </Text>
                    </Col>
                    <Col lg={18} xs={16}>
                      <Text disabled>
                        {dayjs(dataSalesPolicy?.modifiedDate).format(FORMAT_DATE_TIME)}&nbsp;&nbsp;
                        {`${dataSalesPolicy?.modifiedBy?.userName || ''} - ${dataSalesPolicy?.modifiedBy?.fullName || ''}`}
                      </Text>
                    </Col>
                  </Row>
                  <Row gutter={24}>
                    <Col lg={6} xs={8}>
                      <Text disabled>Ngày tạo: </Text>
                    </Col>
                    <Col lg={18} xs={16}>
                      <Text disabled>
                        {dayjs(dataSalesPolicy?.createdDate).format(FORMAT_DATE_TIME)}&nbsp;&nbsp;
                        {`${dataSalesPolicy?.createdBy?.userName || ''} - ${dataSalesPolicy?.createdBy?.fullName || ''}`}
                      </Text>
                    </Col>
                  </Row>
                </div>
              </Col>
            </Col>
            <Col span={24}>
              <PaymentBatch />
            </Col>
            <Col span={24}>
              <div className="list-bonus">
                <Space direction="vertical" size={'middle'}>
                  <Title level={5} style={{ margin: 0 }}>
                    Thưởng vượt chỉ tiêu
                  </Title>
                  <Space direction="horizontal" align="baseline" size={'large'} style={{ marginLeft: 24 }}>
                    <Text>Thưởng theo số lượng sản phẩm</Text>
                    <Form.Item name={['listBonus', 'isProgressiveByQuantity']} valuePropName="checked">
                      <Checkbox disabled={disabled}>Tính lũy tiến</Checkbox>
                    </Form.Item>
                  </Space>
                  <ListBonus<TItemBonusByQuantity>
                    labelAdd="Thêm thưởng SLSP"
                    nameField="byQuantity"
                    columns={columnSold}
                    arrayFieldTable={ARRAY_FIELD_BY_QUANTITY}
                  />

                  <Space direction="horizontal" align="baseline" size={'large'} style={{ marginLeft: 24 }}>
                    <Text>Thưởng theo tỷ lệ bán được/ số sản phẩm ký quỹ </Text>
                    <Form.Item name={['listBonus', 'isProgressiveByMargin']} valuePropName="checked">
                      <Checkbox disabled={disabled}>Tính lũy tiến</Checkbox>
                    </Form.Item>
                  </Space>
                  <ListBonus<TItemBonusByMargin>
                    labelAdd="Thêm thưởng SLKQ"
                    nameField="byMargin"
                    columns={columnPercentage}
                    arrayFieldTable={ARRAY_FIELD_BY_MARGIN}
                  />
                </Space>
              </div>
            </Col>
          </Row>
        </Form>
        {isModified && (
          <ButtonOfPageDetail
            handleSubmit={() => form.submit()}
            handleCancel={handleCancel}
            loadingSubmit={isPending}
          />
        )}
      </Spin>
    </div>
  );
};

export default DetailOfSalesPolicy;
