import { create } from 'zustand';
import { TSalesPolicy } from '../../types/salesPolicy';

interface IActionModal {
  isOpen: boolean;
  type?: 'create' | 'clone';
  id?: string;
}

interface ISalesPolicyStore {
  disabled: boolean;
  initialValue: TSalesPolicy;
  isModified: boolean;
  actionModal: IActionModal;
  setDisabled: (value: boolean) => void;
  setInitialValue: (value?: TSalesPolicy) => void;
  setIsModified: (value: boolean) => void;
  setActionModal: (value: IActionModal) => void;
}

export const useSalesPolicyStore = create<ISalesPolicyStore>(set => ({
  disabled: false,
  initialValue: {} as TSalesPolicy,
  isModified: false,
  actionModal: { isOpen: false, type: undefined, id: undefined },
  setDisabled: value => set({ disabled: value }),
  setInitialValue: value => set({ initialValue: value }),
  setIsModified: value => set({ isModified: value }),
  setActionModal: (value: IActionModal) => set({ actionModal: value }),
}));
