import { MutationFunction } from '@tanstack/react-query';
import { App, Button } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { useMemo, useState } from 'react';
import ConfirmDeleteModal from '../../../components/modal/specials/ConfirmDeleteModal';
import { modalConfirm } from '../../../components/modal/specials/ModalConfirm';
import TableComponent from '../../../components/table';
import { ActionsColumns } from '../../../components/table/components/ActionsColumns';
import { COMMISSION_PERIOD_DEFAULT } from '../../../configs/path';
import { PERMISSION_COMMISSION } from '../../../constants/permissions/commission';
import { useCheckPermissions, useUpdateField } from '../../../hooks';
import {
  changeStatusTimeConfigFeeCommission,
  softDeleteTimeConfigFeeCommission,
} from '../../../service/timeConfigFeeCommission';
import { TListOfTimeConfigFeeCommission, TTabTimeConfigFeeCommission } from '../../../types/timeConfigFeeCommission';
import CreateTimeConfigFeeCommission from '../createTimeConfigFeeCommission';
import { useStoreTimeConfigFeeCommission } from '../storeTimeConfig';
import FilterTimeConfigFeeCommission from './FilterTimeConfigFeeCommission';

interface TTimeConfigFeeCommission {
  tabActive: TTabTimeConfigFeeCommission;
  columns: ColumnsType<TListOfTimeConfigFeeCommission>;
}

const ConfigFeeCommission = (props: TTimeConfigFeeCommission) => {
  const { tabActive, columns } = props;
  const { periodCreate, periodDelete, periodChangeStatus, periodGetId } = useCheckPermissions(PERMISSION_COMMISSION);

  const { modal } = App.useApp();
  const { dataTimeConfig, loading, tab, getCurrentFilter } = useStoreTimeConfigFeeCommission();
  const [open, setOpen] = useState(false);
  const [isOpenModalDelete, setIsOpenModalDelete] = useState<boolean>(false);
  const [currentRecord, setCurrentRecord] = useState<TListOfTimeConfigFeeCommission>();

  const { mutateAsync } = useUpdateField({
    apiQuery: changeStatusTimeConfigFeeCommission,
    keyOfListQuery: ['list-of-time-config-fee-commission', getCurrentFilter()],
    isMessageError: false,
  });

  const columnsActions: ColumnsType<TListOfTimeConfigFeeCommission> = useMemo(() => {
    return [
      ...columns,
      {
        title: 'Hành động',
        dataIndex: 'action',
        key: 'action',
        width: '100px',
        align: 'center',
        render: (_: unknown, record: TListOfTimeConfigFeeCommission) => {
          const textModalConfirmActive = record?.isActive === 1 ? 'Vô hiệu hoá' : 'Kích hoạt';
          const handleActiveTimeConfig = () => {
            return modalConfirm({
              modal: modal,
              title: `${textModalConfirmActive} cấu hình`,
              content: `Bạn có muốn ${textModalConfirmActive} cấu hình này không?`,
              handleConfirm: async () =>
                await mutateAsync({ id: record?.id, isActive: record?.isActive === 1 ? 2 : 1, name: record?.name }),
            });
          };
          const handleDeleteRole = () => {
            setIsOpenModalDelete(true);
            setCurrentRecord(record);
          };

          const openViewDetail = () => {
            window.open(`${COMMISSION_PERIOD_DEFAULT}/${record?.id}?tab=${tab}`, '_blank', 'noopener,noreferrer');
          };

          return (
            <ActionsColumns
              handleViewDetail={periodGetId ? openViewDetail : undefined}
              textModalConfirmActive={textModalConfirmActive}
              handleActive={periodChangeStatus ? handleActiveTimeConfig : undefined}
              handleDelete={periodDelete ? handleDeleteRole : undefined}
            />
          );
        },
      },
    ];
  }, [columns, modal, mutateAsync, periodChangeStatus, periodDelete, periodGetId, tab]);

  const handleOpenModalCreate = () => {
    setOpen(true);
  };

  const handleCloseModalCreate = () => {
    setOpen(false);
  };

  return (
    <>
      <div className="header-filter" style={{ marginBottom: 16 }}>
        <FilterTimeConfigFeeCommission />
        {periodCreate && (
          <Button type="primary" onClick={handleOpenModalCreate}>
            Tạo mới
          </Button>
        )}
      </div>
      <TableComponent
        className="table-time-config-default-list"
        columns={columnsActions}
        queryKeyArr={['list-of-time-config-fee-commission', getCurrentFilter()]}
        dataSource={dataTimeConfig[tabActive]}
        loading={loading}
        rowKey={'id'}
      />
      <CreateTimeConfigFeeCommission open={open} handleCancel={handleCloseModalCreate} />
      <ConfirmDeleteModal
        label="Cấu hình"
        open={isOpenModalDelete}
        apiQuery={softDeleteTimeConfigFeeCommission as MutationFunction<unknown, unknown>}
        keyOfListQuery={['list-of-time-config-fee-commission', getCurrentFilter()]}
        onCancel={() => setIsOpenModalDelete(false)}
        idDetail={currentRecord?.id}
        title="Xóa cấu hình"
        description="Vui lòng nhập lý do muốn xoá cấu hình này"
        fieldNameReason="reasonDelete"
      />
    </>
  );
};

export default ConfigFeeCommission;
