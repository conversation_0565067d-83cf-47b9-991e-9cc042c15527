import { Tabs } from 'antd';
import { useEffect } from 'react';
import BreadCrumbComponent from '../../../components/breadCrumb';
import { DEFAULT_PARAMS } from '../../../constants/common';
import { useFetch } from '../../../hooks';
import useFilter from '../../../hooks/filter';
import { getAllTimeConfigFeeCommission } from '../../../service/timeConfigFeeCommission';
import { TListOfTimeConfigFeeCommission, TTabTimeConfigFeeCommission } from '../../../types/timeConfigFeeCommission';
import { useStoreTimeConfigFeeCommission } from '../storeTimeConfig';
import { columnsTimeConfigFeeCommissionDefault, columnsTimeConfigFeeCommissionOrgChart } from './columns';
import ConfigFeeCommission from './ConfigFeeCommission';
import './styles.scss';

const ListOfTimeConfigFeeCommission = () => {
  const [filter, setFilter] = useFilter();

  const { setTab, tab, setDataForTab, setLoading, tabsFilters, getCurrentFilter, setTabFilter } =
    useStoreTimeConfigFeeCommission();

  const { data, isFetching } = useFetch<TListOfTimeConfigFeeCommission[]>({
    api: getAllTimeConfigFeeCommission,
    queryKeyArrWithFilter: ['list-of-time-config-fee-commission', getCurrentFilter()],
    moreParams: {
      forOrg: tab === 'orgChart' ? true : false,
      ...(getCurrentFilter() as Record<string, string | undefined>),
    },
  });

  useEffect(() => {
    setTab((filter.tab || 'default') as TTabTimeConfigFeeCommission);
  }, [filter.tab, setTab]);

  useEffect(() => {
    if (data?.data?.data?.rows) {
      setDataForTab(tab, data.data.data.rows);
      setLoading(isFetching);
    }
  }, [data, isFetching, setDataForTab, setLoading, tab]);

  const handleChangeTab = (key: string) => {
    const tabValue = key as TTabTimeConfigFeeCommission;
    setTab(tabValue as TTabTimeConfigFeeCommission);
    const savedFilter = tabsFilters[tabValue] || DEFAULT_PARAMS;
    setTabFilter(tabValue, savedFilter);
    setFilter({ tab: tabValue }); // Update filter with the new tab
  };

  const items = [
    {
      label: 'Cấu hình mặc định',
      key: 'default',
      children: <ConfigFeeCommission tabActive="default" columns={columnsTimeConfigFeeCommissionDefault} />,
    },

    {
      label: 'Cấu hình theo đơn vị',
      key: 'orgChart',
      children: <ConfigFeeCommission tabActive="orgChart" columns={columnsTimeConfigFeeCommissionOrgChart} />,
    },
  ];

  return (
    <>
      <BreadCrumbComponent />
      <Tabs
        className="tabs-time-config-fee-commission"
        type="card"
        onChange={handleChangeTab}
        activeKey={tab}
        items={items}
      />
    </>
  );
};

export default ListOfTimeConfigFeeCommission;
