import { Col, Form, Row, Spin, Typography } from 'antd';
import dayjs from 'dayjs';
import omit from 'lodash/omit';
import { useEffect } from 'react';
import { useBeforeUnload, useParams, useSearchParams } from 'react-router-dom';
import BreadCrumbComponent from '../../../components/breadCrumb';
import ButtonOfPageDetail from '../../../components/button/buttonOfPageDetail';
import { FORMAT_DATE_TIME } from '../../../constants/common';
import { useFetch, useUpdateField } from '../../../hooks';
import {
  getByIdTimeConfigFeeCommission,
  updateTimeConfigFeeCommission,
} from '../../../service/timeConfigFeeCommission';
import {
  TListOfTimeConfigFeeCommission,
  TSubmitTimeConfigFeeCommission,
  TTabTimeConfigFeeCommission,
} from '../../../types/timeConfigFeeCommission';
import TimeConfigFields from '../components/TimeConfigFields';
import { useStoreTimeConfigFeeCommission } from '../storeTimeConfig';

const { Text } = Typography;

const DetailOfTimeConfigFeeCommission = () => {
  const { id } = useParams();
  const [form] = Form.useForm();
  const [searchParams] = useSearchParams();
  const tab = searchParams.get('tab');
  const { setTab, initialValue, setInitialValue, setIsModified, isModified } = useStoreTimeConfigFeeCommission();

  const { mutateAsync, isPending } = useUpdateField({
    apiQuery: updateTimeConfigFeeCommission,
    keyOfDetailQuery: ['detail-of-commission', id],
    isMessageError: false,
  });

  const { data: commission, isLoading } = useFetch<TListOfTimeConfigFeeCommission>({
    api: () => getByIdTimeConfigFeeCommission(id),
    queryKeyArr: ['detail-of-commission', id],
    enabled: !!id,
  });
  const dataSource = commission?.data?.data;

  useEffect(() => {
    if (tab) {
      setTab(tab as TTabTimeConfigFeeCommission);
    }
  }, [tab, setTab]);

  useEffect(() => {
    if (!dataSource) return;
    const newDataSource = {
      ...dataSource,
    };
    setInitialValue(newDataSource);
    form.setFieldsValue(newDataSource);
  }, [dataSource, form, setInitialValue]);

  const validateForm = () => {
    setIsModified(true);
  };

  const handleSubmit = async (values: TSubmitTimeConfigFeeCommission) => {
    const cleaned = omit(values, ['periodEndDate']);
    const newData = {
      ...cleaned,
      id,
    };
    const res = await mutateAsync(newData);
    if (res?.data?.statusCode === '0') {
      setIsModified(false);
    }
  };

  const handleCancel = () => {
    form.setFieldsValue(initialValue);
    setIsModified(false);
  };

  useBeforeUnload(event => {
    if (isModified) {
      event.preventDefault();
      return true;
    }
    return undefined;
  });

  return (
    <div>
      <Spin spinning={isLoading}>
        <BreadCrumbComponent titleBread={dataSource?.name} />
        <Form
          form={form}
          layout="vertical"
          onValuesChange={validateForm}
          onFinish={handleSubmit}
          className="wrapper-form-Time-config"
          initialValues={initialValue}
        >
          <TimeConfigFields action="modify" />
          <div className="wrapper-update-time">
            <Row gutter={24} style={{ marginBottom: '10px' }}>
              <Col lg={6} xs={8}>
                <Text>Ngày cập nhật: </Text>
              </Col>
              <Col lg={18} xs={16}>
                <Text>
                  {dataSource?.modifiedDate && dayjs(dataSource?.modifiedDate).format(FORMAT_DATE_TIME)}&nbsp;&nbsp;
                  {`${dataSource?.modifiedBy?.userName} - ${dataSource?.modifiedBy?.fullName}`}
                </Text>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col lg={6} xs={8}>
                <Text>Ngày tạo: </Text>
              </Col>
              <Col lg={18} xs={16}>
                <Text>
                  {dataSource?.createdDate && dayjs(dataSource?.createdDate).format(FORMAT_DATE_TIME)}&nbsp;&nbsp;
                  {`${dataSource?.createdBy?.userName}- ${dataSource?.createdBy?.fullName}`}
                </Text>
              </Col>
            </Row>
          </div>
        </Form>
        {isModified && (
          <ButtonOfPageDetail
            handleSubmit={() => form.submit()}
            handleCancel={handleCancel}
            loadingSubmit={isPending}
          />
        )}
      </Spin>
    </div>
  );
};

export default DetailOfTimeConfigFeeCommission;
