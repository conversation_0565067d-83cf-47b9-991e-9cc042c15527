import { Col, Form, Input, Row, Select, Switch } from 'antd';
import { useMemo } from 'react';
import MultiSelectLazy from '../../../components/select/mutilSelectLazy';
import { PERMISSION_COMMISSION } from '../../../constants/permissions/commission';
import { useCheckPermissions } from '../../../hooks';
import { getOrgChartDropdown } from '../../../service/lead';
import { TListDropdown } from '../../../types/salesPolicy';
import { useStoreTimeConfigFeeCommission } from '../storeTimeConfig';

interface ITimeConfigFields {
  action: 'create' | 'modify';
}
const TimeConfigFields = ({ action }: ITimeConfigFields) => {
  const form = Form.useFormInstance();

  const { tab, initialValue, setIsModified } = useStoreTimeConfigFeeCommission();
  const { periodChangeStatus } = useCheckPermissions(PERMISSION_COMMISSION);
  const isOrgChartTab = tab === 'orgChart';
  const days = Array.from({ length: 31 }, (_, i) => i + 1);

  const formatOrgChartsSelected = useMemo(
    () =>
      action === 'modify'
        ? initialValue?.orgcharts?.map(item => ({
            ...item,
            label: item?.name as string,
            value: item?.id as string,
          }))
        : undefined,
    [action, initialValue?.orgcharts],
  );

  const handleStartDayChange = (value: number) => {
    let endDay = value - 1;
    if (value === 1) {
      endDay = 31;
    }
    form.setFieldsValue({ periodEndDate: `Ngày ${endDay}` });
  };

  const handleSelectListPos = (values: TListDropdown[]) => {
    action === 'modify' && setIsModified(true);
    form.setFieldsValue({ orgcharts: values.map(item => ({ id: item?.id, name: item.name, code: item.code })) });
  };

  return (
    <Col lg={12} xs={24}>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            label="Tên cấu hình"
            name="name"
            rules={[{ required: true, message: 'Vui lòng nhập tên cấu hình', whitespace: true }]}
          >
            <Input placeholder="Nhập tên cấu hình" maxLength={255} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label="Ngày đầu kỳ"
            name="periodStartDate"
            rules={[{ required: true, message: 'Vui lòng chọn ngày đầu kỳ' }]}
          >
            <Select
              placeholder="Chọn ngày đầu kỳ"
              allowClear
              options={days.map(day => ({ label: `Ngày ${day}`, value: day }))}
              onChange={handleStartDayChange}
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Ngày cuối kỳ" name="periodEndDate">
            <Input placeholder="Tự động hiển thị" disabled />
          </Form.Item>
        </Col>
        {isOrgChartTab && (
          <Col span={24}>
            <Form.Item label="Đơn vị" name="orgcharts" rules={[{ required: true, message: 'Vui lòng chọn đơn vị' }]}>
              <MultiSelectLazy
                apiQuery={getOrgChartDropdown}
                queryKey={['orgChart-exchanges']}
                enabled={isOrgChartTab}
                keysTag={'name'}
                keysLabel={['name', 'code']}
                placeholder="Chọn đơn vị"
                handleListSelect={handleSelectListPos}
                defaultValues={formatOrgChartsSelected}
              />
            </Form.Item>
          </Col>
        )}
        <Col span={12}>
          <Form.Item
            label="Trạng thái"
            layout="horizontal"
            labelCol={{ span: 12 }}
            labelAlign="left"
            name="isActive"
            valuePropName="checked"
            getValueFromEvent={checked => (checked ? 1 : 2)}
            getValueProps={value => ({
              checked: value === 1,
            })}
          >
            <Switch disabled={!periodChangeStatus} />
          </Form.Item>
        </Col>

        <Col span={12}>
          <Form.Item shouldUpdate>
            {({ getFieldValue }) => {
              const status = getFieldValue('isActive');
              return (
                <span style={{ color: status === 1 ? '#389E0D' : '#CF1322' }}>
                  {status === 1 ? 'Đã kích hoạt' : 'Vô hiệu hóa'}
                </span>
              );
            }}
          </Form.Item>
        </Col>
      </Row>
    </Col>
  );
};

export default TimeConfigFields;
