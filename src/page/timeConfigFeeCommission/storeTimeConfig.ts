import { create } from 'zustand';
import { TListOfTimeConfigFeeCommission } from '../../types/timeConfigFeeCommission';
import { TTabTimeConfigFeeCommission } from '../../types/timeConfigFeeCommission/index';

type TimeConfigData = {
  default: TListOfTimeConfigFeeCommission[]; // Replace `any` with your real type
  orgChart: TListOfTimeConfigFeeCommission[];
};

interface ITimeConfigFeeCommissionStore {
  tab: TTabTimeConfigFeeCommission;
  dataTimeConfig: TimeConfigData;
  loading?: boolean;
  tabsFilters: Record<string, Record<string, string | undefined>>;
  initialValue?: TListOfTimeConfigFeeCommission;
  isModified: boolean;
  setIsModified: (isModified: boolean) => void;
  setTab: (value: TTabTimeConfigFeeCommission) => void;
  setDataForTab: (tab: TTabTimeConfigFeeCommission, data: TListOfTimeConfigFeeCommission[]) => void;
  setTabFilter: (tab: string, filter: Record<string, string | undefined>) => void;
  setLoading: (loading: boolean) => void;
  getCurrentFilter: () => Record<string, string>;
  setInitialValue: (data: TListOfTimeConfigFeeCommission) => void;
}

export const useStoreTimeConfigFeeCommission = create<ITimeConfigFeeCommissionStore>((set, get) => ({
  tab: 'default',
  dataTimeConfig: {
    default: [],
    orgChart: [],
  },
  tabsFilters: {
    default: {},
    orgChart: {},
  },
  loading: false,
  initialValue: undefined,
  isModified: false,
  setTab: (value: TTabTimeConfigFeeCommission) => set({ tab: value }),
  setDataForTab: (tab: TTabTimeConfigFeeCommission, data: TListOfTimeConfigFeeCommission[]) =>
    set(state => ({ dataTimeConfig: { ...state.dataTimeConfig, [tab]: data } })),
  setLoading: (loading: boolean) => set({ loading }),
  setTabFilter: (tab, filter) =>
    set(state => ({
      tabsFilters: {
        ...state.tabsFilters,
        [tab]: filter,
      },
    })),
  getCurrentFilter: () => {
    const state = get();
    const tabFilter = state.tabsFilters[state.tab] || {};
    const rawFilter = typeof tabFilter === 'object' ? { ...tabFilter, tab: state.tab } : { tab: state.tab };
    return Object.entries(rawFilter)
      .filter(([, value]) => Boolean(value))
      .reduce(
        (acc, [key, value]) => {
          acc[key] = String(value);
          return acc;
        },
        {} as Record<string, string>,
      );
  },
  setInitialValue: (data: TListOfTimeConfigFeeCommission) => set({ initialValue: data }),
  setIsModified: (isModified: boolean) => set({ isModified }),
}));
