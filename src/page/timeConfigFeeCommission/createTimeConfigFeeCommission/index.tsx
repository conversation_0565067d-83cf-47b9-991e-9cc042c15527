import { Button, Form } from 'antd';
import { useBeforeUnload } from 'react-router-dom';
import ModalComponent from '../../../components/modal';
import { showConfirmCancelModal } from '../../../components/modal/specials/ConfirmCancelModal';
import { COMMISSION_PERIOD_DEFAULT } from '../../../configs/path';
import { useCreateField } from '../../../hooks';
import { createTimeConfigFeeCommission } from '../../../service/timeConfigFeeCommission';
import { TListOfTimeConfigFeeCommission, TSubmitTimeConfigFeeCommission } from '../../../types/timeConfigFeeCommission';
import TimeConfigFields from '../components/TimeConfigFields';
import { useStoreTimeConfigFeeCommission } from '../storeTimeConfig';

interface Props {
  handleCancel: () => void;
  open: boolean;
}

const CreateTimeConfigFeeCommission = (props: Props) => {
  const { handleCancel, open } = props;
  const [form] = Form.useForm();
  const { tab, setInitialValue } = useStoreTimeConfigFeeCommission();

  const { mutateAsync, isPending } = useCreateField({
    keyOfListQuery: ['list-of-time-config-fee-commission'],
    apiQuery: createTimeConfigFeeCommission,
    messageSuccess: 'Tạo mới cấu hình thành công',
    isMessageError: false,
  });

  const handleSubmit = async (values: TSubmitTimeConfigFeeCommission) => {
    const newData = {
      isActive: values.isActive,
      periodStartDate: values.periodStartDate,
      name: values.name,
      orgcharts: values?.orgcharts,
    };
    const res = await mutateAsync(newData);
    const data = res?.data?.data as TListOfTimeConfigFeeCommission;
    if (res?.data?.statusCode === '0') {
      handleCancel();
      window.open(`${COMMISSION_PERIOD_DEFAULT}/${data?.id}?tab=${tab}`, '_blank', 'noopener,noreferrer');
      setInitialValue(data);
    }
  };

  useBeforeUnload(event => {
    if (open && form.isFieldsTouched()) {
      event.preventDefault();
      return true;
    }
    return undefined;
  });

  return (
    <ModalComponent
      className="wrapper-modal-create-time-config-commission"
      title={`Tạo mới cấu hình`}
      open={open}
      footer={[
        <Button type="primary" loading={isPending} onClick={() => form.submit()} key="submit">
          Tạo mới
        </Button>,
      ]}
      onCancel={() => {
        if (!form.isFieldsTouched()) {
          handleCancel();
          return;
        }
        showConfirmCancelModal({
          onConfirm: handleCancel,
        });
      }}
      destroyOnClose
      afterClose={() => form.resetFields()}
      maskClosable={false}
    >
      <Form
        form={form}
        onFinish={handleSubmit}
        initialValues={{
          isActive: 1,
        }}
        layout="vertical"
      >
        <TimeConfigFields action="create" />
      </Form>
    </ModalComponent>
  );
};

export default CreateTimeConfigFeeCommission;
