import React, { useState } from 'react';
import { Upload, Button, App, Typography, Tooltip } from 'antd';
import { PaperClipOutlined, UploadOutlined } from '@ant-design/icons';
import type { UploadProps, UploadFile } from 'antd';
import { RcFile } from 'antd/es/upload/interface';
import './styles.scss';
import { modalConfirm } from '../../../../components/modal/specials/ModalConfirm';
import { ALLOWED_EXTENSIONS } from '../../../../constants/common';
import { uploadMedia } from '../../../../service/upload';

const { Text } = Typography;

interface ExtendedUploadFile extends UploadFile {
  key?: string;
}
interface FileUploadProps {
  fileList: ExtendedUploadFile[];
  setFileList: (fileList: ExtendedUploadFile[]) => void;
  uploadPath?: string;
  size?: number; // Kích thước tối đa của mỗi file (MB)
  isDetail?: boolean;
}

const UploadFileDepositContract: React.FC<FileUploadProps> = ({
  fileList,
  setFileList,
  uploadPath = 'primary-contract/policy',
  size = 10, // Kích thước tối đa của mỗi file (MB)
  isDetail,
}) => {
  const { modal } = App.useApp();
  const [uploading, setUploading] = useState(false);

  // Danh sách MIME types được phép
  const allowedMimeTypes = [
    'image/png',
    'image/jpeg',
    'application/vnd.ms-excel', // .xls
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
    'application/msword', // .doc
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
    'application/pdf',
    'application/vnd.ms-powerpoint', // .ppt
    'application/vnd.openxmlformats-officedocument.presentationml.presentation', // .pptx
  ];
  const maxFileSize: number = size * 1024 * 1024; // Kích thước tối đa của mỗi file (bytes)
  const maxTotalSize: number = 25 * 1024 * 1024; // Tổng kích thước tối đa 25MB (bytes)
  const maxFiles: number = 10; // Số lượng file tối đa

  // Hàm kiểm tra file
  const validateFile = (file: RcFile): boolean => {
    const fileListLength = fileList?.length ?? 0;
    const fileSize = file.size as number;
    const fileMimeType = file.type;
    const fileExtension = file.name.split('.').pop()?.toLowerCase();

    // Kiểm tra số lượng file tối đa
    if (fileListLength >= maxFiles) {
      modalConfirm({
        modal,
        title: 'Không thể upload file',
        content: `Đã đạt số lượng file tối đa ${maxFiles} file`,
        cancelText: null,
        okText: 'Đóng',
      });
      return false;
    }

    // Kiểm tra tổng kích thước của tất cả file (bao gồm file mới)
    const totalSize = fileList.reduce((sum, item) => sum + (item.size || 0), 0) + fileSize;
    if (totalSize > maxTotalSize) {
      modalConfirm({
        modal,
        title: 'Không thể upload file',
        content: `Tổng kích thước các file vượt quá giới hạn 25MB`,
        cancelText: null,
        okText: 'Đóng',
      });
      return false;
    }

    // Kiểm tra MIME type và kích thước file
    if (
      !fileMimeType ||
      !allowedMimeTypes.includes(fileMimeType) ||
      fileSize > maxFileSize ||
      !ALLOWED_EXTENSIONS.includes(fileExtension || '')
    ) {
      modalConfirm({
        modal,
        title: 'Không thể upload file',
        content: `Không thể upload file (chỉ chấp nhận .png, .jpg, .jpeg, .xls, .xlsx, .doc, .docx, .pdf, .ppt, .pptx) hoặc vượt quá ${size}MB`,
        cancelText: null,
        okText: 'Đóng',
      });
      return false;
    }

    return true;
  };

  // Hàm xử lý upload file
  const handleUpload = async (file: RcFile): Promise<boolean> => {
    if (!validateFile(file)) {
      return false;
    }
    setUploading(true);
    try {
      const uploadResp = await uploadMedia(file, uploadPath);
      const fileUrl = uploadResp?.data?.data?.key
        ? `${import.meta.env.VITE_S3_IMAGE_URL}/${uploadResp.data.data.key}`
        : '';
      const uploadedFile: ExtendedUploadFile = {
        uid: file.uid,
        name: file.name,
        status: 'done',
        url: fileUrl,
        key: uploadResp?.data?.data?.key,
        size: file.size, // Lưu kích thước file
      };

      // Thêm tệp mới vào filelist hiện có
      setFileList([...fileList, uploadedFile]);
      return false;
    } catch (error) {
      return false;
    } finally {
      setUploading(false);
    }
  };

  // Upload props
  const props: UploadProps = {
    name: 'file',
    multiple: false,
    fileList,
    beforeUpload: isDetail ? undefined : handleUpload,
    onRemove: (file: UploadFile) => {
      const newFileList = fileList.filter(item => item.uid !== file.uid);
      setFileList(newFileList);
    },
    onChange: () => {},
  };

  return (
    <>
      <Upload
        {...props}
        disabled={isDetail}
        showUploadList={{
          showRemoveIcon: isDetail ? false : true,
        }}
        fileList={isDetail ? undefined : fileList}
      >
        <Button icon={<UploadOutlined />} loading={uploading} disabled={uploading}>
          Upload
        </Button>
        <div style={{ marginTop: 10 }}>
          <Text className="custom-text">Tải tối đa 10 files, mỗi file không quá 10MB</Text>
          <br />
          <Text className="custom-text">Tổng dung lượng file không quá 25MB</Text>
        </div>
        <div
          style={{
            marginTop: 10,
          }}
        >
          {isDetail &&
            fileList?.map((f: ExtendedUploadFile) => {
              const fileUrl = f.url ? `${import.meta.env.VITE_S3_IMAGE_URL}/${f.url}` : '';
              return (
                <div className="ant-upload-list-item-container-customer">
                  <div className="ant-upload-list-item ant-upload-list-item-done" key={f.uid}>
                    <div className="ant-upload-icon">
                      <PaperClipOutlined />
                    </div>
                    <Tooltip title={f.name}>
                      <a href={fileUrl} target="_blank" rel="noopener noreferrer" className="ant-upload-list-item-name">
                        {f.name}
                      </a>
                    </Tooltip>
                  </div>
                </div>
              );
            })}
        </div>
      </Upload>
    </>
  );
};

export default UploadFileDepositContract;
