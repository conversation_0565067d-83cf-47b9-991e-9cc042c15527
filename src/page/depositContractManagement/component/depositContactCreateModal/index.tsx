import ModalComponent from '../../../../components/modal';
import DepositContactForm from '../depositContactForm';

interface DepositContactCreateModalProps {
  visible: boolean;
  onClose: () => void;
}

const DepositContactCreateModal = ({ visible, onClose }: DepositContactCreateModalProps) => {
  return (
    <ModalComponent
      className="modal-deposit-contract"
      title="Tạo mới hợp đồng ký quỹ"
      open={visible}
      footer={null}
      onCancel={onClose}
      destroyOnClose
    >
      <DepositContactForm />
    </ModalComponent>
  );
};

export default DepositContactCreateModal;
