import React, { useState } from 'react';
import { TableColumnsType, Typography, Row, Button, Col } from 'antd';
import { useFetch } from '../../../hooks';
import TableComponent from '../../../components/table';
import BreadCrumbComponent from '../../../components/breadCrumb';
import { DEPOSIT_CONTRACT_FORM_NAME, ORGCHART_TYPE_NAME } from '../../../constants/common';
import ConfirmDeleteModal from '../../../components/modal/specials/ConfirmDeleteModal';
import { MutationFunction } from '@tanstack/react-query';
import { ActionsColumns } from '../../../components/table/components/ActionsColumns';
import { useNavigate } from 'react-router-dom';
import { DEPOSIT_CONTRACT } from '../../../configs/path';
import { deleteDepositContract, getListDepositContract } from '../../../service/depositContract';
import { DepositContract } from '../../../types/depositContract';
import FilterSearch from '../component/filterSearch';
import '../styles.scss';
import DepositContactCreateModal from '../component/depositContactCreateModal';

const { Text } = Typography;

const DepositContractList: React.FC = () => {
  const navigate = useNavigate();
  const [isModalCreateDepositContract, setModalCreateDepositContract] = useState<boolean>(false);
  const [isOpenModalDelete, setIsOpenModalDelete] = useState<boolean>(false);
  const [currentRecord, setCurrentRecord] = useState<DepositContract>();

  const {
    data: depositContract,
    isLoading,
    isPlaceholderData,
    isFetching,
  } = useFetch<DepositContract[]>({
    queryKeyArrWithFilter: ['get-deposit-contract'],
    api: getListDepositContract,
  });
  const depositContracts = depositContract?.data?.data?.rows || [];

  const columns: TableColumnsType<DepositContract> = [
    {
      title: 'Mã hợp đồng',
      dataIndex: 'code',
      key: 'code',
      render: (_: string, record: DepositContract) => {
        return <Text>{record?.code}</Text>;
      },
    },
    {
      title: 'Dự án',
      dataIndex: ['project', 'name'],
      key: 'project',
    },
    {
      title: 'Tên đơn vị',
      dataIndex: ['orgchart', 'name'],
      key: 'name',
    },
    {
      title: 'Loại đơn vị',
      dataIndex: ['orgchart', 'type'],
      key: 'project',
      render: (value: string) => {
        return <Text style={{ color: ORGCHART_TYPE_NAME[value] }}>{ORGCHART_TYPE_NAME[value]}</Text>;
      },
    },
    {
      title: 'Hình thức ký quỹ',
      dataIndex: 'depositForm',
      key: 'depositForm',

      render: (value: string) => {
        return <Text style={{ color: DEPOSIT_CONTRACT_FORM_NAME[value] }}>{DEPOSIT_CONTRACT_FORM_NAME[value]}</Text>;
      },
    },
    {
      title: 'Hành động',
      dataIndex: 'action',
      key: 'action',
      align: 'center',
      width: 140,
      render: (_, record: DepositContract) => {
        const handleDeleteDiscountPolicy = () => {
          setIsOpenModalDelete(!isOpenModalDelete);
          setCurrentRecord(record);
        };
        return (
          <ActionsColumns
            moreActions={[
              {
                label: 'Xem chi tiết',
                key: 'detail',
                onClick: () => navigate(`${DEPOSIT_CONTRACT}/${record?.id}`),
              },
              {
                label: 'Xóa',
                key: 'delete',
                onClick: handleDeleteDiscountPolicy,
              },
            ].filter(action => action !== null)}
          />
        );
      },
    },
  ];

  return (
    <div className="deposit-contract-page">
      <BreadCrumbComponent />
      <div className="header-content">
        <FilterSearch />
        <Row gutter={[16, 8]}>
          <Col>
            <Button type="default" onClick={() => setModalCreateDepositContract(true)}>
              Tạo mới hợp đồng ký quỹ
            </Button>
          </Col>
        </Row>
      </div>

      <TableComponent
        queryKeyArr={['get-deposit-contract']}
        className="table-deposit-contract"
        columns={columns}
        loading={isLoading || isPlaceholderData || isFetching}
        dataSource={depositContracts}
        rowKey="id"
      />
      <DepositContactCreateModal
        visible={isModalCreateDepositContract}
        onClose={() => setModalCreateDepositContract(false)}
      />
      <ConfirmDeleteModal
        label="Hợp đồng ký quỹ"
        open={isOpenModalDelete}
        apiQuery={deleteDepositContract as MutationFunction<unknown, unknown>}
        keyOfListQuery={['get-deposit-contract']}
        onCancel={() => setIsOpenModalDelete(false)}
        idDetail={currentRecord?.id}
        title="Xóa hợp đồng ký quỹ"
        description="Vui lòng nhập lý do muốn xoá hợp đồng ký quỹ này"
      />
    </div>
  );
};

export default DepositContractList;
