import { ColumnsType } from 'antd/es/table';
import { Typography } from 'antd';
import dayjs from 'dayjs';
import { TPurchaseContract, TListPurchaseContract } from '../../../../types/contract/purchaseContract';
import { FORMAT_DATE_TIME } from '../../../../constants/common';
import { Link } from 'react-router-dom';
import { DEPOSIT_CONTRACT_MANAGEMENT, PURCHASE_CONTRACT_MANAGEMENT } from '../../../../configs/path';
import { CONTRACT_STATUS_MAP } from '../../../../types/contract/depositContract';

const { Text } = Typography;

export const columns: ColumnsType<TListPurchaseContract> = [
  {
    title: 'Số hợp đồng',
    dataIndex: 'code',
    key: 'code',
    render: (value: string, record?: TPurchaseContract) => (
      <Link to={`${PURCHASE_CONTRACT_MANAGEMENT}/${record?.id}`}>{value || '-'}</Link>
    ),
  },

  {
    title: 'Tên hợp đồng',
    dataIndex: 'name',
    key: 'name',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Tên dự án',
    dataIndex: 'primaryTransaction',
    key: 'projectName',
    render: (_, record: TPurchaseContract) => (
      <>
        <Text>{record?.primaryTransaction?.project?.name}</Text>
      </>
    ),
  },
  {
    title: 'Số sản phẩm',
    dataIndex: 'primaryTransaction',
    key: 'propertyUnit',
    width: 100,
    render: (_, record: TPurchaseContract) => (
      <>
        <Text>{record?.primaryTransaction?.propertyUnit?.code}</Text>
      </>
    ),
  },

  {
    title: 'Mã hợp đồng cọc',
    dataIndex: 'code',
    key: 'code',
    render: (_: string, record?: TPurchaseContract) => (
      <Link to={`${DEPOSIT_CONTRACT_MANAGEMENT}/${record?.deposit?.id}`}>{record?.deposit?.code || '-'}</Link>
    ),
  },

  {
    title: 'Tên hợp đồng cọc',
    dataIndex: 'name',
    key: 'name',
    render: (_: string, record?: TPurchaseContract) => record?.deposit?.name || '-',
  },
  {
    title: 'Ngày tạo',
    dataIndex: 'createdDate',
    key: 'createdDate',
    width: 150,
    render: (value: string) =>
      value ? (
        <>
          <Text>{dayjs(value).format(FORMAT_DATE_TIME)}</Text>
        </>
      ) : (
        '-'
      ),
  },
  {
    title: 'Lý do từ chối',
    dataIndex: 'reason',
    key: 'reason',
    render: (_, record: TPurchaseContract) => (
      <>
        <Text>{record.reason || '-'}</Text>
      </>
    ),
  },
  {
    title: 'Trạng thái',
    dataIndex: 'status',
    key: 'status',
    align: 'center',
    width: 120,
    render: (value: string) => {
      if (value) {
        const status = CONTRACT_STATUS_MAP.find(item => item.status === value);
        return status ? <Text style={{ color: status.color }}>{status.label}</Text> : <Text>-</Text>;
      }
      return <Text>-</Text>;
    },
  },
];
