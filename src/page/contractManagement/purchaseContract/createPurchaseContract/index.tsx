import { Form, Modal } from 'antd';
import { useCallback, useState } from 'react';
import ModalComponent from '../../../../components/modal';
import PurchaseContractForm from '../components/PurchaseContractForm';
import { useCreateField } from '../../../../hooks';
import { createPurchaseContract } from '../../../../service/contract';
import { TPurchaseContractPayload } from '../../../../types/contract/purchaseContract';
import { TDepositContract } from '../../../../types/contract/depositContract';

interface CreatePurchaseContractModalProps {
  visible: boolean;
  onClose: () => void;
  initialValue?: TDepositContract;
}

const CreatePurchaseContractModal = ({ visible, onClose, initialValue }: CreatePurchaseContractModalProps) => {
  const [form] = Form.useForm();
  const [resetUpload, setResetUpload] = useState<boolean>(false);

  const { mutateAsync: create, isPending } = useCreateField({
    keyOfListQuery: ['list-purchase-contract'],
    apiQuery: createPurchaseContract,
    isMessageError: false,
    messageSuccess: 'Tạo mới hợp đồng thuê/ mua bán thành công!',
  });

  const resetFormPurchaseContract = useCallback(async () => {
    await form.resetFields();
    setResetUpload(true);
  }, [form]);

  const handleCancel = useCallback(async () => {
    if (form.isFieldsTouched()) {
      Modal.confirm({
        title: 'Xác nhận hủy',
        content: 'Dữ liệu chưa được lưu, bạn có chắc chắn muốn thoát khỏi trang không?',
        cancelText: 'Quay lại',
        onOk: async () => {
          await resetFormPurchaseContract();
          onClose();
        },
        okButtonProps: { type: 'default' },
        cancelButtonProps: { type: 'primary' },
      });
    } else {
      await resetFormPurchaseContract();
      onClose();
    }
  }, [form, onClose, resetFormPurchaseContract]);

  const handleCreate = useCallback(
    async (values: TPurchaseContractPayload) => {
      try {
        const resp = await create(values);
        if (resp?.data?.statusCode === '0' && resp?.data?.success) {
          await resetFormPurchaseContract();
          onClose();
        }
      } catch (error) {
        console.error('Create error:', error);
      }
    },
    [create, onClose, resetFormPurchaseContract],
  );

  return (
    <ModalComponent
      title="Tạo mới hợp đồng thuê/ mua bán"
      open={visible}
      onCancel={handleCancel}
      footer={null}
      destroyOnClose
    >
      <PurchaseContractForm
        form={form}
        onFinish={handleCreate}
        resetUpload={resetUpload}
        setResetUpload={setResetUpload}
        loading={isPending}
        initialValues={initialValue}
      />
    </ModalComponent>
  );
};

export default CreatePurchaseContractModal;
