.detail-purchase {
  .form-detail {
    margin-top: 2rem;
  }
  .contract-actions {
    display: flex;
    gap: 8px;
    margin-top: 8px;
    align-items: center;
  }
  .payment-info-radio-group {
    margin-top: 8px;
    margin-bottom: 24px;
  }
  .rounded-radio-group .ant-radio-button-wrapper {
    border-radius: 0 !important;
  }

  .rounded-radio-group .ant-radio-button-wrapper:first-child {
    border-top-left-radius: 6px !important;
    border-bottom-left-radius: 6px !important;
  }

  .rounded-radio-group .ant-radio-button-wrapper:last-child {
    border-top-right-radius: 6px !important;
    border-bottom-right-radius: 6px !important;
  }
  .header-content__button {
    display: flex;
    padding: 0px var(--Button-paddingXS, 8px);
    justify-content: center;
    align-items: center;
    gap: 8px;
  }
  @media screen and (max-width: 1440px) {
    .header-content__button {
      padding: 0px var(--Button-paddingXS, 8px);
      justify-content: center;
      flex-wrap: wrap;
      align-items: center;
      gap: 8px;
    }
  }
}
