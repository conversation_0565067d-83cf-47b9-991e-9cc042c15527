import { PrimaryTransaction } from '../../../types/contract/depositContract';

/**
 * Tính giá trị sản phẩm dựa trên loại giá và thông tin giao dịch.
 * @param detailDataTicket - Thông tin giao dịch ch<PERSON>h, chứa propertyUnit với các giá trị giá.
 * @param priceType - Loại giá ('vat', 'non-vat', hoặc 'contract').
 * @returns Giá trị sản phẩm (number).
 */
export const calculateProductPrice = (detailDataTicket: PrimaryTransaction, priceType: string): number => {
  let productPrice = 0;

  if (priceType === 'non-vat') {
    productPrice =
      (detailDataTicket.propertyUnit &&
        (detailDataTicket.propertyUnit.landPrice ?? 0) + (detailDataTicket.propertyUnit.housePrice ?? 0)) ||
      0;
    if (!productPrice) {
      productPrice = (detailDataTicket.propertyUnit && detailDataTicket.propertyUnit.price) || 0;
    }
  } else if (priceType === 'vat') {
    productPrice =
      (detailDataTicket.propertyUnit &&
        (detailDataTicket.propertyUnit.landPriceVat ?? 0) + (detailDataTicket.propertyUnit.housePriceVat ?? 0)) ||
      0;
    if (!productPrice) {
      productPrice = (detailDataTicket.propertyUnit && detailDataTicket.propertyUnit.priceVat) || 0;
    }
  } else if (priceType === 'contract') {
    productPrice = (detailDataTicket.propertyUnit && detailDataTicket.propertyUnit.contractPrice) || 0;
  }

  return productPrice;
};
