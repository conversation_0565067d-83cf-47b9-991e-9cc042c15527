import React from 'react';
import { TableColumnsType } from 'antd';
import { formatNumber } from '../../../utilities/regex';
import TableComponent from '../../../components/table';
import { PaymentHistoryData } from '../../../types/contract/depositContract';

interface PaymentHistoryProps {
  dataPaymentHistory: PaymentHistoryData[];
  isLoadingTable?: boolean;
  productPrice?: number;
  totalTransfered?: number;
}
const PaymentHistory: React.FC<PaymentHistoryProps> = ({
  dataPaymentHistory,
  isLoadingTable,
  productPrice = 0,
  totalTransfered = 0,
}) => {
  // Cột cho bảng nhân viên
  const paymentHistoryColumns: TableColumnsType = [
    {
      title: 'Mã PT',
      dataIndex: 'code',
      key: 'code',
      render: (value: string) => {
        return <span>{value}</span>;
      },
    },
    {
      title: '<PERSON><PERSON><PERSON> thanh toán',
      dataIndex: 'name',
      key: 'name',
      render: (value: string) => {
        return <span>{value}</span>;
      },
    },
    {
      title: '<PERSON>ô tả',
      dataIndex: 'descriptionProgress',
      key: 'descriptionProgress',
      render: (value: string) => {
        return <span>{value}</span>;
      },
    },
    {
      title: 'Hạn thanh toán',
      dataIndex: 'paymentDueDate',
      key: 'paymentDueDate',
      render: (value: string) => {
        return <span>{value}</span>;
      },
    },
    {
      title: 'Tỉ lệ thanh toán',
      dataIndex: 'value',
      key: 'value',
      render: (value: number) => {
        return <span>{value}</span>;
      },
    },
    {
      title: 'Số tiền thanh toán',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      render: (value: number) => {
        return <span>{formatNumber(value)}</span>;
      },
    },
    {
      title: 'Đã thanh toán',
      dataIndex: 'totalTransfered',
      key: 'totalTransfered',
      render: (value: number) => {
        return <span>{formatNumber(value)}</span>;
      },
    },
    {
      title: 'Còn lại',
      dataIndex: 'totalRemaining',
      key: 'totalRemaining',
      className: '',
      render: (value: number) => {
        return <span>{formatNumber(value)}</span>;
      },
    },
    {
      title: 'Ngày thanh toán',
      dataIndex: 'date',
      key: 'date',
      render: (value: string) => {
        return <span>{value}</span>;
      },
    },
    {
      title: 'Trạng thái',
      dataIndex: 'transactionSuccessful',
      key: 'transactionSuccessful',
      render: (value: string) => {
        return <span style={{ color: '#73D13D' }}>{value ? 'Giao dịch thành công' : ''}</span>;
      },
    },
    {
      title: '',
      dataIndex: 'isToContract',
      key: 'isToContract',
      render: (value: string) => {
        return <span>{value ? 'Ra HĐMB' : ''}</span>;
      },
    },
  ];

  return (
    <TableComponent
      dataSource={dataPaymentHistory}
      columns={paymentHistoryColumns}
      queryKeyArr={['']}
      loading={isLoadingTable}
      rowKey={'stt'}
      isPagination={false}
      footer={() => (
        <>
          <div>Tổng số tiền cần thanh toán : {formatNumber(productPrice)} VNĐ</div>
          <div>Tổng số tiền đã thanh toán : {formatNumber(totalTransfered)} VNĐ</div>
          <div>Tổng số tiền còn lại : {formatNumber(productPrice - totalTransfered)} VNĐ</div>
        </>
      )}
    />
  );
};

export default PaymentHistory;
