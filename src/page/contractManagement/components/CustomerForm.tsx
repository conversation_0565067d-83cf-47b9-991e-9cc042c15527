import React, { useCallback } from 'react';
import { Form, Input, Row, Col, Typography, FormInstance, DatePicker, Checkbox, Radio } from 'antd';
import { FORMAT_DATE, OPTIONS_GENDER, OPTIONS_IDENTITIES, REGEX_PHONE_VN } from '../../../constants/common';
import SelectAddress from '../../../components/selectAddress';
import { CheckboxChangeEvent } from 'antd/es/checkbox';
import Indentities from '../../../components/identities';
import SingleSelectLazy from '../../../components/select/singleSelectLazy';
import { getDetailCustomer, getListCustomer } from '../../../service/contract';
import { Customer } from '../../../types/contract/depositContract';
import { usePurchaseContractStore } from '../purchaseContract/store';
import dayjs from 'dayjs';
import { DefaultOptionType } from 'antd/es/select';
import { TIdentities } from '../../../types/customers';
import { OptionTypeSelect } from '../../../types/common/common';

const { Item } = Form;
const { Title } = Typography;

interface CustomerFormProps {
  form?: FormInstance;
  disabledForm?: boolean;
  defaultCustomer?: OptionTypeSelect;
}

const CutomerForm: React.FC<CustomerFormProps> = ({ form: parentForm, disabledForm = false, defaultCustomer }) => {
  // Cột cho bảng nhân viên
  const [internalForm] = Form.useForm();
  const form = parentForm || internalForm;
  // const [yearOnly, setYearOnly] = useState(false);
  const cloneAddress = Form.useWatch(['info', 'cloneAddress'], form);
  const yearOnly = Form.useWatch(['info', 'onlyYear'], form);
  const address = Form.useWatch(['address'], form);
  const rootAddress = Form.useWatch(['rootAddress'], form);

  const { setInitialValue } = usePurchaseContractStore();

  const handleSelectCustomer = async (value: Customer) => {
    if (value) {
      form.setFieldsValue({
        customerSelect: form.getFieldValue({ id: value?.id, code: value.code }),
      });
      try {
        const response = await getDetailCustomer({ id: value?.id });
        const dataCustomer = response?.data?.data;
        // dataCustomer?.info?.birthdayYear ? setYearOnly(true) : setYearOnly(false);
        const initialData = {
          ...dataCustomer,
          customerSelect: { id: dataCustomer?.id, code: dataCustomer.code },
          bankInfo: Array.isArray(dataCustomer?.bankInfo) ? dataCustomer.bankInfo : [],
          info: {
            ...dataCustomer.info,
            birthday: dataCustomer?.info?.birthday ? dayjs(dataCustomer.info.birthday, FORMAT_DATE) : null,
            birthdayYear: dataCustomer?.info?.birthdayYear ? dayjs(dataCustomer.info.birthdayYear, 'YYYY') : null,
            onlyYear: dataCustomer?.info?.birthdayYear ? true : false,
            address: {
              ...dataCustomer?.info?.address,
            },
            rootAddress: {
              ...dataCustomer?.info?.rootAddress,
            },
          },
          personalInfo: {
            ...dataCustomer.personalInfo,
            income: dataCustomer?.personalInfo?.income ? dataCustomer?.personalInfo?.income?.toString() : undefined,
            identities:
              dataCustomer?.personalInfo?.identities?.length > 0
                ? dataCustomer?.personalInfo?.identities?.map((item: TIdentities) => {
                    const getIdentityByValue = (value: string): DefaultOptionType => {
                      return OPTIONS_IDENTITIES
                        ? OPTIONS_IDENTITIES.find(option => option.value === value) || { label: 'Unknown', value: '' }
                        : {};
                    };

                    const newTypeObject: DefaultOptionType = getIdentityByValue(item.type as string);
                    return {
                      ...item,
                      typeObject: newTypeObject,
                      date: item?.date ? dayjs(item?.date, FORMAT_DATE) : undefined,
                    };
                  })
                : [
                    {
                      type: undefined,
                      value: undefined,
                      date: undefined,
                      place: undefined,
                      typeObject: undefined,
                    },
                  ],
          },
          address: {
            ...dataCustomer?.info?.address,
          },
          rootAddress: {
            ...dataCustomer?.info?.rootAddress,
          },
        };
        form.setFieldsValue(initialData);
        setInitialValue(initialData);
      } catch (error) {
        console.error('Error fetching customer details:', error);
      }
    } else {
      const initialData = {
        customerSelect: { id: null, code: null },
        info: {
          birthday: null,
          birthdayYear: null,
          onlyYear: false,
        },
        personalInfo: {
          name: null,
          phone: null,
          email: null,
          identities: [
            {
              type: undefined,
              value: undefined,
              date: undefined,
              place: undefined,
              typeObject: undefined,
            },
          ],
        },
        address: null,
        rootAddress: null,
      };
      form.setFieldsValue(initialData);
      // setInitialValue(initialData);
    }
  };

  const handleCloneAddress = useCallback(
    (e: CheckboxChangeEvent) => {
      if (e.target.checked) {
        form.setFieldsValue({
          rootAddress: form.getFieldValue('address'),
        });
      }
    },
    [form],
  );
  return (
    <Row gutter={{ md: 24, lg: 40, xl: 80, xxl: 126 }}>
      <Col xs={24} md={24}>
        <Title level={5} style={{ marginBottom: 16 }}>
          Thông tin đồng sở hữu
        </Title>
        <Form.Item label="Mã khách hàng" name="customerSelect">
          <SingleSelectLazy
            queryKey={['get-list-customer-deposit']}
            apiQuery={getListCustomer}
            placeholder="Chọn mã khách hàng"
            keysLabel={['code', 'name']}
            handleSelect={handleSelectCustomer}
            disabled={disabledForm}
            defaultValues={defaultCustomer}
          />
        </Form.Item>
        <Row gutter={24}>
          <Col xs={24} sm={12}>
            <Item
              name={['personalInfo', 'name']}
              label="Họ và tên"
              rules={[{ required: true, message: 'Vui lòng nhập họ và tên' }]}
            >
              <Input
                maxLength={60}
                placeholder="Nhập họ và tên"
                onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                  e.target.value = e.target.value.toUpperCase(); // Chuyển đổi trực tiếp
                }}
                disabled={disabledForm}
              />
            </Item>
          </Col>

          <Col xs={24} sm={12}>
            <Item
              name={['personalInfo', 'phone']}
              label="Số điện thoại"
              rules={[
                {
                  required: true,
                  message: 'Vui lòng nhập số điện thoại',
                },
                {
                  pattern: REGEX_PHONE_VN,
                  message: 'Số điện thoại phải bắt đầu bằng số 0 và từ 9 đến 15 chữ số',
                },
              ]}
            >
              <Input maxLength={15} placeholder="Nhập số điện thoại" disabled={disabledForm} />
            </Item>
          </Col>
        </Row>

        <Item
          name={['info', 'gender']}
          label="Giới tính"
          className="item-gender"
          labelCol={{ span: 5 }}
          labelAlign="left"
          layout="horizontal"
          rules={[{ required: true, message: 'Vui lòng nhập giới tính' }]}
        >
          <Radio.Group options={OPTIONS_GENDER} disabled={disabledForm} />
        </Item>

        <Row gutter={24}>
          <Col xs={24} sm={12}>
            <Item
              name={['info', 'onlyYear']}
              label="Ngày sinh"
              layout="horizontal"
              labelCol={{ span: 10 }}
              labelAlign="left"
              valuePropName="checked"
            >
              <Checkbox disabled={disabledForm} style={{ marginLeft: '4px' }}>
                Chỉ năm sinh
              </Checkbox>
            </Item>
          </Col>
          <Col xs={24} sm={12} className="item-birthday">
            {yearOnly ? (
              <Item name={['info', 'birthdayYear']}>
                <DatePicker disabled={disabledForm} picker="year" format="YYYY" placeholder="YYYY" />
              </Item>
            ) : (
              <Item name={['info', 'birthday']}>
                <DatePicker disabled={disabledForm} format={FORMAT_DATE} placeholder={FORMAT_DATE} />
              </Item>
            )}
          </Col>
        </Row>
        <Row gutter={24}>
          <Col xs={24} sm={12}>
            <Item
              label="Địa chỉ email"
              name={['personalInfo', 'email']}
              rules={[{ type: 'email', message: 'Địa chỉ email sai định dạng' }]}
            >
              <Input maxLength={25} disabled={disabledForm} placeholder="Nhập địa chỉ email" />
            </Item>
          </Col>
          <Indentities form={form} disabledForm={disabledForm} showAddButton={false} />
        </Row>

        <Title level={5}>Địa chỉ thường trú</Title>
        <Item label="Địa chỉ" name={'address'}>
          <SelectAddress
            isDisable={disabledForm}
            placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
            parentName={'address'}
            address={address}
          />
        </Item>
        <Item name={['address', 'address']}>
          <Input disabled={disabledForm} placeholder="Địa chỉ cụ thể" />
        </Item>

        <Title level={5}>Địa chỉ liên lạc</Title>
        <Item name={['info', 'cloneAddress']} valuePropName="checked">
          <Checkbox disabled={disabledForm} onChange={handleCloneAddress}>
            Sử dụng địa chỉ thường trú
          </Checkbox>
        </Item>
        <Item label="Địa chỉ" name={'rootAddress'}>
          <SelectAddress
            isDisable={cloneAddress || disabledForm}
            placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
            parentName={'rootAddress'}
            address={rootAddress}
          />
        </Item>
        <Item name={['rootAddress', 'address']}>
          <Input placeholder="Địa chỉ cụ thể" disabled={cloneAddress || disabledForm} />
        </Item>
      </Col>
    </Row>
  );
};

export default CutomerForm;
