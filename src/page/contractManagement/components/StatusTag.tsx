import { Tag } from 'antd';
import { CONTRACT_STATUS_MAP } from '../../../types/contract/depositContract';

interface StatusTagProps {
  status?: string;
}

const StatusTag: React.FC<StatusTagProps> = ({ status }) => {
  const statusItem = CONTRACT_STATUS_MAP.find(item => item.status === status);
  if (!statusItem) {
    return <Tag color="default">Không xác định</Tag>;
  }
  const { label, tagColor } = statusItem;
  return <Tag color={tagColor}>{label}</Tag>;
};

export default StatusTag;
