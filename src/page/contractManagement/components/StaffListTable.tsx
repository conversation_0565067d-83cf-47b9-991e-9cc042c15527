import { Button, Form, FormInstance, Input, InputNumber, Table } from 'antd';
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { useEffect, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';
import SingleSelectLazy from '../../../components/select/singleSelectLazy';
import { Employee, PrimaryTransaction, StaffItem, TPos } from '../../../types/contract/depositContract';
import { getListEmployeeByOrgchartId } from '../../../service/contract';

interface StaffListTableProps {
  initialStaffList?: StaffItem[];
  isPrimaryTransaction: boolean;
  detailDataTicket?: PrimaryTransaction;
  pos: TPos;
  form: FormInstance;
}

const StaffListTable = ({
  initialStaffList = [],
  isPrimaryTransaction,
  detailDataTicket,
  pos,
  form,
}: StaffListTableProps) => {
  const [staffList, setStaffList] = useState<StaffItem[]>(initialStaffList);

  // Sync initialStaffList with component state and form when it changes
  useEffect(() => {
    if (initialStaffList && initialStaffList.length > 0) {
      const formattedStaffList = initialStaffList.map(item => ({
        ...item,
        key: item.key || uuidv4(),
      }));
      setStaffList(formattedStaffList);

      // Update form values
      const staffFormValues = formattedStaffList.reduce(
        (acc, item) => ({
          ...acc,
          [item.key]: {
            employee: item.id,
            percent: item.percent,
          },
        }),
        {},
      );
      form.setFieldsValue({ staff: staffFormValues });
    }
  }, [initialStaffList, form]);

  // Add new staff
  const addStaff = () => {
    const newStaff: StaffItem = {
      key: uuidv4(),
      percent: undefined,
    };
    setStaffList(prev => [...prev, newStaff]);
  };

  // Remove staff
  const removeStaff = (key: string) => {
    setStaffList(prev => prev.filter(item => item.key !== key));
  };

  // Handle employee selection
  const handleSelectEmployee = (value: Employee, key: string) => {
    if (!value) return;

    setStaffList(prev =>
      prev.map(item =>
        item.key === key
          ? {
              ...item,
              id: value.id,
              code: value.code,
              name: value.name,
            }
          : item,
      ),
    );

    form.setFieldsValue({
      staff: {
        [key]: {
          employee: value.id,
          percent: form.getFieldValue(['staff', key, 'percent']) || undefined,
        },
      },
    });
    form.validateFields([['staff', key, 'employee']]).catch(() => {});
  };

  // Handle percent change
  const handlePercentChange = (value: number | null, key: string) => {
    setStaffList(prev => prev.map(item => (item.key === key ? { ...item, percent: value || 0 } : item)));
    form.setFieldsValue({
      staff: {
        [key]: {
          employee: form.getFieldValue(['staff', key, 'employee']) || undefined,
          percent: value || 0,
        },
      },
    });
    form.validateFields([['staff', key, 'percent']]).catch(() => {});
  };

  const staffColumns = [
    {
      title: 'Nhân viên',
      dataIndex: 'employee',
      key: 'employee',
      width: '60%',
      render: (_: string, record: StaffItem) => (
        <Form.Item
          style={{ margin: 0 }}
          name={['staff', record.key, 'employee']}
          rules={[{ required: true, message: 'Vui lòng chọn nhân viên' }]}
        >
          <SingleSelectLazy
            apiQuery={getListEmployeeByOrgchartId}
            queryKey={['employee-dropdown']}
            keysLabel={['name', 'email']}
            placeholder="Chọn nhân viên"
            handleSelect={(value: Employee) => handleSelectEmployee(value, record.key)}
            defaultValues={record.id ? { value: record.id, label: record.name || record.id } : undefined}
            moreParams={{ projectId: detailDataTicket?.project?.id, id: pos?.id || '' }}
          />
        </Form.Item>
      ),
    },
    {
      title: 'Tỉ lệ tham gia (%)',
      dataIndex: 'percent',
      key: 'percent',
      width: '30%',
      render: (_: string, record: StaffItem) => (
        <Form.Item
          style={{ margin: 0 }}
          name={['staff', record.key, 'percent']}
          rules={[{ required: true, message: 'Vui lòng nhập tỉ lệ' }]}
        >
          <InputNumber
            placeholder="Nhập tỉ lệ (%)"
            min={0}
            max={100}
            step={0.01}
            precision={2}
            formatter={(value: number | undefined) => (value !== undefined ? `${value}` : '')}
            value={record.percent}
            onChange={value => handlePercentChange(value, record.key)}
            style={{ width: '100%' }}
            maxLength={5}
          />
        </Form.Item>
      ),
    },
    {
      title: '',
      key: 'action',
      width: '10%',
      render: (_: string, record: StaffItem) => (
        <Button type="text" danger icon={<DeleteOutlined />} onClick={() => removeStaff(record.key)} />
      ),
    },
  ];

  return (
    <>
      <Form.Item name="staffsInvolved" hidden>
        <Input />
      </Form.Item>
      <Table
        columns={staffColumns}
        dataSource={staffList}
        pagination={false}
        rowKey="key"
        sticky={{ offsetHeader: 0 }}
        summary={() => (
          <Table.Summary fixed="top">
            <Table.Summary.Row>
              <Table.Summary.Cell className="custom-summary-cell" index={0} colSpan={staffColumns.length}>
                <Button disabled={!isPrimaryTransaction} type="default" icon={<PlusOutlined />} onClick={addStaff}>
                  Thêm nhân viên
                </Button>
              </Table.Summary.Cell>
            </Table.Summary.Row>
          </Table.Summary>
        )}
      />
    </>
  );
};

export default StaffListTable;
