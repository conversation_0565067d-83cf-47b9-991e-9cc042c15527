import React from 'react';
import { Form, Input, Row, Col, FormInstance, DatePicker, Typography, Select } from 'antd';
import { FORMAT_DATE, REGEX_PHONE_VN } from '../../../constants/common';
import { useFetch } from '../../../hooks';
import { Bank } from '../../../types/bookingRequest';
import { getBanks } from '../../../service/bank';

const { Item } = Form;

interface CompanyInformationFormProps {
  form?: FormInstance;
}

const CompanyInformationForm: React.FC<CompanyInformationFormProps> = ({ form: parentForm }) => {
  // Cột cho bảng nhân viên
  const [internalForm] = Form.useForm();
  const form = parentForm || internalForm;
  console.log(form);
  const { Title } = Typography;
  const { data: dataBanks, isLoading } = useFetch<Bank[]>({
    queryKeyArrWithFilter: ['get-list-banks'],
    api: getBanks,
    moreParams: { branchIsActvie: true },
  });
  const listBank = dataBanks?.data?.data || [];
  return (
    <>
      <Title level={5} style={{ marginBottom: 16 }}>
        Thông tin công ty
      </Title>
      <Row gutter={24}>
        <Col xs={24} sm={12}>
          <Item name={['companyInformation', 'documentNo']} label="Giấy chứng nhận ĐKDN số">
            <Input placeholder="Nhập giấy chứng nhận ĐKDN " />
          </Item>
        </Col>
        <Col xs={24} sm={12}>
          <Item name={['companyInformation', 'dateOfIssue']} label="Ngày cấp">
            <DatePicker format={FORMAT_DATE} placeholder={FORMAT_DATE} />
          </Item>
        </Col>
        <Col xs={24} sm={24}>
          <Item name={['companyInformation', 'authority']} label="Cơ quan cấp">
            <Input placeholder="Nhập cơ quan cấp" />
          </Item>
        </Col>

        {/* <Col xs={24} sm={24}>
          <Item name={['companyInformation', 'companyAddress']} label="Địa chỉ">
            <Input placeholder="Nhập Địa chỉ" />
          </Item>
        </Col> */}
        <Col xs={24} sm={12}>
          <Item
            name={['companyInformation', 'companyEmail']}
            label="Email"
            rules={[{ type: 'email', message: 'Địa chỉ email sai định dạng' }]}
          >
            <Input placeholder="Nhập Email" />
          </Item>
        </Col>
        <Col xs={24} sm={12}>
          <Item
            name={['companyInformation', 'companyTel']}
            label="Số điện thoại"
            rules={[
              {
                pattern: REGEX_PHONE_VN,
                message: 'Số điện thoại phải bắt đầu bằng số 0 và từ 9 đến 15 chữ số',
              },
            ]}
          >
            <Input maxLength={15} placeholder="Nhập số điện thoại" />
          </Item>
        </Col>
        <Col xs={24} sm={12}>
          <Item name={['companyInformation', 'companyBank']} label="Ngân hàng">
            <Select
              placeholder="Chọn ngân hàng"
              allowClear
              filterOption={(input, option) =>
                typeof option?.label === 'string' ? option.label.toLowerCase().includes(input.toLowerCase()) : false
              }
              showSearch
              loading={isLoading}
              options={listBank.map(item => ({
                value: item.bankCode,
                label: item.bankName,
              }))}
            />
          </Item>
        </Col>
        <Col xs={24} sm={12}>
          <Item name={['companyInformation', 'companyAccountNumber']} label="Số tài khoản ngân hàng">
            <Input placeholder="Nhập số tài khoản" />
          </Item>
        </Col>

        <Col xs={24} sm={12}>
          <Item name={['companyInformation', 'representative']} label="Đại diện">
            <Input placeholder="Nhập đại diện" />
          </Item>
        </Col>
        <Col xs={24} sm={12}>
          <Item name={['companyInformation', 'representativePosition']} label="Chức vụ">
            <Input placeholder="Nhập chức vụ" />
          </Item>
        </Col>
      </Row>
    </>
  );
};

export default CompanyInformationForm;
