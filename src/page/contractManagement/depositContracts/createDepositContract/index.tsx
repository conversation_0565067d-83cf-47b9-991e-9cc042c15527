import { Form, Modal } from 'antd';
import { useCallback, useState } from 'react';
import ModalComponent from '../../../../components/modal';
import DeopsitContractForm from '../components/DepositContractForm';
import { useCreateField } from '../../../../hooks';
import { createDepositContract } from '../../../../service/contract';
import { TDepositContractPayload } from '../../../../types/contract/depositContract';

interface CreateDepositContractModalProps {
  visible: boolean;
  onClose: () => void;
}

const CreateDepositContractModal = ({ visible, onClose }: CreateDepositContractModalProps) => {
  const [form] = Form.useForm();
  const [resetUpload, setResetUpload] = useState<boolean>(false);

  const { mutateAsync: create, isPending } = useCreateField({
    keyOfListQuery: ['list-deposit-contract'],
    apiQuery: createDepositContract,
    isMessageError: false,
    messageSuccess: 'Tạo mới hợp đồng cọc thành công!',
  });

  const resetFormDepositContract = useCallback(async () => {
    await form.resetFields();
    setResetUpload(true);
  }, [form]);

  const handleCancel = useCallback(async () => {
    if (form.isFieldsTouched()) {
      Modal.confirm({
        title: 'Xác nhận hủy',
        content: 'Dữ liệu chưa được lưu, bạn có chắc chắn muốn thoát khỏi trang không?',
        cancelText: 'Quay lại',
        onOk: async () => {
          await resetFormDepositContract();
          onClose();
        },
        okButtonProps: { type: 'default' },
        cancelButtonProps: { type: 'primary' },
      });
    } else {
      await resetFormDepositContract();
      onClose();
    }
  }, [form, onClose, resetFormDepositContract]);

  const handleCreate = useCallback(
    async (values: TDepositContractPayload) => {
      try {
        const resp = await create(values);
        if (resp?.data?.statusCode === '0' && resp?.data?.success) {
          await resetFormDepositContract();
          onClose();
        }
      } catch (error) {
        console.error('Create error:', error);
      }
    },
    [create, onClose, resetFormDepositContract],
  );

  return (
    <ModalComponent title="Tạo mới hợp đồng cọc" open={visible} onCancel={handleCancel} footer={null} destroyOnClose>
      <DeopsitContractForm
        form={form}
        onFinish={handleCreate}
        resetUpload={resetUpload}
        setResetUpload={setResetUpload}
        loading={isPending}
      />
    </ModalComponent>
  );
};

export default CreateDepositContractModal;
