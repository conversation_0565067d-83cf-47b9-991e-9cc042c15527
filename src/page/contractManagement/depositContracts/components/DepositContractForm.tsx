import React, { useEffect, useState } from 'react';
import {
  Form,
  Input,
  Button,
  Row,
  Col,
  Typography,
  FormInstance,
  Select,
  Spin,
  DatePicker,
  Checkbox,
  InputNumber,
  Radio,
  RadioChangeEvent,
} from 'antd';
import SingleSelectLazy from '../../../../components/select/singleSelectLazy';
import { BookingTicket } from '../../../../types/bookingRequest';
import { formatNumber } from '../../../../utilities/regex';
import dayjs from 'dayjs';
import { TPos, TSalePolicy } from '../../../../types/commission';
import CommonFileUpload from '../../../../components/upload/CommonUploadFiles';
import {
  ExtendedUploadFile,
  TDepositContractPayload,
  TDepositContractForm,
  PrimaryTransaction,
  StaffItem,
} from '../../../../types/contract/depositContract';
import {
  getListDiscountPolicy,
  getListPaymentPolicy,
  getListSalesPolicy,
  getListYCDCTicket,
  getDetailYCDCTicket,
  getListBusinessArea,
  getListDistributionChannel,
  getDivision,
} from '../../../../service/contract';
import { IPaymentPolicy } from '../../../../types/paymentPolicy';
import MultiSelectLazy from '../../../../components/select/mutilSelectLazy';
import { DiscountPolicy } from '../../../../types/discountPolicy';
import './styles.scss';
import { OptionTypeSelect } from '../../../../types/common/common';
import { Space } from 'antd/lib';
import CustomerForm from '../../components/CustomerForm';
import CompanyInformationForm from '../../components/CompanyInformationForm';
import { FORMAT_DATE, FORMAT_DATE_API } from '../../../../constants/common';
import { calculateProductPrice } from '../../utils/calculateProductPrice';
import StaffListTable from '../../components/StaffListTable';
import { LISTPAYMENTS } from '../../../../constants/contract';
import { validateEndDate } from '../../../../utilities/shareFunc';
import LoanInfoForm from '../../components/LoanInfoForm';

const { Title } = Typography;
const { Option } = Select;
interface DeopsitContractFromProps {
  form?: FormInstance;
  onFinish?: (values: TDepositContractPayload) => void;
  resetUpload?: boolean;
  setResetUpload?: (value: boolean) => void;
  loading: boolean;
  preSelectedYCDCId?: string;
}

const DeopsitContractFrom: React.FC<DeopsitContractFromProps> = ({
  form: parentForm,
  onFinish,
  setResetUpload,
  resetUpload,
  loading,
  preSelectedYCDCId,
}) => {
  const [internalForm] = Form.useForm();
  const form = parentForm || internalForm;
  const isShowOwned = Form.useWatch('isShowOwned', form);
  const isShowCompanyInformation = Form.useWatch('isShowCompanyInformation', form);

  const [fileList, setFileList] = useState<ExtendedUploadFile[]>([]);
  const [installments, setInstallments] = useState<{ label: string; value: string }[]>([]);
  const [staffList, setStaffList] = useState<StaffItem[]>([]);
  const [detailDataTicket, setDetailDataTicket] = useState<PrimaryTransaction>({});
  const [pos, setPos] = useState<TPos>({ id: '', code: '', name: '' });
  const [maintenanceFeeType, setMaintenanceFeeType] = useState<string>('percent'); // Loại chiết khấu
  const [isPrimaryTransaction, setIsPrimaryTransaction] = useState<boolean>(false);
  const [defaultDiscountPolicy, setDefaultDiscountPolicy] = useState([]);
  const [defaultPaymentPolicy, setDefaultPaymentPolicy] = useState<OptionTypeSelect>({
    value: undefined,
    label: undefined,
  });
  const [defaultSalePolicy, setDefaultSalePolicy] = useState({ value: undefined, label: undefined });
  const [defaultYCDCValue, setDefaultYCDCValue] = useState<
    | {
        label: string;
        value: string;
      }
    | undefined
  >(undefined);
  const loanType = Form.useWatch('loanType', form);

  const handleMaintenanceFeeTypeChange = (value: string) => {
    setMaintenanceFeeType(value);
    form.setFieldsValue({ maintenanceFeeValue: 0 }); // Đặt maintenanceFeeValue về 0
  };

  const handlePriceTypeChange = (e: RadioChangeEvent) => {
    const priceType = e.target.value;
    const calculatedPrice = calculateProductPrice(detailDataTicket, priceType);
    form.setFieldsValue({
      productPrice: calculatedPrice,
    });
  };

  useEffect(() => {
    if (resetUpload) {
      setFileList([]);
      setResetUpload?.(false);
    }
  }, [resetUpload, setResetUpload]);

  const handleSelectSalePolicy = (value: TSalePolicy) => {
    form.setFieldsValue({ salesPolicy: value });
  };

  const handleSelectSDiscountPolicy = (values: DiscountPolicy[]) => {
    form.setFieldsValue({ policyDiscountIds: values?.map(item => item.id) });
  };

  const handleSelectPaymentPolicy = (value: IPaymentPolicy) => {
    const options = value?.schedule?.installments.map(item => ({
      label: item.name,
      value: item.name,
    }));
    setInstallments(options);
    form.setFieldsValue({ policyPaymentId: value?.id, stagePayment: null });
  };

  const handleSelectYCDCTicket = async (value: BookingTicket) => {
    setInstallments([]);
    setStaffList([]);

    if (!value) {
      setIsPrimaryTransaction(false);
      setDetailDataTicket({});
      setPos({ id: '', code: '', name: '' });
      setDefaultDiscountPolicy([]);
      setDefaultPaymentPolicy({ value: undefined, label: undefined });
      setDefaultSalePolicy({ value: undefined, label: undefined });
      setDefaultYCDCValue(undefined);

      form.resetFields([
        'primaryTransactionId',
        'policyPaymentId',
        'policyDiscountIds',
        'salesPolicy',
        'projectName',
        'propertyUnitCode',
        'price',
        'priceVat',
        'landPrice',
        'productPrice',
        'landPriceVat',
        'housePrice',
        'housePriceVat',
        'customerName',
        'posName',
        'contractPriceForMaintenanceFee',
        'contractPrice',
      ]);

      form.setFieldsValue({
        primaryTransactionId: undefined,
        bankName: undefined,
        bankNumber: undefined,
        stagePayment: null,
        policyPaymentId: undefined,
        policyDiscountIds: undefined,
        salesPolicy: undefined,
      });

      return;
    }

    setIsPrimaryTransaction(true);
    setDefaultDiscountPolicy([]);
    setDefaultPaymentPolicy({ value: undefined, label: undefined });
    setDefaultSalePolicy({ value: undefined, label: undefined });

    try {
      const response = await getDetailYCDCTicket({ id: value.id });
      const detailDataTicket = response?.data?.data;
      setDetailDataTicket(detailDataTicket);
      setPos(detailDataTicket?.pos);
      const calculatedPrice = calculateProductPrice(detailDataTicket, form.getFieldValue('priceType') || 'vat');

      form.setFieldsValue({
        primaryTransactionId: value.id,
        policyPaymentId: undefined,
        policyDiscountIds: undefined,
        projectName: detailDataTicket?.project?.name || '',
        propertyUnitCode: detailDataTicket?.propertyUnit?.code || '',
        price: detailDataTicket?.propertyUnit?.price || 0,
        priceVat: detailDataTicket?.propertyUnit?.priceVat || 0,
        landPrice: detailDataTicket?.propertyUnit?.landPrice || 0,
        productPrice: calculatedPrice,
        landPriceVat: detailDataTicket?.propertyUnit?.landPriceVat || 0,
        housePrice: detailDataTicket?.propertyUnit?.housePrice || 0,
        housePriceVat: detailDataTicket?.propertyUnit?.housePriceVat || 0,
        customerName: detailDataTicket?.customer?.personalInfo?.name || '',
        posName: detailDataTicket?.pos?.name || '',
        contractPriceForMaintenanceFee: detailDataTicket?.propertyUnit?.contractPriceForMaintenanceFee || 0,
        contractPrice: detailDataTicket?.propertyUnit?.contractPrice || 0,
        stagePayment: null,
      });
    } catch (error) {
      console.error('Error fetching YCDC ticket details:', error);
    }
  };

  useEffect(() => {
    if (preSelectedYCDCId) {
      const loadYCDCDetail = async () => {
        try {
          const response = await getDetailYCDCTicket({ id: preSelectedYCDCId });
          const ticketData = response?.data?.data;

          if (ticketData) {
            setDefaultYCDCValue({
              label: ticketData.escrowTicketCode,
              value: preSelectedYCDCId,
            });

            await handleSelectYCDCTicket({
              id: preSelectedYCDCId,
              escrowTicketCode: ticketData.escrowTicketCode,
            });
          }
        } catch (error) {
          console.error('Error loading pre-selected YCDC:', error);
        }
      };

      loadYCDCDetail();
    }
  }, [preSelectedYCDCId]);

  const handleFinish = (values: TDepositContractForm) => {
    let calcPriceVat = false;
    let calcContractPrice = false;
    if (values?.priceType === 'contract') {
      calcContractPrice = true;
    } else {
      calcPriceVat = values?.priceType === 'vat';
    }
    const payload: TDepositContractPayload = {
      files: fileList,
      primaryTransactionId: values?.primaryTransactionId || '',
      policyPaymentId: values?.policyPaymentId || '',
      policyDiscountIds: values?.policyDiscountIds || [],
      calcCurrencyFirst: values?.calcCurrencyFirst || false,
      calcPriceVat: calcPriceVat,
      calcContractPrice: calcContractPrice,
      maintenanceFee: {
        ...values?.maintenanceFee,
        type: values?.maintenanceFeeType || '',
        value: values?.maintenanceFeeValue || 0,
      },
      type: 'deposit',
      startDate: values?.startDate ? dayjs(values?.startDate).format(FORMAT_DATE_API) : '',
      expiredDate: values?.expiredDate ? dayjs(values?.expiredDate).format(FORMAT_DATE_API) : '',
      signedDate: values?.signedDate,
      transferType: values?.transferType,
      customer2: {
        id: values?.customerSelect?.id,
        code: values?.customerSelect?.code,
        name: values?.personalInfo?.name,
        phone: values?.personalInfo?.phone,
        email: values?.personalInfo?.email,
        info: {
          ...values?.info,
          rootAddress: values?.rootAddress,
          address: values?.address,
          birthdayYear:
            typeof values?.info?.birthdayYear !== 'string' && values?.info?.birthdayYear
              ? values?.info?.birthdayYear?.format('YYYY')
              : null,
          birthday:
            typeof values?.info?.birthday !== 'string' && !values?.info?.birthdayYear
              ? values?.info?.birthday?.format(FORMAT_DATE)
              : null,
        },
        personalInfo: {
          ...values?.personalInfo,
          identities: values?.personalInfo?.identities?.map(item => ({
            ...item,
            type: typeof item?.typeObject !== 'string' ? item?.typeObject?.value : item?.type,
            date: typeof item?.date !== 'string' ? item?.date?.format(FORMAT_DATE) : null,
          })),
        },
      },
      companyInformation: {
        ...values?.companyInformation,
        dateOfIssue:
          typeof values?.companyInformation?.dateOfIssue !== 'string'
            ? values?.companyInformation?.dateOfIssue?.format(FORMAT_DATE)
            : null,
      },
      isDebtRemind: values?.isDebtRemind,
      changeInstallment: values?.changeInstallment,
      releaseStartDate: values?.releaseStartDate ? dayjs(values?.releaseStartDate).format(FORMAT_DATE_API) : '',
      releaseEndDate: values?.releaseEndDate ? dayjs(values?.releaseEndDate).format(FORMAT_DATE_API) : '',
      salesPolicy: {
        id: values?.salesPolicy?.id || '',
        name: values?.salesPolicy?.name || '',
        code: values?.salesPolicy?.code || '',
      },
      staffsInvolved: staffList
        .filter(item => item.id && item.percent !== undefined)
        .map(item => ({
          id: item.id as string,
          code: item.code || '',
          name: item.name || '',
          percent: item.percent || 0,
        })),
      businessArea: {
        id: values?.businessArea?.id || '',
        name: values?.businessArea?.nameVN || '',
        code: values?.businessArea?.code || '',
      },
      distributionChannel: {
        id: values?.distributionChannel?.id || '',
        name: values?.distributionChannel?.name || '',
        code: values?.distributionChannel?.code || '',
      },
      productCategory: {
        id: values?.productCategory?.id || '',
        name: values?.productCategory?.name || '',
        code: values?.productCategory?.code || '',
      },
      loanBankInfo: values?.loanBankInfo,
      loanType: values?.loanType,
      loanTermYear: values?.loanTermYear,
      loanAmount: values?.loanAmount,
    };
    console.log(payload);
    onFinish?.(payload);
  };

  return (
    <Spin spinning={loading}>
      <Form
        form={form}
        initialValues={{
          signedDate: dayjs(),
          maintenanceFeeType: 'percent',
          priceType: 'vat',
          transferType: 'CASH',
        }}
        onFinish={handleFinish}
        layout="vertical"
      >
        <Row gutter={64}>
          <Col span={12}>
            <Title level={5} style={{ marginBottom: 16 }}>
              Thông tin hợp đồng
            </Title>
            <Row gutter={24}>
              <Col span={14}>
                <Form.Item label="Tên hợp đồng">
                  <Input placeholder="Tên hợp đồng" disabled />
                </Form.Item>
              </Col>
              <Col span={10}>
                <Form.Item label="Mã hợp đồng">
                  <Input placeholder="Mã hợp đồng" disabled />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={14}>
                <Form.Item
                  label="Mã YCĐCO"
                  name="primaryTransactionId"
                  rules={[{ required: true, message: 'Vui lòng chọn mã YCĐCO' }]}
                >
                  <SingleSelectLazy
                    queryKey={['get-booking-tickets']}
                    apiQuery={getListYCDCTicket}
                    placeholder="Chọn mã YCĐCO"
                    keysLabel={'escrowTicketCode'}
                    handleSelect={handleSelectYCDCTicket}
                    moreParams={{
                      ticketType: 'YCDC',
                      _fields: 'id,ticketType,bookingTicketCode,escrowTicketCode,reciept,contract',
                    }}
                    defaultValues={defaultYCDCValue}
                  />
                </Form.Item>
              </Col>
              <Col span={10}>
                <Form.Item name="sapCode" label="Mã hợp đồng SAP">
                  <Input placeholder="Mã hợp đồng SAP" disabled />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  label="Số hợp đồng giấy"
                  name="poNumber"
                  rules={[{ required: true, message: 'Vui lòng nhập số hợp đồng giấy' }]}
                >
                  <Input placeholder="Số hợp đồng giấy" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Mã công ty/ chi nhánh"
                  name="businessArea"
                  rules={[{ required: true, message: 'Vui lòng chọn mã công ty/ chi nhánh' }]}
                >
                  <SingleSelectLazy
                    queryKey={['get-business-area']}
                    apiQuery={getListBusinessArea}
                    placeholder="Chọn mã công ty/ chi nhánh"
                    keysLabel={['code', 'nameVN']}
                    handleSelect={value => {
                      form.setFieldsValue({ businessArea: value });
                    }}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  label="Kênh phân phối"
                  name="distributionChannel"
                  rules={[{ required: true, message: 'Vui lòng chọn kênh phân phối' }]}
                >
                  <SingleSelectLazy
                    queryKey={['get-distribution-channel']}
                    apiQuery={getListDistributionChannel}
                    placeholder="Chọn kênh phân phối"
                    keysLabel={['name']}
                    handleSelect={value => {
                      form.setFieldsValue({ distributionChannel: value });
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Ngành hàng"
                  name="productCategory"
                  rules={[{ required: true, message: 'Vui lòng chọn ngành hàng' }]}
                >
                  <SingleSelectLazy
                    queryKey={['get-division']}
                    apiQuery={getDivision}
                    placeholder="Chọn ngành hàng"
                    keysLabel={['name']}
                    handleSelect={value => {
                      form.setFieldsValue({ productCategory: value });
                    }}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={24}>
                <Form.Item label="Tên khách hàng" name="customerName">
                  <Input placeholder="Tên khách hàng" disabled />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  label="Ngày hiệu lực"
                  name="startDate"
                  rules={[{ required: true, message: 'Vui lòng chọn ngày hiệu lực' }]}
                >
                  <DatePicker placeholder="Chọn ngày hiệu lực" format="DD/MM/YYYY" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Ngày kết thúc hiệu lực"
                  name="expiredDate"
                  dependencies={['startDate']}
                  rules={[
                    {
                      validator: validateEndDate(
                        form,
                        'startDate',
                        'Ngày kết thúc hiệu lực lớn hơn hoặc bằng ngày hiệu lực',
                      ),
                    },
                  ]}
                >
                  <DatePicker placeholder="Chọn ngày kết thúc hiệu lực" format="DD/MM/YYYY" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={12}>
                <Form.Item label="Ngày phát hành" name="releaseStartDate">
                  <DatePicker placeholder="Chọn ngày phát hành" format="DD/MM/YYYY" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Ngày kết thúc"
                  name="releaseEndDate"
                  dependencies={['releaseStartDate']}
                  rules={[
                    {
                      validator: validateEndDate(form, 'startDate', 'Ngày kết thúc lớn hơn hoặc bằng ngày phát hành'),
                    },
                  ]}
                >
                  <DatePicker placeholder="Chọn ngày kết thúc" format="DD/MM/YYYY" />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              label="Ngày ký kết"
              name="signedDate"
              rules={[{ required: true, message: 'Vui lòng chọn ngày ký kết' }]}
            >
              <DatePicker placeholder="Chọn ngày ký kết" format="DD/MM/YYYY" />
            </Form.Item>

            <Form.Item label="Hình thức thanh toán" name="transferType">
              <Select placeholder="Chọn hình thức thanh toán" allowClear options={LISTPAYMENTS} />
            </Form.Item>

            <Form.Item name="isDebtRemind" valuePropName="checked">
              <Checkbox>Nhắc nợ</Checkbox>
            </Form.Item>
            <Title level={5} style={{ marginBottom: 16 }}>
              Thông tin đồng sở hữu
            </Title>
            <Form.Item name="isShowOwned" valuePropName="checked">
              <Checkbox>Thêm thông tin đồng sở hữu</Checkbox>
            </Form.Item>
            {isShowOwned && <CustomerForm form={form} />}

            <Title level={5} style={{ marginBottom: 16 }}>
              Thông tin công ty
            </Title>

            <Form.Item name="isShowCompanyInformation" valuePropName="checked">
              <Checkbox>Thêm thông tin công ty</Checkbox>
            </Form.Item>
            {isShowCompanyInformation && <CompanyInformationForm form={form} />}
            <Title level={5} style={{ marginBottom: 16 }}>
              Thông tin bán hàng
            </Title>
            <Form.Item label="Đơn vị bán hàng" name="posName">
              <Input placeholder="Đơn vị sở hữu" disabled />
            </Form.Item>

            <Form.Item
              label="Chính sách phí - hoa hồng"
              required
              name="salesPolicy"
              rules={[{ required: true, message: 'Vui lòng chọn chính sách phí - hoa hồng' }]}
            >
              <SingleSelectLazy
                apiQuery={getListSalesPolicy}
                queryKey={['sales-policy']}
                keysLabel={['code', 'name']}
                placeholder="Chọn chính sách phí - hoa hồng"
                handleSelect={handleSelectSalePolicy}
                moreParams={{ projectID: detailDataTicket?.project?.id, isActive: true }}
                disabled={!isPrimaryTransaction}
                defaultValues={defaultSalePolicy}
              />
            </Form.Item>

            <Title level={5} style={{ marginBottom: 16 }}>
              Thông tin đính kèm
            </Title>

            <Form.Item name="files">
              <CommonFileUpload
                fileList={fileList}
                setFileList={setFileList}
                uploadPath={'contract-deposit'}
                // allowedMimeTypes={allowedMimeTypes}
                // allowedExtensions={ALLOWED_EXTENSIONS}
                maxFileSize={10}
                maxTotalSize={1}
                maxFiles={10}
                buttonText="Upload"
                buttonWidth="100px"
              />
            </Form.Item>
            <Title level={5}>Danh sách nhân viên tham gia bán hàng</Title>
            <Row gutter={{ md: 24, lg: 40 }}>
              <Col xs={24} md={24}>
                <StaffListTable
                  staffList={staffList}
                  onStaffListChange={setStaffList}
                  isPrimaryTransaction={isPrimaryTransaction}
                  detailDataTicket={detailDataTicket}
                  pos={pos}
                  form={form}
                />
              </Col>
            </Row>
            <Title level={5} style={{ marginBottom: 16, marginTop: 16 }}>
              Thông tin vay
            </Title>
            <Form.Item style={{ marginBottom: 16, display: 'flex', alignItems: 'center' }}>
              <span style={{ minWidth: 120 }}>Vay ngân hàng</span>
              <Form.Item name="loanType" noStyle>
                <Radio.Group style={{ marginLeft: 16 }}>
                  <Radio value="yes">Có</Radio>
                  <Radio value="no">Không</Radio>
                </Radio.Group>
              </Form.Item>
            </Form.Item>
            {loanType === 'yes' && <LoanInfoForm form={form} />}
          </Col>
          <Col span={12}>
            <Title level={5} style={{ marginBottom: 16 }}>
              Thông tin dự án
            </Title>
            <Row gutter={24}>
              <Col span={14}>
                <Form.Item label="Tên dự án" name="projectName">
                  <Input placeholder="Tên dự án" disabled />
                </Form.Item>
              </Col>
              <Col span={10}>
                <Form.Item label="Mã sản phẩm" name="propertyUnitCode">
                  <Input placeholder="Mã sản phẩm" disabled />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={24}>
                <Form.Item label="Giá trị sản phẩm trên hợp đồng" name="productPrice">
                  <InputNumber placeholder="Giá sản phẩm" disabled formatter={formatNumber} />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={14}>
                <Form.Item label="Giá bán chưa VAT" name="price">
                  <InputNumber placeholder="Giá bán chưa VAT" disabled formatter={formatNumber} />
                </Form.Item>
              </Col>
              <Col span={10}>
                <Form.Item label="Giá bán có VAT" name="priceVat">
                  <InputNumber placeholder="Giá bán có VAT" disabled formatter={formatNumber} />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={14}>
                <Form.Item label="Giá nhà chưa VAT" name="housePrice">
                  <InputNumber placeholder="Giá nhà chưa VAT" disabled formatter={formatNumber} />
                </Form.Item>
              </Col>
              <Col span={10}>
                <Form.Item label="Giá nhà có VAT" name="housePriceVat">
                  <InputNumber placeholder="Giá nhà có VAT" disabled formatter={formatNumber} />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={14}>
                <Form.Item label="Giá đất chưa VAT" name="landPrice">
                  <InputNumber placeholder="Giá đất chưa VAT" disabled formatter={formatNumber} />
                </Form.Item>
              </Col>
              <Col span={10}>
                <Form.Item label="Giá đất có VAT" name="landPriceVat">
                  <InputNumber placeholder="Giá đất có VAT" disabled formatter={formatNumber} />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={24}>
                <Form.Item label="Tổng giá trị CH sau CK gồm VAT, chưa bao gồm PBT" name="contractPrice">
                  <Input placeholder="Giá trị CH sau CK gồm VAT, chưa bao gồm PBT" disabled />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={24}>
                <Form.Item label="Giá trị CH tính phí bảo trì" name="contractPriceForMaintenanceFee">
                  <Input placeholder="Giá trị CH tính phí bảo trì" disabled />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={24}>
                <Form.Item label="Giá trị chiết khấu" name="discountValue">
                  <Input placeholder="Giá trị chiết khấu" disabled />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={24}>
                <Form.Item label="Giá ban hành" name="issuedPrice">
                  <Input placeholder="Giá ban hành" disabled />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={24}>
                <div style={{ display: 'flex', marginBottom: 16 }}>
                  <div style={{ width: 120 }}>Tính giá trị:</div>
                  <Form.Item name="priceType" style={{ marginBottom: 0 }}>
                    <Radio.Group onChange={handlePriceTypeChange}>
                      <Space direction="vertical">
                        <Radio value="vat">Giá có VAT</Radio>
                        <Radio value="non-vat">Giá không có VAT</Radio>
                        <Radio
                          disabled={
                            form.getFieldValue('contractPrice') ? form.getFieldValue('contractPrice') <= 0 : true
                          }
                          value="contract"
                        >
                          Tổng giá trị CH sau CK gồm VAT, chưa gồm PBT
                        </Radio>
                      </Space>
                    </Radio.Group>
                  </Form.Item>
                </div>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={24}>
                <Form.Item name="changeInstallment" valuePropName="checked">
                  <Checkbox>Không cho phép thay đổi đợt thanh toán</Checkbox>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={24}>
                <Form.Item
                  label="Chính sách thanh toán"
                  required
                  name="policyPaymentId"
                  rules={[{ required: true, message: 'Vui lòng chọn chính sách thanh toán' }]}
                >
                  <SingleSelectLazy
                    disabled={!isPrimaryTransaction}
                    apiQuery={getListPaymentPolicy}
                    queryKey={['payment-policy']}
                    keysLabel={['code', 'name']}
                    placeholder="Chọn chính sách thanh toán"
                    handleSelect={handleSelectPaymentPolicy}
                    defaultValues={defaultPaymentPolicy}
                    moreParams={{ active: true, projectIds: detailDataTicket?.project?.id }}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={24}>
                <Form.Item name="calcCurrencyFirst" valuePropName="checked">
                  <Checkbox>Tính chiết khấu tiền mặt trước</Checkbox>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={24}>
                <Form.Item label="Chính sách chiết khấu" name="policyDiscountIds">
                  <MultiSelectLazy
                    apiQuery={getListDiscountPolicy}
                    queryKey={['discount-policy']}
                    keysLabel={['name']}
                    keysTag={'name'}
                    placeholder="Chọn chính sách chiết khấu"
                    disabled={!isPrimaryTransaction}
                    handleListSelect={handleSelectSDiscountPolicy}
                    defaultValues={defaultDiscountPolicy}
                    moreParams={{ active: true, projectIds: detailDataTicket?.project?.id }}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={24}>
                <Form.Item label="Phí bảo trì" name="maintenanceFeeValue">
                  <InputNumber
                    maxLength={maintenanceFeeType === 'percent' ? 5 : 15}
                    style={{ width: '100%' }}
                    placeholder="Nhập giá trị chiết khấu"
                    min={0}
                    max={maintenanceFeeType === 'percent' ? 100 : undefined}
                    step={maintenanceFeeType === 'percent' ? 0.01 : 1}
                    precision={2}
                    formatter={(value: number | string | undefined) =>
                      maintenanceFeeType === 'currency' && value !== undefined
                        ? `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                        : `${value}`
                    }
                    parser={(value: string | undefined) =>
                      maintenanceFeeType === 'currency' && value ? parseFloat(value.replace(/,/g, '')) : value || ''
                    }
                    addonAfter={
                      <Form.Item name="maintenanceFeeType" noStyle>
                        <Select style={{ width: 91 }} onChange={handleMaintenanceFeeTypeChange}>
                          <Option value="percent">%</Option>
                          <Option value="currency">VND</Option>
                        </Select>
                      </Form.Item>
                    }
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={24}>
                <Form.Item label="Đợt thu phí bảo trì" name={['maintenanceFee', 'stagePayment']}>
                  <Select placeholder="Chọn đợt thu phí" options={installments} />
                </Form.Item>
              </Col>
            </Row>
          </Col>
        </Row>
        <div className="create-footer">
          <div className="button-create">
            <Button type="primary" htmlType="submit" loading={loading} size="small">
              Lưu
            </Button>
          </div>
        </div>
      </Form>
    </Spin>
  );
};

export default DeopsitContractFrom;
