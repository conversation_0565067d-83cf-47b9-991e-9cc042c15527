import { Form, Modal } from 'antd';
import { useCallback } from 'react';
import { useCreateField } from '../../../../hooks';
import { createLiquidation } from '../../../../service/liquidation';
import ModalComponent from '../../../../components/modal';
import LiquidationForm from '../../../liquidationManagement/components/formLiquidation';
import { ILiquidation } from '../../../../types/liquidation';

interface ModalProps {
  visible: boolean;
  onClose: () => void;
  initialValue: ILiquidation;
}
const CreateLiquidationModal = ({ visible, onClose, initialValue }: ModalProps) => {
  const [form] = Form.useForm();
  const { mutateAsync: create } = useCreateField({
    keyOfListQuery: ['get-list-liquidation'],
    apiQuery: createLiquidation,
    isMessageError: false,
    messageSuccess: 'Tạo đề nghị thanh lý thành công!',
  });

  const resetFormLiquidation = useCallback(async () => {
    await form.resetFields();
  }, [form]);

  const handleCancel = useCallback(async () => {
    if (form.isFieldsTouched()) {
      Modal.confirm({
        title: 'Xác nhận hủy',
        content: 'Dữ liệu chưa được lưu, bạn có chắc chắn muốn thoát khỏi trang không?',
        cancelText: 'Quay lại',
        onOk: async () => {
          await resetFormLiquidation();
          onClose();
        },
        okButtonProps: { type: 'default' },
        cancelButtonProps: { type: 'primary' },
      });
    } else {
      await resetFormLiquidation();
      onClose();
    }
  }, [form, onClose, resetFormLiquidation]);
  const handleCreate = useCallback(
    async (values: unknown) => {
      try {
        const resp = await create(values);
        if (resp?.data?.statusCode === '0' && resp?.data?.success) {
          await resetFormLiquidation();
          onClose();
        }
      } catch (error) {
        console.error('Create error:', error);
      }
    },
    [create, onClose, resetFormLiquidation],
  );

  return (
    <ModalComponent
      title="Tạo mới thanh lý hợp đồng"
      open={visible}
      onCancel={handleCancel}
      footer={null}
      destroyOnClose
    >
      <LiquidationForm initialValues={initialValue} form={form} onFinish={handleCreate} loading={false} />
    </ModalComponent>
  );
};

export default CreateLiquidationModal;
