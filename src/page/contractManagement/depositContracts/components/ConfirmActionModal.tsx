import { MutationFunction, QueryKey } from '@tanstack/react-query';
import { But<PERSON>, DatePicker, Form, Modal, notification } from 'antd';
import { useForm, useWatch } from 'antd/es/form/Form';
// import './styleModalDelete.scss';
import { useQueryClient } from '@tanstack/react-query';
import dayjs from 'dayjs';
import { useUpdateField } from '../../../../hooks';
import { FORMAT_DATE_API } from '../../../../constants/common';
interface UpdateReleaseDateProps {
  open: boolean;
  onCancel: () => void;
  title?: string;
  description?: string;
  keyOfListQuery?: QueryKey;
  keyOfDetailQuery?: QueryKey;
  apiQuery: MutationFunction | MutationFunction<unknown, unknown>;
  label?: string;
  path?: string;
  disable?: boolean;
  extraValues?: { [key: string]: unknown };
  maxLength?: number;
  fieldNameReason?: string;
  isTitlePlaceholder?: boolean;
  labelConfirm?: string;
  isUpdate?: boolean;
  isMessageSuccess?: string;
  defaultFilter?: Record<string, unknown>;
  labelCancel?: string;
  idDetail?: string;
  payload?: Record<string, unknown>;
  showReasonField?: boolean; // Thêm prop để kiểm soát hiển thị Form.Item
}

const UpdateReleaseDateModal: React.FC<UpdateReleaseDateProps> = ({
  open,
  onCancel,
  title,
  keyOfListQuery,
  apiQuery,
  label,
  path,
  keyOfDetailQuery,
  disable = false,
  labelConfirm = 'Xác nhận',
  labelCancel,
  payload,
}) => {
  const { RangePicker } = DatePicker;
  const queryClient = useQueryClient();
  const [form] = useForm();
  const hasReason = useWatch('releaseDate', form);
  const api = useUpdateField({
    keyOfListQuery,
    keyOfDetailQuery,
    apiQuery,
    label,
    path,
    isMessageError: false,
    isMessageSuccess: false,
  });

  const handleConfirm = async (values: { [key: string]: [string, string] }) => {
    if (!values?.releaseDate) return;
    // Tạo payload
    const finalPayload = {
      ...payload,
      startDate: values?.releaseDate ? dayjs(values?.releaseDate[0]).format(FORMAT_DATE_API) : '',
      endDate: values?.releaseDate ? dayjs(values?.releaseDate[1]).format(FORMAT_DATE_API) : '',
    };
    const res = await api.mutateAsync(finalPayload);
    if (res?.data?.statusCode === '0') {
      notification.success({ message: `${title} thành công` });
      // Làm mới query chi tiết
      if (keyOfListQuery) {
        queryClient.invalidateQueries({ queryKey: keyOfListQuery });
      }
      onCancel();
      form.resetFields();
    }
  };

  const handleCancel = () => {
    onCancel();
    form.resetFields();
  };

  return (
    <Modal
      className="modal-confirm-delete"
      open={open}
      title={title}
      centered
      closable={false}
      okText="Tạo"
      cancelText="Huỷ"
      destroyOnClose
      footer={[
        <Button key="cancel" className="btn-cancel" type="text" onClick={handleCancel}>
          {labelCancel || 'Huỷ'}
        </Button>,
        <Button
          key="confirm"
          className="btn-confirm"
          type="primary"
          onClick={form.submit}
          loading={api.status === 'pending'}
          disabled={!hasReason && disable}
        >
          {labelConfirm || 'Xác nhận'}
        </Button>,
      ]}
    >
      <Form form={form} layout="vertical" onFinish={handleConfirm}>
        <Form.Item style={{ marginTop: '1rem' }} label="" name="releaseDate">
          <RangePicker placeholder={['Ngày bắt đầu', 'Ngày kết thúc']} format="DD/MM/YYYY" allowClear={true} />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default UpdateReleaseDateModal;
