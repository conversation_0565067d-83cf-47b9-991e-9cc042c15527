import { ColumnsType } from 'antd/es/table';
import { Typography } from 'antd';
import dayjs from 'dayjs';
import {
  CONTRACT_STATUS_MAP,
  TDepositContract,
  TListDepositContract,
} from '../../../../types/contract/depositContract';
import { FORMAT_DATE_TIME } from '../../../../constants/common';
import { Link } from 'react-router-dom';
import { DEPOSIT_CONTRACT_MANAGEMENT } from '../../../../configs/path';

const { Text } = Typography;

export const columns: ColumnsType<TListDepositContract> = [
  {
    title: 'Tên dự án',
    dataIndex: 'primaryTransaction',
    key: 'projectName',
    render: (_, record: TDepositContract) => (
      <>
        <Text>{record?.primaryTransaction?.project?.name}</Text>
      </>
    ),
  },
  {
    title: '<PERSON><PERSON>',
    dataIndex: 'primaryTransaction',
    key: 'propertyUnit',
    width: 100,
    render: (_, record: TDepositContract) => (
      <>
        <Text>{record?.primaryTransaction?.propertyUnit?.code}</Text>
      </>
    ),
  },

  {
    title: 'Số hợp đồng',
    dataIndex: 'code',
    key: 'code',
    render: (value: string, record?: TDepositContract) => (
      <Link to={`${DEPOSIT_CONTRACT_MANAGEMENT}/${record?.id}`}>{value || '-'}</Link>
    ),
  },

  {
    title: 'Tên hợp đồng',
    dataIndex: 'name',
    key: 'name',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Ngày tạo',
    dataIndex: 'createdDate',
    key: 'createdDate',
    width: 150,
    render: (value: string) =>
      value ? (
        <>
          <Text>{dayjs(value).format(FORMAT_DATE_TIME)}</Text>
        </>
      ) : (
        '-'
      ),
  },
  {
    title: 'Mã phiếu cọc',
    dataIndex: 'primaryTransaction',
    key: 'escrowTicketCode',
    render: (_, record: TDepositContract) => (
      <>
        <Text>{record?.primaryTransaction?.escrowTicketCode}</Text>
      </>
    ),
  },

  {
    title: 'Trạng thái',
    dataIndex: 'status',
    key: 'status',
    align: 'center',
    width: 120,
    render: (value: string) => {
      if (value) {
        const status = CONTRACT_STATUS_MAP.find(item => item.status === value);
        return status ? <Text style={{ color: status.color }}>{status.label}</Text> : <Text>-</Text>;
      }
      return <Text>-</Text>;
    },
  },
  // {
  //   title: 'Lý do từ chối',
  //   dataIndex: 'reason',
  //   key: 'reason',
  //   width: 120,
  //   render: (value: string) => {
  //     return <Text>{value}</Text>;
  //   },
  // },
];
