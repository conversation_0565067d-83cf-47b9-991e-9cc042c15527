.wrapper-filter {
  .group-period {
    .ant-form-item {
      margin-bottom: 0;
    }
  }
}

.wrapper-list-deposit-contract {
  .table-deposit-contract {
    .ant-table-selection-column:where(th)::before {
      content: '';
      position: absolute;
      top: 50%;
      inset-inline-end: 0;
      width: 1px;
      height: 1.6em;
      background-color: #f0f0f0;
      transform: translateY(-50%);
      transition: background-color 0.2s;
    }
    .ant-table-cell {
      .cell-name {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
      }
    }
    .ant-table-tbody {
      .ant-table-selection-column:where(td) {
        border-right: 1px solid #0000000f;
        border-left: 1px solid #0000000f;
      }
    }
  }
}

.wrapper-filter-contract .wrapper-dropdown-content {
  width: 355px;
  overflow-y: auto;
  max-height: 600px;
}
