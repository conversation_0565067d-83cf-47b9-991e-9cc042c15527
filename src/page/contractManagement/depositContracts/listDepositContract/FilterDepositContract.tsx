import { Form } from 'antd';
import { useState, useCallback, useMemo } from 'react';
import { useSearchParams } from 'react-router-dom';
import DropdownFilterSearch from '../../../../components/dropdown/dropdownFilterSearch';
import MultiSelectLazy from '../../../../components/select/mutilSelectLazy';
import { getListProject } from '../../../../service/project';
import { CONTRACT_STATUS_MAP, TFilterDepositContract } from '../../../../types/contract/depositContract';
import { Project } from '../../../../types/project/project';
import { DEFAULT_PARAMS } from '../../../../constants/common';
import { IPaymentPolicy } from '../../../../types/paymentPolicy';
import { getListPaymentPolicy } from '../../../../service/paymentPolicy';
import { getListDiscountPolicy } from '../../../../service/discountPolicy';
import MultiSelectStatic from '../../../../components/select/mutilSelectStatic';
import { OptionTypeSelect } from '../../../../types/common/common';

// Định nghĩa các constant
const DEFAULT_PAGE = '1';
const DEFAULT_PAGE_SIZE = '10';
const PAGE_KEY = 'page';
const PAGE_SIZE_KEY = 'pageSize';

interface Status {
  label: string;
  value: string;
  color?: string;
  status?: string;
  tagColor?: string;
}

interface FilterDepositContractProps {
  onFilterChange: (filterParams: Record<string, unknown>) => void;
}

const FilterDepositContract = ({ onFilterChange }: FilterDepositContractProps) => {
  const [form] = Form.useForm();
  const [isOpenFilter, setIsOpenFilter] = useState(false);
  const [searchParams, setSearchParams] = useSearchParams();
  const [filterValues, setFilterValues] = useState<Record<string, unknown>>({});
  const [listStatus, setListStatus] = useState('');

  const handleSubmitFilter = useCallback(
    (values: TFilterDepositContract) => {
      const newFilter: Record<string, unknown> = {
        projectId: values?.projectId ?? null,
        paymentPolicyIds: values?.paymentPolicyIds ?? null,
        discountPolicyIds: values?.discountPolicyIds ?? null,
        status: listStatus ?? null,
        ...DEFAULT_PARAMS,
      };

      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const cleanParams = Object.fromEntries(Object.entries(newFilter).filter(([_, value]) => value != null));

      setFilterValues(cleanParams);

      const newSearchParams = new URLSearchParams(searchParams);
      if (Object.keys(cleanParams).length > 1) {
        newSearchParams.set(PAGE_KEY, DEFAULT_PAGE);
        newSearchParams.set(PAGE_SIZE_KEY, DEFAULT_PAGE_SIZE);
        setSearchParams(newSearchParams);
      } else {
        setSearchParams({});
      }

      onFilterChange(cleanParams);
      setIsOpenFilter(false);
    },
    [listStatus, searchParams, onFilterChange, setSearchParams],
  );

  const handleOpenChangeDropdown = useCallback((value: boolean) => {
    setIsOpenFilter(value);
  }, []);

  const handleClearFilters = useCallback(() => {
    form.resetFields();
    setFilterValues({});
    setSearchParams({});
    onFilterChange(DEFAULT_PARAMS);
    setTimeout(() => setIsOpenFilter(false), 100);
  }, [form, onFilterChange, setSearchParams]);

  const handleSearch = useCallback(
    (value: string) => {
      const newParams = { ...filterValues, [PAGE_KEY]: 1 };
      if (value) {
        (newParams as Record<string, unknown>).search = value;
        const newSearchParams = new URLSearchParams(searchParams);
        newSearchParams.set(PAGE_KEY, DEFAULT_PAGE);
        setSearchParams(newSearchParams);
      } else {
        delete (newParams as Record<string, unknown>).search;
        setSearchParams({});
      }

      setFilterValues(newParams);
      onFilterChange(newParams);
    },
    [filterValues, searchParams, setSearchParams, onFilterChange],
  );

  const extraFormItems = useMemo(
    () => (
      <>
        <Form.Item label="Dự án" name="projectId">
          <MultiSelectLazy
            enabled={isOpenFilter}
            apiQuery={getListProject}
            queryKey={['Project-dropdown']}
            keysLabel={['name']}
            handleListSelect={(values: Project[]) => {
              form.setFieldsValue({ projectId: values.map(item => item.id).join(',') });
            }}
            placeholder="Chọn dự án"
            keysTag="name"
          />
        </Form.Item>
        <Form.Item label="Chính sách thanh toán" name="paymentPolicyIds">
          <MultiSelectLazy
            enabled={isOpenFilter}
            apiQuery={getListPaymentPolicy}
            queryKey={['payment-policy-dropdown']}
            keysLabel={['name']}
            handleListSelect={(values: IPaymentPolicy[]) => {
              form.setFieldsValue({ paymentPolicyIds: values.map(item => item.id).join(',') });
            }}
            placeholder="Chọn chính sách thanh toán"
            keysTag="name"
          />
        </Form.Item>
        <Form.Item label="Chính sách chiết khấu" name="discountPolicyIds">
          <MultiSelectLazy
            enabled={isOpenFilter}
            apiQuery={getListDiscountPolicy}
            queryKey={['discount-policy-dropdown']}
            keysLabel={['name']}
            handleListSelect={(values: OptionTypeSelect[]) => {
              form.setFieldsValue({ discountPolicyIds: values?.map(item => item?.option?.id).join(',') });
            }}
            placeholder="Chọn chính sách chiết khấu"
            keysTag="name"
          />
        </Form.Item>

        <Form.Item label="Trạng thái" name="status">
          <MultiSelectStatic
            data={CONTRACT_STATUS_MAP as unknown as OptionTypeSelect[]}
            handleListSelect={(values: Status[]) => {
              form.setFieldsValue({ status: values.map(item => item.value).join(',') });
              setListStatus(values.map(item => item.status).join(','));
            }}
            placeholder="Chọn trạng thái"
            keysTag={['label']}
          />
        </Form.Item>
      </>
    ),
    [isOpenFilter, form],
  );

  return (
    <DropdownFilterSearch
      rootClassName="wrapper-filter-contract"
      submitFilter={handleSubmitFilter}
      placeholder="Tìm kiếm"
      showParams={false}
      handleOpenChange={handleOpenChangeDropdown}
      isOpenFilter={isOpenFilter}
      form={form}
      onClearFilters={handleClearFilters}
      onChangeSearch={handleSearch}
      extraFormItems={extraFormItems}
    />
  );
};

export default FilterDepositContract;
