import { useMemo, useState } from 'react';
import TableComponent from '../../../../components/table';
import { useFetch } from '../../../../hooks';
import { softDelete, getListDepositContract, updateManyPrimaryContract } from '../../../../service/contract';
import { TDepositContract, TListDepositContract } from '../../../../types/contract/depositContract';
import { columns } from './columns';
import { Button, Col, Flex, Row, TableColumnsType, Typography } from 'antd';
import BreadCrumbComponent from '../../../../components/breadCrumb';
import FilterDepositContract from './FilterDepositContract';
import ConfirmDeleteModal from '../../../../components/modal/specials/ConfirmDeleteModal';
import { MutationFunction } from '@tanstack/react-query';
import CreateDepositContract from '../createDepositContract';
import './styles.scss';
import { Key, TableRowSelection } from 'antd/es/table/interface';
import useFilter from '../../../../hooks/filter';
import UpdateReleaseDateModal from '../components/ConfirmActionModal';

const DepositContract = () => {
  const [isOpenModalDelete, setIsOpenModalDelete] = useState<boolean>(false);
  const [isOpenModalUpdateReleaseDate, setIsOpenModalUpdateReleaseDate] = useState<boolean>(false);

  const [currentRecord, setCurrentRecord] = useState<TListDepositContract>();
  const [isOpenModalCreate, setIsOpenModalCreate] = useState(false);
  const [selectedRows, setSelectedRows] = useState<TDepositContract[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<Key[]>([]);
  const [filterParams, setFilterParams] = useState<Record<string, unknown>>({});
  const [filter] = useFilter();

  const combinedFilter = useMemo(
    () => ({
      ...filterParams,
      type: filter.type || 'deposit',
      page: filter.page || '1',
      pageSize: filter.pageSize || '10',
      _fields:
        '_id,id,code,name,primaryTransaction.project.name,primaryTransaction.propertyUnit.salesProgram.name,primaryTransaction.propertyUnit.code,primaryTransaction.escrowTicketCode,primaryTransaction.customer.personalInfo.name,createdDate,type,liquidation.code,status,modifiedBy,createdBy,reason,deposit,isTransferred',
    }),
    [filterParams, filter.type, filter.page, filter.pageSize],
  );
  const { data, isFetching } = useFetch<TListDepositContract[]>({
    queryKeyArrWithFilter: ['list-deposit-contract', combinedFilter],
    defaultFilter: combinedFilter,
    api: getListDepositContract,
    moreParams: combinedFilter,
  });

  const handleFilterChange = (newFilterParams: Record<string, unknown>) => {
    setFilterParams(newFilterParams);
  };

  const dataDepositContract = data?.data?.data?.rows || [];

  const actionsColumns: TableColumnsType<TListDepositContract> = useMemo(() => {
    return [
      ...columns,
      {
        title: '',
        dataIndex: 'action',
        key: 'action',
        align: 'center',
        width: '100px',
        render: (_, record) => {
          const handleDeleteDepositContract = (): void => {
            setIsOpenModalDelete(true);
            setCurrentRecord(record);
          };
          if (record.status === 'init') {
            return (
              <Typography.Link
                style={{ color: '#FF3B30' }}
                onClick={() => {
                  handleDeleteDepositContract();
                }}
              >
                Xoá
              </Typography.Link>
            );
          }
        },
      },
    ];
  }, []);

  const handleCancelModalCreate = () => {
    setIsOpenModalCreate(false);
  };

  const onSelectChange = (selectedRowKeys: Key[], selectedRows: TDepositContract[]) => {
    setSelectedRows(selectedRows);
    setSelectedRowKeys(selectedRowKeys);
  };

  const rowSelection: TableRowSelection<TDepositContract> = {
    selectedRowKeys,
    onChange: (selectedRowKeys: Key[], selectedRows: TDepositContract[]) =>
      onSelectChange(selectedRowKeys, selectedRows),
  };

  return (
    <div className="wrapper-list-deposit-contract">
      <BreadCrumbComponent />
      <div style={{ marginBottom: 16 }}>
        <Flex gap={17} justify="space-between">
          <FilterDepositContract onFilterChange={handleFilterChange} />

          <Row gutter={[16, 16]}>
            <Col>
              <Button onClick={() => setIsOpenModalCreate(true)}>Tạo mới hợp đồng cọc</Button>
            </Col>
            {selectedRows?.length > 0 && (
              <Col>
                <Button onClick={() => setIsOpenModalUpdateReleaseDate(true)}>Cập nhật hàng loạt</Button>
              </Col>
            )}
            <Col>
              <Button onClick={() => {}}>Tải về biểu mẫu hợp đồng cọc</Button>
            </Col>
            <Col>
              <Button onClick={() => {}}>Tải nhập hợp đồng cọc</Button>
            </Col>{' '}
            <Col>
              <Button onClick={() => {}}>Tải về</Button>
            </Col>
          </Row>
        </Flex>
      </div>
      <TableComponent
        queryKeyArr={['list-deposit-contract', combinedFilter]}
        loading={isFetching}
        className="table-deposit-contract"
        columns={actionsColumns}
        rowSelection={rowSelection}
        dataSource={dataDepositContract || []}
        rowKey={'id'}
        isPagination={true}
        defaultFilter={combinedFilter}
      />
      <ConfirmDeleteModal
        label="Hợp đồng cọc"
        open={isOpenModalDelete}
        apiQuery={softDelete as MutationFunction<unknown, unknown>}
        keyOfListQuery={['list-deposit-contract']}
        onCancel={() => setIsOpenModalDelete(false)}
        idDetail={currentRecord?.id}
        title="Xoá hợp đồng cọc"
        description="Vui lòng nhập lý do muốn xoá "
      />
      <CreateDepositContract onClose={handleCancelModalCreate} visible={isOpenModalCreate} />
      <UpdateReleaseDateModal
        open={isOpenModalUpdateReleaseDate}
        apiQuery={updateManyPrimaryContract as MutationFunction<unknown, unknown>}
        keyOfListQuery={['list-deposit-contract']}
        onCancel={() => {
          setIsOpenModalUpdateReleaseDate(false);
        }}
        title={`Cập nhật ngày phát hành HĐMB`}
        description={``}
        isTitlePlaceholder
        labelCancel="Quay lại"
        fieldNameReason="reason"
        isUpdate={true}
        disable={false}
        payload={{ lstIdPrimaryContract: selectedRows.map(item => item.id) }}
      />
    </div>
  );
};

export default DepositContract;
