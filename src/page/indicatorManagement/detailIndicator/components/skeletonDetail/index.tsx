import { Col, Form, Row, Skeleton, Switch, Table, Typography } from 'antd';
import { columnsIndicator } from '../../../listIndicators/columns';

const { Item } = Form;
const { Title } = Typography;
const { Input } = Skeleton;

const SkeletonDetail = () => {
  return (
    <Form layout="vertical">
      <Row gutter={{ md: 24, lg: 40, xl: 80, xxl: 126 }}>
        <Col span={24}>
          <Title level={5}>Thông tin chung</Title>
        </Col>
        <Col xs={24} md={12}>
          <Row gutter={{ md: 24, lg: 40 }}>
            <Col xs={24} md={12}>
              <Item label="Mã chỉ tiêu" name="code" required rules={[{ required: false }]}>
                <Input active block />
              </Item>
            </Col>
            <Col xs={24} md={12}>
              <Item label="Đơn vị" name={['pos']}>
                <Input active block />
              </Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item label="Kỳ tính phí/ hoa hồng" name="year">
                <Input active block />
              </Form.Item>
            </Col>
            <Col xs={24} md={12} style={{ alignContent: 'end' }}>
              <Form.Item name="period">
                <Input active block />
              </Form.Item>
            </Col>

            {/**Phần chọn trạng thái */}
            <Col xs={24} md={4}>
              <Item label="Trạng thái" name="isActive"></Item>
            </Col>
            <Col xs={24} md={3}>
              <Switch defaultValue={true} />
            </Col>
            <Col xs={24} md={6}></Col>
          </Row>
        </Col>

        {/* <Row gutter={{ md: 24, lg: 40 }}> */}
        <Col xs={24} md={24}>
          <Row gutter={{ md: 24, lg: 40 }} align={'middle'}>
            <Col span={24}>
              <Title level={5}>Danh sách chỉ tiêu</Title>
            </Col>
            <Col span={10}>{/* <FilterSearch /> */}</Col>
            <Col span={14}></Col>
          </Row>
        </Col>
        {/* </Row> */}
        <Col xs={24} md={24} style={{ marginTop: '8px' }}>
          <Table
            columns={columnsIndicator}
            // loading={loadingListIndicator}
            // dataSource={indicatorList}
            pagination={false}
          />
        </Col>
      </Row>
    </Form>
  );
};

export default SkeletonDetail;
