import { MutationFunction } from '@tanstack/react-query';
import { App, Button, Flex, notification, TableColumnsType } from 'antd';
import { useMemo, useState } from 'react';
import BreadCrumbComponent from '../../../components/breadCrumb';
import ConfirmDeleteModal from '../../../components/modal/specials/ConfirmDeleteModal';
import TableComponent from '../../../components/table';
import { ActionsColumns } from '../../../components/table/components/ActionsColumns';
import { INDICATOR } from '../../../configs/path';
import { useCheckPermissions, useFetch, useUpdateField } from '../../../hooks';
import FilterSearch from './components/filterSearch';
import './styles.scss';
import { IIndicator } from '../../../types/indicator';
import {
  changeStatusIndicator,
  cloneIndicator,
  getListIndicators,
  softDeleteIndicator,
} from '../../../service/indicator';
import { modalConfirm } from '../../../components/modal/specials/ModalConfirm';
import { columns } from './columns';
import ModalCreateIndicator from './components/modalCreate';
import { PERMISSION_INDICATOR } from '../../../constants/permissions/commission';

function ListIndicators() {
  const { modal } = App.useApp();

  const [isOpenModalDelete, setIsOpenModalDelete] = useState<boolean>(false);
  const [currentRecord, setCurrentRecord] = useState<IIndicator>();
  const [isOpenModalCreate, setIsOpenModalCreate] = useState(false);

  const {
    create: indicatorCreate,
    clone: indicatorClone,
    delete: indicatorDelete,
    // update: indicatorUpdate,
    listGetId: indicatorGetById,
    changeStatus: indicatorChangeStatus,
  } = useCheckPermissions(PERMISSION_INDICATOR);

  const {
    data: listIndicators,
    isLoading,
    isPlaceholderData,
    isFetching,
  } = useFetch<IIndicator[]>({
    queryKeyArrWithFilter: ['get-indicators'],
    api: getListIndicators,
  });

  const { mutateAsync: cloneIndicators } = useUpdateField({
    apiQuery: cloneIndicator,
    keyOfListQuery: ['get-indicators'],
  });

  const { mutateAsync: updateActive } = useUpdateField({
    apiQuery: changeStatusIndicator,
    keyOfListQuery: ['get-indicators'],
    isMessageError: false,
    isMessageSuccess: false,
  });

  const columnActions: TableColumnsType<IIndicator> = useMemo(() => {
    return [
      ...columns,
      {
        title: 'Hành động',
        dataIndex: 'action',
        key: 'action',
        width: '100px',
        align: 'center',
        fixed: 'right',
        render: (_, record: IIndicator) => {
          const textModalConfirmActive = Number(record?.isActive) === 1 ? 'Vô hiệu hoá' : 'Kích hoạt';
          const openViewDetail = () => {
            window.open(`${INDICATOR}/${record?.id}`, '_blank', 'noopener,noreferrer');
          };
          const handleActiveIndicator = () => {
            return modalConfirm({
              modal: modal,
              title: `${textModalConfirmActive} chỉ tiêu`,
              content: `Bạn có muốn ${textModalConfirmActive} chỉ tiêu này không?`,
              handleConfirm: async () => {
                const res = await updateActive({
                  id: record?.id ?? '',
                  isActive: Number(record?.isActive) === 1 ? 2 : 1,
                });
                if (res?.data?.statusCode === '0') {
                  notification.success({ message: `${textModalConfirmActive} chỉ tiêu thành công` });
                  return Promise.resolve();
                } else {
                  notification.error({ message: `${textModalConfirmActive} chỉ tiêu Thất bại!` });
                }
              },
            });
          };
          const handleCloneIndicator = () => {
            return modalConfirm({
              modal: modal,
              title: `Sao chép chỉ tiêu`,
              content: `Bạn có muốn sao chép chỉ tiêu này không?`,
              handleConfirm: async () => {
                const res = await cloneIndicators({ id: record?.id ?? '' });
                if (res?.data?.statusCode === '0') {
                  return Promise.resolve();
                } else {
                  return Promise.reject();
                }
              },
            });
          };
          const handleDeleteIndicator = () => {
            setIsOpenModalDelete(true);
            setCurrentRecord(record);
          };

          return (
            <ActionsColumns
              handleViewDetail={indicatorGetById ? openViewDetail : undefined}
              textModalConfirmActive={textModalConfirmActive}
              handleCloneRole={indicatorClone ? handleCloneIndicator : undefined}
              handleDelete={indicatorDelete ? handleDeleteIndicator : undefined}
              handleActive={indicatorChangeStatus ? handleActiveIndicator : undefined}
            />
          );
        },
      },
    ];
  }, [cloneIndicators, indicatorChangeStatus, indicatorClone, indicatorDelete, indicatorGetById, modal, updateActive]);

  const handleOpenModalCreate = () => {
    setIsOpenModalCreate(true);
  };

  const handleCancelModalCreate = () => {
    setIsOpenModalCreate(false);
  };

  return (
    <div className="wrapper-list-personal-indicators">
      <BreadCrumbComponent />
      <div className="header-content">
        <Flex gap={17}>
          <FilterSearch />
        </Flex>
        <Flex gap={10}>
          {indicatorCreate && (
            <Button type="primary" onClick={handleOpenModalCreate}>
              Thêm mới
            </Button>
          )}
        </Flex>
      </div>
      <div className="table-personal-indicators">
        <TableComponent
          queryKeyArr={['get-indicators']}
          columns={columnActions}
          // rowSelection={rowSelection}
          loading={isLoading || isPlaceholderData || isFetching}
          dataSource={listIndicators?.data?.data?.rows ?? []}
          rowKey="id"
        />
      </div>
      <ModalCreateIndicator isOpen={isOpenModalCreate} handleCancel={handleCancelModalCreate} />
      <ConfirmDeleteModal
        label="chỉ tiêu"
        open={isOpenModalDelete}
        apiQuery={softDeleteIndicator as MutationFunction<unknown, unknown>}
        keyOfListQuery={['get-indicators']}
        onCancel={() => setIsOpenModalDelete(false)}
        idDetail={currentRecord?.id}
        title="Xoá chỉ tiêu"
        description="Vui lòng nhập lý do muốn xoá chỉ tiêu này"
      />
    </div>
  );
}

export default ListIndicators;
