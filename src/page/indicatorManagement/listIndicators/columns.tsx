import { TableColumnsType, Typography } from 'antd';
import dayjs from 'dayjs';
import { FORMAT_DATE_TIME } from '../../../constants/common';
import { IIndicator } from '../../../types/indicator';

const { Text } = Typography;
export const columns: TableColumnsType = [
  {
    title: 'Mã chỉ tiêu',
    dataIndex: 'code',
    key: 'code',
    width: 160,
    fixed: 'left',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Kỳ tính phí/ hoa hồng',
    dataIndex: 'period',
    key: 'period',
    width: 140,
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Trạng thái',
    dataIndex: 'isActive',
    width: 120,
    align: 'center',
    key: 'isActive',
    render: (value: number) =>
      value === 1 ? (
        <Text style={{ color: '#389E0D' }}><PERSON><PERSON> kích hoạt</Text>
      ) : (
        <Text style={{ color: '#CF1322' }}><PERSON><PERSON> hiệu hóa</Text>
      ),
  },
  {
    title: '<PERSON><PERSON>y tạo',
    dataIndex: 'createdDate',
    key: 'createdDate',
    width: 200,

    render: (value: string, record: IIndicator) => (
      <>
        <Text>{dayjs(value).format(FORMAT_DATE_TIME)}</Text>
        <br />
        <Text>{record?.createdBy ? `${record?.createdBy?.fullName}` : '-'}</Text>
      </>
    ),
  },
  {
    title: 'Ngày cập nhật',
    dataIndex: 'modifiedDate',
    key: 'modifiedDate',
    width: 200,

    render: (value: string, record: IIndicator) => (
      <>
        <Text>{dayjs(value).format(FORMAT_DATE_TIME)}</Text> <br />
        <Text>{record?.modifiedBy ? `${record?.modifiedBy?.fullName}` : '-'}</Text>
      </>
    ),
  },
];

export const columnsIndicator: TableColumnsType = [
  {
    title: 'STT',
    dataIndex: 'index',
    key: 'index',
    width: 80,
    fixed: 'left',
    render: (_value, _record, index) => index + 1,
  },
  {
    title: 'Mã hệ thống',
    dataIndex: 'code',
    key: 'code',
    width: 140,
    render: (value: string[]) => (value ? value : '-'),
  },
  {
    title: 'Mã nhân viên',
    dataIndex: 'employeeCode',
    key: 'employeeCode',
    width: 150,

    render: (value: string[]) => (value ? value : '-'),
  },
  {
    title: 'Tên NVKD',
    dataIndex: 'employeeName',
    key: 'employeeName',
    width: 150,

    render: (value: string[]) => (value ? value : '-'),
  },
  {
    title: 'Chức vụ',
    dataIndex: 'position',
    key: 'position',
    width: 150,

    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Sàn',
    dataIndex: 'posCode',
    key: 'posCode',
    width: 150,

    render: (value: string[]) => (value ? value : '-'),
  },
  {
    title: 'Chỉ tiêu doanh thu',
    dataIndex: 'indicatorRevenue',
    key: 'indicatorRevenue',
    width: 150,

    render: (value: string[]) => (value ? value : '-'),
  },
  {
    title: 'Chỉ tiêu sản phẩm',
    dataIndex: 'indicatorProduct',
    key: 'indicatorProduct',
    width: 150,

    render: (value: string[]) => (value ? value : '-'),
  },
];
