import { PlusOutlined } from '@ant-design/icons';
import { Button, Form, Input, Spin, TimePicker, Typography } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';
import { v4 as uuid } from 'uuid';
import ButtonOfPageDetail from '../../../components/button/buttonOfPageDetail';
import { ColumnTypesCustom, EditTable, EditTableColumns } from '../../../components/editTable';
import CurrencyInput from '../../../components/input/CurrencyInput';
import { useFetch, useUpdateField } from '../../../hooks';
import { useRowFieldErrors } from '../../../hooks/errorsInFormTable';
import { getDebtRemainderInProject, putDebtRemainderInProject } from '../../../service/debtInProject';
import { IDebtInProject, TReminder } from '../../../types/debtInProject';
import { ARRAY_PHASE } from '../../../constants/debtCommission';
import { FORMAT_TIME } from '../../../constants/common';

const { Title } = Typography;
const { Item } = Form;

const DebtReminder = () => {
  const [form] = Form.useForm();
  const { id } = useParams();
  const [debtReminder, setDebtReminder] = useState<TReminder[]>([]);
  const [isModified, setIsModified] = useState(false);
  const { clearError, hasAnyError, markError, resetErrors } = useRowFieldErrors();

  const { data, isFetching } = useFetch<IDebtInProject>({
    api: () => !!id && getDebtRemainderInProject(id),
    queryKeyArr: ['debt-reminder-in-project', id],
    enabled: !!id,
  });

  const dataDebtReminder = data?.data?.data?.setting?.debtReminder;
  const { mutateAsync: updateDebtRemainder, isPending } = useUpdateField({
    apiQuery: putDebtRemainderInProject,
    keyOfDetailQuery: ['debt-reminder-in-project', id],
    isMessageError: false,
  });

  const formattedData = useMemo(
    () =>
      dataDebtReminder?.map(item => ({
        ...item,
        remindTime: item?.remindTime ? dayjs(item?.remindTime, FORMAT_TIME) : null,
      })),
    [dataDebtReminder],
  );

  useEffect(() => {
    if (dataDebtReminder) {
      setDebtReminder(formattedData || []);
      form.setFieldsValue({ debtReminder: formattedData });
    }
  }, [dataDebtReminder, form, formattedData]);

  const columns: (ColumnTypesCustom<TReminder>[number] & EditTableColumns)[] = [
    {
      key: 'name',
      title: 'Lần nhắc',
      dataIndex: 'name',
      alwaysEditable: true,
      editable: true,
      width: '150px',
      inputType: 'text',
      inputProps: {
        disabled: true,
      },
    },
    {
      key: 'phase',
      title: 'Nhắc vào đợt thanh toán',
      dataIndex: 'phase',
      alwaysEditable: true,
      editable: true,
      width: '200px',
      inputType: 'select',
      optionsSelect: ARRAY_PHASE.map(item => ({
        label: item,
        value: item,
      })),
      rules: [
        {
          required: true,
          message: 'Vui lòng chọn Nhắc vào đợt thanh toán',
        },
      ],
    },
    {
      key: 'remindTime',
      title: 'Thời gian nhắc nợ',
      dataIndex: 'remindTime',
      alwaysEditable: true,
      editable: true,
      width: '200px',
      inputType: 'custom',
      renderEditComponent:
        () =>
        ({ onChange, save }) => (
          <TimePicker
            format={FORMAT_TIME}
            onChange={value => {
              onChange(value);
              save();
            }}
            allowClear
            placeholder="Chọn giờ"
            suffixIcon="giờ"
          />
        ),
      rules: (_, index) => [
        {
          validator: (_, value) => {
            if (!value) {
              markError(debtReminder[index].key, 'remindTime');
              return Promise.reject('Vui lòng chọn thời gian nhắc nợ.');
            }
            clearError(debtReminder[index].key, 'remindTime');
            return Promise.resolve();
          },
        },
      ],
    },
    {
      key: 'remindDays',
      dataIndex: 'remindDays',
      title: 'Nhắc trước/sau (ngày)',
      alwaysEditable: true,
      editable: true,
      width: '250px',
      inputType: 'custom',
      rules: (_, index) => [
        {
          validator: (_, value) => {
            if (!value) {
              markError(debtReminder[index].key, 'remindDays');
              return Promise.reject('Vui lòng nhập số ngày nhắc nợ.');
            }
            clearError(debtReminder[index].key, 'remindDays');
            return Promise.resolve();
          },
        },
      ],
      renderEditComponent:
        record =>
        ({ onChange, save }) => {
          const reminder = record as TReminder;
          const prefix = reminder?.phase === ARRAY_PHASE[2] ? 'Sau' : 'Trước';
          return (
            <Input
              maxLength={5}
              onChange={value => onChange(value?.target?.value)}
              onBlur={save}
              onPressEnter={save}
              prefix={`${prefix}:`}
              suffix="ngày"
              placeholder="Nhập số ngày nhắc nợ"
            />
          );
        },
    },
    {
      key: 'smsTemplate',
      title: 'Mẫu SMS',
      dataIndex: 'smsTemplate',
      alwaysEditable: true,
      editable: true,
      width: '250px',
      inputProps: {
        placeholder: 'Chọn mẫu SMS',
        allowClear: true,
      },
      inputType: 'text',
    },
    {
      key: 'emailTemplate',
      title: 'Mẫu Email',
      dataIndex: 'emailTemplate',
      alwaysEditable: true,
      editable: true,
      width: '250px',
      inputType: 'text',
      inputProps: {
        placeholder: 'Chọn mẫu Email',
        allowClear: true,
      },
      //   rules: [
      //     {
      //       required: true,
      //       message: 'Vui lòng chọn mẫu email',
      //     },
      //   ],
    },
    {
      key: 'debtAmountLimit',
      dataIndex: 'debtAmountLimit',
      title: 'Số tiền tối thiểu nhắc nợ (VNĐ)',
      alwaysEditable: true,
      inputType: 'custom',
      editable: true,
      width: '250px',
      renderEditComponent:
        () =>
        ({ onChange, save }) => (
          <CurrencyInput
            onChange={onChange}
            onPressEnter={save}
            onBlur={save}
            placeholder="Nhập số tiền tối thiểu nhắc nợ"
            style={{ width: '100%' }}
            maxLength={17}
            suffix="VNĐ"
          />
        ),
    },
    {
      dataIndex: 'action',
      key: 'action',
      align: 'center',
      render: (_, __, index) => {
        return (
          <Button
            type="link"
            danger
            onClick={() => {
              const newData = [...debtReminder];
              newData.splice(index, 1);
              form.setFieldsValue({
                debtReminder: newData,
              });
              setDebtReminder(newData);
              setIsModified(true);
            }}
          >
            Xoá
          </Button>
        );
      },
    },
  ];

  // Hàm thêm một mục mới vào cuối danh sách
  const handleAdd = () => {
    const newData: TReminder = {
      key: uuid(),
      phase: 'Trước khi thanh toán',
      name: `Lần ${debtReminder.length + 1}`,
      remindTime: null,
      remindDays: '',
      smsTemplate: undefined,
      emailTemplate: undefined,
      debtAmountLimit: '',
    };

    const newDebtReminder = [...debtReminder, newData];
    setDebtReminder(newDebtReminder);
    form.setFieldsValue({
      debtReminder: newDebtReminder,
    });
  };

  // Hàm xử lý lưu thay đổi trên bảng
  const handleSaveDebt = (row: TReminder) => {
    const newData = [...debtReminder];
    const index = newData.findIndex(item => row.key === item.key);
    if (index > -1) {
      const item = newData[index];
      newData.splice(index, 1, { ...item, ...row });
      setDebtReminder(newData);
      form.setFieldsValue({
        debtReminder: newData,
      });
    }
    setIsModified(true);
  };

  const handleSubmit = async () => {
    try {
      if (hasAnyError()) {
        return;
      }
      await form.validateFields();
      const values = form.getFieldsValue(true);
      const newData = values?.debtReminder?.map((item: TReminder) => ({
        ...item,
        id: id,
        remindTime: item?.remindTime ? dayjs(item?.remindTime).format(FORMAT_TIME) : null,
        debtAmountLimit: Number(item?.debtAmountLimit) || 0,
        remindDays: Number(item?.remindDays) || 0,
      }));

      const res = await updateDebtRemainder({ debtReminder: newData, id: id });
      if (res?.data?.statusCode === '0') {
        setIsModified(false);
        resetErrors();
      }
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };
  const handleCancel = () => {
    form.setFieldsValue(dataDebtReminder);
    setDebtReminder(formattedData || []);
    setIsModified(false);
    resetErrors();
  };

  return (
    <Spin spinning={isFetching}>
      <Title level={5}>Nhóm tuổi nợ</Title>
      <Button type="default" icon={<PlusOutlined />} onClick={handleAdd} style={{ marginBottom: 16 }}>
        Thêm cấu hình
      </Button>
      <Form
        form={form}
        layout="vertical"
        initialValues={{ debtReminder: debtReminder }}
        onFinish={handleSubmit}
        onFieldsChange={() => {
          setIsModified(true);
        }}
      >
        <Item
          name="debtReminder"
          rules={[
            {
              required: true,
              validator: () => {
                const debtReminder = form.getFieldValue('debtReminder');
                const requiredFields = ['phase', 'remindDays', 'remindTime'];
                const hasInvalid = debtReminder?.some((item: TReminder) =>
                  requiredFields.some(key => {
                    const value = item[key as keyof TReminder];
                    return value === undefined || value === null || value === '' || value === 0;
                  }),
                );
                if (hasInvalid) {
                  return Promise.reject(`Vui lòng nhập đủ thông tin làn nhắc.`);
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <EditTable
            scroll={{ x: 1500 }}
            footer={() => <div style={{ height: 14 }}></div>}
            columns={columns}
            dataSource={debtReminder}
            handleSave={handleSaveDebt}
          />
        </Item>
        {isModified && (
          <ButtonOfPageDetail handleSubmit={handleSubmit} handleCancel={handleCancel} loadingSubmit={isPending} />
        )}
      </Form>
    </Spin>
  );
};

export default DebtReminder;
