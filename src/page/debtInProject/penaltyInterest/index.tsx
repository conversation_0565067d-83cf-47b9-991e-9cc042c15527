import { PlusOutlined } from '@ant-design/icons';
import { Button, Checkbox, Col, Form, Input, Row, Spin, TimePicker, Typography } from 'antd';
import { CheckboxChangeEvent } from 'antd/es/checkbox';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { v4 as uuid } from 'uuid';
import ButtonOfPageDetail from '../../../components/button/buttonOfPageDetail';
import { ColumnTypesCustom, EditTable, EditTableColumns } from '../../../components/editTable';
import CurrencyInput from '../../../components/input/CurrencyInput';
import PercentInput from '../../../components/input/PercentInput';
import { FORMAT_TIME } from '../../../constants/common';
import { useFetch, useUpdateField } from '../../../hooks';
import { useRowFieldErrors } from '../../../hooks/errorsInFormTable';
import { getPenaltyInterestInProject, putPenaltyInterestInProject } from '../../../service/debtInProject';
import { IDebtInProject, TDebtAge, TDebtInterestPolicy } from '../../../types/debtInProject';
import { handleKeyDownEnterNumber } from '../../../utilities/regex';

const { Title } = Typography;
const { Item } = Form;

const PenaltyInterest = () => {
  const [form] = Form.useForm();
  const { id } = useParams();
  const [debtAge, setDebtAge] = useState<TDebtAge[]>([]);
  const [initialData, setInitialData] = useState<TDebtInterestPolicy>();
  const [isModified, setIsModified] = useState(false);
  const [isFinalRange, setIsFinalRange] = useState(false);
  const { markError, clearError, hasAnyError, resetErrors } = useRowFieldErrors();

  const ItemBadDebt = debtAge.find(item => !!item.isBadDebt);
  const { data, isFetching } = useFetch<IDebtInProject>({
    api: () => !!id && getPenaltyInterestInProject(id),
    queryKeyArr: ['penalty-interest-in-project', id],
    enabled: !!id,
  });

  const { mutateAsync: updatePenaltyInterest, isPending } = useUpdateField({
    apiQuery: putPenaltyInterestInProject,
    keyOfDetailQuery: ['penalty-interest-in-project', id],
    isMessageError: false,
  });

  const dataSettingInProject = data?.data?.data?.setting;
  const firstBadDebtIndex = dataSettingInProject?.debtage?.findIndex(item => item.isBadDebt);

  useEffect(() => {
    if (dataSettingInProject) {
      const formattedData: TDebtAge[] =
        dataSettingInProject?.debtage?.map((item, index) => ({
          ...item,
          isBadDebt: index === firstBadDebtIndex,
          toDay: item?.toDay ? item.toDay : '',
          key: item?.key || uuid(),
        })) || [];
      const dataPenaltyInterest: TDebtInterestPolicy = {
        onlyWorkday: dataSettingInProject?.onlyWorkday,
        latePaymentFee: dataSettingInProject?.latePaymentFee,
        daysPerYear: dataSettingInProject?.daysPerYear,
        delayDay: dataSettingInProject?.delayDay,
        dueDateReminderDays: dataSettingInProject?.dueDateReminderDays,
        interestJobTime: dataSettingInProject?.interestJobTime,
        debtage: formattedData,
      };

      const checkIsFinalRange = formattedData?.at?.(-1)?.isFinalRange;
      setIsFinalRange(!!checkIsFinalRange);
      setInitialData(dataPenaltyInterest);
      setDebtAge(formattedData);
      form.setFieldsValue(dataPenaltyInterest);
    }
  }, [dataSettingInProject, firstBadDebtIndex, form]);

  // Cột cho bảng EditTable
  const columns: (ColumnTypesCustom<TDebtAge>[number] & EditTableColumns)[] = [
    {
      key: 'name',
      title: 'Tên nhóm',
      dataIndex: 'name',
      alwaysEditable: true,
      editable: true,
      width: '30%',
      inputType: 'custom',
      align: 'center',

      renderEditComponent:
        (_, index) =>
        ({ save, disabled, onChange }) => {
          const disabledIsDelayDay = index === 0 && initialData?.delayDay ? true : false;

          return (
            <Input
              placeholder="Nhập tên nhóm"
              onChange={e => {
                onChange(e.target.value);
                setDebtAge(prev => {
                  const newData = [...prev];
                  newData[index].name = e.target.value || '';
                  return newData;
                });
              }}
              onBlur={save}
              onPressEnter={save}
              maxLength={60}
              style={{ textAlign: 'center' }}
              disabled={disabled || disabledIsDelayDay}
            />
          );
        },
      rules: (_, index) => [
        {
          required: true,

          validator: (_, value) => {
            if (!value || value.trim() === '') {
              markError(debtAge[index].key, 'name');
              return Promise.reject('Vui lòng nhập tên nhóm.');
            }
            clearError(debtAge[index].key, 'name');
            return Promise.resolve();
          },
        },
      ],
    },
    {
      key: 'fromDay',
      title: 'Từ ngày',
      dataIndex: 'fromDay',
      alwaysEditable: true,
      editable: true,
      width: 100,
      inputType: 'custom',
      renderEditComponent:
        (_, index) =>
        ({ save, disabled, onChange }) => {
          index === 0 ? onChange(1) : onChange(Number(debtAge[index - 1]?.toDay) + 1);
          return (
            <Input
              onChange={e => onChange(e.target.value)}
              onKeyDown={handleKeyDownEnterNumber}
              maxLength={5}
              onBlur={save}
              onPressEnter={save}
              placeholder="Nhập từ ngày"
              disabled={disabled || true}
            />
          );
        },
    },
    {
      key: 'toDay',
      title: 'Đến ngày',
      dataIndex: 'toDay',
      alwaysEditable: true,
      editable: true,
      width: 100,
      inputType: 'custom',
      renderEditComponent:
        (_, index) =>
        ({ save, disabled, onChange }) => {
          const isFinalRange = debtAge[index]?.isFinalRange;
          const disabledIsDelayDay = index === 0 && initialData?.delayDay ? true : false;
          onChange(debtAge[index]?.toDay || '');

          return (
            <Input
              onChange={e => {
                const value = e.target.value;
                onChange(value);
                if (index !== undefined) {
                  const newDataSource = debtAge.map((item, idx) => {
                    if (idx === index) return { ...item, toDay: value || '' };
                    if (idx === index + 1) return { ...item, fromDay: Number(value) + 1 || '' };
                    return item;
                  });
                  setDebtAge(newDataSource);
                }
              }}
              onKeyDown={handleKeyDownEnterNumber}
              maxLength={5}
              onBlur={save}
              onPressEnter={save}
              placeholder="Nhập đến ngày"
              disabled={disabled || isFinalRange || disabledIsDelayDay}
            />
          );
        },
      dependencies: ['fromDay'],
      rules: (_, index) => [
        ({ getFieldValue }) => ({
          validator: (_, value) => {
            const formatNumberValue = value ? Number(value) : 0;
            const prevToDay = debtAge[index - 1]?.toDay;
            const fromDay = getFieldValue('fromDay');
            if (isFinalRange && index === debtAge?.length - 1) {
              clearError(debtAge[debtAge?.length - 1].key, 'toDay');
              return Promise.resolve();
            }
            if (formatNumberValue <= fromDay) {
              markError(debtAge[index].key, 'toDay');
              return Promise.reject('Đến ngày không thể nhỏ hơn từ ngày.');
            } else if (prevToDay && formatNumberValue <= Number(prevToDay)) {
              markError(debtAge[index].key, 'toDay');
              return Promise.reject('Đến ngày phải lớn hơn đến ngày của nhóm tuổi nợ trước đó.');
            } else if (!value || value === 0) {
              markError(debtAge[index].key, 'toDay');
              return Promise.reject('Vui lòng nhập ngày kết thúc nhóm tuổi nợ.');
            }
            clearError(debtAge[index].key, 'toDay');
            return Promise.resolve();
          },
        }),
      ],
    },
    {
      key: 'interestRate',
      title: 'Tỉ lệ lãi',
      dataIndex: 'interestRate',
      alwaysEditable: true,
      editable: true,
      width: 100,
      inputType: 'custom',
      renderEditComponent:
        (_, index) =>
        ({ save, disabled, onChange }) => {
          const disabledIsDelayDay = index === 0 && initialData?.delayDay ? true : false;

          return (
            <PercentInput
              onChange={val => {
                onChange(val);
                setDebtAge(prev => {
                  const newData = [...prev];
                  newData[index].interestRate = val;
                  return newData;
                });
              }}
              onBlur={save}
              onPressEnter={save}
              placeholder="Nhập tỉ lệ lãi"
              suffix="%"
              disabled={disabled || disabledIsDelayDay}
            />
          );
        },

      rules: (_, index) => [
        {
          validator: (_, value) => {
            if (!value || value === 0) {
              markError(debtAge[index].key, 'interestRate');
              return Promise.reject('Vui lòng nhập tỉ lệ lãi hợp lệ.');
            }
            clearError(debtAge[index].key, 'interestRate');
            return Promise.resolve();
          },
        },
      ],
    },

    {
      dataIndex: 'action',
      key: 'action',
      width: '30%',
      align: 'center',
      render: (_, record, index) => {
        const isDebtBad = ItemBadDebt?.key === record.key;
        const isDisableDebtBad = ItemBadDebt?.key !== undefined && ItemBadDebt?.key !== record.key;
        const disabledIsDelayDay = index === 0 && initialData?.delayDay ? true : false;

        const handleDelete = () => {
          const newData = [...debtAge];
          newData.splice(index, 1);
          form.setFieldsValue({
            debtage: newData,
          });
          if (debtAge.length === index + 1) setIsFinalRange(false);
          setDebtAge(newData);
          setIsModified(true);
        };

        const handleChangeIsFinalRange = (e: CheckboxChangeEvent) => {
          const isChecked = e.target.checked;
          setIsFinalRange(isChecked);
          const newData = [...debtAge];
          if (isChecked) {
            newData[index].toDay = '';
          }
          newData[index].isFinalRange = isChecked;
          form.setFieldsValue({
            debtage: newData,
          });
          setDebtAge(newData);
          setIsModified(true);
          !isChecked ? markError(newData[index].key, 'toDay') : clearError(newData[index].key, 'toDay');
        };

        const handleChangeIsDebtBad = (e: CheckboxChangeEvent) => {
          const isChecked = e.target.checked;
          const newData = [...debtAge];
          newData[index].isBadDebt = isChecked;

          form.setFieldsValue({
            debtage: newData,
          });
          setDebtAge(newData);
          setIsModified(true);
        };

        return (
          <Row align={'middle'}>
            <Col span={10}>
              {debtAge.length === index + 1 && (
                <Checkbox
                  onChange={handleChangeIsFinalRange}
                  disabled={disabledIsDelayDay}
                  checked={record?.isFinalRange}
                >
                  Không giới hạn
                </Checkbox>
              )}
            </Col>
            <Col span={10}>
              <Checkbox
                onChange={handleChangeIsDebtBad}
                disabled={!!isDisableDebtBad || disabledIsDelayDay}
                checked={isDebtBad}
              >
                Ngưỡng nợ xấu
              </Checkbox>
            </Col>
            <Col span={4}>
              <Button type="text" danger onClick={handleDelete} disabled={disabledIsDelayDay}>
                Xoá
              </Button>
            </Col>
          </Row>
        );
      },
    },
  ];

  // Hàm thêm một mục mới vào cuối danh sách
  const handleAdd = () => {
    const isDebtAge = Array.isArray(debtAge) && debtAge.length > 0;
    const prevItem = debtAge[debtAge.length - 1];
    const newFromDay = prevItem?.toDay ? Number(prevItem.toDay) + 1 : 0;
    const newData: TDebtAge = {
      key: uuid(),
      fromDay: !isDebtAge ? '1' : newFromDay,
      toDay: '',
      name: '',
      interestRate: 0,
      isBadDebt: false,
      isFinalRange: false,
    };

    const newDebtAge = [...debtAge, newData];
    setDebtAge(newDebtAge);
    form.setFieldsValue({
      debtage: newDebtAge,
    });
  };
  // Hàm xử lý lưu thay đổi trên bảng
  const handleSaveDebt = (row: TDebtAge) => {
    const newData = [...debtAge];
    const index = newData.findIndex(item => row.key === item.key);
    if (index > -1) {
      const item = newData[index];
      newData.splice(index, 1, { ...item, ...row });
      setDebtAge(newData);
      form.setFieldsValue({
        debtage: newData,
      });
    }
    setIsModified(true);
  };

  const handleSubmit = async () => {
    try {
      if (hasAnyError()) {
        return;
      }
      await form.validateFields();
      const values: TDebtInterestPolicy = form.getFieldsValue(true);
      const newValues = {
        ...values,
        id,
        daysPerYear: Number(values?.daysPerYear),
        delayDay: Number(values?.delayDay),
        dueDateReminderDays: Number(values?.dueDateReminderDays),
        debtage: values?.debtage?.map(item => ({
          ...item,
          fromDay: item?.fromDay ? Number(item.fromDay) : 0,
          toDay: item?.toDay && !item?.isFinalRange ? Number(item.toDay) : undefined,
          interestRate: item?.interestRate ? Number(item.interestRate) : 0,
        })),
      };

      const res = await updatePenaltyInterest(newValues);
      if (res?.data?.statusCode === '0') {
        setIsModified(false);
        resetErrors();
      }
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };
  const handleCancel = () => {
    form.setFieldsValue(initialData);
    setDebtAge(
      dataSettingInProject?.debtage?.map((item, index) => ({
        ...item,
        isBadDebt: index === firstBadDebtIndex,
        key: item?.key || uuid(),
      })) || [],
    );
    resetErrors();
    setIsModified(false);
  };

  return (
    <Spin spinning={isFetching}>
      <Title level={5}>Nhóm tuổi nợ</Title>
      <Button
        type="default"
        icon={<PlusOutlined />}
        onClick={handleAdd}
        style={{ marginBottom: 16 }}
        disabled={isFinalRange}
      >
        Thêm nhóm tuổi nợ
      </Button>
      <Form
        form={form}
        layout="vertical"
        initialValues={{ debtage: debtAge }}
        onFinish={handleSubmit}
        onFieldsChange={() => {
          setIsModified(true);
        }}
      >
        <Item
          name="debtage"
          rules={[
            {
              required: true,
              validator: () => {
                const debtage = form.getFieldValue('debtage');
                const checkToday = !!initialData?.delayDay && !!debtage?.[0]?.toDay;
                const hasInvalid = debtage?.some((item: TDebtAge) =>
                  Object.entries(item)?.some(([key, value]) => {
                    // bỏ qua trường hợp toDay của nhóm tuổi nợ cuối cùng khi isFinalRange là true
                    if (key === 'toDay' && (item.isFinalRange === true || (!!initialData?.delayDay && value === 0))) {
                      return false;
                    }
                    return key !== 'key' && (value === undefined || value === null || value === '' || value === 0);
                  }),
                );
                if (hasInvalid && !checkToday) {
                  return Promise.reject(`Vui lòng nhập đủ thông tin nhóm tuổi nọ`);
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <EditTable columns={columns} dataSource={debtAge} handleSave={handleSaveDebt} />
        </Item>
        <Col md={12} xl={8} xs={24}>
          <Item
            name={'latePaymentFee'}
            label="Phí phạt quá hạn"
            getValueProps={value => ({
              value: value ? Number(value) : '',
            })}
            getValueFromEvent={value => {
              return value ? Number(value) : '';
            }}
            rules={[{ required: true, message: 'Vui lòng nhập phí phạt quá hạn' }]}
          >
            <CurrencyInput suffix="VNĐ" placeholder="Nhập phí phạt quá hạn" />
          </Item>
          <Item
            name={'daysPerYear'}
            label="Số ngày tính lãi/Năm"
            rules={[
              { required: true, message: 'Vui lòng nhập số ngày tính lãi trên Năm' },
              {
                validator: (_, value) => {
                  const num = Number(value);
                  if (isNaN(num) || num < 0 || num > 366) {
                    return Promise.reject('Chỉ nhập số nguyên từ 0 đến 366');
                  }
                  return Promise.resolve();
                },
              },
            ]}
          >
            <Input
              suffix="ngày"
              onKeyDown={handleKeyDownEnterNumber}
              placeholder="Nhập số ngày tính lãi/Năm"
              maxLength={3}
            />
          </Item>
          <Item
            name={'delayDay'}
            label="Số ngày miễn lãi nếu thanh toán trễ (Ngày)"
            rules={[{ required: true, message: 'Vui lòng nhập số ngày miễn lãi nếu thanh toán trễ' }]}
          >
            <Input
              suffix="ngày"
              maxLength={5}
              placeholder="Nhập số ngày miễn lãi"
              onKeyDown={handleKeyDownEnterNumber}
            />
          </Item>
          <Item name={'onlyWorkday'} valuePropName="checked">
            <Checkbox>Tính theo ngày làm việc</Checkbox>
          </Item>
          <Item name={'dueDateReminderDays'} label="Thời gian nhắc hạn trước">
            <Input
              suffix="ngày"
              maxLength={5}
              placeholder="Nhập thời gian nhắc hạn trước"
              onKeyDown={handleKeyDownEnterNumber}
            />
          </Item>
          <Item
            name={'interestJobTime'}
            label="Giờ chạy tính lãi"
            rules={[{ required: true, message: 'Vui lòng chọn giờ chạy tính lãi' }]}
            getValueProps={value => ({
              value: value ? dayjs(value, FORMAT_TIME) : null,
            })}
            getValueFromEvent={value => {
              return value ? dayjs(value, FORMAT_TIME).format(FORMAT_TIME) : null;
            }}
          >
            <TimePicker format={FORMAT_TIME} allowClear placeholder="Chọn giờ chạy tính lãi" suffixIcon="giờ" />
          </Item>
        </Col>
        {isModified && (
          <ButtonOfPageDetail handleSubmit={handleSubmit} handleCancel={handleCancel} loadingSubmit={isPending} />
        )}
      </Form>
    </Spin>
  );
};

export default PenaltyInterest;
