import React, { useState, useEffect } from 'react';
import { Form, Button, Row, Col, Typography, FormInstance, Select, Spin, Input } from 'antd';
import './styles.scss';
import SingleSelectLazy from '../../../../components/select/singleSelectLazy';

import MyDatePicker from '../../../../components/datePicker';
import dayjs from 'dayjs';
import { getListContract, getListProjectAdmin } from '../../../../service/liquidation';
import { ILiquidation, LiquidationFormValues } from '../../../../types/liquidation';
import ButtonOfPageDetail from '../../../../components/button/buttonOfPageDetail';
import { useNavigate, useParams } from 'react-router-dom';
import { LIQUIDATION_MANAGEMENT } from '../../../../configs/path';

const { Title } = Typography;
const { Option } = Select;

interface LiquidationFormProps {
  form?: FormInstance;
  onFinish?: (values: LiquidationFormValues) => void;
  loading: boolean;
  initialValues?: ILiquidation;
  isUpdate?: boolean;
}

const LiquidationForm: React.FC<LiquidationFormProps> = ({
  form: parentForm,
  onFinish,
  loading,
  isUpdate = false,
  initialValues,
}) => {
  const navigate = useNavigate();
  const id = useParams<{ id: string }>().id;
  const [internalForm] = Form.useForm();
  const form = parentForm || internalForm;
  const [selectedDate, setSelectedDate] = useState<dayjs.Dayjs | null>(null);
  const [selectedProjectId, setSelectedProjectId] = useState<string | undefined>();
  const [contractKey, setContractKey] = useState(0);
  const [projectKey, setProjectKey] = useState(0);
  const [projectDefaultValue, setProjectDefaultValue] = useState<
    { label: string | React.ReactElement | undefined; value: string | undefined } | undefined
  >(undefined);
  const [contractDefaultValue, setContractDefaultValue] = useState<
    { label: string | React.ReactElement | undefined; value: string | undefined } | undefined
  >(undefined);
  const [isModified, setIsModified] = useState(false);
  const isDisabledForm = initialValues?.status === 'approved';

  useEffect(() => {
    if (initialValues) {
      let liquidationDate = dayjs();
      if (initialValues.liquidationDate) {
        const dateValue = dayjs(initialValues.liquidationDate);
        if (dateValue.isValid()) {
          liquidationDate = dateValue;
          setSelectedDate(dateValue);
        }
      }

      if (initialValues.contract.primaryTransaction.project) {
        const project = initialValues.contract.primaryTransaction.project;
        setProjectDefaultValue({
          label: `${project.code} - ${project.name}` || '',
          value: project.id || '',
        });
        setSelectedProjectId(project.id);

        form.setFieldsValue({
          project: project,
        });
      }

      if (initialValues.contract) {
        setContractDefaultValue({
          label: initialValues.contract.name || '',
          value: initialValues.contract.id || '',
        });

        form.setFieldsValue({
          contractId: initialValues.contract.id,
        });
      }

      form.setFieldsValue({
        type: initialValues.type || 'termination',
        liquidationDate: liquidationDate,
        escrowTicketCode: initialValues.contract.primaryTransaction.escrowTicketCode,
        liquidationDocumentName: initialValues.liquidationDocumentName,
        customerName: initialValues.contract?.primaryTransaction?.customer?.personalInfo?.name,
        productCode: initialValues.contract?.primaryTransaction?.propertyUnit?.code,
        code: initialValues.code,
      });

      setTimeout(() => {
        form.validateFields(['project', 'contractId']).catch(() => {});
      }, 100);
    }
  }, [isUpdate, initialValues, form]);

  const handleDateChange = (date: dayjs.Dayjs | null) => {
    if (date && dayjs.isDayjs(date) && date.isValid()) {
      setSelectedDate(date);
      form.setFieldsValue({ liquidationDate: date });
    } else if (date === null) {
      setSelectedDate(null);
      form.setFieldsValue({ liquidationDate: null });
    }
  };

  const handleTypeChange = (value: string) => {
    form.setFieldsValue({ type: value });
  };

  const handleProjectChange = (values: unknown) => {
    const projectData = values as { id?: string; code?: string; name?: string } | null;

    if (projectData && projectData.id) {
      setSelectedProjectId(projectData.id);

      form.setFieldsValue({
        project: projectData,
      });

      setProjectDefaultValue({
        label: `${projectData.code} - ${projectData.name}`,
        value: projectData.id,
      });
    } else {
      setSelectedProjectId(undefined);
      setProjectDefaultValue(undefined);
      form.setFieldsValue({ project: undefined });
      setProjectKey(prev => prev + 1);
    }

    form.setFieldsValue({
      contractId: undefined,
      escrowTicketCode: undefined,
      liquidationDocumentName: undefined,
      customerName: undefined,
      productCode: undefined,
      code: undefined,
    });

    setContractDefaultValue(undefined);
    setContractKey(prev => prev + 1);
    setIsModified(true);
  };

  const handleContractSelect = (
    contract: {
      id?: string;
      name?: string;
      primaryTransaction?: {
        escrowTicketCode?: string;
        customer?: { personalInfo?: { name?: string } };
        propertyUnit?: { code?: string };
      };
    } | null,
  ) => {
    if (contract && contract.id) {
      form.setFieldsValue({
        contractId: contract.id,
        escrowTicketCode: contract?.primaryTransaction?.escrowTicketCode,
        customerName: contract?.primaryTransaction?.customer?.personalInfo?.name,
        productCode: contract?.primaryTransaction?.propertyUnit?.code,
      });

      setContractDefaultValue({
        label: contract.name || '',
        value: contract.id || '',
      });
    } else {
      form.setFieldsValue({
        contractId: undefined,
        escrowTicketCode: undefined,
        customerName: undefined,
        productCode: undefined,
      });

      setContractDefaultValue(undefined);
    }
    setIsModified(true);
  };

  const handleFinish = (values: LiquidationFormValues) => {
    const payload = {
      id: id,
      type: values.type,
      contractId: values.contractId,
      liquidationDate: values.liquidationDate,
    };
    onFinish?.(payload);
  };
  const handleCancel = () => {
    form.resetFields();
    setIsModified(false);
    navigate(LIQUIDATION_MANAGEMENT);
  };
  const validateForm = () => {
    setIsModified(true);
  };
  useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  return (
    <Spin spinning={loading}>
      <Form
        form={form}
        onFinish={handleFinish}
        layout="vertical"
        initialValues={{
          type: 'termination',
          liquidationDate: dayjs(),
        }}
        onValuesChange={validateForm}
        className="liquidation-form"
      >
        <Row gutter={64}>
          <Col span={12}>
            <Title level={5} style={{ marginBottom: 16 }}>
              Thông tin thanh lý
            </Title>
            <Row gutter={24}>
              <Col span={20}>
                <Form.Item label="Dự án" name="project" rules={[{ required: true, message: 'Vui lòng chọn dự án' }]}>
                  <SingleSelectLazy
                    queryKey={['get-list-project-admin']}
                    apiQuery={getListProjectAdmin}
                    placeholder="Chọn dự án"
                    key={`project-${projectKey}`}
                    keysLabel={['code', 'name']}
                    handleSelect={handleProjectChange}
                    allowClear={true}
                    defaultValues={projectDefaultValue}
                    disabled={isDisabledForm}
                  />
                </Form.Item>
              </Col>
              <Col span={20}>
                <Form.Item label="Loại thanh lý" name="type">
                  <Select placeholder="Chọn loại đề nghị" onChange={handleTypeChange} disabled={isDisabledForm}>
                    <Option value="termination">Thanh lý chấm dứt</Option>
                    <Option value="transfer">Thanh lý chuyển nhượng</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={20}>
                <Form.Item label="Ngày thanh lý" name="liquidationDate">
                  <MyDatePicker
                    placeholder="Chọn ngày thanh lý"
                    onDateChange={handleDateChange}
                    value={selectedDate}
                    disabled={isDisabledForm}
                  />
                </Form.Item>
              </Col>
            </Row>
          </Col>

          {/* Chỉ hiển thị khi có selectedProjectId hoặc isUpdate = true */}
          {(selectedProjectId || (isUpdate && initialValues)) && (
            <Col span={12}>
              <Title level={5} style={{ marginBottom: 16 }}>
                Hợp đồng thanh lý
              </Title>
              <Row gutter={24}>
                <Col span={20}>
                  <Form.Item
                    label="Tên hợp đồng"
                    name="contractId"
                    rules={[{ required: true, message: 'Vui lòng chọn hợp đồng' }]}
                  >
                    {' '}
                    <SingleSelectLazy
                      placeholder="Chọn hợp đồng"
                      disabled={(!selectedProjectId && !isUpdate) || isDisabledForm}
                      apiQuery={getListContract}
                      queryKey={['get-list-contract', selectedProjectId]}
                      key={`contract-${contractKey}`}
                      keysLabel={['name']}
                      moreParams={{ projectIds: selectedProjectId }}
                      enabled={!!selectedProjectId || isUpdate}
                      allowClear={true}
                      defaultValues={contractDefaultValue}
                      handleSelect={handleContractSelect}
                    />
                  </Form.Item>
                </Col>
                <Col span={20}>
                  {' '}
                  <Form.Item label="Phiếu YCĐCO" name="escrowTicketCode">
                    <Input disabled={true} placeholder="Nhập phiếu YCĐCO" />
                  </Form.Item>
                </Col>
                <Col span={20}>
                  {' '}
                  <Form.Item label="Tên BB thanh lý" name="liquidationDocumentName">
                    <Input disabled={true} placeholder="Nhập tên BB thanh lý" />
                  </Form.Item>
                </Col>
                <Col span={20}>
                  {' '}
                  <Form.Item label="Tên khách hàng" name="customerName">
                    <Input disabled={true} placeholder="Nhập tên khách hàng" />
                  </Form.Item>
                </Col>
                <Col span={20}>
                  {' '}
                  <Form.Item label="Mã sản phẩm" name="productCode">
                    <Input disabled={true} placeholder="Nhập mã sản phẩm" />
                  </Form.Item>
                </Col>
                <Col span={20}>
                  {' '}
                  <Form.Item label="Mã BB thanh lý" name="code">
                    <Input disabled={true} placeholder="Nhập mã BB thanh lý" />
                  </Form.Item>
                </Col>
              </Row>
            </Col>
          )}
        </Row>{' '}
        {isUpdate ? (
          <ButtonOfPageDetail
            handleSubmit={() => form.submit()}
            handleCancel={handleCancel}
            loadingSubmit={loading}
            isShowModal={isModified}
            disabled={isDisabledForm}
          />
        ) : (
          <div className="create-footer">
            <div className="button-create">
              <Button type="primary" htmlType="submit" loading={loading} size="small" disabled={isDisabledForm}>
                Lưu
              </Button>
            </div>
          </div>
        )}
      </Form>
    </Spin>
  );
};

export default LiquidationForm;
