import { Card, Col, Flex, Row, Space, Statistic, Typography } from 'antd';
import dayjs from 'dayjs';
import { useCallback, useState } from 'react';
import BreadCrumbComponent from '../../components/breadCrumb';
import { LEAD_DASHBOARD } from '../../configs/path';
import { EXPLOIT_STATUS_MAP, FORMAT_DATE_TIME } from '../../constants/common';
import { useFetch } from '../../hooks';
import { getListOfExploitLeadDashboard } from '../../service/lead';
import { TLeadDashboard, TLeadDashboardData } from '../../types/leadAssigned';
import { formatEncryptPhone } from '../../utilities/regex';
import ModalDetailOfLead from '../leadsAssignedManagement/component/modalDetailOfLead';
import './styles.scss';

const { Title, Text } = Typography;
const { Meta } = Card;
const { Countdown } = Statistic;

const countdownClock = (lead: TLeadDashboard) => {
  const startTime = new Date(lead.assignedDate || 0).getTime();

  return startTime + lead?.assignDuration * 60 * 1000;
};

const LeadDashboard = () => {
  const [idDetail, setIdDetail] = useState<string>('');
  const desiredOrder = ['assign', 'processing', 'done', 'cancel'];

  const { data, isLoading } = useFetch<TLeadDashboardData>({
    api: getListOfExploitLeadDashboard,
    queryKeyArr: ['list-lead-dashboard'],
    withFilter: false,
    cacheTime: 1,
  });
  const dataLeadDashboard = data?.data?.data;

  const orderedData = desiredOrder?.reduce<Record<string, TLeadDashboard[]>>((acc, key) => {
    // Safely access the property using optional chaining without type casting
    const leads = (dataLeadDashboard?.[key as keyof typeof dataLeadDashboard] as TLeadDashboard[] | undefined) || [];
    if (Array.isArray(leads)) {
      acc[key] = leads;
    }
    return acc;
  }, {});

  const handleCloseModalDetail = useCallback(() => {
    setIdDetail('');
  }, []);

  return (
    <div className="wrap-lead-dashboard">
      <BreadCrumbComponent />
      <Row gutter={16}>
        {Object.entries(orderedData)?.map(([key, leads]) => {
          const card = EXPLOIT_STATUS_MAP[key as keyof typeof EXPLOIT_STATUS_MAP];
          return (
            <Col key={key} span={6}>
              <Card loading={isLoading}>
                <Meta
                  title={
                    <Flex justify="space-between" className="title-card">
                      <Title level={5} style={{ color: card?.color }}>
                        {card.label}
                      </Title>
                      <Title level={5} style={{ color: card?.color }}>
                        {leads.length}
                      </Title>
                    </Flex>
                  }
                />
                <Space direction="vertical" size="middle">
                  {leads?.map(lead => (
                    <Card
                      key={lead?.id}
                      className="card-lead-item"
                      hoverable
                      onClick={() => setIdDetail(lead?.id)}
                      style={{ backgroundColor: card?.backgroundColor }}
                    >
                      <Title level={5}>{lead?.code}</Title>
                      <Text>{dayjs(lead?.createdDate).format(FORMAT_DATE_TIME)}</Text>
                      <br />
                      <Text>{lead?.name}</Text>
                      <br />
                      <Text>{formatEncryptPhone(lead?.phone)}</Text>
                      <br />
                      {lead?.exploitStatus === 'processing' || lead?.exploitStatus === 'assign' ? (
                        <div className="container-expireTime" style={{ background: card?.color }}>
                          {lead?.assignDuration > 0 ? (
                            <Countdown value={countdownClock(lead)} format="HH:mm:ss" />
                          ) : null}
                        </div>
                      ) : null}
                    </Card>
                  ))}
                </Space>
              </Card>
            </Col>
          );
        })}
      </Row>
      <ModalDetailOfLead
        id={idDetail}
        handleCancel={handleCloseModalDetail}
        urlRedirect={LEAD_DASHBOARD}
        keyOfDetailQuery={['list-lead-dashboard']}
      />
    </div>
  );
};

export default LeadDashboard;
