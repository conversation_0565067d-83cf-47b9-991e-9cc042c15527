import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

type SellProgramState = {
  selectedProgramNames: string[];
  setSelectedProgramNames: (names: string[]) => void;
  ArrSalesProgramIds: string[];
  ArrSetSalesProgramIds: (ids: string[]) => void;
};

export const useSellProgramStore = create(
  persist<SellProgramState>(
    set => ({
      selectedProgramNames: [],
      setSelectedProgramNames: names => set({ selectedProgramNames: names }),
      ArrSalesProgramIds: [],
      ArrSetSalesProgramIds: ids => set({ ArrSalesProgramIds: ids }),
    }),
    {
      name: 'sell-program-store',
      storage: createJSONStorage(() => localStorage),
    },
  ),
);
