import { Form } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useMemo, useState } from 'react';
import { useLocation } from 'react-router-dom';
import DatePickerFilter from '../../../components/datePicker/DatePickerFilter';
import DropdownFilterSearch from '../../../components/dropdown/dropdownFilterSearch';
import { FORMAT_DATE_API } from '../../../constants/common';
import useFilter from '../../../hooks/filter';
import { TFilterOwnershipCertificateConfig } from '../../../types/ownershipCertificateConfig';

const FilterOwnershipCertificateConfig = () => {
  const { search } = useLocation();
  const [form] = Form.useForm();
  const params = useMemo(() => new URLSearchParams(search), [search]);
  const [filter, setFilter] = useFilter();
  const [initialValues, setInitialValues] = useState<TFilterOwnershipCertificateConfig>();
  const [isOpenFilter, setIsOpenFilter] = useState(false);

  useEffect(() => {
    if (params) {
      const initialValue = {
        startCreatedDate: params.get('startCreatedDate') ? dayjs(params.get('startCreatedDate')) : undefined,
        endCreatedDate: params.get('endCreatedDate') ? dayjs(params.get('endCreatedDate')) : undefined,
      };
      setInitialValues(initialValue);
      form.setFieldsValue(initialValue);
    }
  }, [form, params]);

  const handleSubmitFilter = (values: TFilterOwnershipCertificateConfig) => {
    const newFilter: Record<string, unknown> = {
      startCreatedDate: values?.startCreatedDate ? dayjs(values?.startCreatedDate).format(FORMAT_DATE_API) : null,
      endCreatedDate: values?.endCreatedDate ? dayjs(values?.endCreatedDate).format(FORMAT_DATE_API) : null,
    };
    setFilter({ ...filter, page: '1', ...newFilter });
    setIsOpenFilter(false);
  };

  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };

  const handleClearFilters = () => {
    setInitialValues(undefined);
    setTimeout(() => {
      setIsOpenFilter(false);
    }, 100);
  };

  return (
    <>
      <DropdownFilterSearch
        rootClassName="wrapper-filter"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        showParams={true}
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        initialValues={initialValues}
        form={form}
        onClearFilters={handleClearFilters}
        extraFormItems={
          <>
            <DatePickerFilter startDate="startCreatedDate" endDate="endCreatedDate" />
          </>
        }
      />
    </>
  );
};

export default FilterOwnershipCertificateConfig;
