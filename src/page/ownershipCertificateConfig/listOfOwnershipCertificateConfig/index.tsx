import { MutationFunction } from '@tanstack/react-query';
import { App, Button } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { useMemo, useState } from 'react';
import BreadCrumbComponent from '../../../components/breadCrumb';
import ConfirmDeleteModal from '../../../components/modal/specials/ConfirmDeleteModal';
import { modalConfirm } from '../../../components/modal/specials/ModalConfirm';
import TableComponent from '../../../components/table';
import { ActionsColumns } from '../../../components/table/components/ActionsColumns';
import { OWNERSHIP_CERTIFICATE_CONFIGURATION } from '../../../configs/path';
import { PRIMARY_CONTRACT_PERMISSION } from '../../../constants/permissions/primaryContract';
import { useCheckPermissions, useFetch, useUpdateField } from '../../../hooks';

import CreateOwnershipCertificateConfig from '../createOwnershipCertificateConfig';
import { Columns } from './Columns';
import FilterOwnershipCertificateConfig from './FilterOwnershipCertificateConfig';
import { TListOfOwnershipCertificateConfig } from '../../../types/ownershipCertificateConfig';
import { useStoreOwnershipCertificateConfig } from '../storeOwnershipCertificateConfig';
import {
  changeStatusOwnershipCertificateConfig,
  deleteOwnershipCertificateConfig,
  getAllOwnershipCertificateConfig,
} from '../../../service/ownershipCertificateConfig';

const ListOfOwnershipCertificateConfig = () => {
  const { modal } = App.useApp();
  const [isOpenModalDelete, setIsOpenModalDelete] = useState<boolean>(false);
  const [currentRecord, setCurrentRecord] = useState<TListOfOwnershipCertificateConfig>();
  const { setOpenModalCreate } = useStoreOwnershipCertificateConfig();

  const {
    handoverChangeStatus,
    handoverGetId,
    handoverCreate,
    handoverDelete: isDelete,
  } = useCheckPermissions(PRIMARY_CONTRACT_PERMISSION);

  const { data, isLoading } = useFetch<TListOfOwnershipCertificateConfig[]>({
    api: getAllOwnershipCertificateConfig,
    queryKeyArrWithFilter: ['list-ownership-certificate'],
  });
  const dataSourceOwnershipCertificateConfig = data?.data?.data?.rows;

  const { mutateAsync, isPending } = useUpdateField({
    keyOfListQuery: ['list-ownership-certificate'],
    apiQuery: changeStatusOwnershipCertificateConfig,
    isMessageError: false,
  });

  const columnsActions: ColumnsType<TListOfOwnershipCertificateConfig> = useMemo(() => {
    return [
      ...Columns,
      {
        title: 'Hành động',
        dataIndex: 'action',
        key: 'action',
        width: '100px',
        align: 'center',
        render: (_: unknown, record: TListOfOwnershipCertificateConfig) => {
          const textModalConfirmActive = record?.isActive === 1 ? 'Vô hiệu hoá' : 'Kích hoạt';
          const handleActivePolicy = () => {
            return modalConfirm({
              modal: modal,
              loading: isPending,
              title: `${textModalConfirmActive}  thiết lập bàn giao`,
              content: `Bạn có muốn ${textModalConfirmActive} thiết lập bàn giao này không?`,
              handleConfirm: async () => {
                await mutateAsync({ id: record?.id });
              },
            });
          };
          const handleDeleteRole = () => {
            setIsOpenModalDelete(true);
            setCurrentRecord(record);
          };

          const openViewDetail = () => {
            window.open(`${OWNERSHIP_CERTIFICATE_CONFIGURATION}/${record?.id}`, '_blank', 'noopener,noreferrer');
          };

          return (
            <ActionsColumns
              handleViewDetail={handoverGetId ? openViewDetail : undefined}
              textModalConfirmActive={textModalConfirmActive}
              handleActive={handoverChangeStatus ? handleActivePolicy : undefined}
              handleDelete={isDelete ? handleDeleteRole : undefined}
            />
          );
        },
      },
    ];
  }, [handoverChangeStatus, handoverGetId, isDelete, isPending, modal, mutateAsync]);

  const handleOpenModalCreate = () => {
    setOpenModalCreate(true);
  };
  const handleCancelModalCreate = () => {
    setOpenModalCreate(false);
  };

  return (
    <>
      <BreadCrumbComponent />
      <div className="header-filter" style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
        <FilterOwnershipCertificateConfig />
        {handoverCreate && (
          <Button type="primary" onClick={handleOpenModalCreate}>
            Tạo mới
          </Button>
        )}
      </div>
      <TableComponent
        className="table-time-config-default-list"
        columns={columnsActions}
        queryKeyArr={['list-ownership-certificate']}
        dataSource={dataSourceOwnershipCertificateConfig}
        loading={isLoading}
        rowKey={'id'}
      />
      <ConfirmDeleteModal
        label="Bộ thiết lập Bàn giao"
        open={isOpenModalDelete}
        apiQuery={deleteOwnershipCertificateConfig as MutationFunction<unknown, unknown>}
        keyOfListQuery={['list-ownership-certificate']}
        onCancel={() => setIsOpenModalDelete(false)}
        idDetail={currentRecord?.id}
        title="Xóa bộ thiết lập Bàn giao"
        description="Vui lòng nhập lý do muốn thiết lập bàn giao này"
        fieldNameReason="reasonDelete"
      />
      <CreateOwnershipCertificateConfig onCancel={handleCancelModalCreate} />
    </>
  );
};

export default ListOfOwnershipCertificateConfig;
