.modal-handover {
  .ant-modal-content {
    padding: 20px 0;
    .ant-modal-title {
      padding: 0 24px;
      border-bottom: 1px solid #d9d9d9;
      margin-bottom: 0;
      padding-bottom: 18px;
    }
    .ant-modal-body {
      padding: 16px 24px 0 24px;
      .ant-form {
        .group-period {
          .ant-form-item {
            flex: 1;
          }
        }
        .ant-form-item-required::after {
          display: none !important;
        }
      }
    }
    .ant-modal-footer {
      padding: 0 20px;
      margin-top: 0;
    }
  }
  .row-handover {
    align-items: baseline;
  }
  .delete-handover {
    text-align: center;
  }
}
.handover-table {
  .ant-table-tbody {
    .ant-table-row-level-0 {
      background-color: #0000000f;
    }
  }
}
