import { Collapse, Form, FormInstance } from 'antd';
import {
  defaultEmailTemplateCerHandedOver,
  defaultEmailTemplateForCerReadyHandover,
  defaultEmailTemplateForEligible,
  useStoreOwnershipCertificateConfig,
} from '../../storeOwnershipCertificateConfig';
import NotificationOwnershipCertificateConfig from './NotificationOwnershipCertificateConfig';
import '../styles.scss';

interface NotificationTabProps {
  form: FormInstance;
}
const NotificationTab = (props: NotificationTabProps) => {
  const { form } = props;

  const { initialValueNoti, setIsModified } = useStoreOwnershipCertificateConfig();

  const validateForm = () => {
    setIsModified(true);
  };

  return (
    <Form
      form={form}
      layout="vertical"
      name="formNotification"
      initialValues={{
        ...initialValueNoti,
        emailForEligible: initialValueNoti?.emailForEligible
          ? initialValueNoti?.emailForEligible
          : {
              emailTemplate: defaultEmailTemplateForEligible,
            },
        emailCerHandedOver: initialValueNoti?.emailCerHandedOver
          ? initialValueNoti?.emailCerHandedOver
          : {
              emailTemplate: defaultEmailTemplateCerHandedOver,
            },
        emailForCerReadyHandover: initialValueNoti?.emailForCerReadyHandover
          ? initialValueNoti?.emailForCerReadyHandover
          : {
              emailTemplate: defaultEmailTemplateForCerReadyHandover,
            },
      }}
      onValuesChange={validateForm}
    >
      <Collapse
        className="notification-config-collapse"
        ghost
        expandIconPosition="end"
        defaultActiveKey={['1']}
        style={{ background: '#fff' }}
      >
        <Collapse.Panel header={<h4>Sản phẩm đủ điều kiện</h4>} key="1">
          <NotificationOwnershipCertificateConfig fieldName="emailForEligible" />
        </Collapse.Panel>
        <Collapse.Panel header={<h4>Sản phẩm đã có sổ - đợi bàn giao</h4>} key="2">
          <NotificationOwnershipCertificateConfig fieldName="emailCerHandedOver" />
        </Collapse.Panel>
        <Collapse.Panel header={<h4>Sản phẩm đã bàn giao sổ</h4>} key="3">
          <NotificationOwnershipCertificateConfig fieldName="emailForCerReadyHandover" />
        </Collapse.Panel>
      </Collapse>
    </Form>
  );
};

export default NotificationTab;
