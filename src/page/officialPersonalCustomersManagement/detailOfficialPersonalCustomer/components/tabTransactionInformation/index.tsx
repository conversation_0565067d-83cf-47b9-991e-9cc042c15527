import { Flex, Tabs, TabsProps } from 'antd';
import FilterSearch from './filterSearch';
import './styles.scss';
import { columnsHistoryTab, columnsProductsTab } from './columns';
import TableExpandComponent from '../../../../../components/tableExpand';
import { useFetch } from '../../../../../hooks';
import {
  TTransitionHistory,
  TTransitionInformation,
  TTreeTransitionHistory,
  TTreeTransitionInformation,
} from '../../../../../types/customers';
import { getListTransactionHistory, getListTransactionInformation } from '../../../../../service/officialCustomers';
import { useParams } from 'react-router-dom';

function TabTransactionInformation() {
  const { id } = useParams();
  const {
    data: listTransactionInformation,
    isLoading,
    isPlaceholderData,
    isFetching,
  } = useFetch<TTransitionInformation[]>({
    queryKeyArrWithFilter: ['get-transaction-information'],
    api: getListTransactionInformation,
    moreParams: { id: id },
  });

  const {
    data: listTransactionHistory,
    isLoading: isLoadingHistory,
    isPlaceholderData: isPlaceholderDataHistory,
    isFetching: isFetchingHistory,
  } = useFetch<TTransitionInformation[]>({
    queryKeyArrWithFilter: ['get-history-transaction-information'],
    api: getListTransactionHistory,
    moreParams: { id: id },
  });

  const buildTreeByProject = (items: TTransitionInformation[]) => {
    const tree: Record<string, TTreeTransitionInformation> = {};
    const result: TTreeTransitionInformation[] = [];
    items?.forEach(item => {
      const projectId = item?.project?.id;

      // If project doesn't exist in tree, initialize it
      if (!tree[projectId as string]) {
        tree[projectId as string] = { ...item.project, nameProject: item?.project?.name, children: [] };
      }

      // Add item under its corresponding project
      tree[projectId as string]?.children?.push({ ...item, key: item?.id });
    });

    // Convert tree object into an array of grouped projects
    result.push(...Object.values(tree));
    return result;
  };

  const buildTreeByProjectAndProduct = (items: TTransitionHistory[]) => {
    const result: TTreeTransitionHistory[] = [];

    items?.forEach(item => {
      const projectId = item?.project?.id;
      const productCode = item?.propertyUnit?.code ?? '-';

      if (!projectId || !productCode) return; // Validate data

      // Find or create project entry
      let projectNode = result.find(p => p.id === projectId);
      if (!projectNode) {
        projectNode = {
          ...item.project,
          key: projectId,
          nameProject: item?.project?.name,
          children: [],
        };
        result.push(projectNode);
      }

      // Find or create product entry inside the project
      let productNode = projectNode?.children?.find(p => p.key === productCode);
      if (!productNode) {
        productNode = {
          key: productCode,
          code: productCode,
          children: [],
        };
        projectNode?.children?.push(productNode);
      }

      // Add the current item to the product group
      productNode?.children?.push({ ...item, key: item?.id });
    });

    return result;
  };

  const treeListTransactionInformation = buildTreeByProject(
    listTransactionInformation?.data?.data as TTransitionInformation[],
  );

  const treeListTransactionHistory = buildTreeByProjectAndProduct(
    listTransactionHistory?.data?.data?.rows as TTransitionInformation[],
  );

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: `Danh sách sản phẩm (${listTransactionInformation?.data?.data?.length || 0})`,
      children: (
        <TableExpandComponent
          id={'table-transaction-information'}
          columns={columnsProductsTab}
          dataSource={treeListTransactionInformation}
          isLoading={isLoading}
          isFetching={isFetching}
          isPlaceholderData={isPlaceholderData}
        />
      ),
    },
    {
      key: '2',
      label: `Lịch sử giao dịch (${listTransactionHistory?.data?.data?.rows?.length || 0})`,
      children: (
        <TableExpandComponent
          id={'table-transaction-history'}
          columns={columnsHistoryTab}
          dataSource={treeListTransactionHistory}
          level={2}
          isLoading={isLoadingHistory}
          isFetching={isFetchingHistory}
          isPlaceholderData={isPlaceholderDataHistory}
        />
      ),
    },
  ];

  return (
    <>
      <div className="header-content">
        <Flex gap={17}>
          <FilterSearch />
        </Flex>
      </div>
      <div className="table-official-customers-personal-transaction-information">
        <Tabs defaultActiveKey="1" items={items} />
      </div>
    </>
  );
}

export default TabTransactionInformation;
