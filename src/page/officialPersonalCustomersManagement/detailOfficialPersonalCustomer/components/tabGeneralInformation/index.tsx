import { Checkbox, Col, DatePicker, Form, Input, Radio, Row, Select, Typography } from 'antd';
import { FormProps } from 'antd/lib';
import { CheckboxChangeEvent } from 'antd/lib/checkbox';
import dayjs from 'dayjs';
import React from 'react';
import { useParams } from 'react-router-dom';
import ButtonOfPageDetail from '../../../../../components/button/buttonOfPageDetail';
import CurrencyInput from '../../../../../components/input/CurrencyInput';
import { OptionType } from '../../../../../components/select/selectEmployees';
import SelectAddress from '../../../../../components/selectAddress';
import {
  FORMAT_DATE,
  FORMAT_DATE_TIME,
  OPTIONS_GENDER,
  OPTIONS_RELATIONSHIP,
  REGEX_EMAIL,
} from '../../../../../constants/common';
import { FetchResponse, useCheckPermissions, useUpdateField } from '../../../../../hooks';
import { handleErrors } from '../../../../../service/error/errorsService';
import { updateAllOfficialCustomer, updateCommonOfficialCustomer } from '../../../../../service/officialCustomers';
import { ICustomers } from '../../../../../types/customers';
import { handleKeyDownEnterNumber } from '../../../../../utilities/regex';
import BankInfo from '../bankInfo';
import { PERMISSION_CUSTOMER } from '../../../../../constants/permissions/customer';
import Identities from '../identities';

const { Item } = Form;
const { Title, Text } = Typography;
const { TextArea } = Input;

type Props = {
  dataDetail?: FetchResponse<ICustomers> | undefined;
  initialValues?: ICustomers | undefined;
  valuesShareEmail?: OptionType[];
  yearOnly?: boolean;
  isModified?: boolean;
  setIsModified: React.Dispatch<React.SetStateAction<boolean>>;
  setYearOnly: React.Dispatch<React.SetStateAction<boolean>>;
};

function TabGeneralInformation({ dataDetail, initialValues, yearOnly, isModified, setIsModified, setYearOnly }: Props) {
  const { id } = useParams();
  const [form] = Form.useForm();
  const cloneAddress = Form.useWatch(['info', 'cloneAddress'], form);
  const address = Form.useWatch(['address'], form);
  const rootAddress = Form.useWatch(['rootAddress'], form);

  const { customerUpdateAll, customerUpdateCommon } = useCheckPermissions(PERMISSION_CUSTOMER);
  const data = dataDetail?.data?.data;

  const { mutateAsync, isPending } = useUpdateField({
    apiQuery: customerUpdateAll ? updateAllOfficialCustomer : updateCommonOfficialCustomer,
    keyOfListQuery: ['get-official-customers-personal'],
    keyOfDetailQuery: ['get-detail-official-customer-personal', id],
    // checkDuplicate: true,
  });

  // cảnh báo khi thoát trang khi chưa lưu dữ liệu
  React.useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  const handleCancel = () => {
    form.resetFields();
    setIsModified(false);
  };

  const validateForm = () => {
    setIsModified(true);
  };

  const handleCloneAddress = React.useCallback(
    (e: CheckboxChangeEvent) => {
      if (e.target.checked) {
        form.setFieldsValue({
          rootAddress: form.getFieldValue('address'),
        });
      }
    },
    [form],
  );

  React.useEffect(() => {
    if (cloneAddress) {
      form.setFieldsValue({
        rootAddress: form.getFieldValue('address'),
      });
    }
  }, [cloneAddress, form, address]);

  const onFinish: FormProps['onFinish'] = async (values: ICustomers) => {
    const checkChangePhone = form.isFieldTouched(['personalInfo', 'phone']);

    const newData = {
      id: id,
      company: {
        shortName: values?.company?.shortName,
      },
      taxCode: values?.taxCode,
      bankInfo: values?.bankInfo?.map(item => ({
        ...item,
        name: item?.bank?.label,
        branchCode: item?.bank?.branchCode,
        code: item?.bank?.code,
        bank: undefined,
      })),
      info: {
        ...values?.info,
        birthdayYear:
          typeof values?.info?.birthdayYear !== 'string' ? values?.info?.birthdayYear?.format('YYYY') : null,
        birthday: typeof values?.info?.birthday !== 'string' ? values?.info?.birthday?.format(FORMAT_DATE) : null,
        address: values?.address,
        rootAddress: values?.rootAddress,
      },
      takeNote: values?.takeNote,
      personalInfo: {
        identities: values?.personalInfo?.identities?.map(item => ({
          ...item,
          type: typeof item?.type !== 'string' ? item?.type?.value : item?.type,
          date: typeof item?.date !== 'string' ? item?.date?.format(FORMAT_DATE) : null,
        })),
        email: values?.personalInfo?.email,
        phone: values?.personalInfo?.phone,
        // position: values?.personalInfo?.position,
        job: values?.personalInfo?.job,
        incomeSource: values?.personalInfo?.incomeSource,
        income:
          typeof values?.personalInfo?.income === 'string'
            ? parseInt(values?.personalInfo?.income)
            : values?.personalInfo?.income,
        relationshipStatus: values?.personalInfo?.relationshipStatus,
        shortName: values?.personalInfo?.shortName,
        name: values?.personalInfo?.name,
      },
      type: 'individual',
    }; //updateAll

    const continueUpdate = !(checkChangePhone && data?.personalInfo?.phone !== values?.personalInfo?.phone);
    const response = await mutateAsync({ ...newData, continueUpdate });
    const responseData = response?.data;

    if (responseData) {
      switch (responseData?.statusCode) {
        case '0':
          setIsModified(false);
          break;
        default:
          handleErrors(responseData);
      }
    }
  };

  React.useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);
  return (
    <>
      {initialValues ? (
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          onValuesChange={validateForm}
          initialValues={initialValues}
          className="space-y-6"
        >
          <Row gutter={{ md: 24, lg: 40, xl: 80, xxl: 126 }}>
            <Col span={24}></Col>
            <Col xs={24} md={12}>
              <Title level={5}>Thông tin chung</Title>
              <Item label="Mã số khách hàng" name="code" required>
                <Input placeholder="Mã số khách hàng" disabled />
              </Item>

              <Row gutter={24}>
                <Col xs={24} sm={12}>
                  <Item
                    name={['personalInfo', 'name']}
                    label="Họ và tên"
                    rules={[{ required: true, message: 'Vui lòng nhập họ và tên' }]}
                  >
                    <Input
                      maxLength={60}
                      placeholder="Nhập họ và tên"
                      onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                        e.target.value = e.target.value.toUpperCase(); // Chuyển đổi trực tiếp
                      }}
                      disabled={customerUpdateAll ? !customerUpdateAll : customerUpdateCommon}
                    />
                  </Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Item label="Tên ngắn" name={['personalInfo', 'shortName']}>
                    <Input maxLength={25} placeholder="Nhập tên ngắn" />
                  </Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Item
                    name={['personalInfo', 'phone']}
                    label="Số điện thoại"
                    rules={[
                      {
                        required: true,
                        message: 'Vui lòng nhập số điện thoại',
                      },
                      {
                        pattern: /^0\d*$/,
                        message: 'Số điện thoại phải bắt đầu bằng số 0 và chỉ chứa chữ số!',
                      },
                    ]}
                  >
                    <Input
                      maxLength={15}
                      onKeyDown={handleKeyDownEnterNumber}
                      placeholder="Nhập số điện thoại"
                      disabled={!customerUpdateAll}
                    />
                  </Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Item label="Mã Business Partner" name="bpID">
                    <Input placeholder="Mã Business Partner" disabled />
                  </Item>
                </Col>
              </Row>

              <Item
                name={['info', 'gender']}
                label="Giới tính"
                className="item-gender"
                labelCol={{ span: 5 }}
                labelAlign="left"
                layout="horizontal"
                rules={[{ required: true, message: 'Vui lòng nhập giới tính' }]}
              >
                <Radio.Group options={OPTIONS_GENDER} />
              </Item>

              <Row gutter={24}>
                <Col xs={24} sm={12}>
                  <Item
                    name={['info', 'onlyYear']}
                    label="Ngày sinh"
                    layout="horizontal"
                    labelCol={{ span: 10 }}
                    labelAlign="left"
                    valuePropName="checked"
                  >
                    <Checkbox
                      checked={yearOnly}
                      style={{ marginLeft: '4px' }}
                      onChange={e => setYearOnly(e.target.checked)}
                    >
                      Chỉ năm sinh
                    </Checkbox>
                  </Item>
                </Col>
                <Col xs={24} sm={12} className="item-birthday">
                  {yearOnly ? (
                    <Item name={['info', 'birthdayYear']}>
                      <DatePicker picker="year" format="YYYY" placeholder="YYYY" />
                    </Item>
                  ) : (
                    <Item name={['info', 'birthday']}>
                      <DatePicker format={FORMAT_DATE} placeholder="dd/mm/yyyy" />
                    </Item>
                  )}
                </Col>
              </Row>
              <Row gutter={24}>
                <Col xs={24} sm={12}>
                  <Item
                    label="Địa chỉ email"
                    name={['personalInfo', 'email']}
                    rules={[{ pattern: REGEX_EMAIL, message: 'Địa chỉ email sai định dạng' }]}
                  >
                    <Input maxLength={25} placeholder="Nhập địa chỉ email" />
                  </Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Item label="Mã số thuế" name="taxCode">
                    <Input maxLength={15} placeholder="Nhập mã số thuế" />
                  </Item>
                </Col>
                <Identities form={form} setIsModified={setIsModified} customerRole={!customerUpdateAll} />
              </Row>

              <Title level={5}>Thông tin khác</Title>
              <Row gutter={24}>
                {/* <p style={{ marginBottom: 8 }}> Tài khoản thanh toán</p> */}
                <BankInfo />
                <Col xs={24} sm={12}>
                  <Item label="Ngành nghề" name={['personalInfo', 'job']}>
                    <Input maxLength={50} placeholder="Nhập ngành nghề" />
                  </Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Item label="Thu nhập / tháng (VNĐ)" name={['personalInfo', 'income']}>
                    <CurrencyInput placeholder="Nhập khoảng thu nhập" />
                  </Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Item label="Nguồn thu nhập" name={['personalInfo', 'incomeSource']}>
                    <Input placeholder="VD: công việc hành chính" maxLength={255} />
                  </Item>
                </Col>

                <Col xs={24} sm={12}>
                  <Item label="Tình trạng hôn nhân" name={['personalInfo', 'relationshipStatus']}>
                    <Select placeholder="Chọn tình trạng hôn nhân" options={OPTIONS_RELATIONSHIP} />
                  </Item>
                </Col>
              </Row>

              <Title level={5}>Địa chỉ thường trú</Title>
              <Item label="Địa chỉ" name={'address'}>
                <SelectAddress
                  placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
                  parentName={'address'}
                  address={initialValues?.address}
                  handleAddressChange={validateForm}
                  isDisable={!customerUpdateAll}
                />
              </Item>
              <Item name={['address', 'address']}>
                <Input placeholder="Địa chỉ cụ thể" disabled={!customerUpdateAll} />
              </Item>

              <Title level={5}>Địa chỉ liên lạc</Title>
              <Item name={['info', 'cloneAddress']} valuePropName="checked">
                <Checkbox onChange={handleCloneAddress}>Sử dụng địa chỉ thường trú</Checkbox>
              </Item>
              <Item label="Địa chỉ" name="rootAddress">
                <SelectAddress
                  placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
                  parentName={'rootAddress'}
                  address={rootAddress}
                  handleAddressChange={validateForm}
                />
              </Item>
              <Item name={['rootAddress', 'address']}>
                <Input placeholder="Địa chỉ cụ thể" />
              </Item>
            </Col>
            <Col xs={24} md={12}>
              <Title level={5} style={{ marginBottom: '35px' }}>
                Ghi chú
              </Title>
              <Item name="takeNote">
                <TextArea rows={5} maxLength={500} placeholder="Nhập ghi chú nhanh" />
              </Item>
              <div className="info">
                <Row gutter={24}>
                  <Col lg={6} xs={8}>
                    <Text disabled>Ngày cập nhật: </Text>
                  </Col>
                  <Col lg={18} xs={16}>
                    <Text disabled>
                      {dayjs(data?.updatedDate).format(FORMAT_DATE_TIME)}&nbsp;&nbsp;{' '}
                      {`${data?.updatedByObj?.username || ''} - ${data?.updatedByObj?.fullName || ''}`}
                    </Text>
                  </Col>
                </Row>
                <Row gutter={24}>
                  <Col lg={6} xs={8}>
                    <Text disabled>Ngày tạo: </Text>
                  </Col>
                  <Col lg={18} xs={16}>
                    <Text disabled>
                      {dayjs(data?.createdDate).format(FORMAT_DATE_TIME)}&nbsp;&nbsp;{' '}
                      {`${data?.createdByObj?.username || ''} - ${data?.createdByObj?.fullName || ''}`}
                    </Text>
                  </Col>
                </Row>
              </div>
            </Col>
          </Row>
        </Form>
      ) : (
        <></>
      )}
      {isModified && (
        <ButtonOfPageDetail handleSubmit={() => form.submit()} handleCancel={handleCancel} loadingSubmit={isPending} />
      )}
    </>
  );
}
export default TabGeneralInformation;
