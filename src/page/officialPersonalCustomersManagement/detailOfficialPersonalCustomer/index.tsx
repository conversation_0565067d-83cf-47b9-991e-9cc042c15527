import { Form, Tabs } from 'antd';
import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import BreadCrumbComponent from '../../../components/breadCrumb';
import { getDetailOfficialCustomer } from '../../../service/officialCustomers';
import { ICustomers, IDocumentCustomer, TIdentities } from '../../../types/customers';
import './styles.scss';
import { useFetch } from '../../../hooks';
import TabGeneralInformation from './components/tabGeneralInformation';
import dayjs from 'dayjs';
import { FORMAT_DATE, OPTIONS_IDENTITIES } from '../../../constants/common';
import { OptionType } from '../../../components/select/selectEmployees';
import { DefaultOptionType } from 'antd/lib/select';
import DocumentComponent from '../../../components/document';
import {
  addCustomerDocument,
  addItemsCustomerDocument,
  deleteCustomerDocument,
  deleteItemCustomerDocument,
  getCustomerDocument,
  getDocumentCustomerItems,
  updateCustomerDocument,
} from '../../../service/customers';
import TabTransactionInformation from './components/tabTransactionInformation';

const DetailPersonalCustomer = () => {
  const { id } = useParams();
  const [form] = Form.useForm();
  const cloneAddress = Form.useWatch(['info', 'cloneAddress'], form);
  const address = Form.useWatch(['address'], form);

  const [initialValues, setInitialValues] = useState<ICustomers>();
  const [valuesShareEmail, setValuesShareEmail] = React.useState<OptionType[]>();
  const [yearOnly, setYearOnly] = React.useState(false);
  const [isModified, setIsModified] = useState(false);

  const { data: dataDetail } = useFetch<ICustomers>({
    api: () => id && getDetailOfficialCustomer(id),
    queryKeyArr: ['get-detail-official-customer-personal', id],
    enabled: !!id,
    cacheTime: 10,
  });
  const data = dataDetail?.data?.data;

  React.useEffect(() => {
    if (data) {
      const initialData = {
        ...data,
        bankInfo: Array.isArray(data?.bankInfo) ? data.bankInfo : [],
        info: {
          ...data.info,
          birthday: data?.info?.birthday ? dayjs(data.info.birthday, FORMAT_DATE) : null,
          birthdayYear: data?.info?.birthdayYear ? dayjs(data.info.birthdayYear, 'YYYY') : null,
          onlyYear: data?.info?.birthdayYear ? true : false,
        },
        personalInfo: {
          ...data.personalInfo,
          income: data?.personalInfo?.income ? data?.personalInfo?.income?.toString() : undefined,
          identities: data?.personalInfo?.identities?.map((item: TIdentities) => {
            const getIdentityByValue = (value: string): DefaultOptionType => {
              return OPTIONS_IDENTITIES
                ? OPTIONS_IDENTITIES.find(option => option.value === value) || { label: 'Unknown', value: '' }
                : {};
            };

            const newTypeObject: DefaultOptionType = getIdentityByValue(item.type as string);
            return {
              ...item,
              typeObject: newTypeObject,
              date: item?.date ? dayjs(item?.date, FORMAT_DATE) : undefined,
            };
          }),
        },
        address: {
          ...data?.info?.address,
        },
        rootAddress: {
          ...data?.info?.rootAddress,
        },
      };
      setInitialValues(initialData as ICustomers);
      setYearOnly(!!data.info?.birthdayYear);
      setValuesShareEmail(data?.share?.emails?.map(item => ({ label: item, value: item })) || []);
    }
  }, [data, form, setInitialValues]);

  // cảnh báo khi thoát trang khi chưa lưu dữ liệu
  useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  useEffect(() => {
    if (cloneAddress) {
      form.setFieldsValue({
        rootAddress: form.getFieldValue('address'),
      });
    }
  }, [cloneAddress, form, address]);

  useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  const itemsTabOfficialCustomerPersonal = [
    {
      key: '1',
      label: 'Thông tin khách hàng',
      children: (
        <TabGeneralInformation
          dataDetail={dataDetail}
          // dataProvinces={dataProvinces}
          initialValues={initialValues}
          valuesShareEmail={valuesShareEmail}
          yearOnly={yearOnly}
          isModified={isModified}
          setIsModified={setIsModified}
          setYearOnly={setYearOnly}
        />
      ),
    },
    // {
    {
      key: '2',
      label: 'Tài liệu',
      children: (
        <DocumentComponent<IDocumentCustomer>
          // modelId={id}
          keyQueryList={'getDocument'}
          keyQueryItems={'getDocumentItems'}
          modelIdFieldName={'customer'}
          getDocument={getCustomerDocument}
          getDocumentItems={getDocumentCustomerItems}
          addDocument={addCustomerDocument}
          updateDocument={updateCustomerDocument}
          addItemsDocument={addItemsCustomerDocument}
          deleteDocument={deleteCustomerDocument}
          deleteItemDocument={deleteItemCustomerDocument}
        />
      ),
    },
    { key: '3', label: 'Thông tin giao dịch', children: <TabTransactionInformation /> },
  ];

  return (
    <div className="wrapper-detail-personal-customer">
      <BreadCrumbComponent titleBread={data?.personalInfo?.name} />
      <Tabs defaultActiveKey="1" onChange={() => {}} items={itemsTabOfficialCustomerPersonal} />
    </div>
  );
};

export default DetailPersonalCustomer;
