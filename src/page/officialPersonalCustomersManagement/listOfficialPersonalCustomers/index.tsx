import { Flex, TableColumnsType } from 'antd';
import { useMemo, useState } from 'react';
import BreadCrumbComponent from '../../../components/breadCrumb';
import TableComponent from '../../../components/table';
import { ActionsColumns } from '../../../components/table/components/ActionsColumns';
import { OFFICIAL_CUSTOMER_PERSONAL } from '../../../configs/path';
import { useFetch } from '../../../hooks';
import { ICustomers } from '../../../types/customers';
import { columns } from './columns';
import FilterSearch from './components/filterSearch';
import ModalCreateCustomer from './components/modalCreate';
import ModalShareCare from './components/modalShareCare/ModalShareCare';
import './styles.scss';
import { getListOfficialCustomerPersonal } from '../../../service/officialCustomers';

function ListPersonalCustomers() {
  // const [selectedRows, setSelectedRows] = useState<ICustomers[]>([]);
  const [isOpenModalShare, setIsOpenModalShare] = useState(false);
  const [isOpenModalCreate, setIsOpenModalCreate] = useState(false);

  const {
    data: listOfficialCustomer,
    isLoading,
    isPlaceholderData,
    isFetching,
  } = useFetch<ICustomers[]>({
    queryKeyArrWithFilter: ['get-official-customers-personal'],
    api: getListOfficialCustomerPersonal,
  });

  const columnActions: TableColumnsType<ICustomers> = useMemo(() => {
    return [
      ...columns,
      {
        title: 'Hành động',
        dataIndex: 'action',
        key: 'action',
        width: '100px',
        align: 'center',
        fixed: 'right',
        render: (_, record: ICustomers) => {
          const openViewDetail = () => {
            window.open(`${OFFICIAL_CUSTOMER_PERSONAL}/${record?.id}`, '_blank', 'noopener,noreferrer');
          };

          return (
            <ActionsColumns
              handleViewDetail={openViewDetail}
              // textModalConfirmActive={textModalConfirmActive}
              // handleActive={handleActiveCustomer}
              // handleDelete={handleDeleteCustomer}
            />
          );
        },
      },
    ];
  }, []);

  const handleCancelModalShare = () => {
    setIsOpenModalShare(false);
  };

  const handleCancelModalCreate = () => {
    setIsOpenModalCreate(false);
  };
  return (
    <div className="wrapper-list-personal-customers">
      <BreadCrumbComponent />
      <div className="header-content">
        <Flex gap={17}>
          <FilterSearch />
        </Flex>
      </div>
      <div className="table-personal-customers">
        <TableComponent
          queryKeyArr={['get-official-customers-personal']}
          columns={columnActions}
          loading={isLoading || isPlaceholderData || isFetching}
          dataSource={listOfficialCustomer?.data?.data?.rows ?? []}
          rowKey="id"
        />
      </div>
      <ModalCreateCustomer isOpen={isOpenModalCreate} handleCancel={handleCancelModalCreate} />
      <ModalShareCare
        onClose={handleCancelModalShare}
        isOpen={isOpenModalShare}
        onSubmitted={() => console.log('first')}
      />
    </div>
  );
}

export default ListPersonalCustomers;
