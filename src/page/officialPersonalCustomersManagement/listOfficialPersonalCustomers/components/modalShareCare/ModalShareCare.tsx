import { Button, Modal, Typography } from 'antd';
import SelectEmployees from '../../../../../components/select/selectEmployees';
import './styles.scss';

const { Text } = Typography;

interface ModalShareCareProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmitted: () => void;
}

function ModalShareCare(props: ModalShareCareProps) {
  const { isOpen, onSubmitted, onClose } = props;

  return (
    <Modal
      className="modal-share-care"
      open={isOpen}
      title="Chia sẻ chăm sóc"
      width={380}
      onCancel={onClose}
      confirmLoading
      maskClosable={false}
      footer={
        <Button type="primary" onClick={onSubmitted}>
          Chia sẻ
        </Button>
      }
    >
      <Text>Nhân viên</Text>
      <SelectEmployees isOpen={isOpen} />
    </Modal>
  );
}

export default ModalShareCare;
