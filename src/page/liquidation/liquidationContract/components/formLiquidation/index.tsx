import React, { useState, useEffect } from 'react';
import {
  Form,
  Button,
  Row,
  Col,
  Typography,
  FormInstance,
  Select,
  Spin,
  Input,
  DatePicker,
  Checkbox,
  Radio,
  message,
  notification,
} from 'antd';
import './styles.scss';

import dayjs from 'dayjs';
import { ILiquidation, LiquidationFormValues } from '../../../../../types/liquidation';
import { useNavigate, useParams } from 'react-router-dom';
import { LIQUIDATION_CONTRACT_MANAGEMENT } from '../../../../../configs/path';
import SingleSelectLazy from '../../../../../components/select/singleSelectLazy';
import {
  getLiquidationProposalByContract,
  getListContract,
  getListProjectAdmin,
} from '../../../../../service/liquidation';
import MyDatePicker from '../../../../../components/datePicker';
import { FORMAT_DATE, OPTIONS_GENDER } from '../../../../../constants/common';
import SelectAddress from '../../../../../components/selectAddress';
import ButtonOfPageDetail from '../../../../../components/button/buttonOfPageDetail';
const { Title } = Typography;
const { Option } = Select;

interface LiquidationFormProps {
  form?: FormInstance;
  onFinish?: (values: LiquidationFormValues) => void;
  loading: boolean;
  initialValues?: ILiquidation;
  isUpdate?: boolean;
}

const LiquidationContractForm: React.FC<LiquidationFormProps> = ({
  form: parentForm,
  onFinish,
  loading,
  isUpdate = false,
  initialValues,
}) => {
  const navigate = useNavigate();
  const id = useParams<{ id: string }>().id;
  const [internalForm] = Form.useForm();
  const form = parentForm || internalForm;
  const [selectedDate, setSelectedDate] = useState<dayjs.Dayjs | null>(null);
  const [selectedProjectId, setSelectedProjectId] = useState<string | undefined>();
  const [contractKey, setContractKey] = useState(0);
  const [projectKey, setProjectKey] = useState(0);
  const [projectDefaultValue, setProjectDefaultValue] = useState<
    { label: string | React.ReactElement | undefined; value: string | undefined } | undefined
  >(undefined);
  const [contractDefaultValue, setContractDefaultValue] = useState<
    { label: string | React.ReactElement | undefined; value: string | undefined } | undefined
  >(undefined);
  const [isModified, setIsModified] = useState(false);
  const [liquidationType, setLiquidationType] = useState<string>('termination');
  const [yearOnly, setYearOnly] = useState(false);
  const rootAddress = Form.useWatch(['rootAddress'], form);
  const typeCustomer = Form.useWatch(['typeCustomer'], form);
  const isDisabledForm = initialValues?.status === 'approved' || initialValues?.status === 'waiting';
  const [currentCustomerData, setCurrentCustomerData] = useState<any>(null);

  useEffect(() => {
    if (initialValues) {
      let liquidationDate = dayjs();
      if (initialValues.liquidationDate) {
        const dateValue = dayjs(initialValues.liquidationDate);
        if (dateValue.isValid()) {
          liquidationDate = dateValue;
          setSelectedDate(dateValue);
        }
      }

      if (initialValues.proposal && initialValues.type === 'transfer') {
        setCurrentCustomerData({
          id: initialValues.proposal?.id,
          name: initialValues.proposal?.name,
        });
      }

      if (initialValues.contract.primaryTransaction.project) {
        const project = initialValues.contract.primaryTransaction.project;
        setProjectDefaultValue({
          label: `${project.code} - ${project.name}` || '',
          value: project.id || '',
        });
        setSelectedProjectId(project.id);

        form.setFieldsValue({
          project: project,
        });
      }

      if (initialValues.contract) {
        setContractDefaultValue({
          label: initialValues.contract.name || '',
          value: initialValues.contract.id || '',
        });

        form.setFieldsValue({
          contractId: initialValues.contract.id,
        });
      }
      setLiquidationType(initialValues.type || 'termination');
      let onlyYear = false;
      let birthday = undefined;
      let birthdayYear = undefined;
      let gender = undefined;

      if (initialValues.customerFormat?.info?.gender !== undefined) {
        gender =
          initialValues.customerFormat.info.gender === 0
            ? 0
            : initialValues.customerFormat.info.gender === 1
              ? 1
              : undefined;
      }

      if (initialValues.customerFormat?.info?.onlyYear && initialValues.customerFormat?.info?.birthdayYear) {
        onlyYear = true;
        birthdayYear = dayjs(initialValues.customerFormat.info.birthdayYear, 'YYYY');
      } else if (initialValues.customerFormat?.info?.birthday) {
        birthday = dayjs(initialValues.customerFormat.info.birthday, FORMAT_DATE);
      }

      form.setFieldsValue({
        type: initialValues.type || 'termination',
        liquidationDate: liquidationDate,
        escrowTicketCode: initialValues.contract.primaryTransaction.escrowTicketCode,
        liquidationDocumentName: initialValues?.name,
        customerName: initialValues.contract?.primaryTransaction?.customer?.personalInfo?.name,
        productCode: initialValues.contract?.primaryTransaction?.propertyUnit?.code,
        code: initialValues.code,
        proposalName: initialValues?.proposal?.name,

        typeCustomer: initialValues?.customerFormat?.type || 'individual',
        customerCode: initialValues?.customerFormat?.code,
        identityType: initialValues?.customerFormat?.personalInfo?.identities?.[0]?.type || 'CCCD',
        identityNo: initialValues?.customerFormat?.personalInfo?.identities?.[0]?.value,
        taxCode: initialValues?.customerFormat?.personalInfo?.identities?.[0]?.value, // Cho doanh nghiệp
        issueDate: initialValues?.customerFormat?.personalInfo?.identities?.[0]?.date
          ? dayjs(initialValues.customerFormat.personalInfo.identities[0].date, ['DD/MM/YYYY', 'YYYY-MM-DD'])
          : undefined,
        issueLocation: initialValues?.customerFormat?.personalInfo?.identities?.[0]?.place,
        personalInfo: {
          name: initialValues?.customerFormat?.personalInfo?.name || '',
          phone: initialValues?.customerFormat?.personalInfo?.phone || '',
          email: initialValues?.customerFormat?.personalInfo?.email || '',
        },
        company: {
          name: initialValues?.customerFormat?.company?.name || '',
        },
        info: {
          onlyYear: !!onlyYear,
          birthday: birthday || undefined,
          birthdayYear: birthdayYear || undefined,
          gender: gender,
        },
        address: initialValues?.customerFormat?.info.address || undefined,
        rootAddress: initialValues?.customerFormat?.info.rootAddress || undefined,
        companyAddress: initialValues?.customerFormat?.info.company?.address || undefined,
      });

      setTimeout(() => {
        form.validateFields(['project', 'contractId']).catch(() => {});
      }, 100);
    }
  }, [isUpdate, initialValues, form]);

  const handleDateChange = (date: dayjs.Dayjs | null) => {
    if (date && dayjs.isDayjs(date) && date.isValid()) {
      setSelectedDate(date);
      form.setFieldsValue({ liquidationDate: date });
    } else if (date === null) {
      setSelectedDate(null);
      form.setFieldsValue({ liquidationDate: null });
    }
  };

  const handleTypeChange = (value: string) => {
    setLiquidationType(value);
    form.setFieldsValue({ type: value });
  };

  const handleProjectChange = (values: unknown) => {
    const projectData = values as { id?: string; code?: string; name?: string } | null;

    if (projectData && projectData.id) {
      setSelectedProjectId(projectData.id);

      form.setFieldsValue({
        project: projectData,
      });

      setProjectDefaultValue({
        label: `${projectData.code} - ${projectData.name}`,
        value: projectData.id,
      });
    } else {
      setSelectedProjectId(undefined);
      setProjectDefaultValue(undefined);
      form.setFieldsValue({ project: undefined });
      setProjectKey(prev => prev + 1);
    }

    form.setFieldsValue({
      contractId: undefined,
      escrowTicketCode: undefined,
      liquidationDocumentName: undefined,
      customerName: undefined,
      productCode: undefined,
      code: undefined,
    });

    setContractDefaultValue(undefined);
    setContractKey(prev => prev + 1);
    setIsModified(true);
  };

  const handleContractSelect = async (
    contract: {
      id?: string;
      name?: string;
      primaryTransaction?: {
        escrowTicketCode?: string;
        customer?: { personalInfo?: { name?: string } };
        propertyUnit?: { code?: string };
      };
    } | null,
  ) => {
    if (contract && contract.id) {
      form.setFieldsValue({
        contractId: contract.id,
        escrowTicketCode: contract?.primaryTransaction?.escrowTicketCode,
        customerName: contract?.primaryTransaction?.customer?.personalInfo?.name,
        productCode: contract?.primaryTransaction?.propertyUnit?.code,
      });
      setContractDefaultValue({
        label: contract.name || '',
        value: contract.id || '',
      });

      try {
        const response = await getLiquidationProposalByContract(contract.id);
        const customerData = response?.data?.data;

        if (customerData && customerData.id) {
          setCurrentCustomerData(customerData);
          let onlyYear = false;
          let birthday = undefined;
          let birthdayYear = undefined;
          let gender = undefined;

          if (customerData.customerTransferFormat?.info?.gender !== undefined) {
            const genderValue = customerData.customerTransferFormat.info.gender;
            gender = genderValue === 0 ? 0 : genderValue === 1 ? 1 : undefined;
          }

          if (
            customerData.customerTransferFormat?.info?.onlyYear &&
            customerData.customerTransferFormat?.info?.birthdayYear
          ) {
            onlyYear = true;
            birthdayYear = dayjs(customerData.customerTransferFormat.info.birthdayYear, 'YYYY');
          } else if (customerData.customerTransferFormat?.info?.birthday) {
            birthday = dayjs(customerData.customerTransferFormat.info.birthday, FORMAT_DATE);
          }

          form.setFieldsValue({
            proposalName: customerData.name,
            typeCustomer: customerData.customerTransferFormat?.type || 'individual',
            customerCode: customerData.customerTransferFormat?.code,
            code: customerData.customerTransferFormat?.code,
            identityType: customerData.customerTransferFormat?.personalInfo?.identities?.[0]?.type || 'CCCD',
            identityNo: customerData.customerTransferFormat?.personalInfo?.identities?.[0]?.value,
            taxCode: customerData.customerTransferFormat?.personalInfo?.identities?.[0]?.value,
            issueDate: customerData.customerTransferFormat?.personalInfo?.identities?.[0]?.date
              ? dayjs(customerData.customerTransferFormat.personalInfo.identities[0].date, ['DD/MM/YYYY', 'YYYY-MM-DD'])
              : undefined,
            issueLocation: customerData.customerTransferFormat?.personalInfo?.identities?.[0]?.place,
            personalInfo: {
              name: customerData.customerTransferFormat?.personalInfo?.name || '',
              phone: customerData.customerTransferFormat?.personalInfo?.phone || '',
              email: customerData.customerTransferFormat?.personalInfo?.email || '',
            },
            company: {
              name: customerData.customerTransferFormat?.company?.name || '',
            },
            info: {
              onlyYear: !!onlyYear,
              birthday: birthday || undefined,
              birthdayYear: birthdayYear || undefined,
              gender: gender,
            },
            address: customerData.customerTransferFormat?.info?.address || undefined,
            rootAddress: customerData.customerTransferFormat?.info?.rootAddress || undefined,
            companyAddress: customerData.customerTransferFormat?.company?.address || undefined,
            productName: customerData.name || customerData.code,
          });

          setYearOnly(!!onlyYear);
        } else {
          form.setFieldsValue({
            typeCustomer: 'individual',
            customerCode: undefined,
            code: undefined,
            identityType: undefined,
            identityNo: undefined,
            taxCode: undefined,
            issueDate: undefined,
            issueLocation: undefined,
            personalInfo: {
              name: undefined,
              phone: undefined,
              email: undefined,
            },
            company: {
              name: undefined,
            },
            info: {
              onlyYear: undefined,
              birthday: undefined,
              birthdayYear: undefined,
              gender: undefined,
            },
            address: undefined,
            rootAddress: undefined,
            companyAddress: undefined,
            productName: undefined,
          });
        }
      } catch (error) {
        console.error('Error fetching customer data:', error);
        message.error('Không thể tải thông tin đề nghị thanh lý');
      }
    } else {
      form.setFieldsValue({
        typeCustomer: 'individual',
        customerCode: undefined,
        identityType: undefined,
        identityNo: undefined,
        taxCode: undefined,
        issueDate: undefined,
        issueLocation: undefined,
        personalInfo: {
          name: undefined,
          phone: undefined,
          email: undefined,
        },
        company: {
          name: undefined,
        },
        info: {
          onlyYear: undefined,
          birthday: undefined,
          birthdayYear: undefined,
          gender: undefined,
        },
        address: undefined,
        rootAddress: undefined,
        companyAddress: undefined,
        productName: undefined,
      });
      setContractDefaultValue(undefined);
      setYearOnly(false);
    }
    setIsModified(true);
  };

  const handleFinish = (values: LiquidationFormValues) => {
    if (values.type === 'transfer') {
      if (!currentCustomerData || !currentCustomerData.id) {
        notification.error({ message: 'Vui lòng chọn hợp đồng có đơn đề nghị' });
        return;
      }
    }
    const payload: {
      id: string | undefined;
      type: string | undefined;
      contractId: string | undefined;
      liquidationDate: dayjs.Dayjs | null | undefined;
      proposal?: { id: string };
    } = {
      id: id,
      type: values.type,
      contractId: values.contractId,
      liquidationDate: values.liquidationDate,
    };
    if (values.type === 'transfer') {
      payload.proposal = {
        id: currentCustomerData?.id || '',
      };
    }
    onFinish?.(payload);
  };
  const handleCancel = () => {
    form.resetFields();
    setIsModified(false);
    navigate(LIQUIDATION_CONTRACT_MANAGEMENT);
  };
  useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  return (
    <Spin spinning={loading}>
      <Form
        form={form}
        onFinish={handleFinish}
        layout="vertical"
        initialValues={{
          type: 'termination',
          liquidationDate: dayjs(),
        }}
        className="liquidation-form"
      >
        <Row>
          <Col span={16}>
            <Title level={5} style={{ marginBottom: 16 }}>
              Thông tin thanh lý
            </Title>
            <Row gutter={24}>
              <Col span={20}>
                <Form.Item label="Dự án" name="project" rules={[{ required: true, message: 'Vui lòng chọn dự án' }]}>
                  <SingleSelectLazy
                    queryKey={['get-list-project-admin']}
                    apiQuery={getListProjectAdmin}
                    placeholder="Chọn dự án"
                    key={`project-${projectKey}`}
                    keysLabel={['code', 'name']}
                    handleSelect={handleProjectChange}
                    allowClear={true}
                    defaultValues={projectDefaultValue}
                    disabled={isDisabledForm}
                  />
                </Form.Item>
              </Col>
              <Col span={20}>
                <Form.Item label="Loại thanh lý" name="type">
                  <Select placeholder="Chọn loại đề nghị" onChange={handleTypeChange} disabled={isDisabledForm}>
                    <Option value="termination">Thanh lý chấm dứt</Option>
                    <Option value="transfer">Thanh lý chuyển nhượng</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={20}>
                <Form.Item label="Ngày thanh lý" name="liquidationDate">
                  <MyDatePicker
                    placeholder="Chọn ngày thanh lý"
                    onDateChange={handleDateChange}
                    value={selectedDate}
                    disabled={isDisabledForm}
                  />
                </Form.Item>
              </Col>
              <Col span={20} style={{ marginTop: 50 }}>
                {liquidationType === 'transfer' && (
                  <Row>
                    <Col span={24}>
                      <Title level={5}>Thông tin khách hàng</Title>
                      <Row gutter={24}>
                        <Col span={24}>
                          <Form.Item label="Loại khách hàng" name="typeCustomer" required>
                            <Select placeholder="Chọn loại khách hàng" disabled>
                              <Option value="individual">Cá nhân</Option>
                              <Option value="business">Doanh nghiệp</Option>
                            </Select>
                          </Form.Item>
                        </Col>
                      </Row>
                      <Row gutter={24}>
                        <Col span={24}>
                          <Form.Item label="Mã khách hàng" name="customerCode">
                            <Input placeholder="Mã khách hàng" maxLength={14} disabled />
                          </Form.Item>
                        </Col>
                      </Row>

                      <p style={{ marginBottom: 8 }}>
                        Giấy tờ xác minh <span style={{ color: 'red' }}>*</span>
                      </p>
                      <Row
                        gutter={[8, 8]}
                        style={{
                          backgroundColor: 'rgba(0, 0, 0, 0.02)',
                          border: '1px solid var(--colorBorder, rgba(0, 0, 0, 0.15))',
                          padding: 20,
                          margin: 0,
                          marginBottom: 16,
                        }}
                      >
                        <Col span={6}>
                          <Form.Item label="Loại giấy tờ" name="identityType" required>
                            <Select placeholder="Chọn loại giấy tờ" disabled>
                              {typeCustomer === 'individual' ? (
                                <>
                                  <Option value="CCCD">Căn cước công dân</Option>
                                  <Option value="CMND">CMT</Option>
                                  <Option value="Hộ chiếu">Hộ chiếu</Option>
                                </>
                              ) : (
                                <Option value="MST">Mã số thuế</Option>
                              )}
                            </Select>
                          </Form.Item>
                        </Col>
                        <Col span={6}>
                          <Form.Item
                            label="Số giấy tờ"
                            name={typeCustomer === 'business' ? 'taxCode' : 'identityNo'}
                            required
                          >
                            <Input placeholder="Nhập số giấy tờ" maxLength={60} disabled />
                          </Form.Item>
                        </Col>
                        <Col span={6}>
                          <Form.Item label="Ngày cấp" name="issueDate" required>
                            <DatePicker
                              placeholder="Chọn ngày cấp"
                              format={FORMAT_DATE}
                              disabledDate={current => current && current > dayjs().endOf('day')}
                              disabled
                            />
                          </Form.Item>
                        </Col>
                        <Col span={6}>
                          <Form.Item label="Nơi cấp" name="issueLocation" required>
                            <Select
                              placeholder="Chọn nơi cấp"
                              onChange={value => {
                                form.setFieldsValue({
                                  issueLocation: value || undefined,
                                });
                              }}
                              disabled
                            />
                          </Form.Item>
                        </Col>
                      </Row>

                      <Row gutter={16}>
                        <Col span={12}>
                          <Form.Item
                            label={typeCustomer === 'business' ? 'Tên công ty' : 'Tên khách hàng'}
                            name={typeCustomer === 'business' ? ['company', 'name'] : ['personalInfo', 'name']}
                            required
                          >
                            <Input
                              placeholder={typeCustomer === 'business' ? 'Nhập tên công ty' : 'Nhập tên khách hàng'}
                              disabled
                            />
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item label="Mã khách hàng" name="customerCode">
                            <Input placeholder="Mã khách hàng" disabled />
                          </Form.Item>
                        </Col>
                      </Row>

                      <Row gutter={16}>
                        {typeCustomer === 'business' && (
                          <Col span={12}>
                            <Form.Item
                              label="Tên người đại diện"
                              name={['personalInfo', 'name']}
                              rules={[{ required: true, message: 'Vui lòng nhập tên người đại diện' }]}
                            >
                              <Input
                                placeholder="Nhập tên người đại diện"
                                maxLength={60}
                                onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                                  e.target.value = e.target.value.toUpperCase();
                                }}
                              />
                            </Form.Item>
                          </Col>
                        )}

                        <Col span={12}>
                          <Form.Item label="Số điện thoại" name={['personalInfo', 'phone']} required>
                            <Input placeholder="Nhập số điện thoại" maxLength={15} disabled />
                          </Form.Item>
                        </Col>
                        {typeCustomer === 'individual' && (
                          <Col span={12}>
                            <Form.Item
                              label="Địa chỉ email"
                              name={['personalInfo', 'email']}
                              rules={[{ type: 'email', message: 'Địa chỉ email sai định dạng' }]}
                            >
                              <Input placeholder="Nhập địa chỉ email" maxLength={25} disabled />
                            </Form.Item>
                          </Col>
                        )}
                      </Row>

                      <Row gutter={16}>
                        <Col span={14}>
                          <Row gutter={24}>
                            <Col span={12}>
                              <Form.Item
                                layout="horizontal"
                                label="Ngày sinh"
                                name={['info', 'onlyYear']}
                                valuePropName="checked"
                              >
                                <Checkbox
                                  onChange={e => setYearOnly(e.target.checked)}
                                  checked={yearOnly}
                                  style={{ marginLeft: 30 }}
                                  disabled
                                >
                                  Chỉ năm sinh
                                </Checkbox>
                              </Form.Item>
                            </Col>
                            <Col span={12}>
                              {yearOnly ? (
                                <Form.Item name={['info', 'birthdayYear']}>
                                  <DatePicker picker="year" format="YYYY" placeholder="YYYY" disabled />
                                </Form.Item>
                              ) : (
                                <Form.Item name={['info', 'birthday']}>
                                  <DatePicker format={FORMAT_DATE} placeholder={FORMAT_DATE} disabled />
                                </Form.Item>
                              )}
                            </Col>
                          </Row>
                        </Col>

                        <Col span={10}>
                          <Form.Item required name={['info', 'gender']} label="Giới tính" layout="horizontal">
                            <Radio.Group options={OPTIONS_GENDER} style={{ marginLeft: 30 }} disabled />
                          </Form.Item>
                        </Col>
                      </Row>
                      {/* Địa chỉ công ty */}
                      {typeCustomer === 'business' ? (
                        <>
                          <Title level={5}>Địa chỉ công ty</Title>
                          <Row gutter={16}>
                            <Col span={24}>
                              <Form.Item label="Địa chỉ" name={'companyAddress'} className="input-address">
                                <SelectAddress
                                  placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
                                  parentName={'companyAddress'}
                                  address={form.getFieldValue('companyAddress')}
                                  isDisable={true}
                                />
                              </Form.Item>
                            </Col>
                            <Col span={24} className="address">
                              <Form.Item name={['companyAddress', 'address']}>
                                <Input placeholder="Nhập địa chỉ cụ thể" maxLength={155} disabled />
                              </Form.Item>
                            </Col>
                          </Row>
                        </>
                      ) : null}

                      {/* Địa chỉ thường trú */}
                      {typeCustomer === 'business' ? (
                        <Title level={5}>Địa chỉ thường trú người đại diện</Title>
                      ) : (
                        <Title level={5}>Địa chỉ thường trú</Title>
                      )}
                      <Row gutter={16}>
                        <Col span={24}>
                          <Form.Item label="Địa chỉ" name={['address']} className="input-address">
                            <SelectAddress
                              placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
                              parentName={'address'}
                              address={form.getFieldValue('address')}
                              isDisable={true}
                            />
                          </Form.Item>
                        </Col>
                        <Col span={24} className="address">
                          <Form.Item name={['address', 'address']}>
                            <Input placeholder="Nhập địa chỉ cụ thể" maxLength={155} disabled />
                          </Form.Item>
                        </Col>
                      </Row>

                      {/* Địa chỉ liên lạc */}
                      {typeCustomer === 'business' ? (
                        <Title level={5}>Địa chỉ liên lạc người đại diện</Title>
                      ) : (
                        <Title level={5}>Địa chỉ liên lạc</Title>
                      )}

                      <Row gutter={16}>
                        <Col span={24}>
                          <Form.Item label="Địa chỉ" name={['rootAddress']} className="input-address">
                            <SelectAddress
                              placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
                              parentName="rootAddress"
                              address={rootAddress}
                              isDisable={true}
                            />
                          </Form.Item>
                        </Col>
                        <Col span={24} className="address">
                          <Form.Item name={['rootAddress', 'address']}>
                            <Input placeholder="Nhập địa chỉ cụ thể" maxLength={155} disabled />
                          </Form.Item>
                        </Col>
                      </Row>
                    </Col>
                  </Row>
                )}
              </Col>
            </Row>
          </Col>

          {/* Chỉ hiển thị khi có selectedProjectId hoặc isUpdate = true */}
          {(selectedProjectId || (isUpdate && initialValues)) && (
            <Col span={8}>
              <Title level={5} style={{ marginBottom: 16 }}>
                Hợp đồng thanh lý
              </Title>
              <Row>
                <Col span={20}>
                  <Form.Item
                    label="Tên hợp đồng"
                    name="contractId"
                    rules={[{ required: true, message: 'Vui lòng chọn hợp đồng' }]}
                  >
                    <SingleSelectLazy
                      placeholder="Chọn hợp đồng"
                      disabled={(!selectedProjectId && !isUpdate) || isDisabledForm}
                      apiQuery={getListContract}
                      queryKey={['get-list-contract', selectedProjectId]}
                      key={`contract-${contractKey}`}
                      keysLabel={['name']}
                      moreParams={{ projectIds: selectedProjectId }}
                      enabled={!!selectedProjectId || isUpdate}
                      allowClear={true}
                      defaultValues={contractDefaultValue}
                      handleSelect={handleContractSelect}
                    />
                  </Form.Item>
                </Col>
                <Col span={20}>
                  <Form.Item label="Phiếu YCĐCO" name="escrowTicketCode">
                    <Input disabled={true} placeholder="Nhập phiếu YCĐCO" />
                  </Form.Item>
                </Col>
                <Col span={20}>
                  <Form.Item label="Tên BB thanh lý" name="liquidationDocumentName">
                    <Input disabled={true} placeholder="Nhập tên BB thanh lý" />
                  </Form.Item>
                </Col>
                <Col span={20}>
                  <Form.Item label="Tên khách hàng" name="customerName">
                    <Input disabled={true} placeholder="Nhập tên khách hàng" />
                  </Form.Item>
                </Col>
                <Col span={20}>
                  <Form.Item label="Mã sản phẩm" name="productCode">
                    <Input disabled={true} placeholder="Nhập mã sản phẩm" />
                  </Form.Item>
                </Col>
                <Col span={20}>
                  <Form.Item label="Mã BB thanh lý" name="code">
                    <Input disabled={true} placeholder="Nhập mã BB thanh lý" />
                  </Form.Item>
                </Col>
                {liquidationType === 'transfer' && (
                  <Col span={20}>
                    <Form.Item label="Đơn đề nghị thanh lý" name="proposalName">
                      <Input disabled={true} placeholder="Nhập đơn đề nghị thanh lý" />
                    </Form.Item>
                  </Col>
                )}
              </Row>
            </Col>
          )}
        </Row>
        {isUpdate ? (
          <ButtonOfPageDetail
            handleSubmit={() => form.submit()}
            handleCancel={handleCancel}
            loadingSubmit={loading}
            isShowModal={isModified}
            disabled={isDisabledForm}
          />
        ) : (
          <div className="create-footer">
            <div className="button-create">
              <Button type="primary" htmlType="submit" loading={loading} size="small" disabled={isDisabledForm}>
                Lưu
              </Button>
            </div>
          </div>
        )}
      </Form>
    </Spin>
  );
};

export default LiquidationContractForm;
