import React from 'react';
import { But<PERSON>, Form, Typography } from 'antd';
import { useCreateField, useFetch, useUpdateField } from '../../../../hooks';
import BreadCrumbComponent from '../../../../components/breadCrumb';
import { Link, useNavigate, useParams } from 'react-router-dom';
import { ILiquidation, LiquidationFormValues } from '../../../../types/liquidation';
import {
  approveLiquidation,
  getDetailLiquidation,
  requestApproveLiquidation,
  updateLiquidation,
} from '../../../../service/liquidation';
import FPTLogo from '../../../../assets/images/Default_Logo_Project.png';
import { LIQUIDATION_CONTRACT_MANAGEMENT, PROJECTS_MANAGEMENT } from '../../../../configs/path';
import StatusTag from '../../../contractManagement/components/StatusTag';
import LiquidationContractForm from '../components/formLiquidation';

interface LiquidationDetailProps {
  initialData?: ILiquidation;
}
const { Title, Text } = Typography;

const LiquidationContractDetail: React.FC<LiquidationDetailProps> = ({ initialData }) => {
  const { id } = useParams<{ id: string }>();
  const [form] = Form.useForm();
  const navigate = useNavigate();

  const { data, isLoading, isFetching } = useFetch<ILiquidation>({
    queryKeyArr: ['get-detail-liquidation'],
    api: () => id && getDetailLiquidation(id || ''),
    enabled: !!id,
    cacheTime: 10,
  });
  const liquidation = data?.data?.data || initialData;

  const { mutateAsync: update, isPending: isUpdateLiquidation } = useUpdateField<LiquidationFormValues>({
    keyOfListQuery: ['get-list-liquidation'],
    keyOfDetailQuery: ['get-detail-liquidation'],
    apiQuery: updateLiquidation,
    isMessageError: false,
    messageSuccess: 'Chỉnh sửa biên bản thanh lý thành công!',
  });

  const { mutateAsync: requestApprove, isPending: isPendingApproveLiquidation } = useCreateField<{
    id: string;
    status: string;
  }>({
    keyOfListQuery: ['get-list-liquidation'],
    keyOfDetailQuery: ['get-detail-liquidation'],
    apiQuery: requestApproveLiquidation,
    isMessageError: false,
    messageSuccess: 'Trình duyệt hợp đồng thanh lý thành công!',
  });

  const { mutateAsync: approve, isPending: isPendingApprove } = useCreateField<{ id: string; status: string }>({
    keyOfListQuery: ['get-list-liquidation'],
    keyOfDetailQuery: ['get-detail-liquidation'],
    apiQuery: approveLiquidation,
    isMessageError: false,
    messageSuccess: 'Duyệt hợp đồng thanh lý thành công!',
  });
  const { mutateAsync: reject, isPending: isPendingReject } = useCreateField<{ id: string; status: string }>({
    keyOfListQuery: ['get-list-liquidation'],
    keyOfDetailQuery: ['get-detail-liquidation'],
    apiQuery: approveLiquidation,
    isMessageError: false,
    messageSuccess: 'Từ chối hợp đồng thanh lý thành công!',
  });

  const handleSubmitForApproval = async () => {
    if (liquidation?.id) {
      const resp = await requestApprove({ id: liquidation.id, status: 'waiting' });
      if (resp?.data?.statusCode === '0' && resp?.data?.success) {
        await form.resetFields();
      }
    }
  };
  const canSubmitForApproval = liquidation?.status === 'init' || liquidation?.status === 'rejected';

  const handleFinish = async (values: LiquidationFormValues) => {
    const resp = await update({ ...values, id: liquidation?.id });
    if (resp?.data?.statusCode === '0' && resp?.data?.success) {
      await form.resetFields();
      navigate(LIQUIDATION_CONTRACT_MANAGEMENT);
    }
  };
  const canApprove = liquidation?.status === 'waiting';
  const handleApprove = async () => {
    if (liquidation?.id) {
      const resp = await approve({ id: liquidation.id, status: 'approved' });
      if (resp?.data?.statusCode === '0' && resp?.data?.success) {
        await form.resetFields();
        navigate(LIQUIDATION_CONTRACT_MANAGEMENT);
      }
    }
  };
  const handleReject = async () => {
    if (liquidation?.id) {
      const resp = await reject({ id: liquidation.id, status: 'rejected' });
      if (resp?.data?.statusCode === '0' && resp?.data?.success) {
        await form.resetFields();
        navigate(LIQUIDATION_CONTRACT_MANAGEMENT);
      }
    }
  };

  return (
    <div className="discount-policy-detail">
      <BreadCrumbComponent titleBread={data?.data?.data?.name} />
      <div className="project-card">
        <div className="project-info">
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            <Title level={5} style={{ margin: 0 }}>
              {liquidation?.name}
            </Title>
            <StatusTag status={liquidation?.status} />
          </div>
          <Text type="secondary">
            Dự án: &nbsp;
            <Link to={`${PROJECTS_MANAGEMENT}/${liquidation?.contract?.primaryTransaction?.project?.id}`}>
              {liquidation?.contract?.primaryTransaction?.project?.name || '-'}
            </Link>
          </Text>
        </div>

        <div style={{ display: 'flex', gap: '10px' }}>
          {canSubmitForApproval && (
            <Button type="primary" onClick={handleSubmitForApproval} loading={isPendingApproveLiquidation}>
              Trình và duyệt
            </Button>
          )}
          {canApprove && (
            <Button onClick={handleApprove} loading={isPendingApprove}>
              Duyệt
            </Button>
          )}
          {canApprove && (
            <Button onClick={handleReject} loading={isPendingReject}>
              Từ chối
            </Button>
          )}
          <Button>In hợp đồng</Button>
        </div>
        <div className="project-image">
          <img
            src={
              liquidation?.contract?.primaryTransaction?.project?.imageUrl
                ? `${import.meta.env.VITE_S3_IMAGE_URL}/${liquidation?.contract?.primaryTransaction?.project?.imageUrl}`
                : FPTLogo
            }
            alt="Project"
          />
        </div>
      </div>
      <LiquidationContractForm
        form={form}
        isUpdate={true}
        onFinish={handleFinish}
        initialValues={liquidation}
        loading={isLoading || isFetching || isUpdateLiquidation}
      />
    </div>
  );
};

export default LiquidationContractDetail;
