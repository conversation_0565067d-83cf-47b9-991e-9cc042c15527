import { Form, Modal } from 'antd';
import { useCallback } from 'react';
import { useCreateField } from '../../../../hooks';
import ModalComponent from '../../../../components/modal';
import { createLiquidation } from '../../../../service/liquidation';
import LiquidationContractForm from '../components/formLiquidation';

interface OfferMoneyModalProps {
  visible: boolean;
  onClose: () => void;
}

const LiquidationContractModal = ({ visible, onClose }: OfferMoneyModalProps) => {
  const [form] = Form.useForm();

  const { mutateAsync: create } = useCreateField({
    keyOfListQuery: ['get-list-liquidation'],
    apiQuery: createLiquidation,
    isMessageError: false,
    messageSuccess: 'Tạo đề nghị thanh lý thành công!',
  });

  const resetFormLiquidation = useCallback(async () => {
    await form.resetFields();
  }, [form]);

  const handleCancel = useCallback(async () => {
    if (form.isFieldsTouched()) {
      Modal.confirm({
        title: 'Xác nhận hủy',
        content: 'Dữ liệu chưa được lưu, bạn có chắc chắn muốn thoát khỏi trang không?',
        cancelText: 'Quay lại',
        onOk: async () => {
          await resetFormLiquidation();
          onClose();
        },
        okButtonProps: { type: 'default' },
        cancelButtonProps: { type: 'primary' },
      });
    } else {
      await resetFormLiquidation();
      onClose();
    }
  }, [form, onClose, resetFormLiquidation]);
  const handleCreate = useCallback(
    async (values: unknown) => {
      try {
        const resp = await create(values);
        if (resp?.data?.statusCode === '0' && resp?.data?.success) {
          await resetFormLiquidation();
          onClose();
        }
      } catch (error) {
        console.error('Create error:', error);
      }
    },
    [create, onClose, resetFormLiquidation],
  );

  return (
    <ModalComponent
      title="Tạo mới thanh lý hợp đồng"
      open={visible}
      onCancel={handleCancel}
      footer={null}
      destroyOnClose
    >
      <LiquidationContractForm form={form} onFinish={handleCreate} loading={false} />
    </ModalComponent>
  );
};

export default LiquidationContractModal;
