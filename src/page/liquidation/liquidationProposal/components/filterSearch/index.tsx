import { Form } from 'antd';
import { useCallback, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { DEFAULT_PARAMS, STATUS_LIQUIDATION } from '../../../../../constants/common';
import DropdownFilterSearch from '../../../../../components/dropdown/dropdownFilterSearch';
import { getListProjectHistory } from '../../../../../service/uploadHistory';
import { Project } from '../../../../../types/offer';
import MultiSelectStatic from '../../../../../components/select/mutilSelectStatic';
import MultiSelectLazy from '../../../../../components/select/mutilSelectLazy/index';

type TFilter = {
  status?: string;
  page?: number;
  pageSize?: number;
  projectIds?: string[] | string | null;
  search?: string;
};
interface Status {
  label: string;
  value: string;
  color?: string;
  status?: string;
  tagColor?: string;
}
const DEFAULT_PAGE = '1';
const DEFAULT_PAGE_SIZE = '10';
const PAGE_KEY = 'page';
const PAGE_SIZE_KEY = 'pageSize';

interface FilterLiquidationProps {
  onFilterChange: (filterParams: Record<string, unknown>) => void;
}

const FilterLiquidationProposal = ({ onFilterChange }: FilterLiquidationProps) => {
  const [form] = Form.useForm();
  const [isOpenFilter, setIsOpenFilter] = useState(false);
  const [searchParams, setSearchParams] = useSearchParams();
  const [filterValues, setFilterValues] = useState<Record<string, unknown>>({});
  const [listStatus, setListStatus] = useState('');

  const handleSubmitFilter = useCallback(
    (values: TFilter) => {
      const newFilter: Record<string, unknown> = {
        projectIds: values?.projectIds ?? null,
        status: listStatus ?? null,
        ...DEFAULT_PARAMS,
      };

      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const cleanParams = Object.fromEntries(Object.entries(newFilter).filter(([_, value]) => value != null));

      setFilterValues(cleanParams);

      const newSearchParams = new URLSearchParams(searchParams);
      if (Object.keys(cleanParams).length > 1) {
        newSearchParams.set(PAGE_KEY, DEFAULT_PAGE);
        newSearchParams.set(PAGE_SIZE_KEY, DEFAULT_PAGE_SIZE);
        setSearchParams(newSearchParams);
      } else {
        setSearchParams({});
      }

      onFilterChange(cleanParams);
      setIsOpenFilter(false);
    },
    [listStatus, searchParams, onFilterChange, setSearchParams],
  );

  const handleOpenChangeDropdown = useCallback((value: boolean) => {
    setIsOpenFilter(value);
  }, []);

  const handleClearFilters = useCallback(() => {
    form.resetFields();
    setFilterValues({});
    setSearchParams({});
    onFilterChange(DEFAULT_PARAMS);
    setTimeout(() => setIsOpenFilter(false), 100);
  }, [form, onFilterChange, setSearchParams]);

  const handleSearch = useCallback(
    (value: string) => {
      const newParams = { ...filterValues, [PAGE_KEY]: 1 };
      if (value) {
        (newParams as Record<string, unknown>).search = value;
        const newSearchParams = new URLSearchParams(searchParams);
        newSearchParams.set(PAGE_KEY, DEFAULT_PAGE);
        setSearchParams(newSearchParams);
      } else {
        delete (newParams as Record<string, unknown>).search;
        setSearchParams({});
      }

      setFilterValues(newParams);
      onFilterChange(newParams);
    },
    [filterValues, searchParams, setSearchParams, onFilterChange],
  );

  return (
    <>
      <DropdownFilterSearch
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        showParams={false}
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        form={form}
        onClearFilters={handleClearFilters}
        onChangeSearch={handleSearch}
        extraFormItems={
          <>
            <Form.Item label="Trạng thái" name="status">
              <MultiSelectStatic
                data={STATUS_LIQUIDATION}
                handleListSelect={(values: Status[]) => {
                  form.setFieldsValue({ status: values.map(item => item.value).join(',') });
                  setListStatus(values.map(item => item.value).join(','));
                }}
                placeholder="Chọn trạng thái"
                keysTag={'label'}
              />
            </Form.Item>
            <Form.Item label="Dự án" name="projectIds">
              <MultiSelectLazy
                enabled={isOpenFilter}
                queryKey={['get-project']}
                apiQuery={getListProjectHistory}
                keysLabel={['code', 'name']}
                keysTag={['name']}
                handleListSelect={(values: Project[]) => {
                  form.setFieldsValue({ projectIds: values.map(item => item.id).join(',') });
                }}
                placeholder="Chọn dự án"
              />
            </Form.Item>
          </>
        }
      />
    </>
  );
};
export default FilterLiquidationProposal;
