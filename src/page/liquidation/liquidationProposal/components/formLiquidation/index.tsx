import React, { useState, useEffect, useCallback } from 'react';
import {
  Form,
  Button,
  Row,
  Col,
  Typography,
  FormInstance,
  Select,
  Spin,
  Input,
  DatePicker,
  Checkbox,
  Radio,
} from 'antd';
import './styles.scss';

import dayjs from 'dayjs';
import { LiquidationFormValues } from '../../../../../types/liquidation';
import { useNavigate } from 'react-router-dom';
import { LIQUIDATION_PROPOSAL_MANAGEMENT } from '../../../../../configs/path';
import SingleSelectLazy from '../../../../../components/select/singleSelectLazy';
import { getListContract, getListProjectAdmin } from '../../../../../service/liquidation';
import SelectAddress, { AddressType } from '../../../../../components/selectAddress';
import ButtonOfPageDetail from '../../../../../components/button/buttonOfPageDetail';
import UploadFileBookingTicket from '../../../../operateSellProgram/bookingTicketPersonal/components/UploadFileBookingTicket';
import { ExtendedUploadFile } from '../../../../../types/contract/depositContract';
import { getAllCustomersByQuery } from '../../../../../service/customers';
import { FORMAT_DATE, OPTIONS_GENDER } from '../../../../../constants/common';
import { CheckboxChangeEvent } from 'antd/es/checkbox';
import { hasNonNullValue } from '../../../../../utilities/shareFunc';
import { useFetch } from '../../../../../hooks';
import { getProvinces } from '../../../../../service/address';
import MyDatePicker from '../../../../../components/datePicker';
const { Title } = Typography;
const { Option } = Select;

interface LiquidationFormProps {
  form?: FormInstance;
  onFinish?: (values: LiquidationFormValues) => void;
  loading: boolean;
  initialValues?: any;
  isUpdate?: boolean;
}

const RESET_FORM_VALUES = {
  customerCode: undefined,
  newCustomerCode: undefined,
  newCustomerName: undefined,
  phone: undefined,
  email: undefined,
  taxCode: undefined,
  companyAddress: undefined,
  files: [],
  info: {
    onlyYear: false,
    birthday: undefined,
    birthdayYear: undefined,
    gender: undefined,
    cloneAddress: false,
  },
  personalInfo: {
    name: undefined,
    phone: undefined,
    email: undefined,
  },
  company: {
    name: undefined,
  },
  address: undefined,
  rootAddress: undefined,
  companyAdress: undefined,
  identityType: undefined,
  identityNo: undefined,
  identityDate: undefined,
  identityPlace: undefined,
};

const LiquidationProposalForm: React.FC<LiquidationFormProps> = ({
  form: parentForm,
  onFinish,
  loading,
  isUpdate = false,
  initialValues,
}) => {
  const navigate = useNavigate();
  const [internalForm] = Form.useForm();
  const form = parentForm || internalForm;
  const [isModified, setIsModified] = useState(false);
  const [yearOnly, setYearOnly] = useState(false);
  const [fileList, setFileList] = useState<ExtendedUploadFile[]>([]);
  const typeCustomer = Form.useWatch('typeCustomer', form);
  const [contractKey, setContractKey] = useState(0);
  const [projectKey, setProjectKey] = useState(0);
  const [selectedProjectId, setSelectedProjectId] = useState<string | undefined>();
  const [projectDefaultValue, setProjectDefaultValue] = useState<
    { label: string | React.ReactElement | undefined; value: string | undefined } | undefined
  >(undefined);
  const [contractDefaultValue, setContractDefaultValue] = useState<
    { label: string | React.ReactElement | undefined; value: string | undefined } | undefined
  >(undefined);
  const cloneAddress = Form.useWatch(['info', 'cloneAddress'], form);
  const address = Form.useWatch(['address'], form);
  const isAddressNonNull = hasNonNullValue(address);
  const companyAddress = Form.useWatch('companyAdress', form);
  const isCompanyAdressNonNull = hasNonNullValue(companyAddress);
  const rootAddress = Form.useWatch(['rootAddress'], form);
  const isRootAddressNonNull = hasNonNullValue(rootAddress);
  const [isKHCT, setIsKHCT] = useState('');
  const [selectedDate, setSelectedDate] = useState<dayjs.Dayjs | null>(null);

  const isDisabledForm = initialValues?.status === 'approved' || initialValues?.status === 'waiting';

  const { data: dataProvinces } = useFetch<AddressType[]>({
    queryKeyArrWithFilter: ['get-list-identity'],
    api: getProvinces,
  });
  const provinces = dataProvinces?.data?.data;

  useEffect(() => {
    if (initialValues) {
      let onlyYear = false;
      let birthday = undefined;
      let birthdayYear = undefined;
      let gender = undefined;

      if (initialValues.customerTransferFormat?.info?.gender !== undefined) {
        const genderValue = initialValues.customerTransferFormat.info.gender;
        gender = genderValue === 0 ? 0 : genderValue === 1 ? 1 : undefined;
      }
      if (initialValues.customerTransferFormat?.info?.onlyYear) {
        onlyYear = true;
        birthdayYear = initialValues.customerTransferFormat?.info?.birthdayYear
          ? dayjs(initialValues.customerTransferFormat.info.birthdayYear, 'YYYY')
          : undefined;
      } else if (initialValues.customerTransferFormat?.info?.birthday) {
        birthday = initialValues.customerTransferFormat.info.birthday
          ? dayjs(initialValues.customerTransferFormat.info.birthday, FORMAT_DATE)
          : undefined;
      }
      form.setFieldsValue({
        project: initialValues.escrowTicket?.project
          ? {
              id: initialValues.escrowTicket.project.id,
              code: initialValues.escrowTicket.project.code,
              name: initialValues.escrowTicket.project.name,
            }
          : undefined,
        contractId: initialValues.escrowTicket?.contract?.id,
        liquidationName: initialValues.name,
        liquidationCode: initialValues.code,
        customerName: initialValues.escrowTicket?.customer.personalInfo?.name,
        productCode: initialValues.escrowTicket?.propertyUnit?.code,
        reasonLiquidation: initialValues.reasonLiquidation,
        createdDate: initialValues.proposalDate ? dayjs(initialValues.proposalDate) : dayjs(),
        note: initialValues.note,
        typeCustomer: initialValues.customerTransferFormat?.type || 'individual',

        // Thông tin khách hàng chuyển nhượng - ưu tiên customerTransferFormat
        personalInfo: {
          name: initialValues.customerTransferFormat?.personalInfo?.name || '',
        },
        phone: initialValues.customerTransferFormat?.personalInfo?.phone || '',
        email: initialValues.customerTransferFormat?.personalInfo?.email || '',
        customerCode: initialValues.customerTransferFormat?.code || initialValues.escrowTicket?.customer?.code,
        identityType: initialValues.customerTransferFormat?.personalInfo?.identities?.[0]?.type || 'CCCD',
        identityNo: initialValues.customerTransferFormat?.personalInfo?.identities?.[0]?.value || '',
        identityDate: initialValues?.customerTransfer?.identityDate
          ? dayjs(initialValues.customerTransfer?.identityDate)
          : dayjs(),

        identityPlace:
          initialValues.customerTransferFormat?.personalInfo?.identities?.[0]?.place ||
          initialValues.escrowTicket?.customer?.identityPlace,

        // Thông tin cá nhân
        info: {
          onlyYear: !!onlyYear,
          birthday: birthday || undefined,
          birthdayYear: birthdayYear || undefined,
          gender: gender,
          cloneAddress: initialValues.escrowTicket?.customer?.info?.useResidentialAddress || false,
        },
        address: initialValues.customerTransferFormat?.info?.address || undefined,
        rootAddress: initialValues.customerTransferFormat?.info?.rootAddress || undefined,
        files: initialValues.files || [],
      });
      if (initialValues.escrowTicket?.project) {
        setSelectedProjectId(initialValues.escrowTicket.project.id);
        setProjectDefaultValue({
          label: `${initialValues.escrowTicket.project.code} - ${initialValues.escrowTicket.project.name}`,
          value: initialValues.escrowTicket.project.id,
        });
      }

      if (initialValues.escrowTicket?.contract) {
        setContractDefaultValue({
          label: initialValues.escrowTicket.contract.code || '',
          value: initialValues.escrowTicket.contract.id,
        });
      }
      if (initialValues.proposalDate) {
        setSelectedDate(dayjs(initialValues.proposalDate));
      }
      setYearOnly(!!onlyYear);
      setFileList(initialValues.files || []);
    }
  }, [initialValues, form, typeCustomer, isUpdate]);
  const handleCancel = () => {
    form.resetFields();
    setIsModified(false);
    navigate(LIQUIDATION_PROPOSAL_MANAGEMENT);
  };
  const handleCloneAddress = useCallback(
    (e: CheckboxChangeEvent) => {
      if (e.target.checked) {
        form.setFieldsValue({
          rootAddress: form.getFieldValue('address'),
        });
      }
    },
    [form],
  );
  useEffect(() => {
    if (cloneAddress) {
      form.setFieldsValue({
        rootAddress: form.getFieldValue('address'),
      });
    }
  }, [cloneAddress, form, address]);
  useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);
  const handleChangeTypeCustomer = () => {
    form.setFieldsValue(RESET_FORM_VALUES);
    setYearOnly(false);
    setFileList([]);
  };

  const handleCustomerSelect = (customer: any) => {
    if (!customer) {
      form.setFieldsValue(RESET_FORM_VALUES);
      setYearOnly(false);
      setFileList([]);
      return;
    }

    let onlyYear = false;
    let birthday = undefined;
    let birthdayYear = undefined;
    if (customer.info?.onlyYear) {
      onlyYear = true;
      birthdayYear = customer.info?.birthdayYear ? dayjs(customer.info.birthdayYear, 'YYYY') : undefined;
    } else if (customer.info?.birthday) {
      birthday = customer.info.birthday ? dayjs(customer.info.birthday, ['DD/MM/YYYY', 'YYYY-MM-DD']) : undefined;
    }

    form.setFieldsValue({
      newCustomerName: customer?.personalInfo?.name,
      customerCode: customer?.code,
      gender: customer.info?.gender === 0 ? 'male' : customer.info?.gender === 1 ? 'female' : undefined,
      phone: customer.personalInfo?.phone,
      email: customer.personalInfo?.email,
      identityType: typeCustomer === 'individual' ? customer?.personalInfo?.identities[0]?.type : 'MST',
      identityNo: customer?.personalInfo?.identities[0]?.value,
      identityDate: customer?.personalInfo?.identities[0]?.date
        ? dayjs(customer.personalInfo.identities[0].date, ['DD/MM/YYYY', 'YYYY-MM-DD'])
        : undefined,
      identityPlace: customer?.personalInfo?.identities[0]?.place,
      taxCode: customer.taxCode,
      info: {
        onlyYear: !!onlyYear,
        birthday: birthday || undefined,
        birthdayYear: birthdayYear || undefined,
        gender: customer?.info?.gender,
        cloneAddress: customer?.info?.useResidentialAddress || false,
      },
      personalInfo: {
        name: customer.personalInfo?.name || '',
        phone: customer.personalInfo?.phone || '',
        email: customer.personalInfo?.email || '',
      },
      company: {
        name: customer.company?.name || '',
      },
      address: customer.info?.address || undefined,
      rootAddress: customer.info?.rootAddress || undefined,
      companyAdress: customer.company?.address || undefined,
    });
    setYearOnly(!!onlyYear);
    setIsModified(true);
    setIsKHCT(customer?.customerType);
  };

  const handleProjectChange = (values: unknown) => {
    const projectData = values as { id?: string; code?: string; name?: string } | null;

    if (projectData && projectData.id) {
      setSelectedProjectId(projectData.id);

      form.setFieldsValue({
        project: projectData,
      });

      setProjectDefaultValue({
        label: `${projectData.code} - ${projectData.name}`,
        value: projectData.id,
      });
    } else {
      setSelectedProjectId(undefined);
      setProjectDefaultValue(undefined);
      form.setFieldsValue({ project: undefined });
      setProjectKey(prev => prev + 1);
    }

    form.setFieldsValue({
      contractId: undefined,
      escrowTicketCode: undefined,
      liquidationDocumentName: undefined,
      customerName: undefined,
      productCode: undefined,
      code: undefined,
    });

    setContractDefaultValue(undefined);
    setContractKey(prev => prev + 1);
    setIsModified(true);
  };
  const handleContractSelect = (
    contract: {
      id?: string;
      name?: string;
      primaryTransaction?: {
        escrowTicketCode?: string;
        customer?: { personalInfo?: { name?: string } };
        propertyUnit?: { code?: string };
      };
    } | null,
  ) => {
    if (contract && contract.id) {
      form.setFieldsValue({
        contractId: contract.id,
        escrowTicketCode: contract?.primaryTransaction?.escrowTicketCode,
        customerName: contract?.primaryTransaction?.customer?.personalInfo?.name,
        productCode: contract?.primaryTransaction?.propertyUnit?.code,
      });

      setContractDefaultValue({
        label: contract.name || '',
        value: contract.id || '',
      });
    } else {
      form.setFieldsValue({
        contractId: undefined,
        escrowTicketCode: undefined,
        customerName: undefined,
        productCode: undefined,
      });

      setContractDefaultValue(undefined);
    }
    setIsModified(true);
  };
  const handleDateChange = (date: dayjs.Dayjs | null) => {
    if (date && dayjs.isDayjs(date) && date.isValid()) {
      setSelectedDate(date);
      form.setFieldsValue({ createdDate: date });
    } else if (date === null) {
      setSelectedDate(null);
      form.setFieldsValue({ createdDate: null });
    }
  };

  const handleFinish = async () => {
    const values = await form.validateFields();
    let identityIssueLocationValue: string = '';

    if (typeof values?.issueLocation === 'object' && values?.issueLocation?.value) {
      const selectedProvince = provinces?.find(p => p.code === values.issueLocation.value);
      identityIssueLocationValue = selectedProvince?.nameVN || values.issueLocation.label || '';
    } else if (typeof values?.issueLocation === 'string') {
      const selectedProvince = provinces?.find(p => p.code === values.issueLocation);
      identityIssueLocationValue = selectedProvince?.nameVN || values.issueLocation;
    } else {
      identityIssueLocationValue = values?.issueLocation || '';
    }
    const payload = {
      contractId: values.contractId,
      proposalDate: values.createdDate ? dayjs(values.createdDate).toISOString() : undefined,
      reasonLiquidation: 'Đổi tên chuyển nhượng',
      note: values.note,
      customerTransfer: {
        code: values?.code,
        name: values.personalInfo?.name,
        gender: values?.info?.gender,
        birthday: !yearOnly ? (values?.info?.birthday ? dayjs(values?.info?.birthday).format(FORMAT_DATE) : '') : '',
        phone: values?.phone,
        email: values?.email || undefined,
        identityNumber: values?.type === 'business' ? values?.taxCode : values?.identityNo,
        identityIssueDate: values?.issueDate ? dayjs(values?.issueDate).format(FORMAT_DATE) : '',
        identityIssueLocation: identityIssueLocationValue || values?.identityPlace || '',
        onlyYear: values?.info?.onlyYear,
        birthdayYear: yearOnly && values?.info?.birthdayYear ? dayjs(values?.info?.birthdayYear).format('YYYY') : '',
        identityDate: values?.identityDate ? dayjs(values?.identityDate).format(FORMAT_DATE) : '',
        identityType: values?.identityType,
        address: { ...values?.address },
        taxCode: values?.taxCode,
        position: values?.position,
        type: values?.type,
        rootAddress: { ...values.rootAddress },
        company: {
          ...values?.company,
          issueDate: values?.issueDate ? dayjs(values?.issueDate).format(FORMAT_DATE) : '',
          issueLocation: values?.issueLocation,
          address: values?.companyAddress,
        },
      },

      files: fileList.map(file => ({
        uid: file.uid || '',
        name: file.name,
        url: file.key || '',
        absoluteUrl: file?.absoluteUrl || '',
        uploadName: file.name,
      })),
      customerTransferFormat: {
        texts: {
          code: 'Code',
          active: 'Active',
          createdBy: 'CreatedBy',
          description: 'Description',
          status: 'Status',
          images: 'Images',
          createdDate: 'CreatedDate',
          modifiedBy: 'ModifiedBy',
          modifiedDate: 'ModifiedDate',
        },
        code: values?.customerCode,
        active: true,
        type: values?.typeCustomer,
        isPotential: true,
        status: '1',
        personalInfo: {
          name: values.personalInfo?.name,
          fullName: values.personalInfo?.fullName || '',
          email: values?.email || '',
          incomeSource: '',
          relationshipStatus: '',
          interestedArea: {
            country: 'Việt Nam',
            province: null,
            district: null,
            ward: null,
            address: null,
          },
          job: '',
          incomeFrom: 0,
          incomeTo: 0,
          phone: values?.phone,
          taxCode: values?.taxCode,
          identities: [
            {
              type: values?.identityType,
              value: values?.type === 'business' ? values?.taxCode : values?.identityNo,
              date: values?.identityDate ? dayjs(values?.identityDate) : '',
              place: values?.identityPlace || '',
            },
          ],
        },
        info: {
          birthday: !yearOnly ? (values?.info?.birthday ? dayjs(values?.info?.birthday).format(FORMAT_DATE) : '') : '',
          gender: values?.info?.gender,
          rootAddress: { ...values.rootAddress },
          address: { ...values?.address },
          onlyYear: values?.info?.onlyYear,
          birthdayYear: yearOnly && values?.info?.birthdayYear ? dayjs(values?.info?.birthdayYear).format('YYYY') : '',
          birthDate: {
            day: !yearOnly ? (values?.info?.birthday ? dayjs(values?.info?.birthday).format('DD') : '') : '',
            month: !yearOnly ? (values?.info?.birthday ? dayjs(values?.info?.birthday).format('MM') : '') : '',
            year: !yearOnly ? (values?.info?.birthday ? dayjs(values?.info?.birthday).format('YYYY') : '') : '',
          },
          type: values?.typeCustomer,
          company: {
            ...values?.company,
            issueDate: values?.issueDate ? dayjs(values?.issueDate).format(FORMAT_DATE) : '',
            issueLocation: values?.issueLocation,
            address: values?.companyAddress,
          },
          isPotential: true,
        },
      },
    };

    onFinish?.(payload);
  };

  return (
    <Spin spinning={loading}>
      <Form
        form={form}
        onFinish={handleFinish}
        layout="vertical"
        initialValues={{
          type: 'termination',
          liquidationDate: dayjs(),
          typeCustomer: 'individual',
          createdDate: dayjs(),
        }}
        className="liquidation-form"
      >
        <Row gutter={[50, 50]}>
          <Col span={12}>
            <div className="form-section">
              <Title level={5}>Thông tin chung</Title>

              <Form.Item label="Dự án" name="project" rules={[{ required: true, message: 'Vui lòng chọn dự án' }]}>
                <SingleSelectLazy
                  disabled={isDisabledForm}
                  key={`project-${projectKey}`}
                  queryKey={['get-list-project-admin']}
                  apiQuery={getListProjectAdmin}
                  placeholder="Chọn dự án"
                  keysLabel={['code', 'name']}
                  allowClear={true}
                  handleSelect={handleProjectChange}
                  defaultValues={projectDefaultValue}
                />
              </Form.Item>
              <Form.Item
                label="Mã hợp đồng"
                name="contractId"
                rules={[{ required: true, message: 'Vui lòng chọn mã hợp đồng' }]}
              >
                <SingleSelectLazy
                  placeholder="Chọn hợp đồng"
                  disabled={(!selectedProjectId && !isUpdate) || isDisabledForm}
                  apiQuery={getListContract}
                  queryKey={['get-list-contract', selectedProjectId]}
                  key={`contract-${contractKey}`}
                  keysLabel={['name']}
                  moreParams={{ projectIds: selectedProjectId }}
                  enabled={!!selectedProjectId || isUpdate}
                  allowClear={true}
                  defaultValues={contractDefaultValue}
                  handleSelect={handleContractSelect}
                />
              </Form.Item>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item label="Tên đề nghị thanh lý" name="liquidationName">
                    <Input placeholder="Nhập tên đề nghị thanh lý" disabled />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="Mã đề nghị" name="liquidationCode">
                    <Input placeholder="Mã đề nghị" disabled />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item label="Tên khách hàng" name="customerName">
                    <Input placeholder="Tên khách hàng" disabled />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="Mã sản phẩm" name="productCode">
                    <Input placeholder="Mã sản phẩm" disabled />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item label="Lý do" name="reasonLiquidation" required>
                <Select placeholder="Chọn lý do" defaultValue={'Đổi tên chuyển nhượng'} disabled={isDisabledForm}>
                  {/* <Option value="Hoàn tiền đặt cọc">Hoàn tiền đặt cọc</Option>
                  <Option value="Thanh lý không hoàn tiền">Thanh lý không hoàn tiền</Option> */}
                  <Option value="Đổi tên chuyển nhượng">Đổi tên chuyển nhượng</Option>
                </Select>
              </Form.Item>

              <Form.Item
                label="Ngày tạo"
                name="createdDate"
                rules={[{ required: true, message: 'Vui lòng chọn ngày tạo' }]}
              >
                <MyDatePicker
                  placeholder="Chọn ngày tạo"
                  value={selectedDate}
                  onDateChange={handleDateChange}
                  disabled={isDisabledForm}
                />
              </Form.Item>

              <Form.Item label="Ghi chú" name="note">
                <Input placeholder="Nhập ghi chú" maxLength={255} disabled={isDisabledForm} />
              </Form.Item>
            </div>

            <div className="form-section" style={{ marginTop: 24 }}>
              <Title level={5}>Khách hàng chuyển nhượng</Title>
              <Row gutter={16}>
                <Col span={24}>
                  <Form.Item
                    label="Loại khách hàng"
                    name="typeCustomer"
                    rules={[{ required: true, message: 'Vui lòng chọn loại khách hàng' }]}
                  >
                    <Select
                      placeholder="Khách hàng cá nhân"
                      onChange={handleChangeTypeCustomer}
                      disabled={isDisabledForm}
                    >
                      <Option value="individual">Khách hàng cá nhân</Option>
                      <Option value="business">Khách hàng doanh nghiệp</Option>
                    </Select>
                  </Form.Item>
                  <Form.Item label="Mã khách hàng" name="customerCode">
                    <SingleSelectLazy
                      key={typeCustomer}
                      queryKey={['get-list-customer', typeCustomer]}
                      apiQuery={getAllCustomersByQuery}
                      placeholder="Tìm theo mã khách hàng"
                      keysLabel={['code', 'name']}
                      allowClear={true}
                      moreParams={{
                        type: typeCustomer || 'individual',
                      }}
                      enabled={typeCustomer !== undefined}
                      handleSelect={handleCustomerSelect}
                      defaultValues={{
                        label: form.getFieldValue('customerCode') || '',
                        value: form.getFieldValue('customerCode') || '',
                      }}
                      disabled={isDisabledForm}
                    />
                  </Form.Item>
                </Col>
              </Row>

              <div
                style={{
                  background: '#fafafa',
                  border: '1px solid #e4e4e4',
                  borderRadius: 6,
                  padding: 16,
                  marginBottom: 16,
                }}
              >
                <Row gutter={16}>
                  <Col span={6}>
                    <Form.Item
                      label="Loại giấy tờ"
                      name="identityType"
                      rules={[{ required: true, message: 'Vui lòng chọn loại giấy tờ' }]}
                    >
                      <Select
                        placeholder="Chọn loại giấy tờ"
                        disabled={(isKHCT === 'customer' && !!form.getFieldValue('customerCode')) || isDisabledForm}
                      >
                        {typeCustomer === 'individual' ? (
                          <>
                            <Option value="CCCD">Căn cước công dân</Option>
                            <Option value="CMND">CMT</Option>
                            <Option value="PASSPORT">Hộ chiếu</Option>
                          </>
                        ) : (
                          <Option value="MST">Mã số thuế</Option>
                        )}
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      label="Số giấy tờ"
                      name={typeCustomer === 'business' ? 'taxCode' : 'identityNo'}
                      rules={[
                        {
                          required: true,
                          validator: (_, value) => {
                            if (!value || value.trim() === '') {
                              return Promise.reject(new Error('Vui lòng nhập số giấy tờ'));
                            }
                            return Promise.resolve();
                          },
                        },
                      ]}
                    >
                      <Input
                        placeholder="Nhập số giấy tờ"
                        maxLength={60}
                        onBlur={e => {
                          form.setFieldsValue({ taxCode: e.target.value.trim() });
                          form.setFieldsValue({ identityNo: e.target.value.trim() });
                        }}
                        disabled={(isKHCT === 'customer' && !!form.getFieldValue('customerCode')) || isDisabledForm}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      label="Ngày cấp"
                      name="identityDate"
                      rules={[{ required: true, message: 'Vui lòng chọn ngày cấp' }]}
                    >
                      <DatePicker
                        placeholder="Chọn ngày cấp"
                        format={FORMAT_DATE}
                        style={{ width: '100%' }}
                        disabled={(isKHCT === 'customer' && !!form.getFieldValue('customerCode')) || isDisabledForm}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      label="Nơi cấp"
                      name="identityPlace"
                      rules={[{ required: true, message: 'Vui lòng chọn nơi cấp' }]}
                    >
                      <Select
                        placeholder="Chọn nơi cấp"
                        disabled={(isKHCT === 'customer' && !!form.getFieldValue('customerCode')) || isDisabledForm}
                        filterOption={(input, option) =>
                          typeof option?.label === 'string'
                            ? option.label.toLowerCase().includes(input.toLowerCase())
                            : false
                        }
                        options={provinces?.map(item => ({
                          value: item.code,
                          label: item.nameVN,
                        }))}
                        labelInValue
                        showSearch
                        onChange={value => {
                          form.setFieldsValue({
                            identityPlace: value || undefined,
                          });
                        }}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              </div>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label={typeCustomer === 'business' ? 'Tên công ty' : 'Tên khách hàng'}
                    name={typeCustomer === 'business' ? ['company', 'name'] : ['personalInfo', 'name']}
                    rules={[
                      {
                        required: true,
                        message:
                          typeCustomer === 'business' ? 'Vui lòng nhập tên công ty' : 'Vui lòng nhập tên khách hàng',
                      },
                      {
                        validator(_, value) {
                          if (value && value.trim() === '') {
                            return Promise.reject('Không được nhập khoảng trống');
                          }
                          return Promise.resolve();
                        },
                      },
                    ]}
                  >
                    <Input
                      placeholder={typeCustomer === 'business' ? 'Nhập tên công ty' : 'Nhập tên khách hàng'}
                      maxLength={typeCustomer === 'business' ? 120 : 60}
                      onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                        e.target.value = e.target.value.toUpperCase();
                      }}
                      disabled={(isKHCT === 'customer' && !!form.getFieldValue('customerCode')) || isDisabledForm}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="Mã khách hàng" name="customerCode">
                    <Input placeholder="Mã khách hàng" disabled />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={16}>
                {typeCustomer === 'business' && (
                  <Col span={12}>
                    <Form.Item
                      label="Tên người đại diện"
                      name={['personalInfo', 'name']}
                      rules={[{ required: true, message: 'Vui lòng nhập tên người đại diện' }]}
                    >
                      <Input
                        placeholder="Nhập tên người đại diện"
                        maxLength={60}
                        onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                          e.target.value = e.target.value.toUpperCase(); // Chuyển đổi trực tiếp
                        }}
                        disabled={(isKHCT === 'customer' && !!form.getFieldValue('customerCode')) || isDisabledForm}
                      />
                    </Form.Item>
                  </Col>
                )}

                <Col span={12}>
                  <Form.Item
                    label="Số điện thoại"
                    name="phone"
                    rules={[{ required: true, message: 'Vui lòng nhập số điện thoại' }]}
                  >
                    <Input
                      placeholder="Nhập số điện thoại"
                      maxLength={15}
                      disabled={(isKHCT === 'customer' && !!form.getFieldValue('customerCode')) || isDisabledForm}
                    />
                  </Form.Item>
                </Col>
                {typeCustomer === 'individual' && (
                  <Col span={12}>
                    <Form.Item
                      label="Địa chỉ email"
                      name="email"
                      rules={[{ type: 'email', message: 'Địa chỉ email sai định dạng' }]}
                    >
                      <Input
                        placeholder="Nhập địa chỉ email"
                        maxLength={25}
                        disabled={(isKHCT === 'customer' && !!form.getFieldValue('customerCode')) || isDisabledForm}
                      />
                    </Form.Item>
                  </Col>
                )}
              </Row>

              <Row gutter={16}>
                {typeCustomer === 'individual' && (
                  <Col span={24}>
                    <Form.Item
                      name={['info', 'gender']}
                      label="Giới tính"
                      layout="horizontal"
                      rules={[{ required: true, message: 'Vui lòng chọn giới tính' }]}
                    >
                      <Radio.Group
                        options={OPTIONS_GENDER}
                        style={{ marginLeft: 30 }}
                        disabled={(isKHCT === 'customer' && !!form.getFieldValue('customerCode')) || isDisabledForm}
                      />
                    </Form.Item>
                  </Col>
                )}

                <Col span={24}>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        layout="horizontal"
                        label="Ngày sinh"
                        name={['info', 'onlyYear']}
                        valuePropName="checked"
                      >
                        <Checkbox
                          checked={yearOnly}
                          style={{ marginLeft: 30 }}
                          onChange={e => setYearOnly(e.target.checked)}
                          disabled={isDisabledForm}
                        >
                          Chỉ năm sinh
                        </Checkbox>
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      {yearOnly ? (
                        <Form.Item name={['info', 'birthdayYear']}>
                          <DatePicker picker="year" format="YYYY" placeholder="YYYY" disabled={isDisabledForm} />
                        </Form.Item>
                      ) : (
                        <Form.Item name={['info', 'birthday']}>
                          <DatePicker format={FORMAT_DATE} placeholder={FORMAT_DATE} disabled={isDisabledForm} />
                        </Form.Item>
                      )}
                    </Col>
                  </Row>
                </Col>
              </Row>
              {/* Địa chỉ công ty */}
              {typeCustomer === 'business' && (
                <>
                  <Title level={5}>Địa chỉ công ty</Title>
                  <Row gutter={16}>
                    <Col span={24}>
                      <Form.Item label="Địa chỉ" name={'companyAdress'} className="input-address">
                        <SelectAddress
                          placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
                          parentName={'companyAdress'}
                          address={form.getFieldValue('companyAdress')}
                          isDisable={(isKHCT === 'customer' && isCompanyAdressNonNull) || isDisabledForm}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={24} className="address">
                      <Form.Item name={['companyAdress', 'address']}>
                        <Input
                          placeholder="Nhập địa chỉ cụ thể"
                          maxLength={155}
                          disabled={(isKHCT === 'customer' && isCompanyAdressNonNull) || isDisabledForm}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                </>
              )}

              {/* Địa chỉ thường trú */}
              {typeCustomer === 'business' ? (
                <Title level={5}>Địa chỉ thường trú người đại diện</Title>
              ) : (
                <Title level={5}>Địa chỉ thường trú</Title>
              )}
              <Row gutter={16}>
                <Col span={24}>
                  <Form.Item label="Địa chỉ" name={['address']} className="input-address">
                    <SelectAddress
                      placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
                      parentName={'address'}
                      address={form.getFieldValue('address')}
                      isDisable={(isKHCT === 'customer' && isAddressNonNull) || isDisabledForm}
                    />
                  </Form.Item>
                </Col>
                <Col span={24} className="address">
                  <Form.Item name={['address', 'address']}>
                    <Input
                      placeholder="Nhập địa chỉ cụ thể"
                      maxLength={155}
                      disabled={(isKHCT === 'customer' && isAddressNonNull) || isDisabledForm}
                    />
                  </Form.Item>
                </Col>
              </Row>

              {/* Địa chỉ liên lạc */}
              {typeCustomer === 'business' ? (
                <Title level={5}>Địa chỉ liên lạc người đại diện</Title>
              ) : (
                <Title level={5}>Địa chỉ liên lạc</Title>
              )}
              <Form.Item name={['info', 'cloneAddress']} valuePropName="checked">
                <Checkbox onChange={handleCloneAddress} disabled={isDisabledForm}>
                  Sử dụng địa chỉ thường trú
                </Checkbox>
              </Form.Item>

              <Row gutter={16}>
                <Col span={24}>
                  <Form.Item label="Địa chỉ" name={['rootAddress']} className="input-address">
                    <SelectAddress
                      placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
                      parentName="rootAddress"
                      address={rootAddress}
                      isDisable={(isKHCT === 'customer' && isRootAddressNonNull) || cloneAddress || isDisabledForm}
                    />
                  </Form.Item>
                </Col>
                <Col span={24} className="address">
                  <Form.Item name={['rootAddress', 'address']}>
                    <Input
                      placeholder="Nhập địa chỉ cụ thể"
                      maxLength={155}
                      disabled={(isKHCT === 'customer' && isRootAddressNonNull) || cloneAddress || isDisabledForm}
                    />
                  </Form.Item>
                </Col>
              </Row>
            </div>
          </Col>

          <Col span={12}>
            <div className="form-section">
              <Title level={5}>Tài liệu đính kèm</Title>

              <Form.Item name="files">
                <UploadFileBookingTicket
                  fileList={fileList}
                  setFileList={setFileList}
                  uploadPath={typeCustomer === 'business' ? 'liquidation/business' : 'liquidation/personal'}
                  size={25}
                  isDetail={isKHCT === 'customer' || isDisabledForm}
                />
              </Form.Item>
            </div>
          </Col>
        </Row>

        {isUpdate ? (
          <ButtonOfPageDetail
            handleSubmit={() => form.submit()}
            handleCancel={handleCancel}
            loadingSubmit={loading}
            isShowModal={isModified}
            disabled={isDisabledForm}
          />
        ) : (
          <div className="create-footer">
            <div className="button-create">
              <Button type="primary" htmlType="submit" loading={loading} size="small" disabled={isDisabledForm}>
                Lưu
              </Button>
            </div>
          </div>
        )}
      </Form>
    </Spin>
  );
};

export default LiquidationProposalForm;
