//đ<PERSON><PERSON> nghĩa các action có thể thực hiện được đang để test, sửa lại theo yêu cầu

import { ImportProjectHistoryState } from './state';

// payload là dữ liệu truyền vào để update state, đang để string để test
export type ImportProjectHistoryAction = { type: 'increment'; payload: string } | { type: 'decrement' };

export const reducer = (
  state: ImportProjectHistoryState,
  action: ImportProjectHistoryAction,
): ImportProjectHistoryState => {
  switch (action.type) {
    case 'increment':
      return state;
    default:
      return state;
  }
};
