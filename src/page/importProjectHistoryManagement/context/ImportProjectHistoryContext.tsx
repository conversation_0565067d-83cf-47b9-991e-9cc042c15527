import { createContext, useReducer, ReactNode, Dispatch } from 'react';
import { ImportProjectHistoryAction, reducer } from './reducer';
import { ImportProjectHistoryState, initialImportProjectHistoryState } from './state';

interface ImportProjectHistoryContextProps {
  state: ImportProjectHistoryState;
  dispatch: Dispatch<ImportProjectHistoryAction>;
}
interface ImportProjectHistoryActionProviderProps {
  children: ReactNode;
}

export const ImportProjectHistoryContext = createContext<ImportProjectHistoryContextProps | undefined>(undefined);

export const ImportProjectHistoryProvider = ({ children }: ImportProjectHistoryActionProviderProps) => {
  const [state, dispatch] = useReducer(reducer, initialImportProjectHistoryState);

  return (
    <ImportProjectHistoryContext.Provider value={{ state, dispatch }}>{children}</ImportProjectHistoryContext.Provider>
  );
};
