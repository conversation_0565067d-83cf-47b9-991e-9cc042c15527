import { FormInstance, Modal, Tabs, TabsProps } from 'antd';
import { useRef, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useFetch, useUpdateField } from '../../../hooks';
import PageLayoutDetail from '../../../layout/pageLayoutDetail/PageLayoutDetail';

import GeneralTab from './component/generalTab';
import { AccountTab } from './component/accountTab';
import { DetailEmployeeExternal } from '../../../types/employeeExternal/employeeExternal';
import { getDetailEmployeeExternal, updateEmployeeExternal } from '../../../service/employeeExternal';
import { EmployeeExternalDetailProvider } from './context/EmloyeeExternalDetailContext';
import { EMPLOYEE_EXTERNAL_MANAGEMENT } from '../../../configs/path';

const tabConfigs: { [key: string]: { isEditable: boolean } } = {
  '1': { isEditable: true },
  '2': { isEditable: false },
  '3': { isEditable: false },
};

export interface FormRef extends FormInstance {
  isModified: () => boolean;
  submitForm: () => Promise<void>;
}

const EmployeeExternalDetail = () => {
  const navigate = useNavigate();
  const id = useParams();

  const [activeTabKey, setActiveTabKey] = useState('1');
  const [isEditable, setIsEditable] = useState<boolean>(true);
  const [showUnsavedModal, setShowUnsavedModal] = useState(false);
  const [formGeneralTab, setFormGeneralTab] = useState<FormInstance | null>(null);

  const formRefs: { [key: string]: React.RefObject<FormRef> } = {
    tab2: useRef<FormRef>(null),
  };

  const { data: dataDetailEmployeeExternal } = useFetch<DetailEmployeeExternal>({
    queryKeyArr: ['detail-employee-external', id?.id],
    api: () => getDetailEmployeeExternal(id?.id),
    withFilter: false,
    enabled: !!id,
    cacheTime: 10,
  });
  const detailEmployeeExternal = dataDetailEmployeeExternal?.data?.data;

  const onUpdateEmployeeExternal = useUpdateField({
    keyOfListQuery: ['employee-external'],
    keyOfDetailQuery: ['detail-employee-external', id],
    label: 'Người dùng ngoài cơ cấu',
    apiQuery: updateEmployeeExternal,
  });

  const checkUnsavedChanges = () => {
    for (const refKey of Object.keys(formRefs)) {
      const formInstance = formRefs[refKey]?.current;
      if (formInstance?.isModified) {
        return true;
      }
    }
    return false;
  };

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: 'Thông tin chung',
      tabKey: '1',
      children: <GeneralTab data={detailEmployeeExternal} onFormInstanceChange={setFormGeneralTab} />,
    },
    {
      key: '2',
      label: 'Tài khoản',
      tabKey: '2',
      children: <AccountTab ref={formRefs.tab2} id={detailEmployeeExternal?.id} />,
    },
  ];

  const handleSubmit = async () => {
    const values = formGeneralTab?.getFieldsValue();
    if (activeTabKey === '2' && formRefs.tab2.current) {
      formRefs.tab2.current.submit();
    } else {
      await onUpdateEmployeeExternal.mutateAsync({
        id: id?.id,
        name: values?.name,
        gender: values.gender,
        dob: values?.dob,
        dateOfIdentityCode: values?.dateOfIdentityCode,
        isActive: values?.isActive,
        contactAddress: values?.contactAddress,
        address: values?.address,
        placeOfIdentityCode:
          typeof values.placeOfIdentityCode === 'object'
            ? values.placeOfIdentityCode?.value
            : values.placeOfIdentityCode,
      });
    }
  };

  const handleTabChange = (key: string) => {
    setActiveTabKey(key);
    setIsEditable(tabConfigs[key].isEditable);
  };

  const handleModalCancel = () => {
    const hasUnsavedChanges = checkUnsavedChanges();
    if (!hasUnsavedChanges) {
      setShowUnsavedModal(true);
    } else {
      navigate(EMPLOYEE_EXTERNAL_MANAGEMENT);
    }
  };

  const handleModalConfirm = () => {
    setShowUnsavedModal(false);
    navigate(EMPLOYEE_EXTERNAL_MANAGEMENT);
  };

  const modalCancel = () => {
    Modal.confirm({
      title: 'Thoát trang chi tiết',
      content: 'Dữ liệu đang chưa được lưu, bạn có chắc muốn thoát khỏi trang không?',
      okText: 'Đồng ý',
      cancelText: 'Quay lại',
      onOk: () => handleModalConfirm(),
      onCancel: () => handleModalCancel(),
      open: showUnsavedModal,
    });
  };

  return (
    <EmployeeExternalDetailProvider>
      <PageLayoutDetail
        titleBreadCrumb={detailEmployeeExternal?.email}
        handleSubmit={handleSubmit}
        handleCancel={modalCancel}
        isEditableSubmit={!isEditable}
      >
        <div className="employee-external-detail">
          <Tabs defaultActiveKey="1" items={items} onChange={handleTabChange} />
        </div>
      </PageLayoutDetail>
    </EmployeeExternalDetailProvider>
  );
};

export default EmployeeExternalDetail;
