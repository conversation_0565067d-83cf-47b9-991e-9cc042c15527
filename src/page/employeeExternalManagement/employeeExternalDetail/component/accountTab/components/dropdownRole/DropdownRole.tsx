import { DownOutlined } from '@ant-design/icons';
import { Divider, MenuProps, Space } from 'antd';
import { ItemType } from 'antd/es/menu/interface';
import { Dropdown } from 'antd/lib';
import { cloneElement, useEffect, useState } from 'react';
import InputSearch from '../../../../../../../components/input/InputSearch';
import { useFetch } from '../../../../../../../hooks';
import { getListUserRoles } from '../../../../../../../service/roles';
import { IManagementRole } from '../../../../../../userRolesManagement';
import './styles.scss';
import { Role } from '../../type';

type Props = {
  onClick: (value: { label: string; key: string }, index: number) => void;
  index: number;
  roles: Role[];
  defaultValue?: string;
};

export const DropdownRole = (props: Props) => {
  const { onClick, index, roles, defaultValue } = props;
  const [ListRoles, setListRoles] = useState<MenuProps['items']>([]);
  const [filterData, setFilterData] = useState<MenuProps['items']>([]);
  const [label, setLabel] = useState(defaultValue);
  const [open, setOpen] = useState(false);

  const { data, isFetched } = useFetch<IManagementRole[]>({
    queryKeyArr: ['user-roles'],
    api: getListUserRoles,
    withFilter: false,
  });

  useEffect(() => {
    const listRoles: MenuProps['items'] = data?.data?.data?.rows?.map((item: IManagementRole) => ({
      key: item?.code ?? '',
      label: item?.name ?? '',
    }));
    if (listRoles) {
      const keysToRemove = new Set(roles.map(role => String(role.code)));
      setListRoles(listRoles.filter(el => el?.key && !keysToRemove.has(String(el?.key))));
      setFilterData(listRoles.filter(el => el?.key && !keysToRemove.has(String(el?.key))));
    }
  }, [data, roles]);

  const handleOpenChange = () => {
    if (isFetched) {
      setOpen(!open);
    }
  };

  const handleMenuClick: MenuProps['onClick'] = e => {
    const role = ListRoles?.find(
      (item): item is MenuProps['items'] & { label: string; key: string } => item?.key === e.key,
    );
    if (role) {
      onClick(role, index);
      setOpen(false);
      setLabel(role.label);
    }
  };

  const handleOnChangeSearch = (value: unknown) => {
    setFilterData(
      ListRoles?.filter((el): el is ItemType => {
        if (el && 'label' in el) {
          return (el.label as string).toLowerCase().includes((value as string).toLowerCase());
        }
        return false;
      }),
    );
  };

  return (
    <Dropdown
      onOpenChange={handleOpenChange}
      trigger={['click']}
      open={open}
      menu={{ items: filterData, onClick: handleMenuClick }}
      dropdownRender={menu => (
        <div className="dropdown-role">
          <div className="search-role">
            <InputSearch showParams={false} placeholder="search" onChange={handleOnChangeSearch} />
          </div>
          <Divider />
          {cloneElement(menu as React.ReactElement)}
        </div>
      )}
    >
      <a onClick={e => e.preventDefault()}>
        <Space>
          {!label ? 'Chọn vai trò' : label}
          <DownOutlined />
        </Space>
      </a>
    </Dropdown>
  );
};
