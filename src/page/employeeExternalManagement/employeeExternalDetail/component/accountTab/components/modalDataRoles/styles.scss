.modal-data-roles-account {
  .ant-modal-body {
    padding-top: 16px;
    .ant-space {
      width: 100%;
      .ant-select {
        width: 50%;
      }
      .wrapper-tree-dataRoles {
        height: 200px;
        min-width: 200px;
        overflow: auto;
        position: relative;
        .ant-spin {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
        .ant-tree {
          &.tree-data.external {
            .ant-tree-treenode {
              .ant-tree-switcher-noop {
                display: none;
              }
            }
          }
        }
      }
      .load-more {
        height: 24px;
        text-align: center;
        .error-message {
          color: red;
        }
      }
    }
  }
}
