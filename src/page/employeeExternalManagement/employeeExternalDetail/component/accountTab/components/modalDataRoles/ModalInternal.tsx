import { useInfiniteQuery } from '@tanstack/react-query';
import { Modal, Select, Space, Spin, Tree } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import { Key } from 'antd/es/table/interface';
import { DataNode } from 'antd/es/tree';
import isArray from 'lodash/isArray';
import debounce from 'lodash/debounce';
import { useEffect, useMemo, useRef, useState } from 'react';
import { SOURCE_ROLE_DATA_TYPE } from '../../../../../../../constants/common';
import { FetchResponse, TDataList } from '../../../../../../../hooks';
import { getAllDataRolesExternal, getAllDataRolesInternal } from '../../../../../../../service/account';
import { selectObject, TypeListInternalOrgChart } from '../../type';
import { buildTreeInternalOrgChart } from '../../utilities';
import './styles.scss';
import { LoadingOutlined } from '@ant-design/icons';

export type Props = {
  handleCancel: () => void;
  isOpen: boolean;
  handleOk: (value: selectObject) => void;
  selectedDefaultRoles?: selectObject;
  typeModal: 'create' | 'update';
};

function ModalDataRoles(props: Props) {
  const { handleCancel, isOpen, handleOk, selectedDefaultRoles } = props;
  const [selectedKeys, setSelectedKeys] = useState<Key[]>([]);
  const [SelectedObject, setSelectedObject] = useState<selectObject>();
  const [dataType, setDataType] = useState<DefaultOptionType>();
  const [treeData, setTreeData] = useState<DataNode[]>([]);
  const [filterDataType, setFilterDataType] = useState<DefaultOptionType[] | undefined>(undefined);
  const [messageValidate, setMessageValidate] = useState<string>();

  const treeContainerRef = useRef<HTMLDivElement | null>(null);

  const fetchApi = dataType?.value === 'INTERNAL_ORGCHART' ? getAllDataRolesInternal : getAllDataRolesExternal;

  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, isLoading } = useInfiniteQuery<
    FetchResponse<TDataList<TypeListInternalOrgChart[]>>
  >({
    queryKey: ['getAllDataRoles', dataType?.value],
    queryFn: ({ pageParam = 1 }) => fetchApi({ page: pageParam as number, pageSize: 10 }).then(res => res.data),
    initialPageParam: 1,
    enabled: !!dataType?.value,
    getNextPageParam: lastPage => {
      const hasMore = lastPage?.data?.rows?.length > 0;
      return hasMore ? lastPage?.data?.page + 1 : undefined;
    },
  });

  const flatData = useMemo(() => data?.pages.flatMap(page => page?.data?.rows) || [], [data]);

  const treeDataMemo = useMemo(
    () => buildTreeInternalOrgChart(flatData, selectedDefaultRoles?.listCheck || []),
    [flatData, selectedDefaultRoles?.listCheck],
  );

  useEffect(() => {
    setTreeData(treeDataMemo);
  }, [treeDataMemo]);

  const scrollHandler = useMemo(
    () =>
      debounce(async () => {
        if (!treeContainerRef.current) return;
        const { scrollTop, scrollHeight, clientHeight } = treeContainerRef.current;
        if (scrollTop + clientHeight >= scrollHeight - 1 && hasNextPage && !isFetchingNextPage) {
          await fetchNextPage();
        }
      }, 300),
    [hasNextPage, isFetchingNextPage, fetchNextPage],
  );

  useEffect(() => {
    if (isOpen) {
      const container = treeContainerRef.current;
      if (container) {
        container.addEventListener('scroll', scrollHandler);
        return () => container.removeEventListener('scroll', scrollHandler);
      }
    }
  }, [isOpen, scrollHandler]);

  useEffect(() => {
    if (selectedDefaultRoles?.listCheck) {
      setSelectedKeys(selectedDefaultRoles?.listCheck?.map(item => item?.code));
      setSelectedObject(selectedDefaultRoles);
      if (isArray(selectedDefaultRoles?.type)) {
        const filteredDataType = SOURCE_ROLE_DATA_TYPE?.filter(
          item => !selectedDefaultRoles?.type.includes(item.value),
        );
        setFilterDataType(filteredDataType);
        setDataType(filteredDataType?.length === 3 ? [] : filteredDataType?.[0]);
      } else {
        setFilterDataType(undefined);
        setDataType(SOURCE_ROLE_DATA_TYPE?.find(item => item.value === selectedDefaultRoles?.type));
      }
    }
  }, [selectedDefaultRoles]);

  const handleTreeCheck = (
    checked: Key[] | { checked: Key[]; halfChecked: Key[] },
    info: { checkedNodes: DataNode[] },
  ) => {
    const selectedArrays = info.checkedNodes.map((node: DataNode) => ({
      code: node.key as string,
      name: node.title as string,
      type: dataType?.value as string,
      isDelete: !node.disabled,
    }));
    if (dataType) {
      setSelectedObject({ type: dataType, listCheck: selectedArrays });
      setMessageValidate('');
    }
    if (Array.isArray(checked)) {
      setSelectedKeys(checked);
    } else {
      setSelectedKeys(checked.checked);
    }
  };
  const handleSubmit = () => {
    if (SelectedObject && SelectedObject?.listCheck.length > 0) {
      setMessageValidate('');
      handleOk(SelectedObject);
    } else {
      setMessageValidate('Vui lòng chọn vùng dữ liệu!');
    }
  };

  const handleSelectChange = (_: string, option: DefaultOptionType) => {
    setDataType(option);
  };

  return (
    <Modal
      className="modal-data-roles-account"
      title="Chọn vùng dữ liệu"
      open={isOpen}
      onOk={handleSubmit}
      destroyOnClose
      onCancel={handleCancel}
      okText="Lựa chọn"
      centered
    >
      <Space direction="vertical" size={'large'} style={{ width: '100%' }}>
        <Select
          placeholder="Chọn vùng dữ liệu"
          onChange={handleSelectChange}
          defaultValue={dataType?.label?.toString()}
          options={filterDataType}
          disabled={filterDataType ? false : true}
        />
        <div>
          <div
            className="wrapper-tree-dataRoles"
            ref={node => {
              if (node) {
                treeContainerRef.current = node;
              }
            }}
          >
            {isLoading ? (
              <Spin style={{ position: 'absolute', top: '50%' }} />
            ) : (
              <>
                <Tree
                  className={`tree-data ${dataType?.value === 'INTERNAL_ORGCHART' ? 'internal' : 'external'}`}
                  checkable
                  selectable={false}
                  treeData={treeData}
                  checkedKeys={selectedKeys}
                  onCheck={handleTreeCheck}
                  checkStrictly
                />
              </>
            )}
          </div>
          <div className="load-more">
            {isFetchingNextPage && <LoadingOutlined />}
            {messageValidate && <span className="error-message">{messageValidate}</span>}
          </div>
        </div>
      </Space>
    </Modal>
  );
}

export default ModalDataRoles;
