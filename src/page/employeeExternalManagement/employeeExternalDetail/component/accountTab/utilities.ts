import { TreeDataNode } from 'antd';
import { DataRole, GroupedDataRole, Role, TypeListInternalOrgChart } from './type';

export function groupByType(dataRoles: DataRole[], parentCode?: string): GroupedDataRole[] {
  const grouped: { [key: string]: GroupedDataRole } = {};

  dataRoles.forEach(({ code, name, type, isDelete }) => {
    if (!grouped[type]) {
      grouped[type] = {
        type,
        isDelete,
        key: Math.random().toString(),
        parentCode: parentCode ?? '',
        isChildren: true,
        area: [],
      };
    }
    grouped[type].area.push({
      name,
      code,
      labelDataRole: `${code}-${name}`,
      type,
      isDelete,
    });
  });

  return Object.values(grouped);
}

export function transformRoles(roles: Role[] | DataRole[]): Role[] {
  return roles.map(role => {
    const key = Math.random().toString();
    return {
      ...role,
      key,
      children:
        'dataRoles' in role && role.dataRoles
          ? groupByType(role.dataRoles, key).sort((a, b) => a.type.localeCompare(b.type))
          : [],
    };
  });
}

export const buildTreeInternalOrgChart = (tree: TypeListInternalOrgChart[], userData: DataRole[]): TreeDataNode[] => {
  return tree?.map(item => {
    const user = userData.find(u => u?.code === item?.code);
    return {
      key: item?.code || item?.partnershipCode,
      title: item?.nameVN || item?.partnershipName,
      disabled: user ? (!user.isDelete ? true : false) : false,
      children: item?.children ? buildTreeInternalOrgChart(item?.children, userData) : [],
    };
  });
};

export function checkDataRoles(obj: { roles: Role[] }): boolean {
  return obj.roles.every((role: Role) => role.dataRoles && role.dataRoles.length > 0);
}
