import { Col, DatePicker, Form, FormInstance, Input, Row, Select } from 'antd';
import {
  FORMAT_DATE,
  OPTIONS_GENDER,
  OPTIONS_LINEMANAGER_EMPLOYEE,
  OPTIONS_STATUS,
} from '../../../../../constants/common';
import { useEffect } from 'react';
import dayjs from 'dayjs';
import SelectAddress, { AddressType } from '../../../../../components/selectAddress';
import { DetailEmployeeExternal } from '../../../../../types/employeeExternal/employeeExternal';
import './style.scss';
import { getProvinces } from '../../../../../service/address';
import { useFetch } from '../../../../../hooks';
interface CommonFormProps {
  data?: DetailEmployeeExternal;
  onFormInstanceChange?: (formInstance: FormInstance) => void;
}

const GeneralTab: React.FC<CommonFormProps> = ({ data, onFormInstanceChange }) => {
  const [form] = Form.useForm();
  const address = data?.address as undefined;

  // Cập nhật value vào form
  useEffect(() => {
    if (data) {
      form.setFieldsValue({
        ...data,
        dob: dayjs(data?.dob).isValid() ? dayjs(data?.dob) : null,
        dateOfIdentityCode: dayjs(data?.dateOfIdentityCode).isValid() ? dayjs(data?.dateOfIdentityCode) : null,
        isActive: data?.isActive,
        name: data?.name,
        placeOfIdentityCode: data?.placeOfIdentityCode,
        gender: data?.gender,
        contactAddress: data?.contactAddress,
      });
    }
  }, [data, form]);

  const { data: listIdentitys } = useFetch<AddressType[]>({
    queryKeyArrWithFilter: ['get-list-identity'],
    api: getProvinces,
  });

  useEffect(() => {
    if (onFormInstanceChange) {
      onFormInstanceChange(form);
    }
  }, [form, onFormInstanceChange]);

  return (
    <>
      <Form layout="vertical" form={form}>
        <Row className="general-tab-employee-external">
          <Col span={12}>
            <Row gutter={[16, 0]}>
              <Col span={12}>
                <Form.Item
                  label="Tên nhân viên"
                  name="name"
                  rules={[
                    {
                      validator: (_, value) => {
                        if (!value) {
                          return Promise.reject(new Error('Vui lòng nhập tên nhân viên'));
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <Input placeholder="Nhập tên nhân viên" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Mã nhân viên" name="code">
                  <Input disabled />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Số điện thoại" name="phone">
                  <Input disabled />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Trạng thái"
                  name="isActive"
                  rules={[
                    {
                      validator: (_, value) => {
                        if (!value) {
                          return Promise.reject(new Error('Vui lòng chọn trạng thái'));
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <Select
                    options={OPTIONS_STATUS.map(item => {
                      return {
                        value: item.value,
                        label: item.label,
                      };
                    })}
                    allowClear
                    placeholder="Chọn trạng thái"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Địa chỉ email" name="email">
                  <Input disabled />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="CCCD/CMND" name="identityCode">
                  <Input disabled />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Ngày cấp"
                  name="dateOfIdentityCode"
                  rules={[
                    {
                      validator: (_, value) => {
                        if (!value) {
                          return Promise.reject(new Error('Vui lòng chọn ngày cấp'));
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <DatePicker format={FORMAT_DATE} style={{ width: '100%' }} placeholder="Chọn ngày cấp" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Nơi cấp"
                  name="placeOfIdentityCode"
                  rules={[
                    {
                      validator: (_, value) => {
                        if (!value) {
                          return Promise.reject(new Error('Vui lòng chọn nơi cấp'));
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <Select
                    filterOption={(input, option) =>
                      typeof option?.label === 'string'
                        ? option.label.toLowerCase().includes(input.toLowerCase())
                        : false
                    }
                    allowClear
                    options={listIdentitys?.data?.data?.map(item => {
                      return {
                        value: item.code,
                        label: item.nameVN,
                      };
                    })}
                    labelInValue
                    showSearch
                    placeholder="Chọn nơi cấp CMND/CCCD"
                  />
                </Form.Item>
              </Col>

              <Col span={12}>
                <Form.Item
                  label="Giới tính"
                  name="gender"
                  rules={[
                    {
                      validator: (_, value) =>
                        value === 0 || value ? Promise.resolve() : Promise.reject(new Error('Vui lòng chọn giới tính')),
                    },
                  ]}
                >
                  <Select
                    options={OPTIONS_GENDER.map(item => {
                      return {
                        value: item.value,
                        label: item.label,
                      };
                    })}
                    allowClear
                    placeholder="Chọn giới tính"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Ngày sinh"
                  name="dob"
                  rules={[
                    {
                      validator: (_, value) => {
                        if (!value) {
                          return Promise.reject(new Error('Vui lòng chọn ngày sinh'));
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <DatePicker format={FORMAT_DATE} style={{ width: '100%' }} placeholder="Chọn ngày sinh" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Công ty" name={['partnership', 'name']}>
                  <Input disabled />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Vị trí" name="isLineManager">
                  <Select
                    placeholder="Chọn vị trí"
                    options={OPTIONS_LINEMANAGER_EMPLOYEE.map(item => {
                      return {
                        value: item.value,
                        label: item.label,
                      };
                    })}
                    disabled
                    suffixIcon={null}
                    allowClear
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={24}>
                <Form.Item
                  className="input-address"
                  label="Địa chỉ"
                  name="address"
                  rules={[
                    {
                      validator: (_, value) => {
                        if (!value || !value.province || !value.district || !value.ward) {
                          return Promise.reject(
                            new Error('Vui lòng chọn đầy đủ Tỉnh/Thành phố, Quận/Huyện, Phường/Xã'),
                          );
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <SelectAddress parentName="address" address={address} />
                </Form.Item>
              </Col>

              <Col span={24}>
                <Form.Item name="contactAddress" rules={[{ message: 'Vui lòng nhập thông tin địa chỉ chi tiết' }]}>
                  <Input placeholder="Nhập thông tin địa chỉ chi tiết" />
                </Form.Item>
              </Col>
            </Row>
          </Col>
        </Row>
      </Form>
    </>
  );
};

export default GeneralTab;
