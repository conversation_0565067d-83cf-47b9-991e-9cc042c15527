import { createContext, useReducer, ReactNode, Dispatch } from 'react';
import { EmployeeExternalDetailState, initialEmployeeExternalDetailState } from './state';
import { EmployeeExternalDetailAction, reducer } from './reducer';

interface EmployeeExternalDetailContextProps {
  state: EmployeeExternalDetailState;
  dispatch: Dispatch<EmployeeExternalDetailAction>;
}
interface EmployeeExternalDetailProviderProps {
  children: ReactNode;
}

export const EmployeeExternalDetailContext = createContext<EmployeeExternalDetailContextProps | undefined>(undefined);

export const EmployeeExternalDetailProvider = ({ children }: EmployeeExternalDetailProviderProps) => {
  const [state, dispatch] = useReducer(reducer, initialEmployeeExternalDetailState);

  return (
    <EmployeeExternalDetailContext.Provider value={{ state, dispatch }}>
      {children}
    </EmployeeExternalDetailContext.Provider>
  );
};
