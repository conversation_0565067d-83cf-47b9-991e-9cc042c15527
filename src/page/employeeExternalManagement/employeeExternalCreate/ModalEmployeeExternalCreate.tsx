import { Button, Col, Form, Input, Modal, Row, Select } from 'antd';
import ModalComponent from '../../../components/modal';
import { OPTIONS_GENDER, OPTIONS_LINEMANAGER_EMPLOYEE } from '../../../constants/common';
import SelectAddress, { AddressType } from '../../../components/selectAddress';
import { useCallback, useEffect, useMemo, useState } from 'react';
import MyDatePicker from '../../../components/datePicker';
import dayjs from 'dayjs';
import { getProvinces } from '../../../service/address';
import { useCreateField, useFetch } from '../../../hooks';
import { getListCompany } from '../../../service/company';
import SearchableInfiniteScrollSelect from '../../../components/select/SelectScroll';
import {
  checkFormatPhoneNumber,
  checkLengthEqualTen,
  checkLengthIdCard,
  returnNumericValue,
} from '../../../components/validation';
import { EmployeeExternal } from '../../../types/employeeExternal/employeeExternal';
import { sendCreateEmployeeExternal } from '../../../service/employeeExternal';

export interface CompanyType {
  id: string;
  code: string;
  nameVN: string;
}

interface FormModalEmployeeExternalProps {
  visible: boolean;
  onClose: () => void;
}

const FormModalEmployeeExternal = ({ visible, onClose }: FormModalEmployeeExternalProps) => {
  const [form] = Form.useForm();
  const [selectedDate, setSelectedDate] = useState<dayjs.Dayjs | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [page, setPage] = useState(1);
  const [companyOptions, setCompanyOptions] = useState<CompanyType[]>([]);

  const createEmployeeExternal = useCreateField<EmployeeExternal>({
    keyOfListQuery: ['employee-external'],
    apiQuery: sendCreateEmployeeExternal,
    label: 'nhân viên',
  });

  const { data: dataIdentity } = useFetch<AddressType[]>({
    queryKeyArrWithFilter: ['get-list-identity'],
    api: getProvinces,
  });

  const {
    data: dataCompany,
    isLoading: isLoadingCompany,
    isFetching,
  } = useFetch<CompanyType[]>({
    queryKeyArrWithFilter: ['get-list-company', page, searchTerm],
    api: getListCompany,
    moreParams: { page: page, search: searchTerm },
  });

  const listIdentity = dataIdentity?.data?.data;
  const totalPages = dataCompany?.data?.data?.totalPages || 0;

  const listCompany = useMemo(() => {
    return dataCompany?.data?.data?.rows || [];
  }, [dataCompany]);

  const handleDateChange = (date: dayjs.Dayjs | null) => {
    setSelectedDate(date);
    form.setFieldsValue({ representIssuedDate: date });
  };

  const handleCancel = useCallback(() => {
    if (form.isFieldsTouched()) {
      Modal.confirm({
        title: 'Thoát trạng thái tạo mới',
        content: 'Dữ liệu đang chưa được lưu, bạn có chắc chắn muốn thoát khỏi trang không?',
        cancelText: 'Quay lại',
        onOk: () => {
          form.resetFields();
          onClose();
        },
        okButtonProps: {
          type: 'default',
        },
        cancelButtonProps: {
          type: 'primary',
        },
      });
    } else {
      form.resetFields();
      onClose();
    }
  }, [form, onClose]);

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setPage(1);
    setCompanyOptions([]);
  };

  const handleSelectChange = (value: { value: string; label: string } | null) => {
    form.setFieldsValue({
      partnership: value ? { code: value.value, name: value.label } : null,
    });
    if (!value) {
      setSearchTerm('');
    }
  };

  const handleLoadMore = useCallback(() => {
    setPage(prev => prev + 1);
  }, []);

  useEffect(() => {
    setCompanyOptions(prev => {
      const newItems = listCompany.filter(item => !prev.some(existing => existing.code === item.code));
      return [...prev, ...newItems];
    });
  }, [listCompany]);

  const disabledDate = (current: dayjs.Dayjs | null) => current?.isAfter(dayjs().endOf('day')) ?? false;

  const handleFinish = async (values: EmployeeExternal) => {
    const partnership = values.partnership;
    if (partnership) {
      values.partnership = { code: partnership?.code, name: partnership?.name };
    }
    createEmployeeExternal.mutate(values, {
      onSuccess: () => {
        form.resetFields();
        onClose();
      },
    });
  };

  return (
    <ModalComponent
      className="modal-roles"
      title="Tạo mới người dùng ngoài cơ cấu"
      open={visible}
      onCancel={handleCancel}
      destroyOnClose
      footer={
        <Button type="primary" htmlType="submit" onClick={() => form.submit()}>
          Lưu
        </Button>
      }
    >
      <Form
        className="form-roles"
        form={form}
        layout="vertical"
        colon={false}
        labelAlign="left"
        initialValues={{
          isLineManager: OPTIONS_LINEMANAGER_EMPLOYEE[1]?.value,
        }}
        onFinish={handleFinish}
      >
        <Row>
          <Col span={12}>
            <Row gutter={[16, 0]}>
              <Col span={12}>
                <Form.Item
                  label="Tên nhân viên"
                  name="name"
                  rules={[{ required: true, message: 'Vui lòng nhập tên nhân viên' }]}
                >
                  <Input placeholder="Nhập tên nhân viên" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Mã nhân viên" name="code">
                  <Input disabled placeholder="Mã nhân viên" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Số điện thoại"
                  name="phone"
                  rules={[
                    { required: true, message: 'Vui lòng nhập số điện thoại' },
                    {
                      validator: checkLengthEqualTen,
                      message: 'Số điện thoại bao gồm 10 ký tự số',
                    },
                    {
                      validator: checkFormatPhoneNumber,
                      message: 'Số điện thoại sai định dạng',
                    },
                  ]}
                >
                  <Input placeholder="Nhập số điện thoại" onKeyDown={returnNumericValue} maxLength={10} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Trạng thái" name="isActive">
                  <Input disabled placeholder="Trạng thái" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Địa chỉ email"
                  name="email"
                  rules={[
                    { type: 'email', message: 'Email không hợp lệ' },
                    { required: true, message: 'Vui lòng nhập địa chỉ email' },
                  ]}
                >
                  <Input placeholder="Nhập địa chỉ email" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="CCCD/CMND"
                  name="identityCode"
                  rules={[
                    { required: true, message: 'Vui lòng nhập CCCD/CMND' },
                    {
                      validator: checkLengthIdCard,
                      message: 'Số CMND/CCCD bao gồm 9 hoặc 12 ký tự số',
                    },
                  ]}
                >
                  <Input placeholder="Nhập CCCD/CMND" onKeyDown={returnNumericValue} maxLength={12} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Ngày cấp"
                  name="dateOfIdentityCode"
                  required
                  rules={[{ required: true, message: 'Vui lòng chọn ngày cấp' }]}
                >
                  <MyDatePicker
                    placeholder="Chọn ngày cấp"
                    onDateChange={handleDateChange}
                    value={selectedDate}
                    disabledDate={disabledDate}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Nơi cấp"
                  name="placeOfIdentityCode"
                  rules={[{ required: true, message: 'Vui lòng chọn nơi cấp' }]}
                >
                  <Select
                    filterOption={(input, option) =>
                      typeof option?.label === 'string'
                        ? option.label.toLowerCase().includes(input.toLowerCase())
                        : false
                    }
                    allowClear
                    options={listIdentity?.map(item => {
                      return {
                        value: item.code,
                        label: item.nameVN,
                      };
                    })}
                    labelInValue
                    showSearch
                    placeholder="Chọn nơi cấp"
                    onChange={value => {
                      form.setFieldsValue({
                        placeOfIdentityCode: value?.label,
                      });
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Giới tính"
                  name="gender"
                  rules={[{ required: true, message: 'Vui lòng chọn giới tính' }]}
                >
                  <Select
                    options={OPTIONS_GENDER.map(item => {
                      return {
                        value: item.value,
                        label: item.label,
                      };
                    })}
                    allowClear
                    placeholder="Chọn giới tính"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Ngày sinh"
                  name="dob"
                  rules={[{ required: true, message: 'Vui lòng chọn ngày sinh' }]}
                >
                  <MyDatePicker placeholder="Chọn ngày sinh" onDateChange={handleDateChange} value={selectedDate} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Công ty"
                  name="partnership"
                  rules={[{ required: true, message: 'Vui lòng chọn công ty' }]}
                >
                  <SearchableInfiniteScrollSelect
                    options={companyOptions?.map(item => ({
                      value: item.code,
                      label: item.nameVN,
                    }))}
                    onLoadMore={handleLoadMore}
                    onSearch={handleSearch}
                    isLoading={isLoadingCompany}
                    hasMore={page < totalPages}
                    placeholder="Chọn công ty"
                    allowClear
                    onChange={handleSelectChange}
                    isFetching={isFetching}
                    labelInValue
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Vị trí" name="isLineManager" required>
                  <Select
                    placeholder="Chọn vị trí"
                    options={OPTIONS_LINEMANAGER_EMPLOYEE.map(item => {
                      return {
                        value: item.value,
                        label: item.label,
                      };
                    })}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={24}>
                <Form.Item
                  className="input-address"
                  label="Địa chỉ liên hệ"
                  name="address"
                  required
                  rules={[
                    {
                      validator: (_, value) => {
                        if (!value || !value.province || !value.district || !value.ward) {
                          return Promise.reject(
                            new Error('Vui lòng chọn đầy đủ Tỉnh/Thành phố, Quận/Huyện, Phường/Xã'),
                          );
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <SelectAddress parentName="address" address={form.getFieldValue('address')} />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item
                  name="contactAddress"
                  rules={[{ required: true, message: 'Vui lòng nhập địa chỉ chi tiết' }]}
                >
                  <Input placeholder="Địa chỉ chi tiết" />
                </Form.Item>
              </Col>
            </Row>
          </Col>
        </Row>
      </Form>
    </ModalComponent>
  );
};

export default FormModalEmployeeExternal;
