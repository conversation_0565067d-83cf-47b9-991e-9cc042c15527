import { Form } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useMemo, useState } from 'react';
import { useLocation } from 'react-router-dom';
import DropdownFilterSearch from '../../../components/dropdown/dropdownFilterSearch';
import { FORMAT_DATE_API } from '../../../constants/common';
import useFilter from '../../../hooks/filter';
import { TFilterLeadCommon } from '../../../types/leadCommon';

const FilterTrainingUser = () => {
  const { search } = useLocation();
  const [form] = Form.useForm();
  const params = useMemo(() => new URLSearchParams(search), [search]);
  const [filter, setFilter] = useFilter();
  const [initialValues, setInitialValues] = useState<TFilterLeadCommon>();
  // const [isOpenFilter, setIsOpenFilter] = useState(false);

  useEffect(() => {
    if (params) {
      const initialValue = {
        createdFrom: params.get('createdFrom') ? dayjs(params.get('createdFrom')) : undefined,
        createdTo: params.get('createdTo') ? dayjs(params.get('createdTo')) : undefined,
        isHot: params.get('isHot') || undefined,
        exploitStatus: params.get('exploitStatus') || undefined,
        source: params.get('source') || undefined,
      };
      setInitialValues(initialValue);
      form.setFieldsValue(initialValue);
    }
  }, [form, params]);

  const handleSubmitFilter = (values: TFilterLeadCommon) => {
    const newFilter: Record<string, unknown> = {
      createdFrom: values?.createdFrom ? dayjs(values?.createdFrom).format(FORMAT_DATE_API) : null,
      createdTo: values?.createdTo ? dayjs(values?.createdTo).format(FORMAT_DATE_API) : null,
      isHot: values?.isHot || null,
      exploitStatus: values?.exploitStatus || null,
      source: values?.source || null,
    };
    setFilter({ ...filter, page: '1', ...newFilter });
  };

  const handleClearFilters = () => {
    setInitialValues(undefined);
  };

  return (
    <>
      <DropdownFilterSearch
        rootClassName={'lead-assigned-filter'}
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        showParams={true}
        handleOpenChange={() => {}}
        isOpenFilter={false}
        initialValues={initialValues}
        form={form}
        keySearch="search"
        onClearFilters={handleClearFilters}
      />
    </>
  );
};

export default FilterTrainingUser;
