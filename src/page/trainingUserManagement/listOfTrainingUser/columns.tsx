import { Typography } from 'antd';
import { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { FORMAT_DATE_TIME } from '../../../constants/common';
import { TRepo } from '../../../types/leadCommon';
import { ITrainingUser } from '../../../types/trainingUser';

const { Text } = Typography;

export const columns: ColumnsType<ITrainingUser> = [
  {
    title: 'Tên khách hàng',
    dataIndex: 'name',
    key: 'name',
    width: 180,
    render: value => (value ? value : '-'),
  },
  {
    title: 'Email',
    dataIndex: 'email',
    key: 'email',
    align: 'center',
    width: 200,
    render: value => (value ? value : '-'),
  },
  {
    title: 'Số điện thoại',
    dataIndex: 'phone',
    key: 'phone',
    width: 200,
    render: (value: TRepo) => (value?.name ? value?.name : '-'),
  },
  {
    title: 'Check In',
    dataIndex: 'createdDate',
    key: 'createdDate',
    width: 180,
    render: (value: string) => (
      <>
        <Text>{value ? dayjs(value).format(FORMAT_DATE_TIME) : '-'}</Text> <br />
      </>
    ),
  },
  {
    title: 'Check Out',
    dataIndex: 'updatedDate',
    key: 'updatedDate',
    width: 180,
    render: (value: string) => (
      <>
        <Text>{value ? dayjs(value).format(FORMAT_DATE_TIME) : '-'}</Text> <br />
      </>
    ),
  },
  {
    title: 'Ngày tạo',
    dataIndex: 'createdDate',
    width: 150,
    key: 'createdDate',
    render: (value, { createdBy }) =>
      value ? (
        <>
          <Text>{value ? dayjs(value).format(FORMAT_DATE_TIME) : '-'}</Text>
          <br />
          <Text>{createdBy?.fullName || '-'}</Text>
        </>
      ) : (
        '-'
      ),
  },
];
