import BreadCrumbComponent from '../../../components/breadCrumb';
import TableComponent from '../../../components/table';
import { useDeleteField, useFetch } from '../../../hooks';
import { columns } from './columns';
import { Button, Col, Flex, Modal, notification, Row, Select } from 'antd';
import { Key, TableRowSelection } from 'antd/lib/table/interface';
import React from 'react';
import {
  deleteTrainingUsers,
  exportTrainingUser,
  getEvents,
  getListOfTrainingUser,
} from '../../../service/trainingUser';
import { ITrainingUser, TEvent } from '../../../types/trainingUser';
import { useMutation } from '@tanstack/react-query';
import { downloadArrayBufferFile } from '../../../utilities/shareFunc';
import FilterTrainingUser from './FilterTrainingUser';

const ListTraningUsers = () => {
  // const { modal } = App.useApp();
  const [, setSelectedRows] = React.useState<ITrainingUser[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = React.useState<Key[]>([]);
  const [eventSelected, setEventSelected] = React.useState<string>('');
  const [isOpenModalDelete, setIsOpenModalDelete] = React.useState<boolean>(false);

  const {
    data: listCustomer,
    isLoading,
    isPlaceholderData,
    isFetching,
  } = useFetch<ITrainingUser[]>({
    queryKeyArrWithFilter: ['get-training-users', eventSelected],
    api: getListOfTrainingUser,
    moreParams: { id: eventSelected },
    enabled: !!eventSelected,
  });

  const { data: dataEvent } = useFetch<TEvent[]>({
    queryKeyArrWithFilter: ['get-event'],
    api: getEvents,
  });

  const listEvent = dataEvent?.data?.data;

  const { mutateAsync: deleteTrainingUsersMutate } = useDeleteField({
    apiQuery: deleteTrainingUsers,
    keyOfListQuery: ['get-training-users'],
    messageSuccess: 'Xóa người tham dự thành công',
  });

  const handleDeleteLeadAssigner = React.useCallback(async () => {
    const res = await deleteTrainingUsersMutate({ lstId: [...selectedRowKeys] });
    if (res?.data?.statusCode === '0') setIsOpenModalDelete(false);
  }, [deleteTrainingUsersMutate, selectedRowKeys]);

  const onSelectChange = (selectedRowKeys: Key[], selectedRows: ITrainingUser[]) => {
    setSelectedRows(selectedRows);
    setSelectedRowKeys(selectedRowKeys);
  };

  const rowSelection: TableRowSelection<ITrainingUser> = {
    selectedRowKeys,
    onChange: (selectedRowKeys: Key[], selectedRows: ITrainingUser[]) => onSelectChange(selectedRowKeys, selectedRows),
  };

  const exportHistoryMutation = useMutation({
    mutationFn: () => exportTrainingUser(eventSelected),
  });

  const handleSubmitExport = async () => {
    try {
      const response = await exportHistoryMutation.mutateAsync();
      downloadArrayBufferFile({ data: response.data, fileName: `Danh sach khach moi.xlsx` });
    } catch (error) {
      notification.error({ message: 'Xuất dữ liệu thất bại.' });
    }
  };

  const handleChangeSelectEvent = React.useCallback((value: string) => {
    setEventSelected(value);
  }, []);

  return (
    <div className="wrapper-list-personal-customers">
      <BreadCrumbComponent />
      <div className="header-content">
        <Flex gap={17}>
          <Select
            style={{ width: 180 }}
            onChange={handleChangeSelectEvent}
            options={listEvent?.map(o => ({ ...o, value: o?.id, label: o?.eventName }))}
          />
          <FilterTrainingUser />
        </Flex>
        <Row gutter={[16, 8]}>
          <Col>
            <Button type="primary" onClick={() => {}}>
              Gửi lại email
            </Button>
          </Col>
          <Col>
            <Button type="default" onClick={() => setIsOpenModalDelete(true)}>
              Xóa khách mời
            </Button>
          </Col>
          <Col>
            <Button type="default" onClick={handleSubmitExport}>
              Tải xuống danh sách
            </Button>
          </Col>
        </Row>
      </div>
      <div className="table-personal-customers">
        <TableComponent
          queryKeyArr={['get-training-users', eventSelected]}
          columns={columns}
          rowSelection={rowSelection}
          loading={isLoading || isPlaceholderData || isFetching}
          dataSource={listCustomer?.data?.data?.rows ?? []}
          rowKey="id"
        />
      </div>

      <Modal
        className="modal-confirm-delete-assigner"
        open={isOpenModalDelete}
        title={'Xóa người tham dự'}
        centered
        closable={false}
        cancelText="Huỷ"
        destroyOnClose
        style={{ textAlign: 'center' }}
        footer={[
          <Button key="cancel" className="btn-cancel" type="text" onClick={() => setIsOpenModalDelete(false)}>
            Huỷ
          </Button>,
          <Button key="confirm" className="btn-confirm" type="primary" onClick={handleDeleteLeadAssigner}>
            Xác nhận
          </Button>,
        ]}
      >
        Bạn có chắc chắn muốn xóa khách mời tham dự?
      </Modal>
    </div>
  );
};

export default ListTraningUsers;
