import { create } from 'zustand';
import { TEventType, TTraining } from '../../types/training/training';

interface ITrainingStore {
  eventType: TEventType;
  openModalCreate: boolean;
  isModified?: boolean;
  initialValues?: TTraining;
  openReportChatAndReact?: boolean;
  setOpenReportChatAndReact: (value: boolean) => void;
  setInitialValues: (values: TTraining) => void;
  setIsModified: (value: boolean) => void;
  setOpenModalCreate: (value: boolean) => void;
  setEventType: (value: TEventType) => void;
}

export const useStoreTraining = create<ITrainingStore>(set => ({
  eventType: 'ONLINE',
  openModalCreate: false,
  isModified: false,
  initialValues: undefined,
  openReportChatAndReact: false,
  setOpenReportChatAndReact: value => set({ openReportChatAndReact: value }),
  setInitialValues: values => set({ initialValues: values }),
  setIsModified: value => set({ isModified: value }),
  setOpenModalCreate: value => set({ openModalCreate: value }),
  setEventType: value => set({ eventType: value }),
}));
