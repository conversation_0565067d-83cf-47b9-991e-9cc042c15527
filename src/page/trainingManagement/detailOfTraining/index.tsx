import { Form, FormInstance, Spin, Tabs } from 'antd';
import { TabsProps } from 'antd/lib';
import { useEffect, useState } from 'react';
import { useBeforeUnload, useParams } from 'react-router-dom';
import BreadCrumbComponent from '../../../components/breadCrumb';
import ButtonOfPageDetail from '../../../components/button/buttonOfPageDetail';
import { useFetch, useUpdateField } from '../../../hooks';
import { getDetailOfTraining, putTraining } from '../../../service/training';
import { TTraining } from '../../../types/training/training';
import TabGeneralInfo from '../components/tabs/tabGeneralInfo';
import TabGuestList from '../components/tabs/tabGuestList';
import TabSpinThePrize from '../components/tabs/tabSpinThePrize';
import { useStoreTraining } from '../storeTraining';
import ModelReportChatAndReact from '../components/ModelReportChatAndReact';
import './styles.scss';
import { slugify } from '../../../utilities/shareFunc';

function filteredForm(form: FormInstance, initialValue: TTraining) {
  const fields = form.getFieldsValue();
  const filteredFields = Object.keys(fields).reduce(
    (acc, key) => {
      if (key in initialValue) {
        acc[key] = initialValue[key as keyof TTraining];
      }
      return acc;
    },
    {} as Record<string, unknown>,
  );
  return filteredFields;
}

const DetailOfTraining = () => {
  const { id } = useParams();
  const [formGeneralInfo] = Form.useForm();
  const [formPrize] = Form.useForm();
  const [tab, setTab] = useState('general-info');
  const { isModified, setInitialValues, setIsModified, setEventType } = useStoreTraining();

  const { data, isLoading } = useFetch<TTraining>({
    api: () => getDetailOfTraining(id),
    queryKeyArr: ['detail-of-training', id],
  });
  const dataSource = data?.data?.data;

  const { mutateAsync } = useUpdateField({
    apiQuery: putTraining,
    keyOfDetailQuery: ['detail-of-training', id],
    isMessageError: false,
  });

  useEffect(() => {
    if (dataSource) {
      const initialValue = {
        ...dataSource,
        isActive: dataSource.isActive,
        eventType: dataSource.eventType || 'ONLINE',
      };
      const generalInfo = filteredForm(formGeneralInfo, initialValue);

      const prize = filteredForm(formPrize, initialValue);
      setEventType(initialValue.eventType);
      setInitialValues(dataSource);
      formGeneralInfo.setFieldsValue(generalInfo);
      formPrize.setFieldsValue(prize);
    }
  }, [dataSource, formGeneralInfo, formPrize, setEventType, setInitialValues]);

  const handleSubmit = async () => {
    const formPrizeFields = formPrize.getFieldsValue();

    const generalInfo = await formGeneralInfo.validateFields().catch(() => {
      return { error: true, form: 'general' };
    });
    if (generalInfo?.error) {
      formGeneralInfo.scrollToField(generalInfo.form);
      return;
    }
    const newData = {
      id,
      ...generalInfo,
      urlEvent: slugify(generalInfo?.urlEvent),
      ...formPrizeFields,
    };
    const res = await mutateAsync(newData);
    if (res?.data?.statusCode === '0') {
      setIsModified(false);
    }
  };

  useBeforeUnload(event => {
    if (isModified && (formGeneralInfo.isFieldsTouched() || formPrize.isFieldsTouched())) {
      event.preventDefault();
      return true;
    }
    return undefined;
  });
  const handleCancel = () => {
    if (isModified && (formGeneralInfo.isFieldsTouched() || formPrize.isFieldsTouched())) {
      formGeneralInfo.resetFields();
      formPrize.resetFields();
      setIsModified(false);
    }
  };

  const items: TabsProps['items'] = [
    { label: 'Thông tin chung', key: 'general-info', children: <TabGeneralInfo form={formGeneralInfo} /> },
    { label: 'Quay thưởng', key: 'prize', children: <TabSpinThePrize form={formPrize} /> },
    { label: 'Danh sách khách mời', key: 'guest-list', children: <TabGuestList /> },
  ];

  return (
    <Spin spinning={isLoading}>
      <div className="wrapper-detail-of-training">
        <BreadCrumbComponent titleBread={dataSource?.name || 'Chi tiết sự kiện'} />
        <Tabs
          items={items}
          onChange={value => {
            setTab(value);
            const url = new URL(window.location.href);
            url.searchParams.set('tab', value);
            window.history.pushState({}, '', url);
          }}
          activeKey={tab}
        />
        {isModified && (
          <ButtonOfPageDetail handleCancel={handleCancel} handleSubmit={handleSubmit} loadingSubmit={false} />
        )}
      </div>
      <ModelReportChatAndReact />
    </Spin>
  );
};

export default DetailOfTraining;
