import { App, But<PERSON>, Flex, notification } from 'antd';
import { ColumnsType } from 'antd/es/table';
import BreadCrumbComponent from '../../../components/breadCrumb';
import { modalConfirm } from '../../../components/modal/specials/ModalConfirm';
import TableComponent from '../../../components/table';
import { ActionsColumns } from '../../../components/table/components/ActionsColumns';
import { PERMISSION_TRAINING } from '../../../constants/permissions/training';
import { useCheckPermissions, useFetch, useUpdateField } from '../../../hooks';
import { getAllTraining, putStatusTraining } from '../../../service/training';
import { TFilterTraining, TListOfTraining } from '../../../types/training/training';
import ModalCreateTraining from '../createTraining';
import { useStoreTraining } from '../storeTraining';
import { Columns } from './Columns';
import FilterSearchTraining from './FilterSearchTraining';
import './styles.scss';
import { useState } from 'react';
import { TRAINING } from '../../../configs/path';

const ListOfTraining = () => {
  const { create, game: changeStatus, getId: checkGetDetail } = useCheckPermissions(PERMISSION_TRAINING);
  const { setOpenModalCreate } = useStoreTraining();
  const { modal } = App.useApp();
  const [filterParams, setFilterParams] = useState<TFilterTraining>({});

  const { data, isLoading } = useFetch<TListOfTraining[]>({
    queryKeyArrWithFilter: ['training', filterParams],
    api: getAllTraining,
    moreParams: { ...filterParams },
  });

  const { mutateAsync, isPending } = useUpdateField({
    keyOfListQuery: ['training'],
    apiQuery: putStatusTraining,
    isMessageError: false,
    isMessageSuccess: false,
  });

  const dataSource = data?.data?.data?.rows || [];

  const actionColumns: ColumnsType<TListOfTraining> = [
    ...Columns,
    {
      dataIndex: 'action',
      key: 'action',
      width: '50px',
      align: 'center',
      render: (_, record) => {
        const textModalConfirmActive = record?.isActive === 1 ? 'Vô hiệu hoá' : 'Kích hoạt';
        const handleActiveTraining = () => {
          return modalConfirm({
            modal: modal,
            loading: isPending,
            title: `${textModalConfirmActive} sự kiện`,
            content: `Bạn có muốn ${textModalConfirmActive} sự kiện này không?`,
            handleConfirm: async () => {
              const res = await mutateAsync({
                id: record?.id,
                isActive: record?.isActive === 1 ? false : true,
              });
              if (res?.data?.statusCode === '0') {
                notification.success({ message: `${textModalConfirmActive} sự kiện thành công` });
              }
            },
          });
        };

        const openViewDetail = () => {
          window.open(`${TRAINING}/${record?.id}`, '_blank', 'noopener,noreferrer');
        };

        return (
          <ActionsColumns
            handleViewDetail={checkGetDetail ? openViewDetail : undefined}
            textModalConfirmActive={textModalConfirmActive}
            handleActive={changeStatus ? handleActiveTraining : undefined}
          />
        );
      },
    },
  ];
  const handleOpenModalCreate = () => {
    setOpenModalCreate(true);
  };
  const handleCancelModalCreate = () => {
    setOpenModalCreate(false);
  };

  return (
    <div className="wrapper-list-of-training">
      <BreadCrumbComponent />
      <Flex className="header-filter-training" justify="space-between" style={{ marginBottom: 16 }}>
        <FilterSearchTraining setFilterParams={setFilterParams} />
        {create && (
          <Button type="primary" onClick={handleOpenModalCreate}>
            Tạo mới
          </Button>
        )}
      </Flex>
      <div className="table-training">
        <TableComponent
          queryKeyArr={['training']}
          rowKey="id"
          dataSource={dataSource}
          columns={actionColumns}
          scroll={{ x: '1500px' }}
          loading={isLoading}
        />
      </div>

      <ModalCreateTraining onCancel={handleCancelModalCreate} />
    </div>
  );
};

export default ListOfTraining;
