import { ColumnsType } from 'antd/es/table';
import { TListOfTraining } from '../../../types/training/training';
import { Button, Space, Typography } from 'antd';
import dayjs from 'dayjs';
import { FORMAT_DATE } from '../../../constants/common';

const { Text } = Typography;
export const Columns: ColumnsType<TListOfTraining> = [
  {
    title: 'STT',
    key: 'index',
    width: '5%',
    render: (_, __, index) => index + 1,
  },
  {
    title: 'Mã sự kiện',
    dataIndex: 'code',
    key: 'code',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Dự án',
    dataIndex: ['project', 'name'],
    key: 'projectName',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Tên sự kiện',
    dataIndex: 'name',
    key: 'name',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: '<PERSON><PERSON>y tạo',
    dataIndex: 'createdDate',
    key: 'createdDate',

    render: (value: string, record) =>
      value ? (
        <>
          <Text>{dayjs(value).format(FORMAT_DATE)}</Text>
          <br />
          <Text>{record?.createdBy?.fullName ? record.createdBy.fullName : '-'}</Text>
        </>
      ) : (
        '-'
      ),
  },
  {
    title: 'Ngày cập nhật',
    dataIndex: 'modifiedDate',
    key: 'modifiedDate',
    render: (value: string, record) =>
      value ? (
        <>
          <Text>{dayjs(value).format(FORMAT_DATE)}</Text>
          <br />
          <Text>{record?.modifiedBy?.fullName ? record.modifiedBy.fullName : '-'}</Text>
        </>
      ) : (
        '-'
      ),
  },
  {
    title: 'Trạng thái',
    dataIndex: 'isActive',
    width: 100,
    render: (value: number) =>
      value === 1 ? <Text type="success">Đã kích hoạt</Text> : <Text type="danger">Vô hiệu hoá</Text>,
  },
  {
    title: 'Ghi chú',
    dataIndex: 'note',
    key: 'note',
    width: 250,
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Hành động',
    key: 'actionButton',
    render: () => {
      return (
        <div className="action-button-training">
          <Space>
            <Button className="btn-purple">Bắt đầu</Button>
            <Button className="btn-orange">Quay thưởng</Button>
            <Button className="btn-blue">Đăng ký</Button>
          </Space>
        </div>
      );
    },
  },
];
