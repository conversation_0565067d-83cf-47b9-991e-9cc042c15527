import { Button, Col, Modal, notification, Row, Typography } from 'antd';
import { useParams } from 'react-router-dom';
import { useFetch } from '../../../hooks';
import { exportListOfChatAndReact, getListOfChatAndReact } from '../../../service/trainingUser';
import { useStoreTraining } from '../storeTraining';
import { useMutation } from '@tanstack/react-query';
import { downloadArrayBufferFile } from '../../../utilities/shareFunc';

const { Title, Text } = Typography;
type InteractionData = {
  numberOfPeopleComment: number;
  numberOfPeopleReact: number;
  ratioComment: string;
  ratioReact: string;
  totalComment: number;
  totalReact: number;
};

const ModelReportChatAndReact = () => {
  const { id } = useParams();
  const { openReportChatAndReact, setOpenReportChatAndReact } = useStoreTraining();

  const { data } = useFetch<InteractionData>({
    api: () => getListOfChatAndReact(id),
    queryKeyArr: ['get-list-of-chat-and-react', id],
  });
  const interactionData = data?.data?.data;

  const exportChatAndReacMutation = useMutation({
    mutationFn: () => exportListOfChatAndReact(id),
  });
  const handleSubmitExport = async () => {
    try {
      const response = await exportChatAndReacMutation.mutateAsync();
      downloadArrayBufferFile({ data: response.data, fileName: `Bao cao tuong tac.xlsx` });
    } catch (error) {
      notification.error({ message: 'Xuất dữ liệu thất bại.' });
    }
  };
  return (
    <Modal
      className="wrapper-modal-report-chat-and-react"
      open={openReportChatAndReact}
      title="Báo cáo tương tác, bình luận"
      footer={null}
      onCancel={() => setOpenReportChatAndReact(false)}
    >
      <Title level={5}>Bình luận</Title>
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={8}>
          <Text>Tổng số: </Text>
          <Text strong>{interactionData?.totalComment}</Text>
        </Col>
        <Col span={8}>
          <Text>Số người: </Text>
          <Text strong>{interactionData?.numberOfPeopleComment}</Text>
        </Col>
        <Col span={8}>
          <Text>Tỉ lệ: </Text>
          <Text strong>{interactionData?.ratioComment}</Text>
        </Col>
      </Row>
      <Title level={5}>Reactions</Title>
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={8}>
          <Text>Tổng số: </Text>
          <Text strong>{interactionData?.totalReact}</Text>
        </Col>
        <Col span={8}>
          <Text>Số người: </Text>
          <Text strong>{interactionData?.numberOfPeopleReact}</Text>
        </Col>
        <Col span={8}>
          <Text>Tỉ lệ: </Text>
          <Text strong>{interactionData?.ratioReact}</Text>
        </Col>
      </Row>

      <div style={{ textAlign: 'right', marginTop: 24 }}>
        <Button type="primary" onClick={handleSubmitExport} loading={exportChatAndReacMutation.isPending}>
          Xuất excel tương tác
        </Button>
      </div>
    </Modal>
  );
};

export default ModelReportChatAndReact;
