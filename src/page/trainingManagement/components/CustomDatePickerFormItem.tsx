import { Col, DatePicker, Form } from 'antd';
import { Rule } from 'antd/es/form';
import dayjs from 'dayjs';
import { FORMAT_DATE } from '../../../constants/common';

const { Item } = Form;

interface ICustomDatePickerFormItem {
  labelStart: string;
  nameStart: string;
  rulesStart?: Rule[];
  placeholderStart?: string;
  labelEnd: string;
  nameEnd: string;
  rulesEnd?: Rule[];
  placeholderEnd?: string;
  format?: string;
  isShowTime?: boolean;
}

const CustomDatePickerFormItem = (props: ICustomDatePickerFormItem) => {
  const form = Form.useFormInstance();
  const { labelStart, nameStart, rulesStart, labelEnd, nameEnd, rulesEnd, format = FORMAT_DATE, isShowTime } = props;
  return (
    <>
      <Col span={12}>
        <Item
          label={labelStart}
          name={nameStart}
          rules={rulesStart}
          getValueFromEvent={date => (date ? date.toISOString() : null)}
          getValueProps={value => ({
            value: value ? dayjs(value) : null,
          })}
        >
          <DatePicker
            showTime={isShowTime}
            format={format}
            disabledDate={current => {
              const endDate = form.getFieldValue(nameEnd);
              return (
                endDate && current > dayjs(endDate).startOf('minutes') // Disable ngày sau "Đến ngày"
              );
            }}
            placeholder="Chọn thời gian"
          />
        </Item>
      </Col>
      <Col span={12}>
        <Item
          label={labelEnd}
          name={nameEnd}
          rules={rulesEnd}
          getValueFromEvent={date => (date ? date.toISOString() : null)}
          getValueProps={value => ({
            value: value ? dayjs(value) : null,
          })}
        >
          <DatePicker
            format={format}
            showTime={isShowTime}
            disabledDate={current => {
              const startDate = form.getFieldValue(nameStart);
              return (
                startDate && current < dayjs(startDate).startOf('minutes') // Disable ngày trước "Ngày tạo"
              );
            }}
            placeholder="Chọn thời gian"
          />
        </Item>
      </Col>
    </>
  );
};

export default CustomDatePickerFormItem;
