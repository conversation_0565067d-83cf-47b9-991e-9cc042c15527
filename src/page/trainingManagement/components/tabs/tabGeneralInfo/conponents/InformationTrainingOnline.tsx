import { Col, Form, Input, Select } from 'antd';
import { useParams } from 'react-router-dom';
import SingleSelectLazy from '../../../../../../components/select/singleSelectLazy';
import { FORMAT_DATE_TIME, OPTIONS_SOURCE_LIVE } from '../../../../../../constants/common';
import { getAllProjectsOfCommission } from '../../../../../../service/commission';
import { TProjectDropdown } from '../../../../../../types/common/common';
import { handleKeyDownEnterNumber } from '../../../../../../utilities/regex';
import { useStoreTraining } from '../../../../storeTraining';
import CustomDatePickerFormItem from '../../../CustomDatePickerFormItem';
import { SwitchFormItem } from '../../../SwitchFormItem';
import { useEffect } from 'react';
import { slugify } from '../../../../../../utilities/shareFunc';

const { Item } = Form;
const { TextArea } = Input;

const InformationTrainingOnline = () => {
  const { id } = useParams();
  const form = Form.useFormInstance();
  const nameValue = Form.useWatch('name', form);

  const { openModalCreate, initialValues, setIsModified } = useStoreTraining();

  useEffect(() => {
    if (nameValue !== undefined) {
      form.setFieldValue('urlEvent', slugify(nameValue));
    }
  }, [form, nameValue]);

  const handleSelectProject = (values: TProjectDropdown) => {
    form.setFieldsValue({
      project: values
        ? {
            name: values?.name,
            id: values?.id,
          }
        : undefined,
    });

    setIsModified(true);
  };

  return (
    <>
      <Col span={12}>
        <Item label="Thời gian bắt đầu" name="timeStartEvent">
          <Input placeholder="Nhập thời gian bắt đầu" />
        </Item>
      </Col>
      <Col span={12}>
        <Item label="Link tên sự kiện" name="urlEvent">
          <Input placeholder="Tự động hiển thị link sự kiện" />
        </Item>
      </Col>
      <Col span={12}>
        <Item label="Nội dung sự kiện" name="contentEvent">
          <TextArea placeholder="Nhập nội dung sự kiện" rows={2} />
        </Item>
      </Col>
      <Col span={12}>
        <Item label="Nội dung kết thúc sự kiện" name="contentOffEvent">
          <TextArea placeholder="Nhập nội dung kết thúc sự kiện" rows={2} />
        </Item>
      </Col>
      <Col span={12}>
        <Item name="project" label="Dự án áp dụng" rules={[{ required: true, message: 'Vui lòng chọn dự án áp dụng' }]}>
          <SingleSelectLazy
            apiQuery={getAllProjectsOfCommission}
            queryKey={['list-projects']}
            enabled={!!openModalCreate || !!id}
            placeholder="Chọn dự án áp dụng"
            keysLabel={['name']}
            handleSelect={handleSelectProject}
            defaultValues={
              initialValues?.project && {
                value: initialValues?.project?.id,
                label: initialValues?.project?.name,
              }
            }
          />
        </Item>
      </Col>

      <Col span={12}>
        <Item
          label="Link Livestream"
          name="livestreamUrl"
          rules={[{ required: true, message: 'Vui lòng nhập link livestream', whitespace: true }]}
        >
          <Input placeholder="Nhập link livestream" />
        </Item>
      </Col>
      <Col span={12}>
        <Item
          label="Nguồn live"
          name="typeLiveStream"
          rules={[{ required: true, message: 'Vui lòng chọn nguồn live', whitespace: true }]}
        >
          <Select placeholder="Chọn nguồn livestream" options={OPTIONS_SOURCE_LIVE} />
        </Item>
      </Col>
      <Col span={12}>
        <Item label="Link đăng ký" name="registerUrl">
          <Input placeholder="Nhập link đăng ký sự kiện" />
        </Item>
      </Col>
      <Col span={12}>
        <Item label="Hotline" name="hotline">
          <Input placeholder="VD: 18002099" />
        </Item>
      </Col>
      <Col span={12}>
        <Item label="Số lượng người xem" name="fLive">
          <Input placeholder="Nhập số lượng người xem" onKeyDown={handleKeyDownEnterNumber} />
        </Item>
      </Col>

      <CustomDatePickerFormItem
        labelStart="Thời gian bắt đầu sự kiện"
        nameStart="displayBannerStartTime"
        rulesStart={[{ required: true, message: 'Vui lòng chọn thời gian' }]}
        labelEnd="Thời gian kết thúc sự kiện"
        nameEnd="displayBannerEndTime"
        rulesEnd={[{ required: true, message: 'Vui lòng chọn thời gian' }]}
        isShowTime={true}
        format={FORMAT_DATE_TIME}
      />
      <CustomDatePickerFormItem
        labelStart="Thời gian bắt đầu checkin"
        nameStart="startTimeCheckIn"
        rulesStart={[{ required: true, message: 'Vui lòng chọn thời gian' }]}
        labelEnd="Thời gian kết thúc checkin"
        nameEnd="endTimeCheckIn"
        rulesEnd={[{ required: true, message: 'Vui lòng chọn thời gian' }]}
        isShowTime={true}
        format={FORMAT_DATE_TIME}
      />

      <SwitchFormItem label="Cho phép bỏ qua đăng nhập" name="skipAuthen" />
      <SwitchFormItem label="Kết thúc sự kiện" name="isEventOff" />
      <SwitchFormItem label="Ẩn bình luận" name="hideChat" />
      <SwitchFormItem label="Ẩn reaction" name="hideReaction" />
      <SwitchFormItem label="Khoá bình luận" name="lockComment" />
      <SwitchFormItem label="Ẩn nút fullscreen" name="disableFullScreen" />
      <SwitchFormItem label="Cho phép tạo  yêu cầu tư vấn/đặt hàng" name="allowRequestDemand" />
      <SwitchFormItem label="Cho phép đặt câu hỏi" name="allowRaiseHand" />
      <SwitchFormItem label="Đăng ký không bắt buộc email" name="unRequiredEmailRegister" />
      <Col span={24}>
        <Item label="Mô tả sự kiện" name="eventDescription">
          <TextArea placeholder="Nhập mô tả sự kiện" rows={2} />
        </Item>
      </Col>
      <Col span={24}>
        <Item label="Ghi chú" name="note">
          <TextArea placeholder="Nhập ghi chú" rows={2} />
        </Item>
      </Col>
    </>
  );
};

export default InformationTrainingOnline;
