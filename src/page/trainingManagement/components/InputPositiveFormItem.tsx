import { Form, Input } from 'antd';
import { handleKeyDownEnterNumber } from '../../../utilities/regex';
import { Rule } from 'antd/es/form';

interface InputPositiveFormItemProps {
  label: string;
  name: string | (string | number)[];
  rules?: Rule[];
  placeholder?: string;
}

const InputPositiveFormItem = ({ label, name, rules, placeholder }: InputPositiveFormItemProps) => {
  const handleInput = (e: React.FormEvent<HTMLInputElement>) => {
    const input = e.currentTarget;

    // Xóa nếu nhập 0 hoặc số âm
    if (input.value === '0' || input.value.startsWith('-')) {
      input.value = '';
    }
  };

  return (
    <Form.Item label={label} name={name} rules={rules}>
      <Input placeholder={placeholder} onKeyDown={handleKeyDownEnterNumber} onInput={handleInput} inputMode="numeric" />
    </Form.Item>
  );
};

export default InputPositiveFormItem;
