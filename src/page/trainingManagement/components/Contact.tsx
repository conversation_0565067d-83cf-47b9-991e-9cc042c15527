import { Button, Checkbox, Form, Input } from 'antd';
import { useEffect, useRef, useState } from 'react';
import ReactQuill from 'react-quill';
import { MAX_LENGTH } from '../../../constants/common';
import RichEditor from '../../../components/richEditor';
import { useStoreTraining } from '../storeTraining';
import { useParams } from 'react-router-dom';

const { Item } = Form;
const { TextArea } = Input;

const ContactTraining = () => {
  const { id } = useParams();
  const [notificationForm] = Form.useForm();
  const [isEmailEnabled, setEmailEnabled] = useState(true);
  const [emailContent, setEmailContent] = useState('');
  const quillRef = useRef<ReactQuill>(null);
  const { eventType } = useStoreTraining();

  const handleQuillChange = (value: string) => {
    const editor = quillRef.current?.getEditor();
    const plainText = editor?.getText() || '';

    if (plainText.trim().length <= MAX_LENGTH) {
      setEmailContent(value);
    } else {
      // Prevent inserting more text
      const delta = editor?.getContents();
      if (delta && editor) {
        editor.setContents(delta.slice(0, MAX_LENGTH));
      }
    }
  };

  useEffect(() => {
    const editor = quillRef.current?.getEditor();
    if (!editor) return;

    editor.on('text-change', () => {
      const plainText = editor.getText() || '';
      if (plainText.trim().length > MAX_LENGTH) {
        editor.deleteText(MAX_LENGTH, plainText.length);
      }
    });
  }, []);

  const handleSendNotification = async () => {
    try {
      const values = await notificationForm.validateFields();
      console.log('Gửi thông báo với nội dung:', values.notification);
      // Gọi API hoặc xử lý gửi thông báo ở đây
    } catch (err) {
      console.log('Lỗi validate:', err);
    }
  };

  return (
    <>
      {eventType === 'ONLINE' && id && (
        <>
          <h3>Gửi thông báo trực tiếp</h3>
          <Form form={notificationForm} layout="vertical">
            <Form.Item
              label="Nội dung thông báo"
              name="notification"
              rules={[{ required: true, message: 'Vui lòng nhập nội dung thông báo', whitespace: true }]}
            >
              <TextArea placeholder="Nhập nội dung thông báo cần gửi" rows={2} />
            </Form.Item>
            <Button type="primary" style={{ marginBottom: '16px' }} onClick={handleSendNotification}>
              Gửi
            </Button>
          </Form>
        </>
      )}
      {/* Mẫu SMS */}
      <h3>Mẫu SMS</h3>
      <Item label="Tiêu đề sms" name={['templateConfig', 'smsBrandName']}>
        <Input placeholder="Nhập tiêu đề sms" />
      </Item>
      <Item name={['templateConfig', 'smsContent']}>
        <TextArea placeholder="Nhập nội dung sms" rows={2} />
      </Item>
      {/* Mẫu email */}
      <h3>Mẫu email</h3>
      <Item
        name={['templateConfig', 'resend']}
        label="Gửi lại email"
        valuePropName="checked"
        labelCol={{ span: 6 }}
        labelAlign="left"
        layout="horizontal"
      >
        <Checkbox checked={isEmailEnabled} onChange={e => setEmailEnabled(e.target.checked)} />
      </Item>
      <Item label="Tiêu đề mail" name={['templateConfig', 'subject']}>
        <Input placeholder="Nhập tiêu đề mail" disabled={!isEmailEnabled} />
      </Item>
      <Item name={['templateConfig', 'body']}>
        <RichEditor
          onChange={handleQuillChange}
          value={emailContent}
          ref={quillRef}
          placeholder='"Nhập nội dung email"'
        />
      </Item>
    </>
  );
};

export default ContactTraining;
