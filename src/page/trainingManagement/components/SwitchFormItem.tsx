import { Col, Form, Switch } from 'antd';
import { Rule } from 'antd/es/form';

interface ISwitchFormItem {
  label: string;
  name: string;
  rules?: Rule[];
}

export const SwitchFormItem = ({ label, name, rules }: ISwitchFormItem) => {
  return (
    <Col span={24}>
      <Form.Item
        labelAlign="left"
        layout={'horizontal'}
        labelCol={{ span: 12 }}
        label={label}
        name={name}
        valuePropName="checked"
        rules={rules}
      >
        <Switch />
      </Form.Item>
    </Col>
  );
};
