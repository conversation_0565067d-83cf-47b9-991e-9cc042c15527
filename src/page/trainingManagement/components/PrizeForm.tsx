import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Col, ColorPicker, Form, Input, Row, Typography } from 'antd';
import FormUploadImage from '../../../components/upload/FormUploadImage';
import InputPositiveFormItem from './InputPositiveFormItem';
import { useStoreTraining } from '../storeTraining';

const { Title } = Typography;
const { Item } = Form;

const PrizeForm = ({ isTransaction }: { isTransaction?: boolean }) => {
  const form = Form.useFormInstance();
  const { setIsModified } = useStoreTraining();

  const fieldName = isTransaction ? 'transactionPrizes' : 'prizes';
  const colorTitle = isTransaction ? 'colorTitleTransaction' : 'colorTitle';
  const colorPrize = isTransaction ? 'colorPrizeTransaction' : 'colorPrize';
  const colorWiner = isTransaction ? 'colorWinerTransaction' : 'colorWiner';

  return (
    <>
      <Col span={24}>
        <Item label="Tên chương trình quay số" name={isTransaction ? 'transactionPrizeName' : 'prizeName'}>
          <Input placeholder="VD: Quay số trúng thưởng - Nhân quà lí tưởng" />
        </Item>
      </Col>
      <Col span={12}>
        <Item label="Màu chữ" name={colorTitle}>
          <ColorPicker
            showText
            onChangeComplete={color => {
              form.setFieldValue(colorTitle, color.toHexString());
            }}
          />
        </Item>
      </Col>
      <Col span={12}>
        <Item label="Màu tên giải thưởng" name={colorPrize}>
          <ColorPicker
            showText
            onChangeComplete={color => {
              form.setFieldValue(colorPrize, color.toHexString());
            }}
          />
        </Item>
      </Col>
      <Col span={24}>
        <Title level={5}>Giải thưởng</Title>
        <Form.List name={fieldName}>
          {(fields, { add, remove }) => (
            <>
              <Form.Item>
                <Button type="default" onClick={() => add()} icon={<PlusOutlined />}>
                  Thêm giải
                </Button>
              </Form.Item>
              {fields.map(({ key, name, ...restField }) => {
                const defaultImage = form.getFieldValue([fieldName, name, 'prizeImage']);
                return (
                  <Row gutter={16} key={key} align={'top'}>
                    <Col span={14} style={{ marginBottom: 8 }}>
                      <Form.Item
                        {...restField}
                        name={[name, 'prizeName']}
                        rules={[{ required: true, message: 'Nhập tên giải thưởng' }]}
                      >
                        <Input placeholder="Tên giải thưởng" />
                      </Form.Item>

                      <Form.Item
                        {...restField}
                        name={[name, 'prizeValue']}
                        rules={[{ required: true, message: 'Nhập giá trị' }]}
                      >
                        <Input placeholder="Giá trị giải thưởng" />
                      </Form.Item>

                      <InputPositiveFormItem
                        label=""
                        rules={[{ required: true, message: 'Nhập số lượng' }]}
                        placeholder="Số lượng giải"
                        name={[name, 'amount']}
                      />
                    </Col>
                    <Col span={5}>
                      <FormUploadImage
                        path="image"
                        defaultImage={defaultImage}
                        reset={false}
                        fieldName={[fieldName, name, 'prizeImage']}
                        label="Ảnh giải thưởng"
                        textType="định dạng: jpeg, .jpg, .png, .svg, .gif"
                        moreTypes={['image/gif', 'image/svg+xml']}
                        onChange={() => {
                          setIsModified(true);
                        }}
                        iconUpload={<PlusOutlined style={{ color: '#00000073' }} />}
                      />
                    </Col>
                    <Col span={5}>
                      <Button type="default" danger icon={<DeleteOutlined />} onClick={() => remove(name)}>
                        Xoá giải
                      </Button>
                    </Col>
                  </Row>
                );
              })}
            </>
          )}
        </Form.List>
      </Col>
      <Col span={12}>
        <Item label="Màu tên người trúng thưởng" name={colorWiner}>
          <ColorPicker
            showText
            onChangeComplete={color => {
              form.setFieldValue(colorWiner, color.toHexString());
            }}
          />
        </Item>
      </Col>
    </>
  );
};

export default PrizeForm;
