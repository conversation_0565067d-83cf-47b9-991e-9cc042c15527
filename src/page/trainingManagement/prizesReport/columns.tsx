import { ColumnsType } from 'antd/es/table';
import { TRepo } from '../../../types/leadCommon';
import { ITrainingUser } from '../../../types/trainingUser';

export const columns: ColumnsType<ITrainingUser> = [
  {
    title: 'STT',
    dataIndex: 'index',
    key: 'index',
    width: 80,
    fixed: 'left',
    render: (_value, _record, index) => index + 1,
  },
  {
    title: 'Tên giải thưởng',
    dataIndex: 'priceName',
    key: 'priceName',
    width: 180,
    render: value => (value ? value : '-'),
  },
  {
    title: 'Trị giá',
    dataIndex: 'prizeValue',
    key: 'prizeValue',
    align: 'center',
    width: 200,
    render: value => (value ? value : '-'),
  },
  {
    title: 'Tên chương trình quay số',
    dataIndex: 'prizeName',
    key: 'prizeName',
    width: 200,
    render: (value: TRepo) => (value?.name ? value?.name : '-'),
  },
  {
    title: 'Tên kh<PERSON>ch hàng',
    dataIndex: 'name',
    key: 'name',
    width: 180,
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'SĐT',
    dataIndex: 'phone',
    key: 'phone',
    width: 180,
    render: (value: string) => (value ? value : '-'),
  },
];
