import { Button, Form, Tabs } from 'antd';
import { TabsProps } from 'antd/lib';
import { useBeforeUnload } from 'react-router-dom';
import ModalComponent from '../../../components/modal';
import { showConfirmCancelModal } from '../../../components/modal/specials/ConfirmCancelModal';
import { useCreateField } from '../../../hooks';
import { postTraining } from '../../../service/training';
import TabGeneralInfo from '../components/tabs/tabGeneralInfo';
import TabGuestList from '../components/tabs/tabGuestList';
import TabSpinThePrize from '../components/tabs/tabSpinThePrize';
import { useStoreTraining } from '../storeTraining';
import './styles.scss';
import { slugify } from '../../../utilities/shareFunc';

interface Props {
  onCancel: () => void;
}

const ModalCreateTraining = (props: Props) => {
  const { onCancel } = props;
  const [formGeneralInfo] = Form.useForm();
  const [formPrize] = Form.useForm();
  const { openModalCreate, setOpenModalCreate } = useStoreTraining();

  const { mutateAsync } = useCreateField({
    apiQuery: postTraining,
    keyOfListQuery: ['training'],
    isMessageError: false,
  });

  const handleSubmit = async () => {
    const formPrizeFields = formPrize.getFieldsValue(true);

    const generalInfo = await formGeneralInfo.validateFields().catch(() => {
      return { error: true, form: 'general' };
    });
    if (generalInfo?.error) {
      formGeneralInfo.scrollToField(generalInfo.form);
      return;
    }

    const newData = {
      ...generalInfo,
      urlEvent: slugify(generalInfo?.urlEvent),
      ...formPrizeFields,
    };
    const res = await mutateAsync(newData);
    if (res?.data?.statusCode === '0') {
      setOpenModalCreate(false);
      formGeneralInfo.resetFields();
      formPrize.resetFields();
    }
  };

  useBeforeUnload(event => {
    if (openModalCreate && (formGeneralInfo.isFieldsTouched() || formPrize.isFieldsTouched())) {
      event.preventDefault();
      return true;
    }
    return undefined;
  });

  const items: TabsProps['items'] = [
    { label: 'Thông tin chung', key: 'general-info', children: <TabGeneralInfo form={formGeneralInfo} /> },
    { label: 'Quay thưởng', key: 'prize', children: <TabSpinThePrize form={formPrize} /> },
    { label: 'Danh sách khách mời', key: 'guest-list', children: <TabGuestList />, disabled: true },
  ];

  return (
    <>
      <ModalComponent
        rootClassName="wrapper-create-of-training"
        open={openModalCreate}
        onCancel={() => {
          if (formGeneralInfo.isFieldsTouched() || formPrize.isFieldsTouched()) {
            showConfirmCancelModal({
              onConfirm: onCancel,
            });
            return;
          }
          onCancel();
        }}
        afterClose={() => {
          formGeneralInfo.resetFields();
          formPrize.resetFields();
        }}
        title="Tạo mới sự kiện"
        destroyOnClose
        footer={[
          <Button key="submit" type="primary" htmlType="submit" onClick={handleSubmit}>
            Lưu
          </Button>,
        ]}
      >
        <Tabs items={items} />
      </ModalComponent>
    </>
  );
};

export default ModalCreateTraining;
