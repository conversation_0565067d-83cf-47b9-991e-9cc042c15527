import { Flex, Modal, Typography } from 'antd';
import { TableProps } from 'antd/lib';
import dayjs from 'dayjs';
import { FORMAT_DATE_TIME } from '../../../constants/common';
import {
  CALCULATE_TYPES,
  COLOR_STATUS_DEBT_ADJUSTMENT_VERSION,
  DEBT_TYPES,
  LABEL_STATUS_DEBT_ADJUSTMENT_VERSION,
} from '../../../constants/debtCommission';
import {
  IAdjustmentVersions,
  ICustomerPenalty,
  InstallmentPenalty,
  ITransactionDebt,
  ITransactionDebtPenalty,
} from '../../../types/commissionDebt';

const columnTitleStyle = { whiteSpace: 'nowrap' };

export const columnsCommissionOriginal: TableProps<ITransactionDebt>['columns'] = [
  {
    title: <span style={columnTitleStyle}>Nhân viên công nợ</span>,
    dataIndex: 'debtCollector',
    key: 'debtCollector',
    width: 200,
    render: (value: { name: string; code: string }) =>
      value?.name || value?.code ? (
        <>
          {value.name}
          <br />
          {value.code}
        </>
      ) : (
        '-'
      ),
  },
  {
    title: <span style={columnTitleStyle}>Hợp đồng</span>,
    dataIndex: ['contract', 'name'],
    key: 'contractName',
    width: 200,
    render: (value: string) => value || '-',
  },
  {
    title: <span style={columnTitleStyle}>Số hợp đồng</span>,
    dataIndex: ['contract', 'code'],
    key: 'contractCode',
    render: (value: string) => value || '-',
  },
  {
    title: <span style={columnTitleStyle}>Khách hàng</span>,
    dataIndex: 'customer',
    key: 'customer',
    width: 200,
    render: (value: { name: string; code: string }) =>
      value?.name || value?.code ? (
        <>
          {value.name}
          <br />
          {value.code}
        </>
      ) : (
        '-'
      ),
  },
  {
    title: <span style={columnTitleStyle}>Đợt thanh toán</span>,
    dataIndex: 'installmentName',
    width: 120,
    render: (value: string) => value || '-',
  },
  {
    title: <span style={columnTitleStyle}>Tuổi nợ</span>,
    dataIndex: ['debtage', 'name'],
    key: 'debtAgeName',
    render: (value: string) => value || '-',
  },
  {
    title: <span style={columnTitleStyle}>Loại nợ</span>,
    dataIndex: 'debtType',
    key: 'debtAgeType',
    render: (value: string) => DEBT_TYPES.find(item => item.value === value)?.name || '-',
  },
  {
    title: <span style={columnTitleStyle}>Doanh thu thu hồi (VNĐ)</span>,
    dataIndex: ['employees', 0, 'commissions', 0, 'debtRevenue'],
    key: 'debtRevenue',
    render: (value: number) => (value ? value : '-'),
  },
  {
    title: <span style={columnTitleStyle}>Tính theo</span>,
    dataIndex: ['commissionPolicy', 'type'],
    key: 'commissionType',
    render: (value: string) => (value ? CALCULATE_TYPES.find(item => item?.value === value)?.label : '-'),
  },
  {
    title: <span style={columnTitleStyle}>Hệ số thù lao (%)</span>,
    dataIndex: ['commissionPolicy', 'rate'],
    key: 'commissionRate',
    render: (value: number) => (value ? value : '-'),
  },
  {
    title: <span style={columnTitleStyle}>Hoa hồng công nợ (VNĐ)</span>,
    dataIndex: ['employees', 0, 'commissions', 0, 'debtCommissionRevenue'],
    key: 'debtCommission',
    render: (value: number) => (value ? value.toLocaleString('vi-VN') : '-'),
  },
  {
    title: <span style={columnTitleStyle}>Tỉ lệ hoa hồng (%)</span>,
    dataIndex: ['employees', 0, 'commissions', 0, 'recordedCommission'],
    key: 'debtCommissionRate',
    align: 'center',
    render: (value: number) => (
      <a
        onClick={() => {
          Modal.confirm({
            title: 'Bảng luỹ tiến',
            icon: null,
            width: 600,
            footer: null,
            closable: true,
            centered: true,
            content: (
              <Flex justify="space-between" style={{ paddingTop: '8px' }}>
                <p>Doanh thu thu hồi theo mốc </p>
                <p>{value || '-'}</p>
                <p>{0}%</p>
              </Flex>
            ),
          });
        }}
      >
        Xem chi tiết
      </a>
    ),
  },
];

export const columnsPenaltyOriginal: TableProps<ITransactionDebtPenalty>['columns'] = [
  {
    title: <span style={columnTitleStyle}>Nhân viên công nợ</span>,
    dataIndex: 'debtCollector',
    key: 'debtCollector',
    width: 200,
    render: (value: { name: string; code: string }) =>
      value?.name || value?.code ? (
        <>
          {value.name}
          <br />
          {value.code}
        </>
      ) : (
        '-'
      ),
  },
  {
    title: <span style={columnTitleStyle}>Hợp đồng</span>,
    dataIndex: 'name',
    key: 'contractName',
    width: 200,
    render: (value: string) => value || '-',
  },
  {
    title: <span style={columnTitleStyle}>Số hợp đồng</span>,
    dataIndex: 'code',
    key: 'contractCode',
    render: (value: string) => value || '-',
  },
  {
    title: <span style={columnTitleStyle}>Khách hàng</span>,
    dataIndex: ['primaryTransaction', 'customer'],
    key: 'customer',
    width: 200,
    render: (value: ICustomerPenalty) =>
      value ? (
        <>
          {value?.personalInfo?.name || '-'}
          <br />
          {value?.code || '-'}
        </>
      ) : (
        '-'
      ),
  },
  {
    title: <span style={columnTitleStyle}>Đợt thanh toán</span>,
    dataIndex: ['policyPayment', 'schedule', 'installments'],
    width: 120,
    render: (value: InstallmentPenalty[]) => (value ? value?.map(item => item?.name).join(', ') : '-'),
  },
  {
    title: <span style={columnTitleStyle}>Số ngày quá hạn</span>,
    dataIndex: ['interestCalculations', 0, 'totalDelayDate'],
    key: 'totalDelayDate',
    render: (value: string) => value || '-',
  },
  {
    title: <span style={columnTitleStyle}>Tuổi nợ</span>,
    dataIndex: ['interestCalculations', 0, 'debtage', 'name'],
    key: 'debtAgeName',
    render: (value: string) => value || '-',
  },
  {
    title: <span style={columnTitleStyle}>Số tiền gốc</span>,
    dataIndex: ['interestCalculations', 0, 'needTransfer'],
    key: 'needTransfer',
    render: (value: number) => value || 0,
  },
  {
    title: <span style={columnTitleStyle}>Số tiền lãi</span>,
    dataIndex: ['interestCalculations', 0, 'interestAmount'],
    key: 'interestAmount',
    render: (value: number) => value || 0,
  },
  {
    title: <span style={columnTitleStyle}>Số tiền phạt</span>,
    dataIndex: ['interestCalculations', 0, 'latePaymentFee'],
    key: 'latePaymentFee',
    render: (value: number) => value || 0,
  },
  {
    title: <span style={columnTitleStyle}>Tổng nợ</span>,
    dataIndex: ['interestCalculations', 0, 'totalSettlementAmount'],
    key: 'totalSettlementAmount',
    render: (value: number) => value || 0,
  },
  {
    title: <span style={columnTitleStyle}>Tỷ lệ phạt</span>,
    dataIndex: ['penalty', 'amount'],
    key: 'amount',
    render: (value: string) => value || '-',
  },
  {
    title: <span style={columnTitleStyle}>Tính theo</span>,
    dataIndex: ['penalty', 'type '],
    key: 'type',
    render: (value: string) => value || '-',
  },
  {
    title: <span style={columnTitleStyle}>Số tiền phạt nhân viên</span>,
    dataIndex: 'PenaltyPerContract',
    key: 'PenaltyPerContract',
    render: (value: string) => value || '-',
  },
];

export const columnsCommissionAdjustment: TableProps<IAdjustmentVersions>['columns'] = [
  {
    title: 'Phiên bản',
    dataIndex: 'version',
    key: 'version',
  },
  {
    title: 'Ngày tải lên',
    dataIndex: 'uploadDate',
    key: 'uploadDate',
    render: (text, record) => (
      <div>
        {text ? dayjs(text).format(FORMAT_DATE_TIME) : '-'}
        <br />
        {record?.uploadBy ? record?.uploadBy : '-'}
      </div>
    ),
  },
  {
    title: 'Log file',
    dataIndex: 'fileName',
    key: 'fileName',
    render: (text, record: IAdjustmentVersions) => (
      <Typography.Link href={record?.fileUrl} target="_blank" download>
        {text}
      </Typography.Link>
    ),
  },
  {
    title: 'Trạng thái',
    dataIndex: 'status',
    key: 'status',
    align: 'center',
    render: value => {
      return value ? (
        <Typography.Text style={{ color: COLOR_STATUS_DEBT_ADJUSTMENT_VERSION[value] }}>
          {LABEL_STATUS_DEBT_ADJUSTMENT_VERSION[value]}
        </Typography.Text>
      ) : (
        'null'
      );
    },
  },
];
