import { TableProps, Tabs, TabsProps } from 'antd';
import { AnyObject } from 'antd/es/_util/type';
import { useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { IAdjustmentVersions } from '../../../types/commissionDebt';
import { useStoreCommissionDebt } from '../storeCommissionDebt';
import { columnsCommissionAdjustment, columnsCommissionOriginal, columnsPenaltyOriginal } from './columns';
import AdjustmentData from './components/AdjustmentData';
import OriginalData from './components/OriginalData';

interface ICommissionTabs<T extends AnyObject> {
  draftDataTransactions?: T[];
}

const CommissionTabs = <T extends AnyObject>({ draftDataTransactions }: ICommissionTabs<T>) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const params = Object.fromEntries([...searchParams]);
  const { initialValue } = useStoreCommissionDebt();
  const isTabCommissionDebt = params?.tabCommission === 'commission-debt';
  const [tab, setTab] = useState<string>(params?.tabData || 'original-data');
  const columnsOrigin = isTabCommissionDebt ? columnsCommissionOriginal : columnsPenaltyOriginal;

  const handleTabChange = (activeKey: string) => {
    setTab(activeKey);
    setSearchParams({ ...params, tabData: activeKey });
  };

  const items: TabsProps['items'] = [
    {
      key: 'original-data',
      label: 'Dữ liệu gốc',
      children: (
        <OriginalData<T>
          columns={columnsOrigin as TableProps<T>['columns']}
          initialValueTransaction={draftDataTransactions || (initialValue?.transactions as unknown as T[])}
          filterFields={['debtCollector', 'contract', 'customer']}
        />
      ),
    },
    {
      key: 'Adjustment-data',
      label: 'Dữ liệu điều chỉnh',
      children: (
        <AdjustmentData<IAdjustmentVersions>
          initialValue={initialValue?.adjustmentVersions}
          columns={columnsCommissionAdjustment}
        />
      ),
    },
  ];
  return (
    <div>
      <Tabs
        type="card"
        activeKey={tab}
        onChange={handleTabChange}
        className="tabs-card-calculate"
        size="small"
        items={items}
        tabBarStyle={{
          marginBottom: 16,
        }}
      />
    </div>
  );
};

export default CommissionTabs;
