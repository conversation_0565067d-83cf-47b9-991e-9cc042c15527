import { QueryKey, useQueryClient } from '@tanstack/react-query';
import { Button, notification, Typography, Upload } from 'antd';
import { RcFile } from 'antd/es/upload';
import { AxiosError } from 'axios';
import { useState } from 'react';
import { useParams } from 'react-router-dom';
import { postUploadAdjustmentVersionDebt } from '../../../../service/commissionDebt';

const UploadFileAdjustmentVer = ({ pathRedirect, queryKey }: { pathRedirect: string; queryKey?: QueryKey }) => {
  const { id } = useParams();
  const queryClient = useQueryClient();

  const [isLoadingImport, setIsLoadingImport] = useState(false);

  const handleBeforeUpload = async (file: RcFile) => {
    const isAllowedType = file?.name.toLowerCase().endsWith('.xlsx');
    if (!isAllowedType) {
      notification.error({ message: 'File không đúng định dạng. Vui lòng sử dụng file .xlsx' });
      return Upload.LIST_IGNORE;
    }
    return true;
  };
  return (
    <Upload
      beforeUpload={handleBeforeUpload}
      showUploadList={false}
      customRequest={async ({ file, onSuccess, onError }) => {
        try {
          setIsLoadingImport(true);
          if (file instanceof Blob) {
            const response = await postUploadAdjustmentVersionDebt({
              file: file as RcFile,
              commissionId: id!,
            });
            const { statusCode } = response.data;

            const link = (
              <Typography.Link href={pathRedirect} target="_blank">
                đây
              </Typography.Link>
            );
            if (statusCode === '0') {
              notification.success({
                message: <>Hoàn thành upload giao dịch</>,
              });
              queryClient.invalidateQueries({
                queryKey,
              });
            } else {
              notification.error({
                message: <>Upload thất bại, xem tại {link}</>,
              });
              onSuccess && onSuccess('ok');
            }
          }
        } catch (error: unknown) {
          onError && onError(error as AxiosError);
        } finally {
          setIsLoadingImport(false);
        }
      }}
      name="file-upload"
    >
      <Button type="default" loading={isLoadingImport}>
        Tải nhập giao dịch
      </Button>
    </Upload>
  );
};

export default UploadFileAdjustmentVer;
