import { App, Button, Space } from 'antd';
import { ButtonType } from 'antd/es/button';
import React from 'react';
import { useParams, useSearchParams } from 'react-router-dom';
import { modalConfirm } from '../../../../components/modal/specials/ModalConfirm';
import { STATUS_DEBT_ADJUSTMENT_VERSION } from '../../../../constants/debtCommission';
import { PERMISSION_DEBT_COMMISSION } from '../../../../constants/permissions/debtCommission';
import { useCheckPermissions, useUpdateField } from '../../../../hooks';
import { putChangeStatusCommissionDebt } from '../../../../service/commissionDebt';
import { IAdjustmentVersions } from '../../../../types/commissionDebt';
import { useStoreCommissionDebt } from '../../storeCommissionDebt';
import UploadFileAdjustmentVer from './UploadFileAdjustmentVer';

interface GroupButtonProps {
  versionAdjustment?: IAdjustmentVersions | null;
  checkButtonConfirm: boolean;
}
interface ButtonConfig {
  key: string;
  label: string;
  permission?: boolean;
  onClick?: () => void;
  type?: ButtonType;
  component?: React.ReactNode;
  loading?: boolean;
}

const GroupButtonActionDebt: React.FC<GroupButtonProps> = ({ versionAdjustment, checkButtonConfirm }) => {
  const { id } = useParams();
  const [searchParams] = useSearchParams();
  const tabData = searchParams.get('tabData');
  const tabCommission = searchParams.get('tabCommission');
  const { modal, notification } = App.useApp();

  const { initialValue } = useStoreCommissionDebt();
  const nameFile =
    tabCommission === 'commission-debt'
      ? `Debt_Commission_List_${initialValue?.name}`
      : ` Debt_Penalty_List_${initialValue?.name}`;

  const { update, getId } = useCheckPermissions(PERMISSION_DEBT_COMMISSION);

  const { mutateAsync, isPending } = useUpdateField({
    apiQuery: putChangeStatusCommissionDebt,
    keyOfDetailQuery: ['detail-of-commission-debt', id],
    isMessageError: false,
    isMessageSuccess: false,
  });

  const handleDownloadOriginal = async () => {
    const linkDownload = initialValue?.adjustmentVersions?.[0]?.fileUrl;

    if (!linkDownload) {
      notification.error({ message: 'Không tìm thấy đường dẫn tải xuống' });
      return;
    }
    const response = await fetch(linkDownload);
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${nameFile}`;
    link.click();
    window.URL.revokeObjectURL(url);
  };

  const handleChangeStatus = async () => {
    if (!versionAdjustment || !id) return;
    const res = await mutateAsync({
      id: id!,
      status:
        versionAdjustment?.status === STATUS_DEBT_ADJUSTMENT_VERSION.CREATED
          ? STATUS_DEBT_ADJUSTMENT_VERSION.CONFIRMED
          : STATUS_DEBT_ADJUSTMENT_VERSION.CREATED,
      adjustmentVersionId: versionAdjustment?.id,
    });
    if (res?.data?.statusCode === '0') {
      notification.success({
        message: `${versionAdjustment?.status === STATUS_DEBT_ADJUSTMENT_VERSION.CREATED ? 'xác nhận sử dụng' : 'huỷ sử dụng'} thành công`,
      });
    }
  };

  const buttonConfigs: ButtonConfig[] = [
    {
      key: 'confirmUse',
      label: 'Xác nhận sử dụng',
      loading: isPending,
      permission: update && checkButtonConfirm && !!versionAdjustment,
      onClick: () => {
        modalConfirm({
          title: 'Xác nhận sử dụng',
          content: 'Xác nhận sử dụng, Chi tiết bên dưới?',
          handleConfirm: () => handleChangeStatus(),
          modal,
        });
      },
      type: 'default',
    },
    {
      key: 'cancelUse',
      label: 'Huỷ sử dụng',
      permission: update && tabData !== 'original-data' && !checkButtonConfirm && !!versionAdjustment,
      onClick: () => {
        modalConfirm({
          title: 'Huỷ sử dụng',
          content: 'Huỷ sử dụng, Chi tiết bên dưới?',
          handleConfirm: () => handleChangeStatus(),
          modal,
        });
      },
      type: 'default',
    },
    {
      key: 'uploadTemplate',
      label: 'Tải nhập giao dịch',
      permission: update,
      component: <UploadFileAdjustmentVer pathRedirect="/" />,
    },
    {
      key: 'downloadOriginal',
      label: 'Tải về dữ liệu gốc',
      permission: update || getId,
      onClick: handleDownloadOriginal,
      type: 'default',
    },
  ];
  return (
    <Space>
      {buttonConfigs.map(button => {
        if (!button.permission) return null;

        return (
          <React.Fragment key={button.key}>
            {button.component ? (
              button.component
            ) : (
              <Button className={button.key} loading={button?.loading} type={button.type} onClick={button.onClick}>
                {button.label}
              </Button>
            )}
          </React.Fragment>
        );
      })}
    </Space>
  );
};

export default GroupButtonActionDebt;
