import { App, But<PERSON>, Col, Form, Input, Row, Spin, Tabs, TabsProps, Typography } from 'antd';
import dayjs from 'dayjs';
import { useCallback, useEffect, useState } from 'react';
import { useBeforeUnload, useParams, useSearchParams } from 'react-router-dom';
import BreadCrumbComponent from '../../../components/breadCrumb';
import FormPeriodDebt from '../../../components/select/FormPeriodDebt';
import SingleSelectLazy from '../../../components/select/singleSelectLazy';
import { FORMAT_DATE_TIME } from '../../../constants/common';
import { PERMISSION_DEBT_COMMISSION } from '../../../constants/permissions/debtCommission';
import { useCheckPermissions, useCreateField, useFetch, useUpdateField } from '../../../hooks';
import {
  getByIdCommissionDebt,
  postCommissionDebtCalculate,
  postPenaltyCalculate,
  postPenaltyMergeData,
  putUpdateCommissionDebt,
} from '../../../service/commissionDebt';
import {
  getAllOfCommissionDebtPolicy,
  getProjectsForCommissionDebtPolicy,
} from '../../../service/commissionDebtPolicy';
import {
  IDataSubmitCalculate,
  IDataSubmitUpdate,
  ITransactionDebt,
  TCommissionPeriodDetail,
} from '../../../types/commissionDebt';
import { ICommissionDebtPolicy, ProjectDebtPolicy } from '../../../types/commissionDebtPolicy';
import { useStoreCommissionDebt } from '../storeCommissionDebt';
import CommissionTabs from './CommissionTabs';
import ButtonOfPageDetail from '../../../components/button/buttonOfPageDetail';
import './styles.scss';
import PenaltyDebtTab from './PenaltyDebtTab';

const { Title, Text } = Typography;

const DetailOfDebtCommission = () => {
  const { id } = useParams();
  const { notification } = App.useApp();
  const [form] = Form.useForm();
  const [searchParams, setSearchParams] = useSearchParams();
  const params = Object.fromEntries([...searchParams]);
  const period = Form.useWatch('period', form);
  const formProject = Form.useWatch('project', form);
  const commissionPolicy = Form.useWatch('commissionPolicy', form);
  const { update } = useCheckPermissions(PERMISSION_DEBT_COMMISSION);

  const [tab, setTab] = useState<string>(params?.tabCommission || 'commission-penalty');
  const [draftTransactions, setDraftTransactions] = useState<ITransactionDebt[]>();
  const [dataDraftPenaltyTransactions, setDataDraftPenaltyTransactions] = useState<ITransactionDebt[]>([]);
  const { initialValue, setInitialValue, setIsModified, isModified } = useStoreCommissionDebt();

  const { data, isLoading } = useFetch<TCommissionPeriodDetail>({
    api: () => getByIdCommissionDebt(id!),
    queryKeyArr: ['detail-of-commission-debt', id],
    enabled: !!id,
  });
  const dataSource = data?.data?.data;

  const calculatePeriod = useCreateField({
    apiQuery: postCommissionDebtCalculate,
    isMessageError: false,
    isMessageSuccess: false,
  });

  const calculatePeriodPenalty = useCreateField({
    apiQuery: postPenaltyCalculate,
    isMessageError: false,
    isMessageSuccess: false,
  });

  const updateCommissionDebt = useUpdateField({
    apiQuery: putUpdateCommissionDebt,
    keyOfDetailQuery: ['detail-of-commission-debt', id],
    isMessageError: false,
  });

  const mergeData = useCreateField({
    apiQuery: postPenaltyMergeData,
    keyOfDetailQuery: ['debt-expenses-list', id],
    isMessageError: false,
    isMessageSuccess: false,
  });

  useEffect(() => {
    if (dataSource) {
      setInitialValue(dataSource);
      form.setFieldsValue(dataSource);
    }
  }, [dataSource, form, setInitialValue]);

  const handleSubmit = async () => {
    try {
      const { code, project, periodFrom, periodTo, year, periodName, commissionPolicy } = form.getFieldsValue(true);
      await form.validateFields();

      const payload: IDataSubmitUpdate = {
        id: id!,
        projectId: project.code,
        commissionCode: code,
        periodName,
        periodFrom,
        periodTo,
        year,
        commissionPolicyCode: commissionPolicy?.code,
      };

      if (tab === 'commission-penalty' && dataDraftPenaltyTransactions.length > 0) {
        await mergeData.mutateAsync({
          id: id!,
          modelList: dataDraftPenaltyTransactions,
        });
      }

      // After successful merge (or if not on penalty tab), proceed with update
      const res = await updateCommissionDebt.mutateAsync(payload);
      if (res?.data?.statusCode === '0') {
        setIsModified(false);
      }
    } catch (error) {
      console.error('Submit failed:', error);
    }
  };

  const handleCalculate = async () => {
    try {
      const { code, project, periodFrom, periodTo, year, commissionPolicy } = form.getFieldsValue(true);
      await form.validateFields();

      const payload: IDataSubmitCalculate = {
        commissionCode: code,
        projectId: project.code,
        periodFrom,
        periodTo,
        year,
        commissionPolicyCode: commissionPolicy?.code,
      };

      const res = await calculatePeriod.mutateAsync(payload);
      if (res?.data?.statusCode === '0') {
        notification.success({
          message: 'Tính hoa hồng thành công',
        });
        setDraftTransactions((res?.data?.data as { transactions?: ITransactionDebt[] })?.transactions || []);
      }
    } catch (error) {
      console.error('Calculate failed:', error);
    }
  };

  const handleCalculatePenalty = async () => {
    try {
      const res = await calculatePeriodPenalty.mutateAsync({
        debtCommissionId: id!,
      });
      if (res?.data?.statusCode === '0') {
        setDataDraftPenaltyTransactions(Array.isArray(res?.data?.data) ? res.data.data : []);
        setIsModified(true);
      }
    } catch (error) {
      console.error('Calculate penalty failed:', error);
    }
  };

  const handleSelectProject = (value: ProjectDebtPolicy) => {
    setIsModified(true);
    form.setFieldsValue({
      project: value
        ? {
            id: value.id,
            name: value.name,
            code: value.code,
          }
        : undefined,
    });
    clearFormValueCommissionPolicy;
  };

  const handleSelectCommissionPolicy = (value: ICommissionDebtPolicy) => {
    form.setFieldsValue({
      commissionPolicy: value ? value : undefined,
    });
    setIsModified(true);
  };

  const clearFormValueCommissionPolicy = useCallback(() => {
    form.setFieldValue('commissionPolicy', undefined);
  }, [form]);

  useBeforeUnload(event => {
    if (isModified) {
      event.preventDefault();
      return true;
    }
    return undefined;
  });

  const handleCancel = () => {
    form.setFieldsValue(initialValue);
    setIsModified(false);
  };

  const handleTabChange = (activeKey: string) => {
    setTab(activeKey);
    setSearchParams({ ...params, tabCommission: activeKey });
  };

  const items: TabsProps['items'] = [
    {
      key: 'commission-penalty',
      label: 'Tính tiền phạt',
      children: (
        <PenaltyDebtTab<ITransactionDebt>
          key={'commission-penalty'}
          draftDataTransactions={dataDraftPenaltyTransactions}
        />
      ),
    },
    {
      key: 'commission-debt',
      label: 'Tính hoa hồng',
      children: <CommissionTabs<ITransactionDebt> key={'commission-debt'} draftDataTransactions={draftTransactions} />,
    },
  ];

  return (
    <Spin spinning={isLoading}>
      <div className="wrapper-detail-commission-debt">
        <BreadCrumbComponent titleBread={dataSource?.code} />
        <Form
          form={form}
          layout="vertical"
          onValuesChange={() => setIsModified(true)}
          onFinish={handleSubmit}
          initialValues={initialValue}
        >
          <Title level={5}>Thông tin chi tiết</Title>
          <Row gutter={{ md: 24, lg: 40, xl: 80, xxl: 126 }}>
            <Col sm={12} xs={24}>
              <Form.Item label="Mã đợt tính hoa hồng" required name="code">
                <Input disabled />
              </Form.Item>

              <Form.Item label="Chọn dự án" name="project" rules={[{ required: true, message: 'Vui lòng chọn dự án' }]}>
                <SingleSelectLazy
                  gcTime={1000 * 60 * 15} // 15 phuts
                  staleTime={1000 * 60 * 5} // 5 phút
                  apiQuery={getProjectsForCommissionDebtPolicy}
                  queryKey={['list-projects']}
                  enabled={!!id}
                  allowClear={false}
                  placeholder="Chọn dự án"
                  keysLabel={['name']}
                  handleSelect={handleSelectProject}
                  defaultValues={{
                    ...initialValue?.project,
                    label: initialValue?.project?.name,
                    value: initialValue?.project?.code,
                  }}
                />
              </Form.Item>

              <FormPeriodDebt
                required
                label="Kỳ tính hoa hồng công nợ"
                fieldName="project"
                clearFormValueDependency={clearFormValueCommissionPolicy}
              />

              <Form.Item
                label="Bộ chỉ tiêu KPI công nợ"
                name="commissionPolicy"
                rules={[{ required: true, message: 'Vui lòng chọn bộ chỉ tiêu KPI công nợ' }]}
              >
                <SingleSelectLazy
                  apiQuery={getAllOfCommissionDebtPolicy}
                  queryKey={['list-of-commission-debt-policy']}
                  enabled={!!formProject?.id && !!period}
                  moreParams={{ project: formProject?.id, period: period, isActive: 1 }}
                  placeholder="Chọn bộ chỉ tiêu KPI công nợ"
                  keysLabel={['code', 'name']}
                  handleSelect={handleSelectCommissionPolicy}
                  disabled={!formProject?.id || !period}
                  defaultValues={{
                    ...form.getFieldValue('commissionPolicy'),
                    value: form.getFieldValue('commissionPolicy')?.id,
                    label: form.getFieldValue('commissionPolicy')?.name,
                  }}
                />
              </Form.Item>
              {update && (
                <Form.Item>
                  <Button
                    type="primary"
                    disabled={!formProject || !period || !commissionPolicy}
                    onClick={() => (tab === 'commission-penalty' ? handleCalculatePenalty() : handleCalculate())}
                  >
                    Tính hoa hồng
                  </Button>
                </Form.Item>
              )}
            </Col>
            <Col sm={12} xs={24}>
              <Row gutter={24}>
                <Col lg={6} xs={8}>
                  <Text disabled>Ngày cập nhật: </Text>
                </Col>
                <Col lg={18} xs={16}>
                  <Text disabled>
                    {dayjs(dataSource?.createdDate).format(FORMAT_DATE_TIME)}&nbsp;&nbsp;
                    {`${dataSource?.createdBy?.userName} - ${dataSource?.createdBy?.fullName}`}
                  </Text>
                </Col>
              </Row>
              <Row gutter={24}>
                <Col lg={6} xs={8}>
                  <Text disabled>Ngày tạo: </Text>
                </Col>
                <Col lg={18} xs={16}>
                  <Text disabled>
                    {dayjs(dataSource?.modifiedDate).format(FORMAT_DATE_TIME)}&nbsp;&nbsp;
                    {`${dataSource?.modifiedBy?.userName} - ${dataSource?.modifiedBy?.fullName}`}
                  </Text>
                </Col>
              </Row>
            </Col>
          </Row>
        </Form>

        <Tabs
          activeKey={tab}
          onChange={handleTabChange}
          items={items}
          tabBarStyle={{
            marginBottom: 16,
          }}
        />
      </div>
      {isModified && update && (
        <ButtonOfPageDetail
          handleSubmit={() => form.submit()}
          handleCancel={handleCancel}
          loadingSubmit={mergeData.isPending || updateCommissionDebt.isPending}
        />
      )}
    </Spin>
  );
};

export default DetailOfDebtCommission;
