import { Flex, Typography } from 'antd';
import { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { useState } from 'react';
import BreadCrumbComponent from '../../../components/breadCrumb';
import TableComponent from '../../../components/table';
import { ActionsColumns } from '../../../components/table/components/ActionsColumns';
import { FORMAT_DATE } from '../../../constants/common';
import { PERMISSION_DEBT_COMMISSION } from '../../../constants/permissions/debtCommission';
import { useCheckPermissions, useFetch } from '../../../hooks';
import { deleteRequestCommissionDebt, getAllCommissionDebt } from '../../../service/commissionDebt';
import { ICommissionDebt, TFilterCommissionDebt } from '../../../types/commissionDebt';
import CreateCommissionDebt from '../modalCreate';
import FilterListOfCommissionDebt from './FilterListOfCommissionDebt';
import ConfirmDeleteModal from '../../../components/modal/specials/ConfirmDeleteModal';
import { MutationFunction } from '@tanstack/react-query';
import { COMMISSION_DEBT_PENALTY } from '../../../configs/path';

const { Text } = Typography;

function ListOfCommissionDebt() {
  const { getId, update, delete: deleteCommission } = useCheckPermissions(PERMISSION_DEBT_COMMISSION);
  const [filterParams, setFilterParams] = useState<TFilterCommissionDebt>({});
  const [isOpenModalDelete, setIsOpenModalDelete] = useState<boolean>(false);
  const [currentRecord, setCurrentRecord] = useState<ICommissionDebt>();

  const { data, isLoading } = useFetch<ICommissionDebt[]>({
    api: getAllCommissionDebt,
    queryKeyArrWithFilter: ['list-of-commission-debt-penalty', filterParams],
    moreParams: { ...filterParams },
  });
  const dataSource = data?.data?.data?.rows || [];

  const columns: ColumnsType<ICommissionDebt> = [
    {
      title: 'Mã đợt tính hoa hồng',
      dataIndex: 'code',
      key: 'code',
      render: (value: string) => (value ? value : '-'),
    },
    {
      title: 'Dự án',
      dataIndex: 'project',
      key: 'project',
      render: value => (value?.name ? value?.name : '-'),
    },
    {
      title: 'Kỳ tính hoa hồng',
      dataIndex: 'period',
      key: 'period',
      render: value => (value ? value : '-'),
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdDate',
      key: 'createdDate',
      render: (value: string, record) =>
        value ? (
          <>
            <Text>{dayjs(value).format(FORMAT_DATE)}</Text>
            <br />
            <Text>{record?.createdBy?.fullName ? record.createdBy.fullName : '-'}</Text>
          </>
        ) : (
          '-'
        ),
    },
    {
      title: 'Ngày cập nhật',
      dataIndex: 'modifiedDate',
      key: 'modifiedDate',
      render: (value: string, record) =>
        value ? (
          <>
            <Text>{dayjs(value).format(FORMAT_DATE)}</Text>
            <br />
            <Text>{record?.modifiedBy?.fullName ? record.modifiedBy.fullName : '-'}</Text>
          </>
        ) : (
          '-'
        ),
    },
    {
      title: 'Hành động',
      dataIndex: 'action',
      key: 'action',
      width: '100px',
      align: 'center',
      render: (_: unknown, record) => {
        const handleDeleteRole = () => {
          setIsOpenModalDelete(true);
          setCurrentRecord(record);
        };

        const openViewDetail = () => {
          window.open(`${COMMISSION_DEBT_PENALTY}/${record?.id}`, '_blank', 'noopener,noreferrer');
        };

        return (
          <ActionsColumns
            handleViewDetail={getId || update ? openViewDetail : undefined}
            handleDelete={deleteCommission ? handleDeleteRole : undefined}
          />
        );
      },
    },
  ];

  return (
    <div className="wrapper-list-of-debt-policy">
      <BreadCrumbComponent />
      <Flex justify="space-between" style={{ marginBottom: 16 }}>
        <FilterListOfCommissionDebt setFilterParams={setFilterParams} />
        <CreateCommissionDebt />
      </Flex>
      <TableComponent
        columns={columns}
        dataSource={dataSource}
        loading={isLoading}
        rowKey="id"
        queryKeyArr={['list-of-commission-debt-policy', filterParams]}
      />
      <ConfirmDeleteModal
        label="đợt tính hoa hồng"
        fieldNameReason="reasonDelete"
        open={isOpenModalDelete}
        apiQuery={deleteRequestCommissionDebt as MutationFunction<unknown, unknown>}
        keyOfListQuery={['list-of-commission-debt-penalty', filterParams]}
        onCancel={() => setIsOpenModalDelete(false)}
        idDetail={currentRecord?.id}
        title="xóa đợt tính hoa hồng"
        description="Vui lòng nhập lý do muốn xoá đợt tính hoa hồng này"
      />
    </div>
  );
}

export default ListOfCommissionDebt;
