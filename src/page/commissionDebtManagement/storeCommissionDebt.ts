import { create } from 'zustand';
import { TCommissionPeriodDetail } from '../../types/commissionDebt';

interface IStoreCommissionDebt {
  initialValue?: TCommissionPeriodDetail;
  isModified?: boolean;
  setIsModified: (value: boolean) => void;
  setInitialValue: (value: TCommissionPeriodDetail) => void;
}

export const useStoreCommissionDebt = create<IStoreCommissionDebt>(set => ({
  initialValue: undefined,
  isModified: false,
  setIsModified: (value: boolean) => set(() => ({ isModified: value })),
  setInitialValue: (value: TCommissionPeriodDetail) => set(() => ({ initialValue: value })),
}));
