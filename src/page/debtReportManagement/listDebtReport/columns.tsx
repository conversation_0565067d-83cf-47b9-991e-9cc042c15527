import { TableColumnsType, Typography } from 'antd';
import dayjs from 'dayjs';
import {
  FORMAT_DATE_TIME,
  OPTIONS_STATUS_ASSIGN_DEBT_REPORT,
  OPTIONS_STATUS_DEBT_REMINDER,
  OPTIONS_STATUS_PAYMENT_DEBT_REPORT,
} from '../../../constants/common';

const { Text } = Typography;

export const columns: TableColumnsType = [
  {
    title: 'Tên hợp đồng',
    dataIndex: 'contractName',
    key: 'contractName',
    width: 140,
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Dự án',
    dataIndex: 'project',
    key: 'projectName',
    width: 140,
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Loại sản phẩm',
    dataIndex: 'propertyType',
    width: 140,
    key: 'propertyType',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Số sản phẩm',
    dataIndex: 'propertyCode',
    width: 140,
    key: 'propertyCode',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Mã khách hàng',
    dataIndex: 'customerCode',
    width: 140,
    key: 'customerCode',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Khách hàng',
    dataIndex: 'customerName',
    width: 200,
    key: 'customerName',
    render: (value: string) => (value ? value : '-'),
  },
  // {
  //   title: 'Nhân viên công nợ',
  //   dataIndex: ['primaryTransaction', 'customer', 'personalInfo', 'name'],
  //   width: 200,
  //   key: 'customerPersonalInfoName',
  //   render: (value: string) => (value ? value : '-'),
  // },
  {
    title: 'Đợt thanh toán',
    dataIndex: 'installmentName',
    width: 200,
    key: 'installmentName',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Ngày dự kiến thanh toán',
    dataIndex: 'paymentDueDate',
    key: 'paymentDueDate',
    width: 200,

    render: (value: string) => (
      <>
        <Text>{dayjs(value).format(FORMAT_DATE_TIME)}</Text>
      </>
    ),
  },
  {
    title: 'Ngày thanh toán',
    dataIndex: 'receiptDate',
    key: 'receiptDate',
    width: 200,

    render: (value: string) => (
      <>
        <Text>{value ? dayjs(value).format(FORMAT_DATE_TIME) : '-'}</Text>
      </>
    ),
  },
  {
    title: 'Trạng thái nhắc nợ',
    dataIndex: 'debtReminderStatus',
    key: 'debtReminderStatus',
    width: 200,
    render: (value: string) => {
      const status = OPTIONS_STATUS_DEBT_REMINDER.find(item => item.value === value);
      return <Text style={{ color: status?.color }}>{status?.label}</Text>;
    },
  },
  {
    title: 'Số ngày trễ hạn',
    dataIndex: 'totalDelayDate',
    width: 200,
    key: 'totalDelayDate',
    render: (value: number) => value,
  },
  {
    title: 'Tuổi nợ',
    dataIndex: 'debtage',
    width: 200,
    key: 'debtage',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Công nợ (VND)',
    dataIndex: 'needTransfer',
    width: 200,
    key: 'needTransfer',
    render: (value: number) => value,
  },
  {
    title: 'Phí phạt (VND)',
    dataIndex: 'latePaymentFee',
    width: 200,
    key: 'latePaymentFee',
    render: (value: number) => value,
  },
  {
    title: 'Lãi phạt (VND)',
    dataIndex: 'interestAmount',
    width: 200,
    key: 'interestAmount',
    render: (value: number) => value,
  },
  {
    title: 'Tổng tiền cần TT (VND)',
    dataIndex: 'totalSettlementAmount',
    width: 200,
    key: 'totalSettlementAmount',
    render: (value: number) => value,
  },
  {
    title: 'Số tiền đã TT (VND)',
    dataIndex: 'interestAmountTransferred',
    width: 200,
    key: 'interestAmountTransferred',
    render: (value: number) => value,
  },
  {
    title: 'Trạng thái TT',
    dataIndex: 'paymentStatus',
    key: 'paymentStatus',
    width: 200,
    render: (value: string) => {
      const status = OPTIONS_STATUS_PAYMENT_DEBT_REPORT.find(item => item.value === value);
      return <Text style={{ color: status?.color }}>{status?.label}</Text>;
    },
  },
  {
    title: 'Trạng thái phân bổ',
    dataIndex: 'assignStatus',
    key: 'assignStatus',
    width: 200,
    render: (value: string) => {
      const status = OPTIONS_STATUS_ASSIGN_DEBT_REPORT.find(item => item.value === value);
      return <Text style={{ color: status?.color }}>{status?.label}</Text>;
    },
  },
];
