.wrapper-list-personal-customers {
  .table-personal-customers {
    .ant-table-cell {
      .cell-name {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
      }
    }
    .ant-table-tbody {
      .ant-table-selection-column {
        border-right: 1px solid #0000000f;
        border-left: 1px solid #0000000f;
      }
    }
  }
}
.modal-confirm-delete-assigner {
  .ant-modal-content {
    .ant-modal-body {
      .description {
        padding-bottom: 12px;
      }
    }
    .ant-modal-title {
      font-size: 20px;
    }
    .ant-modal-footer {
      display: flex;
      justify-content: center;
      margin-top: 26px;
      .ant-btn {
        width: 100%;
      }
      .ant-btn-text {
        background: #0000000f;
        border: none;
        &:hover {
          background: #00000014;
        }
      }
    }
  }
}
