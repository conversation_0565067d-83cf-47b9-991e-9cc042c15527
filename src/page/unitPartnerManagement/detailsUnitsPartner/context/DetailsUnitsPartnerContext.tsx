import React, { createContext, useReducer, ReactNode } from 'react';
import { reducer } from './reducer';
import { initialDetailsUnitsPartnerState, DetailsUnitsPartnerState } from './state';

interface DetailsUnitsPartnerContextProps {
  state: DetailsUnitsPartnerState;
}

export const DetailsUnitsPartnerContext = createContext<DetailsUnitsPartnerContextProps | undefined>(undefined);

export const DetailsUnitsPartnerProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state] = useReducer(reducer, initialDetailsUnitsPartnerState);

  return <DetailsUnitsPartnerContext.Provider value={{ state }}>{children}</DetailsUnitsPartnerContext.Provider>;
};
// eslint-disable-next-line react-refresh/only-export-components
export const useDetailsUnitsPartnerContext = (): DetailsUnitsPartnerContextProps => {
  const context = React.useContext(DetailsUnitsPartnerContext);
  if (context === undefined) {
    throw new Error('useDetailsUnitsPartnerContext must be used within a DetailsUnitsPartnerProvider');
  }
  return context;
};
