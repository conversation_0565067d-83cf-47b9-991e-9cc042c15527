import { DetailsUnitsPartnerAction, DetailsUnitsPartnerState } from './state';

export const reducer = (
  state: DetailsUnitsPartnerState,
  action: DetailsUnitsPartnerAction,
): DetailsUnitsPartnerState => {
  switch (action.type) {
    case 'SAVE_CONFIGURATION_EMAIL':
      return {
        ...state,
        configurationEmail: [...state.configurationEmail, action.configurationEmail],
      };
    case 'SET_MODE':
      return {
        ...state,
      };
    default:
      return state;
  }
};
