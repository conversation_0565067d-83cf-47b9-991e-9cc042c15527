export interface DetailsUnitsPartnerState {
  tabsActive: string;
  configurationEmail: ConfigurationEmail[];
  mode: string;
}

export interface ConfigurationEmail {
  id: string;
  name: string;
  title: string;
  type: string;
  template: string;
  mailForm: string;
}

export interface DetailInfo {
  partnershipName: string;
  taxCode: string;
  partnershipCode: string;
  partnershipLevel: string;
  partnerCode: [];
  logo: string;
}

export interface Mode {
  mode: string;
}

export const initialDetailsUnitsPartnerState: DetailsUnitsPartnerState = {
  tabsActive: '1',
  configurationEmail: [],
  mode: 'detail',
};

export type DetailsUnitsPartnerAction =
  | { type: 'SAVE_CONFIGURATION_EMAIL'; configurationEmail: ConfigurationEmail }
  | { type: 'EDIT_CONFIGURATION_EMAIL'; configurationEmail: ConfigurationEmail }
  | { type: 'DELETE_CONFIGURATION_EMAIL'; configurationEmail: ConfigurationEmail }
  | { type: 'SET_MODE'; mode: Mode };
