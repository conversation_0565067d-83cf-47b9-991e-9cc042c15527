import { Col, Form, FormInstance, Input, Row, Typography, Select } from 'antd';
import './index.scss';
import { useEffect } from 'react';
const { Title } = Typography;
import customParseFormat from 'dayjs/plugin/customParseFormat';
import dayjs from 'dayjs';

dayjs.extend(customParseFormat);
interface CommonFormProps {
  form: FormInstance; // Type của Ant Design Form instance
}

const DetailsInfoTab: React.FC<CommonFormProps> = ({ form }) => {
  useEffect(() => {
    form?.getFieldsValue()?.partnerCode?.representIssuedDate;
  }, [form]);
  return (
    <>
      <Row gutter={48} style={{ marginBottom: '40px' }}>
        <Col span={24}>
          <Title style={{ marginBottom: '20px' }} level={5}>
            Thông tin pháp lý
          </Title>
        </Col>
        <Col span={24}>
          <Row gutter={[32, 8]}>
            <Col span={8}>
              <Form.Item label="Tên công ty" name={['partnerCode', 'branchName']}>
                <Input placeholder="Nhập tên công ty" />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label="Fax" name={['partnerCode', 'contactFax']}>
                <Input placeholder="Nhập fax" />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label="Điện thoại" name={['partnerCode', 'contactPhone']}>
                <Input placeholder="Nhập số điện thoại" />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label="Giấy CNĐKDN số" name={['partnerCode', 'certNumber']}>
                <Input placeholder="Nhập giấy CNĐKDN số" />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label="Ngân hàng" name={['partnerCode', 'bank']}>
                <Select placeholder="Chọn ngân hàng">
                  <Select.Option value="demo">Demo</Select.Option>
                </Select>
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label="Số tài khoản" name={['partnerCode', 'bankInfo']}>
                <Input placeholder="Nhập số tài khoản" />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label="Địa chỉ" name={['partnerCode', 'contactAddress']}>
                <Input placeholder="Nhập địa chỉ" />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label="Tỉnh/Thành phố" name={['partnerCode', 'provinceName']}>
                <Select placeholder="Chọn Tỉnh/Thành phố">
                  <Select.Option value="demo">Chưa có api</Select.Option>
                </Select>
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label="Quận/Huyện" name={['partnerCode', 'districtName']}>
                <Select placeholder="Chọn Quận/Huyện">
                  <Select.Option value="demo">Chưa có api</Select.Option>
                </Select>
              </Form.Item>
            </Col>

            <Col span={8}></Col>

            <Col span={8}>
              <Form.Item label="Phường/Xã" name={['partnerCode', 'wardName']}>
                <Select placeholder="Chọn Phường/Xã">
                  <Select.Option value="demo">Chưa có api</Select.Option>
                </Select>
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label="Quốc gia" name={['partnerCode', 'nationName']}>
                <Select placeholder="Chọn Quốc gia">
                  <Select.Option value="demo">Chưa có api</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Col>
      </Row>

      <Row gutter={48} style={{ marginBottom: '40px' }}>
        <Col span={24}>
          <Title style={{ marginBottom: '20px' }} level={5}>
            Thông tin người đại diện
          </Title>
        </Col>
        <Col span={24}>
          <Row gutter={[32, 8]}>
            <Col span={8}>
              <Form.Item label="Đại diện bởi" name={['partnerCode', 'representBy']}>
                <Input placeholder="Nhập tên đại diện" />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label="Điện thoại" name={['partnerCode', 'representPhone']}>
                <Input placeholder="Nhập số điện thoại" />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label="Mã số thuế cá nhân" name={['partnerCode', 'representTaxNumber']}>
                <Input placeholder="Nhập mã số thuế cá nhân" />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label="Số CMND/CCCD" name={['partnerCode', 'representIDValue']}>
                <Input placeholder="Nhập số CMND/CCCD" />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label="Ngày cấp CMND/CCCD" name={['partnerCode', 'representIssuedDate']}>
                {/* <DatePicker
                  value={dayjs(time).isValid() ? dayjs(time).format(FORMAT_DATE) : ''}
                  onChange={(date: DatePickerProps['value'], dateString: string | string[]) => {
                    console.log(date, dateString);
                  }}
                /> */}
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label="Nơi cấp CMND/CCCD" name={['partnerCode', 'representIssuedPlace']}>
                <Input placeholder="Nhập nơi cấp CMND/CCCD" />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label="Địa chỉ" name={['partnerCode', 'contactAddress']}>
                <Input placeholder="Nhập địa chỉ liên hệ" />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label="Tỉnh/Thành phố" name={['partnerCode', 'provinceName']}>
                <Select placeholder="Chọn Tỉnh/Thành phố">
                  <Select.Option value="demo">Chưa có api</Select.Option>
                </Select>
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label="Quận/Huyện" name={['partnerCode', 'districtName']}>
                <Select placeholder="Chọn Quận/Huyện">
                  <Select.Option value="demo">Chưa có api</Select.Option>
                </Select>
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label="Email" name={['partnerCode', 'representEmail']}>
                <Input placeholder="Nhập email" />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label="Phường/Xã" name={['partnerCode', 'wardName']}>
                <Select placeholder="Chọn Phường/Xã">
                  <Select.Option value="demo">Chưa có api</Select.Option>
                </Select>
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label="Quốc gia" name={['partnerCode', 'nationName']}>
                <Select placeholder="Chọn Quốc gia">
                  <Select.Option value="demo">Chưa có api</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Col>
      </Row>
    </>
  );
};

export default DetailsInfoTab;
