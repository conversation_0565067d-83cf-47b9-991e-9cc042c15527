import React, { useEffect, useState } from 'react';
import { Form, Input, Button, Select, Space, Checkbox, Row, Col, FormInstance } from 'antd';
import { CloseOutlined, MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';
import './index.scss';

const { Option } = Select;

interface CommonFormProps {
  form: FormInstance; // Type của Ant Design Form instance
}

export interface Other {
  type: string;
  key: string;
  value: string;
  arrayData: string[];
  objectData: string[];
}
const DynamicForm: React.FC<CommonFormProps> = ({ form }) => {
  const [selectedType, setSelectedType] = useState<{ [key: string]: string }>({});
  const [selectedRows, setSelectedRows] = useState<number[]>([]);

  // Xử lý thay đổi kiểu dữ liệu
  const handleTypeChange = (type: string, key: number) => {
    setSelectedType(prev => ({
      ...prev,
      [key]: type,
    }));
  };

  // Xử lý sự kiện khi Checkbox được thay đổi
  const handleCheckboxChange = (key: number, checked: boolean) => {
    if (checked) {
      setSelectedRows(prev => [...prev, key]);
    } else {
      setSelectedRows(prev => prev.filter(item => item !== key));
    }
  };

  const handleCheckboxAllChange = (checked: boolean) => {
    if (checked) {
      const newSelectedRows = Array.from({ length: form.getFieldsValue().fields.length }, (_, index) => index);
      setSelectedRows(newSelectedRows);
    } else {
      setSelectedRows([]);
    }
  };

  const isAllChecked = () => {
    if (form.getFieldsValue() && form.getFieldsValue().fields && selectedRows.length > 0)
      return selectedRows.length === form.getFieldsValue().fields.length;
    return false;
  };

  useEffect(() => {
    // lấy dữ liệu của form
    const fieldsValue = form.getFieldsValue().other;
    // update selectedType theo fieldsValue
    const newSelectedType = fieldsValue?.reduce((acc: { [key: string]: string }, item: Other, index: number) => {
      acc[index] = item.type;

      return acc;
    }, {});
    setSelectedType(newSelectedType);
  }, [form]);

  return (
    <div className="AddFieldTab">
      <Row gutter={16} className={'box-main-items'}>
        <Col span={1}>
          <Checkbox onChange={e => handleCheckboxAllChange(e.target.checked)} checked={isAllChecked()} />
        </Col>
        <Col span={7}>
          <div className={'box-border-item'}>Type</div>
        </Col>
        <Col span={7}>
          <div className={'box-border-item'}>Main Key</div>
        </Col>
        <Col span={9}>Data</Col>
      </Row>

      <Form.List name="other">
        {(fields, { add, remove }) => (
          <>
            {fields.map(({ key, name, ...restField }) => (
              <>
                <Row key={key} gutter={[16, 16]} align="middle" className="box-items">
                  <Col span={1}>
                    <Checkbox
                      onChange={e => handleCheckboxChange(name, e.target.checked)}
                      checked={selectedRows.includes(name)}
                    />
                  </Col>
                  <Col span={7}>
                    <Form.Item
                      {...restField}
                      name={[name, 'type']}
                      rules={[{ required: true, message: 'Vui lòng chọn loại' }]}
                    >
                      <Select placeholder="Select type" onChange={type => handleTypeChange(type, key)}>
                        <Option value="Trường đơn">Trường đơn</Option>
                        <Option value="Mảng">Mảng</Option>
                        <Option value="Đối tượng">Đối tượng</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={7}>
                    <Form.Item
                      {...restField}
                      name={[name, 'key']}
                      rules={[{ required: true, message: 'Vui lòng nhập Key' }]}
                    >
                      <Input placeholder="Key" />
                    </Form.Item>
                  </Col>
                  <Col span={9}>
                    {/* Render điều kiện theo từng type */}
                    {selectedType[key] === 'Trường đơn' && (
                      <>
                        <Form.Item
                          {...restField}
                          name={[name, 'value']}
                          rules={[{ required: true, message: 'Vui lòng nhập giá trị' }]}
                        >
                          <Input placeholder="Value" name="value" />
                        </Form.Item>
                      </>
                    )}

                    {selectedType[key] === 'Mảng' && (
                      <Form.List name={[name, 'arrayData']}>
                        {(arrayFields, { add: addArrayField, remove: removeArrayField }) => (
                          <>
                            <br />
                            <br />
                            {arrayFields.map(({ name: arrayName, ...arrayRestField }) => (
                              <>
                                <Row gutter={[12, 12]}>
                                  <Col span={22}>
                                    <Form.Item
                                      {...arrayRestField}
                                      name={[arrayName, 'arrayValue']}
                                      rules={[{ required: true, message: 'Vui lòng nhập dữ liệu' }]}
                                      style={{ width: 'auto' }}
                                    >
                                      <Input placeholder="Data input" />
                                    </Form.Item>
                                  </Col>
                                  <Col span={2}>
                                    <CloseOutlined
                                      onClick={() => {
                                        removeArrayField(arrayName);
                                      }}
                                    />
                                  </Col>
                                </Row>
                              </>
                            ))}
                            <Row>
                              <Col span={22}>
                                <Form.Item>
                                  <Button
                                    style={{ width: '100%' }}
                                    type="dashed"
                                    onClick={() => addArrayField()}
                                    icon={<PlusOutlined />}
                                  >
                                    Thêm mảng giá trị
                                  </Button>
                                </Form.Item>
                              </Col>
                            </Row>
                          </>
                        )}
                      </Form.List>
                    )}

                    {selectedType[key] === 'Đối tượng' && (
                      <Form.List name={[name, 'objectData']}>
                        {(objectFields, { add: addObjectField, remove: removeObjectField }) => (
                          <>
                            <br />
                            <br />
                            {objectFields.map(({ name: objectName, ...objectRestField }) => (
                              <Row gutter={[12, 12]}>
                                <Col span={11}>
                                  <Form.Item
                                    {...objectRestField}
                                    name={[objectName, 'objectKey']}
                                    rules={[{ required: true, message: 'Vui lòng nhập Object key' }]}
                                  >
                                    <Input placeholder="Object key" />
                                  </Form.Item>
                                </Col>
                                <Col span={11}>
                                  <Form.Item
                                    {...objectRestField}
                                    name={[objectName, 'objectValue']}
                                    rules={[{ required: true, message: 'Vui lòng nhập Object value' }]}
                                  >
                                    <Input placeholder="Data input" />
                                  </Form.Item>
                                </Col>
                                <Col span={2}>
                                  <CloseOutlined
                                    onClick={() => {
                                      removeObjectField(objectName);
                                    }}
                                  />
                                </Col>
                              </Row>
                            ))}
                            <Row>
                              <Col span={22}>
                                <Form.Item>
                                  <Button
                                    style={{ width: '100%' }}
                                    type="dashed"
                                    onClick={() => addObjectField()}
                                    icon={<PlusOutlined />}
                                  >
                                    Thêm đối tượng
                                  </Button>
                                </Form.Item>
                              </Col>
                            </Row>
                          </>
                        )}
                      </Form.List>
                    )}
                  </Col>
                </Row>
              </>
            ))}
            <Space className={'box-main-handler'}>
              <Button onClick={() => add()} type="dashed">
                <PlusOutlined /> Thêm trường mới
              </Button>
              <Button
                type="primary"
                danger
                onClick={() => {
                  remove(selectedRows);
                  setSelectedRows([]);
                }}
                disabled={selectedRows.length === 0}
              >
                <MinusCircleOutlined /> Xóa
              </Button>
            </Space>
          </>
        )}
      </Form.List>
    </div>
  );
};

export default DynamicForm;
