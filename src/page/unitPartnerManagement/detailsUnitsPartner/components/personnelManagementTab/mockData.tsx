import type { TableColumnsType } from 'antd';

interface DataType {
  key: string;
  name: string;
  jobTitle: string | number;
}

export const columns: TableColumnsType<DataType> = [
  {
    title: 'Nhân viên',
    dataIndex: 'name',
  },
  {
    title: '<PERSON><PERSON>c vụ',
    dataIndex: 'jobTitle',
  },
];

export const data: DataType[] = [
  {
    key: '1',
    name: '<PERSON>',
    jobTitle: 1,
  },
  {
    key: '2',
    name: '<PERSON>',
    jobTitle: 2,
  },
  {
    key: '3',
    name: '<PERSON>',
    jobTitle: 3,
  },
];

export const nonAffData: DataType[] = [
  {
    key: '4',
    name: '<PERSON>',
    jobTitle: 4,
  },
  {
    key: '5',
    name: '<PERSON>',
    jobTitle: 5,
  },
  {
    key: '6',
    name: '<PERSON>',
    jobTitle: 6,
  },
];
