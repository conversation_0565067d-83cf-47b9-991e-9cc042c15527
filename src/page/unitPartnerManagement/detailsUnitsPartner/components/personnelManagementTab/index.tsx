import { Button, Col, FormInstance, Input, Row, Select, Typography } from 'antd';
import EmployeesAffiliated from './employeesAffiliated';
import NonAffiliatedEmployees from './nonAffiliatedEmployees';
import { ArrowLeftOutlined, SearchOutlined } from '@ant-design/icons';
import './index.scss';

const { Title } = Typography;
interface CommonFormProps {
  form: FormInstance; // Type của Ant Design Form instance
}

const PersonnelManagementTab: React.FC<CommonFormProps> = () => {
  return (
    <div className={'PersonnelManagement'}>
      <Row style={{ marginBottom: '30px' }} gutter={43}>
        <Col style={{ paddingRight: '100px' }} span={12}>
          <Title style={{ marginBottom: '20px' }} level={5}>
            Quản lý bởi
          </Title>
          <Select
            style={{ width: '100%' }}
            showSearch
            placeholder="Quản lý bởi"
            filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input.toLowerCase())}
            options={[
              { value: '1', label: 'Jack' },
              { value: '2', label: 'Lucy' },
              { value: '3', label: 'Tom' },
            ]}
          />
        </Col>
      </Row>
      <Row className="PersonnelManagementTable" gutter={43}>
        <Col className="StaffOfTheUnit" span={12}>
          <Title style={{ marginBottom: '20px' }} level={5}>
            Nhân viên trực thuộc đơn vị
          </Title>
          <Input suffix={<SearchOutlined />} style={{ marginBottom: '20px' }} placeholder="Tìm kiếm" />
          <br />
          <EmployeesAffiliated />
        </Col>

        <Col className="NonAffiliatedStaff" span={12}>
          <Title style={{ marginBottom: '20px' }} level={5}>
            Nhân viên chưa trực thuộc
          </Title>
          <Input suffix={<SearchOutlined />} style={{ marginBottom: '20px' }} placeholder="Tìm kiếm" />
          <NonAffiliatedEmployees />
        </Col>

        <div className="ButtonSwap">
          {/* <Button type="primary" iconPosition={'end'} danger icon={<ArrowRightOutlined />} size={'large'}>
            Rời khỏi đơn vị
          </Button> */}
          <Button type="primary" icon={<ArrowLeftOutlined />}>
            Thêm vào đơn vị
          </Button>
        </div>
      </Row>
    </div>
  );
};
export default PersonnelManagementTab;
