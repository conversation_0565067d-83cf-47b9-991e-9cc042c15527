import { Col, Row, TableColumnsType, Typography } from 'antd';
import './index.scss';
import InputSearch from '../../../../../components/input/InputSearch';
import TableComponent from '../../../../../components/table';
import { useFetch } from '../../../../../hooks';
import { ListCooperativeSalesUnits, PartnerCode } from '../../../../../types/unitsPartner/unitsPartner';
import { getListOfCooperativeSalesUnits } from '../../../../../service/unitsPartner';
import dayjs from 'dayjs';
import { FORMAT_DATE } from '../../../../../constants/common';
const { Text } = Typography;

interface CommonFormProps {
  partnerCode?: PartnerCode;
}

const PartnerManagementTab: React.FC<CommonFormProps> = partnerCode => {
  const currentPartnerCode = partnerCode?.partnerCode;
  const {
    data: listCooperativeSalesUnits,
    isPlaceholderData,
    isLoading,
  } = useFetch<ListCooperativeSalesUnits[]>({
    queryKeyArrWithFilter: ['cooperative-sales-units'],
    api: () => getListOfCooperativeSalesUnits({ partnerCode: currentPartnerCode }),
  });

  const columns: TableColumnsType<ListCooperativeSalesUnits> = [
    {
      title: 'Tên ĐVBH',
      key: 'partnershipName',
      dataIndex: 'partnershipName',
      width: '20%',
      render: (_, record) => {
        return (
          <Row className="groupNameUnitPartner">
            <Col md={12} xs={24}>
              <Text>{record?.partnershipName || '-'}</Text>
            </Col>
          </Row>
        );
      },
    },

    {
      title: 'Mã ĐVBH',
      width: '15%',
      dataIndex: 'partnershipCode',
      key: 'partnershipCode',
      render: (_, record) => {
        return (
          <Row className="groupNameUnitPartner">
            <Col md={12} xs={24}>
              <Text>{record?.partnershipCode || '-'}</Text>
            </Col>
          </Row>
        );
      },
    },
    {
      title: 'Loại ĐVBH',
      dataIndex: 'partnershipLevel',
      width: '15%',
      key: 'partnershipLevel',
      render: (_, record) => {
        return (
          <Row className="groupNameUnitPartner">
            <Col md={12} xs={24}>
              <Text>{record?.partnershipLevel || '-'}</Text>
            </Col>
          </Row>
        );
      },
    },
    {
      title: 'Mã số thuế',
      dataIndex: 'taxCode',
      key: 'taxCode',
      width: '15%',
      render: (_, record) => {
        return (
          <Row className="groupNameUnitPartner">
            <Col md={12} xs={24}>
              <Text>{record?.taxCode || '-'}</Text>
            </Col>
          </Row>
        );
      },
    },

    {
      title: 'Người quản lý',
      dataIndex: 'lineManager',
      width: '10%',
      key: 'lineManager',
      render: (_, record) => {
        return (
          <Row className="groupNameUnitPartner">
            <Col md={12} xs={24}>
              <Text>{record?.lineManager || '-'}</Text>
            </Col>
          </Row>
        );
      },
    },
    {
      title: 'Ngày cập nhật',
      dataIndex: 'lastUpdate',
      width: '10%',
      key: 'lastUpdate',
      render: (_, record) => {
        return (
          <Row className="groupNameUnitPartner">
            <Col md={12} xs={24}>
              <Text>{dayjs(record?.lastUpdate).format(FORMAT_DATE) || '-'}</Text>
            </Col>
          </Row>
        );
      },
    },
    {
      title: 'Trạng thái',
      dataIndex: 'active',
      width: '15%',
      key: 'active',
      render: value => (value ? <Text type="success">Hoạt động</Text> : <Text type="danger">Vô hiệu hoá</Text>),
    },
  ];

  return (
    <>
      <div className="wrapper-listOfUnitsPartner">
        <div className="header-content">
          <InputSearch keySearch="search" />
        </div>
        <TableComponent
          className="table-unit"
          queryKeyArr={['cooperative-sales-units']}
          loading={isLoading || isPlaceholderData}
          dataSource={listCooperativeSalesUnits?.data?.data}
          columns={columns}
        />
      </div>
    </>
  );
};

export default PartnerManagementTab;
