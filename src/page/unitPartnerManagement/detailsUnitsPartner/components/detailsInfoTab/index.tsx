import { Col, Form, FormInstance, Input, Row, Typography, Select, Upload, UploadProps, Space } from 'antd';
import './index.scss';
import { PlusOutlined } from '@ant-design/icons';
import type { GetProp, UploadFile } from 'antd';
import { useState } from 'react';

const { Title, Text } = Typography;
const options = [
  {
    label: 'Hợp tác với đơn vị bán hàng',
    value: 'Hợp tác với đơn vị bán hàng',
  },
];
const partnershipOptions = [{ value: '1', label: 'Đơn vị hợp tác kinh doanh' }];
interface CommonFormProps {
  form: FormInstance; // Type của Ant Design Form instance
}

type FileType = Parameters<GetProp<UploadProps, 'beforeUpload'>>[0];

const getBase64 = (file: FileType): Promise<string> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = error => reject(error);
  });

const uploadButton = (
  <button style={{ border: 0, background: 'none' }} type="button">
    <PlusOutlined />
    <div style={{ marginTop: 8 }}>Upload</div>
  </button>
);

const DetailsInfoTab: React.FC<CommonFormProps> = () => {
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as FileType);
    }
  };

  const handleChange: UploadProps['onChange'] = ({ fileList: newFileList }) => setFileList(newFileList);

  const handleChangePartnerCode = (value: string[]) => {
    console.log(`selected ${value}`);
  };
  return (
    <>
      <Row gutter={[12, 12]}>
        <Col span={24}>
          <Title style={{ marginBottom: '20px' }} level={5}>
            Thông tin quản lý đơn vị
          </Title>
        </Col>
      </Row>
      <Row gutter={48}>
        <Col span={16}>
          <Row gutter={[32, 8]}>
            <Col span={12}>
              <Form.Item label="Tên đơn vị hợp tác kinh doanh" name="partnershipName">
                <Input placeholder="Nhập tên đơn vị hợp tác kinh doanh" />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item label="Loại đơn vị" name="partnershipLevel">
                <Select placeholder="Chọn loại đơn vị hợp tác kinh doanh">
                  {partnershipOptions.map(option => (
                    <Select.Option key={option.value} value={option.value}>
                      {option.label}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item label="Mã đơn vị hợp tác kinh doanh" name="partnershipCode">
                <Input placeholder="Nhập mã đơn vị hợp tác kinh doanh" />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item label="Hợp tác với đơn vị bán hàng" name="partnerCode">
                <Select
                  mode="multiple"
                  allowClear
                  style={{ width: '100%' }}
                  placeholder="Chọn hợp tác với đơn vị bán hàng"
                  onChange={handleChangePartnerCode}
                  options={options}
                  optionRender={option => <Space>{option.data.label}</Space>}
                />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item label="Mã số thuế" name="taxCode">
                <Input placeholder="Nhập mã số thuế" />
              </Form.Item>
            </Col>
          </Row>
        </Col>
        <Col span={8}>
          <Row>
            <Col span={24}>
              <Form.Item name="logo" label="Ảnh đại diện/Logo ĐVBH">
                <Upload
                  action="https://660d2bd96ddfa2943b33731c.mockapi.io/api/upload"
                  listType="picture-card"
                  fileList={fileList}
                  onPreview={handlePreview}
                  onChange={handleChange}
                >
                  {uploadButton}
                </Upload>
                <Text type="secondary">Định dạng .jpeg, .jpg, .png</Text>
              </Form.Item>
            </Col>
          </Row>
        </Col>
      </Row>
    </>
  );
};

export default DetailsInfoTab;
