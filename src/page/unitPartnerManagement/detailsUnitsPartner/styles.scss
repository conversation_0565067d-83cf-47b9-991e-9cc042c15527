.box-detail-units {
  height: 100%;

  .box-main-wrap {
    padding-top: 24px;
    height: 100%;

    .box-inp {
      margin-top: 8px;
    }

    .mb-8 {
      margin-bottom: 8px !important;
    }

    .mt-40 {
      margin-top: 40px !important;
    }

    .box-bg-bt {
      background-color: #00000005;
      height: 48px;
      margin-top: 30px;
    }

    .ant-tabs {
      height: 100%;

      .ant-tabs-content {
        height: 100%;
      }

      .ant-tabs-tabpane-active {
        display: flex;
        flex-direction: column;
        height: 100% !important;
      }

      .ant-tabs-tabpane {
        height: 100%;
      }
    }

    .btn-handlerSave {
      margin-bottom: 16px;
    }

    .box-main-handler {
      margin-bottom: 16px;
    }

    .box-main-items {
      font-weight: bold;
      margin-bottom: 8px;
      background-color: #00000005;
      display: flex;
      align-items: center;
      height: 48px;

      .box-border-item {
        border-right: 1px solid #0000000f;
      }
    }

    .box-items {
      border-bottom: 1px solid #0000000f;
      padding: 10px 0 0 0;

      .box-item {
        margin-bottom: 8px;
      }
    }

    .box-add-other-field__bottom {
      margin-top: auto;
      display: flex;
      justify-content: flex-end;
      padding-top: 14px;
      border-top: 1px solid #0000000f;
      border-bottom: 1px solid #0000000f;
    }
  }
}
