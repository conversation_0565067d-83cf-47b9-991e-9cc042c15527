export interface ListUnitsPartnerState {
  mode: string;
}

export interface UnitPartner {
  id: string;
  partnershipName: string;
  taxCode: string;
  partnershipCode: string;
  partnershipLevel: string;
  partnerCode: [];
  logo: string;
}

export const initialListUnitsPartnerState: ListUnitsPartnerState = {
  mode: 'create',
};

export type ListUnitsPartnerAction = { type: 'increment'; payload: string } | { type: 'decrement' };
