import React, { createContext, useReducer, ReactNode } from 'react';
import { reducer } from './reducer';
import { initialListUnitsPartnerState, ListUnitsPartnerState } from './state';

interface ListUnitsPartnerContextProps {
  state: ListUnitsPartnerState;
}

export const ListUnitsPartnerContext = createContext<ListUnitsPartnerContextProps | undefined>(undefined);

export const ListUnitsPartnerProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state] = useReducer(reducer, initialListUnitsPartnerState);

  return <ListUnitsPartnerContext.Provider value={{ state }}>{children}</ListUnitsPartnerContext.Provider>;
};
// eslint-disable-next-line react-refresh/only-export-components
export const useListUnitsPartnerContext = (): ListUnitsPartnerContextProps => {
  const context = React.useContext(ListUnitsPartnerContext);
  if (context === undefined) {
    throw new Error('useListUnitsPartnerContext must be used within a ListUnitsPartnerProvider');
  }
  return context;
};
