import { <PERSON><PERSON>, Col, Row, TableColumnsType, Typography } from 'antd';
import { useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import InputSearch from '../../../components/input/InputSearch';
import TableComponent from '../../../components/table';
import { UNIT_PARTNER_MANAGEMENT } from '../../../configs/path';
import { useFetch } from '../../../hooks';
import './styles.scss';
import BreadCrumbComponent from '../../../components/breadCrumb';
import { ActionsColumns } from '../../../components/table/components/ActionsColumns';
import { deleteUnitsPartner, getListUnitsPartner } from '../../../service/unitsPartner';
import { UnitPartner } from '../../../types/unitsPartner/unitsPartner';
import ConfirmDeleteModal from '../../../components/modal/specials/ConfirmDeleteModal';
import { MutationFunction } from '@tanstack/react-query';

const { Text } = Typography;

const columns: TableColumnsType<UnitPartner> = [
  {
    title: 'Tên đơn vị DTHT',
    key: 'partnershipName',
    width: '40%',
    render: (_, record) => {
      return (
        <Row className="groupNameUnitPartner">
          <Col md={12} xs={24}>
            <Text>{record?.partnershipName || '-'}</Text>
          </Col>
        </Row>
      );
    },
  },

  {
    title: 'Mã số thuế',
    width: '15%',
    dataIndex: 'taxCode',
    key: 'taxCode',
    render: (_, record) => {
      return (
        <Row className="groupNameUnitPartner">
          <Col md={12} xs={24}>
            <Text>{record?.taxCode || '-'}</Text>
          </Col>
        </Row>
      );
    },
  },
  {
    title: 'Người quản lý',
    dataIndex: 'lineManager',
    width: '15%',
    key: 'lineManager',
    render: (_, record) => {
      return (
        <Row className="groupNameUnitPartner">
          <Col md={12} xs={24}>
            <Text>{record?.lineManager || '-'}</Text>
          </Col>
        </Row>
      );
    },
  },
  {
    title: 'Mã DTHT',
    dataIndex: 'partnershipCode',
    width: '15%',
    key: 'code',
    render: (_, record) => {
      return (
        <Row className="groupNameUnitPartner">
          <Col md={12} xs={24}>
            <Text>{record?.partnershipCode || '-'}</Text>
          </Col>
        </Row>
      );
    },
  },
  {
    title: 'Trạng thái',
    dataIndex: 'active',
    align: 'center',
    width: '15%',
    key: 'active',
    render: value => (value ? <Text type="success">Đang hoạt động</Text> : <Text type="danger">Vô hiệu hoá</Text>),
  },
];

const ListUnitsPartner = () => {
  const navigate = useNavigate();

  const [isOpenModalDelete, setIsOpenModalDelete] = useState<boolean>(false);
  const [currentRecord, setCurrentRecord] = useState<UnitPartner>();

  const {
    data: listUnitPartnerData,
    isPlaceholderData,
    isLoading,
  } = useFetch<UnitPartner[]>({
    queryKeyArrWithFilter: ['units-partner'],
    api: getListUnitsPartner,
  });

  const columnActions: TableColumnsType<UnitPartner> = useMemo(() => {
    return [
      ...columns,
      {
        key: 'action',
        align: 'center',
        width: '10%',
        render: (_, record) => {
          const handleDeleteUnitPartner = () => {
            setIsOpenModalDelete(!isOpenModalDelete);
            setCurrentRecord(record);
          };
          return (
            <ActionsColumns
              handleViewDetail={() => {
                navigate(`${UNIT_PARTNER_MANAGEMENT}/${record.id}`);
              }}
              handleDelete={handleDeleteUnitPartner}
            />
          );
        },
      },
    ];
  }, [isOpenModalDelete, navigate]);

  return (
    <div className="wrapper-listOfUnitsPartner">
      <BreadCrumbComponent />
      <div className="header-content">
        <InputSearch keySearch="search" />
        <Button
          type="primary"
          onClick={() => {
            navigate(`${UNIT_PARTNER_MANAGEMENT}/create`);
          }}
        >
          Thêm mới
        </Button>
      </div>
      <TableComponent
        className="table-unit"
        queryKeyArr={['units-partner']}
        columns={columnActions}
        loading={isLoading || isPlaceholderData}
        dataSource={listUnitPartnerData?.data?.data?.rows}
      />

      <ConfirmDeleteModal
        label="Đối tác hợp tác"
        open={isOpenModalDelete}
        apiQuery={deleteUnitsPartner as MutationFunction<unknown, unknown>}
        keyOfListQuery={['units-partner']}
        onCancel={() => setIsOpenModalDelete(false)}
        idDetail={currentRecord?.id}
        title="Xoá đối tác"
        description="Vui lòng nhập lý do muốn xoá đối tác này"
      />
    </div>
  );
};

export default ListUnitsPartner;
