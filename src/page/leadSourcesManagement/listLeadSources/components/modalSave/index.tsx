import { Button, Form, Input, Modal, notification } from 'antd';
import { useEffect } from 'react';
import './styles.scss';
import { ILead, TCreateLeadSource } from '../../../../../types/leadSources';
import { useCreateField, useUpdateField } from '../../../../../hooks';
import { postCreateLeadSource, updateLeadSource } from '../../../../../service/leadSource';
import React from 'react';

type modalSaveLeadSourceProps = {
  isOpen: boolean;
  title: string;
  currentRecord?: ILead;
  setCurrentRecord: React.Dispatch<React.SetStateAction<ILead | undefined>>;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
};

interface TError {
  data: {
    errors: {
      'leadSource.existed.error': string;
    };
  };
}

const ModalSaveLeadSource = (props: modalSaveLeadSourceProps) => {
  const { isOpen, title, currentRecord, setCurrentRecord, setIsOpen } = props;
  const [form] = Form.useForm();

  const [initialValues, setInitialValues] = React.useState<ILead>();

  const { mutateAsync: createLeadSource } = useCreateField<TCreateLeadSource>({
    apiQuery: postCreateLeadSource,
    keyOfListQuery: ['get-lead-sources'],
    isMessageSuccess: true,
    isMessageError: false,
  });
  const { mutateAsync: mutateAsyncUpdateLeadSource } = useUpdateField({
    apiQuery: updateLeadSource,
    keyOfListQuery: ['get-lead-sources'],
    isMessageSuccess: true,
    isMessageError: false,
  });

  const handleCancelModalUpdate = React.useCallback(() => {
    setCurrentRecord(undefined);
    setIsOpen(false);
  }, [setCurrentRecord, setIsOpen]);

  const handleCancelModalCreate = React.useCallback(() => {
    setCurrentRecord(undefined);
    setIsOpen(false);
    form.resetFields();
  }, [form, setCurrentRecord, setIsOpen]);

  const handleSubmit = React.useCallback(
    async (values: TCreateLeadSource) => {
      const newData: TCreateLeadSource = {
        ...values,
        id: currentRecord ? currentRecord?.id : undefined,
      };
      try {
        const res = !currentRecord?.name
          ? await createLeadSource(newData)
          : await mutateAsyncUpdateLeadSource(newData as ILead);
        if (res?.data?.statusCode === '0') {
          currentRecord?.name ? handleCancelModalUpdate() : handleCancelModalCreate();
        }
      } catch (error: TError | unknown) {
        const message = (error as TError).data?.errors['leadSource.existed.error']
          ? 'Tên nguồn đã tồn tại'
          : 'Có lỗi xảy ra';
        notification.error({ message: message });
      }
    },
    [createLeadSource, currentRecord, handleCancelModalCreate, handleCancelModalUpdate, mutateAsyncUpdateLeadSource],
  );

  useEffect(() => {
    if (form.isFieldsTouched(true) && isOpen) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [form, isOpen]);

  React.useEffect(() => {
    if (currentRecord) {
      form.setFieldValue('name', currentRecord?.name);
      form.setFieldValue('id', currentRecord?.id);
      setInitialValues(currentRecord);
    }
  }, [currentRecord, form]);

  return (
    <>
      <Modal
        className="modal-confirm-delete"
        open={isOpen}
        title={title}
        centered
        closable={false}
        okText="Lưu"
        cancelText="Huỷ"
        destroyOnClose
        footer={[
          <Button
            key="cancel"
            className="btn-cancel"
            type="text"
            onClick={currentRecord ? handleCancelModalUpdate : handleCancelModalCreate}
          >
            Huỷ
          </Button>,
          <Button type="primary" htmlType="submit" onClick={() => form.submit()}>
            Lưu
          </Button>,
        ]}
      >
        <Form form={form} initialValues={initialValues ? initialValues : undefined} onFinish={handleSubmit}>
          <Form.Item name="name" rules={[{ required: true, message: 'Vui lòng nhập tên nguồn lead' }]}>
            <Input
              placeholder={`Nhập tên nguồn lead`}
              // defaultValue={currentRecord?.name ? currentRecord?.name : ''}
              maxLength={60}
            />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default ModalSaveLeadSource;
