import { Form } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { useEffect, useMemo, useState } from 'react';
import { useLocation } from 'react-router-dom';
import DatePickerFilter from '../../../../../components/datePicker/DatePickerFilter';
import DropdownFilterSearch from '../../../../../components/dropdown/dropdownFilterSearch';
import { FORMAT_DATE_API } from '../../../../../constants/common';
import useFilter from '../../../../../hooks/filter';
import './styles.scss';

type TFilter = {
  person?: string;
  isActive?: number | null;
  startDate?: string | Dayjs | null;
  endDate?: string | Dayjs | null;
};

function FilterSearch() {
  const [form] = Form.useForm();
  const [isOpenFilter, setIsOpenFilter] = useState(false);
  const [initialValues, setInitialValues] = useState<TFilter>();
  const [filter, setFilter] = useFilter();
  const { search } = useLocation();
  const params = useMemo(() => new URLSearchParams(search), [search]);

  useEffect(() => {
    setInitialValues({
      // salesPerson: params.get('salesPerson'),
      // isActive: params.get('isActive') ? Number(params.get('isActive')) : undefined,
      startDate: params.get('startDate') ? dayjs(params.get('startDate')) : undefined,
      endDate: params.get('endDate') ? dayjs(params.get('endDate')) : undefined,
    });
  }, [params]);

  const handleSubmitFilter = (values: TFilter) => {
    const newFilter: Record<string, unknown> = {
      startDate: values?.startDate ? dayjs(values?.startDate).format(FORMAT_DATE_API) : null,
      endDate: values?.endDate ? dayjs(values?.endDate).format(FORMAT_DATE_API) : null,
    };
    setFilter({ ...filter, page: '1', ...newFilter });
    setIsOpenFilter(false);
  };

  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };

  const handleClearFilters = () => {
    setInitialValues(undefined);
    setTimeout(() => {
      setIsOpenFilter(false);
    }, 100);
  };

  return (
    <>
      <DropdownFilterSearch
        rootClassName="wrapper-filter-customers"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        showParams={true}
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        initialValues={initialValues}
        form={form}
        onClearFilters={handleClearFilters}
        extraFormItems={
          <>
            <DatePickerFilter startDate="startDate" endDate="endDate" />
          </>
        }
      />
    </>
  );
}

export default FilterSearch;
