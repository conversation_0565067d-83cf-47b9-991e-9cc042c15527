import { TableColumnsType, Typography } from 'antd';
import dayjs from 'dayjs';
import { CUSTOMER_PERSONAL } from '../../../configs/path';
import { FORMAT_DATE_TIME } from '../../../constants/common';
import { ICustomers, TDataDuplicate } from '../../../types/customers';

const { Text } = Typography;
export const columns: TableColumnsType = [
  // {
  //   title: 'Khách hàng',
  //   dataIndex: 'name',
  //   key: 'name',
  //   width: 160,
  //   fixed: 'left',
  //   render: (value: string, record: ICustomers) =>
  //     value ? (
  //       <div className="cell-name">
  //         {record.status === 'CUST_DONE' ? (
  //           <Tag bordered={false} color="success">
  //             KHCT
  //           </Tag>
  //         ) : null}
  //         <Text>{value}</Text>
  //       </div>
  //     ) : (
  //       '-'
  //     ),
  // },
  {
    title: 'Tên <PERSON>n',
    dataIndex: 'name',
    width: '40%',
    key: 'name',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Ngày tạo',
    dataIndex: 'createdDate',
    key: 'createdDate',
    width: '20%',

    render: (value: string, record: ICustomers) => (
      <>
        <Text>{dayjs(value).format(FORMAT_DATE_TIME)}</Text>
        <br />
        <Text>{record?.createdBy ? record.createdBy : '-'}</Text>
      </>
    ),
  },
  {
    title: 'Ngày cập nhật',
    dataIndex: 'updatedDate',
    key: 'updatedDate',
    width: '20%',

    render: (value: string, record: ICustomers) => (
      <>
        <Text>{dayjs(value).format(FORMAT_DATE_TIME)}</Text> <br />
        <Text>{record?.updatedBy ? record.updatedBy : '-'}</Text>
      </>
    ),
  },
];

export const columnsDuplicate: TableColumnsType = [
  {
    title: 'Khách hàng',
    dataIndex: 'name',
    key: 'name',
    fixed: 'left',
    render: (value: string, record: TDataDuplicate) =>
      value ? (
        <a href={`${CUSTOMER_PERSONAL}/${record?.id}`} target="_blank" rel="noopener noreferrer">
          {value}
        </a>
      ) : (
        '-'
      ),
  },
  {
    title: 'Mã khách hàng',
    dataIndex: 'code',
    key: 'code',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Số giấy tờ',
    dataIndex: 'identities',
    key: 'identities',
    render: (value: string[], record: TDataDuplicate) =>
      value?.length > 0 ? value?.join(', ') : record?.taxCode ? record?.taxCode : '-',
  },
  {
    title: 'Loại khách hàng',
    dataIndex: 'type',
    key: 'type',
    render: (value: string) => (value === 'business' ? 'Doanh nghiệp' : 'Cá nhân'),
  },
  {
    title: 'Email',
    dataIndex: 'email',
    key: 'email',
    render: (value: string) => (value ? value : '-'),
  },
];
