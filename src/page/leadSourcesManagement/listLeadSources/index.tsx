import { MutationFunction } from '@tanstack/react-query';
import { Button, Flex, TableColumnsType } from 'antd';
import { useMemo, useState } from 'react';
import BreadCrumbComponent from '../../../components/breadCrumb';
import ConfirmDeleteModal from '../../../components/modal/specials/ConfirmDeleteModal';
import TableComponent from '../../../components/table';
import { ActionsColumns } from '../../../components/table/components/ActionsColumns';
import { useFetch } from '../../../hooks';
import { columns } from './columns';
import FilterSearch from './components/filterSearch';
import './styles.scss';
import { deleteLeadSource, getListLeadSource } from '../../../service/leadSource';
import ModalSaveCustomer from './components/modalSave';
import { ILead } from '../../../types/leadSources';

function ListLeadSources() {
  const [isOpenModalDelete, setIsOpenModalDelete] = useState<boolean>(false);
  const [currentRecord, setCurrentRecord] = useState<ILead>();
  const [isOpenModalCreate, setIsOpenModalCreate] = useState(false);
  const [isOpenModalUpdate, setIsOpenModalUpdate] = useState(false);

  const {
    data: listLeadSources,
    isLoading,
    isPlaceholderData,
    isFetching,
  } = useFetch<ILead[]>({
    queryKeyArrWithFilter: ['get-lead-sources'],
    api: getListLeadSource,
  });

  const handleOpenModalUpdate = (record: ILead) => {
    setCurrentRecord(record);
    setIsOpenModalUpdate(true);
  };

  const columnActions: TableColumnsType<ILead> = useMemo(() => {
    return [
      ...columns,
      {
        key: 'action',
        align: 'center',
        width: '10%',
        render: (_, record: ILead) => {
          const handleDeleteUnitPartner = () => {
            setIsOpenModalDelete(!isOpenModalDelete);
            setCurrentRecord(record);
          };

          return (
            <ActionsColumns
              moreActions={[
                {
                  label: 'Sửa tên nguồn',
                  key: 'updateSourceName',
                  onClick: () => handleOpenModalUpdate(record),
                  disabled: false,
                },
                {
                  label: 'Xóa',
                  key: 'delete',
                  onClick: handleDeleteUnitPartner,
                },
              ]}
            />
          );
        },
      },
    ];
  }, [isOpenModalDelete]);

  const handleOpenModalCreate = () => {
    setIsOpenModalCreate(true);
  };

  return (
    <div className="wrapper-list-personal-customers">
      <BreadCrumbComponent />
      <div className="header-content">
        <Flex gap={17}>
          <FilterSearch />
        </Flex>
        <Flex gap={10}>
          <Button type="primary" onClick={handleOpenModalCreate}>
            Tạo mới
          </Button>
        </Flex>
      </div>
      <div className="table-personal-customers">
        <TableComponent
          queryKeyArr={['get-lead-sources']}
          columns={columnActions}
          loading={isLoading || isPlaceholderData || isFetching}
          dataSource={listLeadSources?.data?.data?.rows ?? []}
          rowKey="id"
        />
      </div>
      <ModalSaveCustomer
        isOpen={isOpenModalCreate}
        title={'Tạo mới nguồn lead'}
        setCurrentRecord={setCurrentRecord}
        setIsOpen={setIsOpenModalCreate}
      />
      <ModalSaveCustomer
        isOpen={isOpenModalUpdate}
        title={'Chỉnh sửa tên nguồn lead'}
        currentRecord={currentRecord}
        setCurrentRecord={setCurrentRecord}
        setIsOpen={setIsOpenModalUpdate}
      />
      <ConfirmDeleteModal
        label="nguồn lead"
        open={isOpenModalDelete}
        apiQuery={deleteLeadSource as MutationFunction<unknown, unknown>}
        keyOfListQuery={['get-lead-sources']}
        onCancel={() => setIsOpenModalDelete(false)}
        idDetail={currentRecord?.id}
        title="Xóa nguồn lead"
        description="Vui lòng nhập lý do muốn xoá nguồn lead này"
      />
    </div>
  );
}

export default ListLeadSources;
