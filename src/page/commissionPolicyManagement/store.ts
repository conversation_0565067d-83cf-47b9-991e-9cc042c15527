import { create } from 'zustand';
import { ICommissionPolicy } from '../../types/commissionPolicy';

interface IActionModal {
  isOpen: boolean;
  type?: 'create' | 'clone';
  id?: string;
}

interface ICommissionPolicyStore {
  disabled: boolean;
  initialValue: ICommissionPolicy;
  isModified: boolean;
  actionModal: IActionModal;
  setDisabled: (value: boolean) => void;
  setInitialValue: (value?: ICommissionPolicy) => void;
  setIsModified: (value: boolean) => void;
  setActionModal: (value: IActionModal) => void;
}

export const useCommissionPolicyStore = create<ICommissionPolicyStore>(set => ({
  disabled: false,
  initialValue: {} as ICommissionPolicy,
  isModified: false,
  actionModal: { isOpen: false, type: undefined, id: undefined },
  setDisabled: value => set({ disabled: value }),
  setInitialValue: value => set({ initialValue: value }),
  setIsModified: value => set({ isModified: value }),
  setActionModal: (value: IActionModal) => set({ actionModal: value }),
}));
