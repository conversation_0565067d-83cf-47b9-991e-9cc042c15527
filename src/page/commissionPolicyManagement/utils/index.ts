import { v4 as uuid } from 'uuid';
import { TRate } from '../../../types/commissionPolicy';

/**
 * Format API response bonus data for display in forms
 */
export const formatListRateForDisplay = (listRate: TRate[]) => {
  if (!listRate) return [];

  return listRate?.map((rate: TRate) => ({
    ...rate,
    key: uuid(),
    bottomPrice: rate?.bottomPrice?.toString(),
    topPrice: rate?.topPrice?.toString(),
    userNVTV: rate?.listUser?.userNVTV?.toString(),
    userNVKD: rate?.listUser?.userNVKD?.toString(),
    userTPBH: rate?.listUser?.userTPBH?.toString(),
    userGDBH: rate?.listUser?.userGDBH?.toString(),
    userGDKD: rate?.listUser?.userGDKD?.toString(),
  }));
};

/**
 * Format form data for submission to API
 */
export const formatListRateForSubmit = (listRate: TRate) => {
  if (!listRate) return {};

  return Array.isArray(listRate)
    ? listRate.map(
        ({ key, userNVTV, userNVKD, userTPBH, userGDBH, userGDKD, bottomPrice, topPrice, calculateType }: TRate) => ({
          key,
          bottomPrice: Number(bottomPrice),
          topPrice: Number(topPrice),
          calculateType: calculateType,
          listUser: {
            userNVTV: Number(userNVTV) ? Number(userNVTV) : null,
            userNVKD: Number(userNVKD),
            userTPBH: Number(userTPBH),
            userGDBH: Number(userGDBH),
            userGDKD: Number(userGDKD),
          },
        }),
      )
    : [];
};

export const ARRAY_FIELD_RATE = ['bottomPrice', 'topPrice', 'userNVTV', 'userNVKD', 'userTPBH', 'userGDBH', 'userGDKD'];

export enum CommissionPolicyType {
  MANAGER = 'MANAGE',
  PERSONAL = 'PERSONAL',
}
