import { Button, Checkbox, Col, Form, FormProps, Input, Row, Select, Switch, Typography } from 'antd';
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import BreadCrumbComponent from '../../../components/breadCrumb';
import {
  FORMAT_DATE_TIME,
  OPTIONS_APPROVAL_STATUS,
  OPTIONS_STATUS_KPI,
  OPTIONS_TYPE_COMMISSION_POLICY,
} from '../../../constants/common';
import { useFetch, useUpdateField } from '../../../hooks';
import './styles.scss';
import ButtonOfPageDetail from '../../../components/button/buttonOfPageDetail';
import { handleErrors } from '../../../service/error/errorsService';
import React from 'react';
import { PlusOutlined } from '@ant-design/icons';
import { ICommissionPolicy, TPos, TRate } from '../../../types/commissionPolicy';
import { getDetailCommissionPolicy, getListPos, updateCommissionPolicy } from '../../../service/commissionPolicy';
import dayjs from 'dayjs';
import { useCommissionPolicyStore } from '../store';
import { ARRAY_FIELD_RATE, CommissionPolicyType, formatListRateForDisplay, formatListRateForSubmit } from '../utils';

import { v4 as uuid } from 'uuid';
import FormPeriod from '../../../components/select/FormPeriod';
import ListRate from '../components/ListRate';
import { columnRate } from '../components/columns';
import { ColumnTypes, EditColumns } from '../components/editTable';
import SingleSelectLazy from '../../../components/select/singleSelectLazy';

const { Item } = Form;
const { Title, Text } = Typography;

const DetailCommissionPolicy = () => {
  const { id } = useParams();
  const [form] = Form.useForm();
  const listPos = Form.useWatch('listPos', form);
  const eappUrl = Form.useWatch('eappUrl', form);
  const policyType = Form.useWatch('policyType', form);

  const { initialValue, setDisabled, setInitialValue, isModified, setIsModified } = useCommissionPolicyStore();

  const [actived, setActived] = useState(OPTIONS_STATUS_KPI[0]?.value);
  const [dataSource, setDataSource] = useState<TRate[]>([
    { key: uuid(), calculateType: 'VND', isNew: true, policyType: 'PERSONAL' },
  ]);

  const { mutateAsync, isPending } = useUpdateField({
    apiQuery: updateCommissionPolicy,
    keyOfListQuery: ['get-commission-policy'],
    keyOfDetailQuery: ['get-detail-commission-policy', id],
    checkDuplicate: true,
    isMessageError: false,
  });

  const { data: dataDetail } = useFetch<ICommissionPolicy>({
    api: () => id && getDetailCommissionPolicy(id),
    queryKeyArr: ['get-detail-commission-policy', id],
    enabled: !!id,
    cacheTime: 10,
  });
  const data = dataDetail?.data?.data;

  const handleChangePolicyType = (value: string) => {
    const newData = {
      ...ARRAY_FIELD_RATE.reduce((acc, field) => ({ ...acc, [field]: '' }), {}),
      key: uuid(),
      isNew: true,
      calculateType: value === CommissionPolicyType.MANAGER ? '%' : 'VND',
      policyType: value,
    } as unknown as TRate;
    const resetFields = [
      'commissionRateManageNVKD',
      'commissionRateManageTPBH',
      'commissionRateManageGDBH',
      'commissionRateManageGDKD',
      'commissionRateRevenueNVKD',
      'commissionRateRevenueTPBH',
      'commissionRateRevenueGDBH',
      'commissionRateRevenueGDKD',
    ];
    form?.setFields(resetFields.map(name => ({ name, value: 0 })));
    form?.setFieldValue('listRate', [newData]);

    setDataSource([newData]);
  };

  const handelChangeStatus = React.useCallback(
    (checked: boolean) => {
      setActived(checked ? 1 : 2);
      setIsModified(true);
    },
    [setIsModified],
  );

  const handleAddRate = () => {
    const newData = {
      ...ARRAY_FIELD_RATE.reduce((acc, field) => ({ ...acc, [field]: '' }), {}),
      key: uuid(),
      isNew: true,
      bottomPrice: dataSource[dataSource?.length - 1]?.topPrice ? dataSource[dataSource?.length - 1]?.topPrice : 0,
      calculateType: dataSource?.length
        ? dataSource[dataSource?.length - 1]?.calculateType
        : policyType === CommissionPolicyType.PERSONAL
          ? 'VND'
          : '%',
    } as unknown as TRate;
    setDataSource([...dataSource, newData]);
  };

  // cảnh báo khi thoát trang khi chưa lưu dữ liệu
  useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  useEffect(() => {
    if (data) {
      const formatListRate = formatListRateForDisplay(data?.listRate as TRate[]) as TRate[];
      const policyTypeValue = OPTIONS_TYPE_COMMISSION_POLICY.find(option => option.label === data?.policyType)?.value;
      const formatData = {
        ...data,
        policyType: policyTypeValue,
        isActive: Number(data?.isActive),
        date: [dayjs(data.startDate || undefined), dayjs(data.endDate || undefined)],
        listRate: formatListRate as unknown as TRate[],
        listPos: {
          ...data?.listPos,
          label: data?.listPos?.name,
          value: data?.listPos?.id,
        },
        periodObj: {
          periodFrom: data?.periodFrom,
          periodTo: data?.periodTo,
          periodName: data?.periodName,
        },
        commissionRateManageNVKD: data?.commissionRateManage?.userNVKD,
        commissionRateManageTPBH: data?.commissionRateManage?.userTPBH,
        commissionRateManageGDBH: data?.commissionRateManage?.userGDBH,
        commissionRateManageGDKD: data?.commissionRateManage?.userGDKD,
        commissionRateRevenueNVKD: data?.commissionRateRevenue?.userNVKD,
        commissionRateRevenueTPBH: data?.commissionRateRevenue?.userTPBH,
        commissionRateRevenueGDBH: data?.commissionRateRevenue?.userGDBH,
        commissionRateRevenueGDKD: data?.commissionRateRevenue?.userGDKD,
      };
      setDisabled(['WAITING', 'APPROVED']?.includes(initialValue?.status as string) ? true : false);
      form.setFieldsValue(formatData);
      setInitialValue(formatData as ICommissionPolicy);
      setDataSource(formatListRate);
    }
  }, [data, form, initialValue?.status, setDisabled, setInitialValue]);

  React.useEffect(() => {
    const observer = new MutationObserver(() => {
      const nodeExpense = document.querySelectorAll<HTMLElement>('.ant-table-body')[0];
      const nodeRevenue = document.querySelectorAll<HTMLElement>('.ant-table-body')[1];

      if (nodeExpense && nodeRevenue) {
        observer.disconnect(); // Dừng theo dõi sau khi tìm thấy phần tử

        const handleScroll = () => {
          nodeRevenue.scrollLeft = nodeExpense.scrollLeft;
        };
        const handleScroll2 = () => {
          nodeExpense.scrollLeft = nodeRevenue.scrollLeft;
        };

        nodeExpense.addEventListener('scroll', handleScroll);
        nodeRevenue.addEventListener('scroll', handleScroll2);

        return () => {
          nodeExpense.removeEventListener('scroll', handleScroll);
          nodeRevenue.removeEventListener('scroll', handleScroll2);
        };
      }
    });

    observer.observe(document.body, { childList: true, subtree: true });

    return () => observer.disconnect();
  }, []);

  const onFinish: FormProps['onFinish'] = async (values: ICommissionPolicy) => {
    const listRate = form.getFieldValue('listRate');
    const periodObj = form.getFieldValue('periodObj');

    const policyTypeSubmit = OPTIONS_TYPE_COMMISSION_POLICY.find(option => option.value === values?.policyType)?.label;

    const submitListRate = formatListRateForSubmit(listRate);

    const transformedValues = {
      ...values,
      ...periodObj,
      id: data?.id,
      policyType: policyTypeSubmit,
      year: Number(values?.year),
      name: values?.name?.trim() || '',
      code: values?.code?.trim() || '',
      isActive: actived,
      date: undefined,
      userNVTV: undefined,
      userNVKD: undefined,
      userTPBH: undefined,
      userGDBH: undefined,
      userGDKD: undefined,
      listRate: submitListRate,
      commissionRateManageNVKD: undefined,
      commissionRateManageTPBH: undefined,
      commissionRateManageGDBH: undefined,
      commissionRateManageGDKD: undefined,
      commissionRateRevenueNVKD: undefined,
      commissionRateRevenueTPBH: undefined,
      commissionRateRevenueGDBH: undefined,
      commissionRateRevenueGDKD: undefined,
      commissionRateManage:
        values?.policyType === CommissionPolicyType.MANAGER
          ? {
              userNVKD: values?.commissionRateManageNVKD,
              userTPBH: values?.commissionRateManageTPBH,
              userGDBH: values?.commissionRateManageGDBH,
              userGDKD: values?.commissionRateManageGDKD,
            }
          : undefined,
      commissionRateRevenue:
        values?.policyType === CommissionPolicyType.MANAGER
          ? {
              userNVKD: values?.commissionRateRevenueNVKD,
              userTPBH: values?.commissionRateRevenueTPBH,
              userGDBH: values?.commissionRateRevenueGDBH,
              userGDKD: values?.commissionRateRevenueGDKD,
            }
          : undefined,
    };
    try {
      const res = await mutateAsync(transformedValues);
      const responseData = res?.data;
      if (responseData) {
        switch (responseData?.statusCode) {
          case '0':
            setIsModified(false);
            break;
          default:
            handleErrors(responseData);
        }
      }
    } catch (error) {
      console.error('Error creating commission policy:', error);
    }
  };

  const handleCancel = () => {
    form.setFieldsValue(initialValue);
    setIsModified(false);
  };

  const validateForm = () => {
    setIsModified(true);
  };

  useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  const handleSelectListPos = (value: TPos) => {
    form.setFieldsValue({ listPos: { id: value?.id, name: value?.name, code: value?.code } });
    setIsModified(true);
  };
  const modifiedColumns: (ColumnTypes<TRate>[number] & EditColumns)[] =
    policyType === CommissionPolicyType.MANAGER ? columnRate?.filter(column => column.key !== 'userNVTV') : columnRate;

  return (
    <div className="wrapper-detail-PERSONAL-commission-policy">
      <BreadCrumbComponent titleBread={data?.name} />

      {initialValue ? (
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          initialValues={initialValue}
          onValuesChange={validateForm}
          className="space-y-6"
        >
          <Row gutter={{ md: 24, lg: 40, xl: 80, xxl: 126 }}>
            <Col span={24}>
              <Title level={5}>Thông tin chung</Title>
            </Col>
            <Col xs={24} md={12}>
              <Row gutter={24}>
                <Col xs={24} md={12}>
                  <Item label="Mã bộ chỉ tiêu KPI" name="code" required>
                    <Input placeholder="Hệ thống tự động hiển thị" disabled />
                  </Item>
                </Col>
                <Col xs={24} md={12}>
                  <Item
                    label="Tên bộ chỉ tiêu KPI"
                    name="name"
                    required
                    rules={[
                      {
                        required: true,
                        validator: (_, value) => {
                          if (!value || value.trim() === '') {
                            return Promise.reject(new Error('Vui lòng nhập tên bộ chỉ tiêu'));
                          }
                          return Promise.resolve();
                        },
                      },
                    ]}
                  >
                    <Input placeholder="Nhập tên bộ chỉ tiêu" maxLength={255} />
                  </Item>
                </Col>
                <Col xs={24} md={12}>
                  <Item label="Loại chỉ tiêu" name="policyType" required rules={[{ required: true }]}>
                    <Select
                      placeholder="Chọn loại chỉ tiêu"
                      allowClear
                      options={OPTIONS_TYPE_COMMISSION_POLICY}
                      onChange={handleChangePolicyType}
                    />
                  </Item>
                </Col>
                <Col xs={24} md={12}>
                  <Item label="Đơn vị bán hàng" name="listPos">
                    <SingleSelectLazy
                      apiQuery={getListPos}
                      queryKey={['get-list-pos']}
                      keysLabel={'name'}
                      placeholder="Chọn đơn vị bán hàng"
                      handleSelect={handleSelectListPos}
                      defaultValues={{
                        value: listPos?.id,
                        label: listPos?.name,
                      }}
                    />
                  </Item>
                </Col>

                <Col xs={24}>
                  <FormPeriod required label="Kỳ thiết lập" fieldPos="listPos" />
                </Col>

                <Col md={12} xs={24}>
                  <Item label="Số E-approve" name="eappNumber">
                    <Input
                      readOnly
                      onClick={() => {
                        if (eappUrl) {
                          window.open(eappUrl, '_blank', 'noopener,noreferrer');
                        }
                      }}
                      style={{
                        cursor: eappUrl ? 'pointer' : 'default',
                        color: eappUrl ? '#1890ff' : 'inherit',
                      }}
                    />
                  </Item>
                </Col>
                <Col md={12} xs={24}>
                  <Item label="Trạng thái phê duyệt" name="status">
                    <Select placeholder="Chọn trạng thái" options={OPTIONS_APPROVAL_STATUS} disabled={true} />
                  </Item>
                </Col>

                <Col xs={24} md={4}>
                  <Item label="Trạng thái"></Item>
                </Col>
                <Col xs={24} md={3}>
                  <Switch value={actived === 1} onChange={handelChangeStatus} />
                </Col>
                <Col xs={24} md={6}>
                  <Text style={{ color: actived === 1 ? '#389E0D' : '#CF1322' }}>
                    {actived === 1 ? OPTIONS_STATUS_KPI[0]?.label : OPTIONS_STATUS_KPI[1]?.label}
                  </Text>
                </Col>
              </Row>
            </Col>

            <Col xs={24} md={12}>
              <div className="info">
                <Row gutter={24}>
                  <Col lg={6} xs={8}>
                    <Text disabled>Ngày cập nhật: </Text>
                  </Col>
                  <Col lg={18} xs={16}>
                    <Text disabled>
                      {dayjs(data?.modifiedDate).format(FORMAT_DATE_TIME)}&nbsp;&nbsp;
                      {`${data?.modifiedBy?.userName} - ${data?.modifiedBy?.fullName}`}
                    </Text>
                  </Col>
                </Row>
                <Row gutter={24}>
                  <Col lg={6} xs={8}>
                    <Text disabled>Ngày tạo: </Text>
                  </Col>
                  <Col lg={18} xs={16}>
                    <Text disabled>
                      {dayjs(data?.createdDate).format(FORMAT_DATE_TIME)}&nbsp;&nbsp;
                      {`${data?.createdBy?.userName} - ${data?.createdBy?.fullName}`}
                    </Text>
                  </Col>
                </Row>
              </div>
            </Col>

            {/* <Row gutter={{ md: 24, lg: 40 }}> */}
            <Col xs={24} md={24}>
              <Row gutter={{ md: 24, lg: 40 }} align={'middle'}>
                <Col span={24}>
                  <Title level={5}>Tỷ lệ hoa hồng</Title>
                </Col>
                <Col span={4}>
                  <Item name="isProgressive" valuePropName="checked" className="custom-checkbox-form">
                    <Checkbox
                      disabled={(() => {
                        return (
                          policyType === CommissionPolicyType.MANAGER ||
                          (dataSource[0]?.calculateType === '%' && policyType === 'PERSONAL')
                        );
                      })()}
                    >
                      Tính lũy tiến
                    </Checkbox>
                  </Item>
                </Col>
                <Col span={6}>
                  <Item name="isVAT" valuePropName="checked" className="custom-checkbox-form">
                    <Checkbox>VAT </Checkbox>
                  </Item>
                </Col>
                <Col
                  span={14}
                  style={{
                    display: 'flex',
                    justifyContent: 'right',
                    alignItems: 'center',
                  }}
                >
                  <Button onClick={() => handleAddRate()}>
                    <PlusOutlined />
                    Thêm tỷ lệ hoa hồng
                  </Button>
                </Col>
              </Row>
            </Col>
            {/* </Row> */}
            <Col xs={24} md={24} style={{ marginTop: '8px' }}>
              <div style={{ overflowX: 'auto' }}>
                {initialValue && (
                  <ListRate<TRate>
                    nameField="listRate"
                    columns={modifiedColumns}
                    dataSource={dataSource?.map(o => ({ ...o, policyType: policyType }))}
                    arrayFieldTable={ARRAY_FIELD_RATE}
                    setDataSource={setDataSource}
                  />
                )}
              </div>
            </Col>
          </Row>
        </Form>
      ) : (
        <></>
      )}
      {isModified && (
        <ButtonOfPageDetail handleSubmit={() => form.submit()} handleCancel={handleCancel} loadingSubmit={isPending} />
      )}
    </div>
  );
};

export default DetailCommissionPolicy;
