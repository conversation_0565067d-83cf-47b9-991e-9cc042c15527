import { Button, Dropdown, InputProps, MenuProps, SelectProps, Table } from 'antd';
import { Rule } from 'antd/es/form';
import { Form, TableProps } from 'antd/lib';
import { TableComponents } from 'rc-table/lib/interface';
import './styles.scss';
import { useCommissionPolicyStore } from '../../store';
import { MoreOutlined } from '@ant-design/icons';
import useFormInstance from 'antd/lib/form/hooks/useFormInstance';
import { EditableSummaryCell } from './EditSummaryCell';
import { CommissionPolicyType } from '../../utils';

export interface EditableRecord {
  key: string;
  isNew?: boolean;
  calculateType?: string;
  bottomPrice?: string;
  topPrice?: string;
  policyType?: string;
}

export type ColumnTypes<T> = Exclude<TableProps<T>['columns'], undefined>;

interface EditableProps<T> {
  components: TableComponents<T>;
  dataSource: T[];
  columns: (ColumnTypes<T>[number] & EditColumns)[];
  handleAdd: () => void;
  handleDelete: (key: React.Key) => void;
  handleClone: (record: T) => void;
  handleSave: (record: T) => void;
}

export interface EditColumns {
  editable?: boolean;
  dataIndex: string;
  inputType?: 'text' | 'select' | 'custom';
  optionsSelect?: SelectProps['options'];
  rules?: Rule[] | ((record: unknown) => Rule[]);
  renderEditComponent?: (props: {
    value: unknown;
    onChange: (value: unknown) => void;
    onPressEnter?: () => void;
    onBlur?: () => void;
    save: () => void;
    disabled?: boolean;
  }) => React.ReactNode;
  inputProps?: Omit<InputProps, 'onPressEnter' | 'onBlur'>;
  selectProps?: Omit<SelectProps, 'onChange' | 'onBlur' | 'options'>;
  dependencies?: string[];
}

export const Editable = <T extends EditableRecord>(props: EditableProps<T>) => {
  const form = useFormInstance();
  const { components, columns, dataSource, handleDelete, handleClone, handleSave } = props;
  const { setIsModified } = useCommissionPolicyStore();

  const actionsColumns: (ColumnTypes<T>[number] & EditColumns)[] = [
    ...columns,
    {
      dataIndex: 'action',
      align: 'center',
      width: '10%',
      render: (_, record) => {
        const items: MenuProps['items'] = [
          {
            label: 'Sao chép',
            key: 3,
            onClick: () => {
              setIsModified(true);
              handleClone(record);
            },
          },
          {
            label: 'Xóa',
            key: 4,
            onClick: () => {
              setIsModified(true);
              handleDelete(record?.key);
            },
          },
        ].filter(Boolean) as MenuProps['items'];

        return dataSource.length >= 1 ? (
          <Dropdown menu={{ items }} placement="bottomRight" overlayStyle={{ width: 150 }}>
            <Button type="text" style={{ padding: '4px 8px' }}>
              <MoreOutlined style={{ fontSize: '20px', cursor: 'pointer' }} />
            </Button>
          </Dropdown>
        ) : null;
      },
    },
  ];

  // Map columns to include inputType for EditableCell
  const columnsEdit = actionsColumns.map(col => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: (record: T) => ({
        record,
        editable: col?.editable,
        dataIndex: col?.dataIndex,
        title: col?.title,
        handleSave,
        inputType: col?.inputType,
        isNewRow: record?.isNew,
        optionsSelect: col?.optionsSelect,
        renderEditComponent: col?.renderEditComponent,
        rules: typeof col?.rules === 'function' ? col?.rules(record) : col.rules,
        inputProps: col?.inputProps,
        selectProps: col?.selectProps,
        dependencies: col?.dependencies,
      }),
    };
  });

  const save = async (value: string, fieldName: string) => {
    //Lưu các trường thuộc form chính thuộc summary của bảng
    try {
      form.setFieldValue(fieldName, value);
    } catch (errInfo) {
      console.log('Save failed:', errInfo);
    }
  };

  return dataSource[0] ? (
    <div>
      <Table
        components={components}
        className={`editable ${dataSource.length === 0 ? 'editable-empty' : ''}`}
        dataSource={dataSource}
        columns={columnsEdit as ColumnTypes<T>}
        sticky={{ offsetHeader: 0 }}
        locale={{ emptyText: null }}
        pagination={false}
        onRow={(_record, index) => {
          return {
            index, // custom prop truyền vào EditableRow
            // formRef: formMapRef,
            initValue: {
              ...dataSource[Number(index)],
              topPrice: dataSource[Number(index)]?.topPrice ? dataSource[Number(index)]?.topPrice : 0,
              bottomPrice: dataSource[Number(index)]?.bottomPrice ? dataSource[Number(index)]?.bottomPrice : 0,
            },
          } as React.HTMLAttributes<HTMLTableRowElement> & { index?: string };
        }}
        summary={() =>
          dataSource[0]?.policyType === CommissionPolicyType.MANAGER ? (
            <Table.Summary fixed="top">
              <Table.Summary.Row>
                <Table.Summary.Cell index={0} colSpan={4}>
                  Tỷ lệ hoa hồng quản lý
                </Table.Summary.Cell>
                <Table.Summary.Cell index={4}>
                  <Form.Item style={{ margin: 0 }} name="commissionRateManageNVKD">
                    <EditableSummaryCell
                      title=""
                      editable={true}
                      dataIndex={'commissionRateManageNVKD' as keyof T}
                      handleSave={save}
                      rules={[]}
                    />
                  </Form.Item>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={5}>
                  <Form.Item style={{ margin: 0 }} name="commissionRateManageTPBH">
                    <EditableSummaryCell
                      title=""
                      editable={true}
                      dataIndex={'commissionRateManageTPBH' as keyof T}
                      handleSave={save}
                      rules={[]}
                    />
                  </Form.Item>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={6}>
                  <Form.Item style={{ margin: 0 }} name="commissionRateManageGDBH">
                    <EditableSummaryCell
                      title=""
                      editable={true}
                      dataIndex={'commissionRateManageGDBH' as keyof T}
                      handleSave={save}
                      rules={[]}
                    />
                  </Form.Item>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={7}>
                  <Form.Item style={{ margin: 0 }} name="commissionRateManageGDKD">
                    <EditableSummaryCell
                      title=""
                      editable={true}
                      dataIndex={'commissionRateManageGDKD' as keyof T}
                      handleSave={save}
                      rules={[]}
                    />
                  </Form.Item>
                </Table.Summary.Cell>
              </Table.Summary.Row>

              <Table.Summary.Row>
                <Table.Summary.Cell index={0} colSpan={4}>
                  Tỷ lệ hoa hồng trên doanh thu NVTV do NVKD quản lý
                </Table.Summary.Cell>
                <Table.Summary.Cell index={4}>
                  <Form.Item style={{ margin: 0 }} name="commissionRateRevenueNVKD">
                    <EditableSummaryCell
                      title=""
                      editable={true}
                      dataIndex={'commissionRateRevenueNVKD' as keyof T}
                      handleSave={save}
                      rules={[]}
                    />
                  </Form.Item>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={5}>
                  <Form.Item style={{ margin: 0 }} name="commissionRateRevenueTPBH">
                    <EditableSummaryCell
                      title=""
                      editable={true}
                      dataIndex={'commissionRateRevenueTPBH' as keyof T}
                      handleSave={save}
                      rules={[]}
                    />
                  </Form.Item>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={6}>
                  <Form.Item style={{ margin: 0 }} name="commissionRateRevenueGDBH">
                    <EditableSummaryCell
                      title=""
                      editable={true}
                      dataIndex={'commissionRateRevenueGDBH' as keyof T}
                      handleSave={save}
                      rules={[]}
                    />
                  </Form.Item>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={7}>
                  <Form.Item style={{ margin: 0 }} name="commissionRateRevenueGDKD">
                    <EditableSummaryCell
                      title=""
                      editable={true}
                      dataIndex={'commissionRateRevenueGDKD' as keyof T}
                      handleSave={save}
                      rules={[]}
                    />
                  </Form.Item>
                </Table.Summary.Cell>
              </Table.Summary.Row>

              <Table.Summary.Row>
                <Table.Summary.Cell index={0} colSpan={columnsEdit.length}>
                  Tỷ lệ hoàn thành chỉ tiêu
                </Table.Summary.Cell>
              </Table.Summary.Row>
            </Table.Summary>
          ) : undefined
        }
      />
    </div>
  ) : (
    <></>
  );
};
