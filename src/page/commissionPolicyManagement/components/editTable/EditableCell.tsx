import { Form, Input, InputProps, Select } from 'antd';
import { Rule } from 'antd/es/form';
import { SelectProps } from 'antd/lib';
import React, { useContext, useEffect, useState } from 'react';
import { useCommissionPolicyStore } from '../../store';
import { EditableContext } from './EditableContext';
import { LeftOutlined } from '@ant-design/icons';
import { CommissionPolicyType } from '../../utils';
import { formatNumber } from '../../../../utilities/regex';

interface EditableCellProps<T> {
  title: React.ReactNode;
  editable: boolean;
  dataIndex: keyof T;
  record: T;
  handleSave: (record: T) => void;
  inputType?: 'text' | 'select' | 'custom'; // New property to specify input type
  isNewRow: boolean;
  optionsSelect?: SelectProps['options'];
  rules: Rule[];
  renderEditComponent?: (props: {
    value: unknown;
    onChange: (value: unknown) => void;
    onPressEnter?: () => void;
    onBlur?: () => void;
    save: () => void;
    disabled?: boolean;
  }) => React.ReactNode;
  inputProps?: Omit<InputProps, 'onPressEnter' | 'onBlur'>;
  selectProps?: Omit<SelectProps, 'onChange' | 'onBlur' | 'options'>;
  dependencies?: string[]; // Dependencies for validation rules
}

export const EditableCell = <T extends object>({
  editable,
  children,
  dataIndex,
  record,
  handleSave,
  inputType,
  optionsSelect,
  rules,
  renderEditComponent,
  inputProps,
  selectProps,
  dependencies,
}: React.PropsWithChildren<EditableCellProps<T>>) => {
  const [editing, setEditing] = useState(false);
  const { form, index } = useContext(EditableContext)!;
  const { disabled, setIsModified } = useCommissionPolicyStore();

  useEffect(() => {
    if (record && !record[dataIndex]) {
      setEditing(true);
    }
    if (['bottomPrice', 'calculateType']?.includes(dataIndex as string) && Number(index) !== 0) setEditing(false);
  }, [dataIndex, index, record]);

  const toggleEdit = () => {
    if (disabled) {
      return;
    }
    setEditing(!editing);
  };

  const save = async () => {
    try {
      const values = (await form.validateFields()) as Record<keyof T, unknown>;
      const currentValue = values[dataIndex];

      if (currentValue === undefined || currentValue === null) {
        return;
      }
      if (currentValue === '') {
        setEditing(!editing);
        form.setFieldsValue({ [dataIndex]: '0' });
        handleSave({ ...record, ...(values as Record<string, unknown>) });
        return;
      }
      toggleEdit();
      form.setFieldsValue({ [dataIndex]: currentValue });
      handleSave({ ...record, ...(values as Record<string, unknown>) });
      setIsModified(true);
    } catch (errInfo) {
      console.log('Save failed:', errInfo);
    }
  };

  let childNode = children;

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    const isNumberKey = /[0-9]/.test(event.key);
    const isAllowedKey = ['Backspace', 'Tab', 'Delete', 'ArrowLeft', 'ArrowRight'].includes(event.key);
    const inputElement = event.currentTarget;
    const inputValue = inputElement.value;

    // If the input starts with "0", clear it when a number is typed
    if (inputValue === '0' && isNumberKey) {
      inputElement.value = '';
    }

    // Prevent non-numeric characters except allowed keys
    if (!isNumberKey && !isAllowedKey && event.key !== '.') {
      event.preventDefault();
    }

    // Prevent multiple decimal points
    if (event.key === '.' && inputValue.includes('.')) {
      event.preventDefault();
    }

    // Ensure the field auto-fills "0" if everything is deleted
    setTimeout(() => {
      if (inputElement.value === '') {
        inputElement.value = '0';
      }
    }, 0);
  };

  if (editable) {
    childNode = editing ? (
      <Form.Item style={{ margin: 0 }} name={dataIndex as string} rules={rules} dependencies={dependencies}>
        {inputType === 'custom' && renderEditComponent ? (
          renderEditComponent({
            value: dataIndex === 'unit' ? record?.['calculateType' as keyof T] : record[dataIndex],
            onChange: val => form.setFieldsValue({ [dataIndex]: val }),
            onPressEnter: save,
            onBlur: save,
            save,
            disabled: disabled,
          })
        ) : inputType === 'select' ? (
          <Select
            onChange={() => setTimeout(save, 100)} // Save when a selection is made
            onBlur={save}
            options={optionsSelect}
            disabled={disabled}
            {...selectProps}
          />
        ) : (
          <Input
            // defaultValue={0}
            {...inputProps}
            onPressEnter={save}
            onBlur={save}
            disabled={disabled}
            className="no-spinner"
            onKeyDown={handleKeyDown}
          />
        )}
      </Form.Item>
    ) : (
      <div
        className="editable-cell-value-wrap"
        onClick={
          (['bottomPrice', 'calculateType']?.includes(dataIndex as string) && Number(index) !== 0) ||
          (record?.['policyType' as keyof T] === CommissionPolicyType.MANAGER && dataIndex === 'calculateType')
            ? undefined
            : toggleEdit
        }
      >
        {dataIndex === 'calculateType' ? (
          <div style={{ color: '#0958D9', textAlign: 'center' }}>
            <LeftOutlined /> <span style={{ fontSize: 18 }}>= </span>
            <span style={{ color: 'black' }}>
              {record[dataIndex] === 'VND' ? 'Tổng doanh thu cá nhân' : 'Tỷ lệ hoàn thành'}
            </span>
            <LeftOutlined />
          </div>
        ) : ['bottomPrice', 'topPrice']?.includes(dataIndex as string) ? (
          formatNumber(record[dataIndex] as string)
        ) : inputType === 'custom' ? (
          <div style={{ textAlign: 'center' }}>{`${record[dataIndex] as string} %`}</div>
        ) : (
          <div style={{ textAlign: 'center' }}>{children}</div>
        )}
      </div>
    );
  }

  return <td align="center">{childNode}</td>;
};
