import { Form } from 'antd';
import React from 'react';
import { EditableContext } from './EditableContext';

// EditableRow component remains unchanged
interface EditableRowProps<T> {
  index?: string;
  initValue?: T;
}

export const EditableRow = <T extends object>({
  index,
  initValue,
  ...props
}: EditableRowProps<T> & React.HTMLAttributes<HTMLTableRowElement>) => {
  const [form] = Form.useForm();
  return (
    <Form form={form} component={false} key={index} initialValues={initValue}>
      <EditableContext.Provider value={{ form: form, index: index as string }}>
        <tr {...props} />
      </EditableContext.Provider>
    </Form>
  );
};
