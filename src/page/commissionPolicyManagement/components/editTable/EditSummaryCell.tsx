import { Form, InputProps } from 'antd';
import { Rule } from 'antd/es/form';
import React, { useEffect, useState } from 'react';
import { useCommissionPolicyStore } from '../../store';
import useFormInstance from 'antd/es/form/hooks/useFormInstance';
import PercentInput from '../percentInputCustom/PercentInput';

interface EditableSummaryCellProps<T> {
  title: React.ReactNode;
  editable: boolean;
  dataIndex: keyof T;
  handleSave: (value: string, fieldName: string) => Promise<void>;
  isNewRow?: boolean;
  rules: Rule[];
  inputProps?: Omit<InputProps, 'onPressEnter' | 'onBlur'>;
}

export const EditableSummaryCell = <T extends object>({
  editable,
  dataIndex,
  handleSave,
  rules,
}: React.PropsWithChildren<EditableSummaryCellProps<T>>) => {
  const form = useFormInstance();

  const [editing, setEditing] = useState(!form?.getFieldValue(dataIndex));

  const { disabled, setIsModified } = useCommissionPolicyStore();

  useEffect(() => {
    if (form?.getFieldValue(dataIndex) === 0) {
      setEditing(true);
    }
  }, [dataIndex, form]);

  const toggleEdit = () => {
    setEditing(!editing);
  };

  const save = async () => {
    try {
      const values = (await form.validateFields([dataIndex])) as Record<keyof T, unknown>;
      const currentValue = values[dataIndex];

      if (currentValue === undefined || currentValue === null) {
        return;
      }
      if (currentValue === '') {
        handleSave(currentValue as string, dataIndex as string);
        return;
      }
      toggleEdit();
      handleSave(currentValue as string, dataIndex as string);
      setIsModified(true);
    } catch (errInfo) {
      console.log('Save failed:', errInfo);
    }
  };

  if (editable) {
    return editing ? (
      <Form.Item style={{ margin: 0 }} name={dataIndex as string} rules={rules}>
        <PercentInput
          placeholder="Nhập..."
          onPressEnter={save}
          onBlur={save}
          // onChange={val => onChange(val)}
          suffix="%"
          disable={disabled}
        />
      </Form.Item>
    ) : (
      <div className="editable-cell-value-wrap" style={{ textAlign: 'center' }} onClick={toggleEdit}>
        {form?.getFieldValue(dataIndex) ? form?.getFieldValue(dataIndex) : 0} %
      </div>
    );
  }

  return (
    <div style={{ textAlign: 'center' }}> {form?.getFieldValue(dataIndex) ? form?.getFieldValue(dataIndex) : 0} %</div>
  );
};
