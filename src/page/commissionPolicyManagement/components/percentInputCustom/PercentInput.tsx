import { InputNumber } from 'antd';
import { useEffect, useState } from 'react';

interface PercentInputProps {
  value?: number;
  onChange?: (value: number) => void;
  onBlur?: (value: number) => void;
  type?: string;
  maxLength?: number;
  placeholder?: string;
  suffix?: string;
  [key: string]: unknown;
}

const PercentInput = (props: PercentInputProps) => {
  const { value, onChange, type = 'number', maxLength, placeholder, suffix = '%', onBlur, ...restProps } = props;
  const [internalValue, setInternalValue] = useState(value || 0);
  const [hadStartDecimal, setHadStartDecimal] = useState<boolean>(false); //Check dấu phẩy thập phân ở cuối để validate giá trị nhập

  useEffect(() => {
    setInternalValue(value || 0);
  }, [value]);

  const handleChange = (value: number | null) => {
    const inputValue = value;
    setInternalValue(inputValue ?? 0);
    onChange && onChange(inputValue ?? 0);
  };

  const handleBlur = () => {
    // const numValue = parseInt(internalValue, 10);
    // if (numValue > 100) {
    //   setInternalValue('100');
    //   onBlur && onBlur('100');
    // }
    onBlur && onBlur(internalValue);
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    const isNumberKey = /[0-9]/.test(event.key);
    const isAllowedKey = ['Backspace', 'Tab', 'Delete', 'ArrowLeft', 'ArrowRight'].includes(event.key);
    const inputValue = event.currentTarget.value;

    // Nếu là phím điều hướng, không cần kiểm tra
    if (isAllowedKey) {
      if (event.key === 'Delete') setHadStartDecimal(true); // Không bị mất đánh dấu Decimal khi xóa
      return;
    }

    // Ngăn nhập ký tự không hợp lệ
    if (!isNumberKey && event.key !== '.') {
      event.preventDefault();
    }

    // Ngăn nhập dấu "." nếu đã có một dấu
    if (event.key === '.' && inputValue.includes('.')) {
      setHadStartDecimal(false);
      event.preventDefault();
    } else if (event.key === '.' && !inputValue.includes('.')) setHadStartDecimal(true);

    // Kiểm tra số ký tự trước và sau dấu "."
    const [integerPart, decimalPart] =
      hadStartDecimal && !inputValue?.includes('.') ? (inputValue + '.' + event.key).split('.') : inputValue.split('.');
    if (integerPart.length >= 4 && event.key !== '.' && !inputValue.includes('.')) {
      event.preventDefault(); // Không cho nhập số thứ 5 trước dấu "."
    }
    if (decimalPart?.length >= 2 && isNumberKey) {
      event.preventDefault(); // Không cho nhập thêm số thứ 3 sau dấu "."
    }

    // Kiểm tra nếu nhập thêm sẽ vượt quá 1000
    const newValue = parseFloat(
      hadStartDecimal && !inputValue?.includes('.') ? inputValue + '.' + event.key : inputValue + event.key,
    );
    if (hadStartDecimal) setHadStartDecimal(false);
    if (isNumberKey && !isNaN(newValue) && newValue >= 1000) {
      event.preventDefault();
    }
  };

  return (
    <InputNumber
      {...restProps}
      type={type}
      value={internalValue}
      onChange={handleChange}
      onBlur={handleBlur}
      maxLength={maxLength}
      max={1000}
      placeholder={placeholder}
      onKeyDown={handleKeyDown}
      suffix={suffix}
      style={{ width: '100%' }}
      controls={false}
      // autoFocus={value !== undefined && value !== null}
      formatter={value => (value !== undefined ? String(value) : '')} // Chuyển số thành string
      parser={value => (value ? parseFloat(value) : 0)} // Chuyển string thành số
    />
  );
};

export default PercentInput;
