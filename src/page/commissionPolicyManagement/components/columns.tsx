import { ColumnTypes, EditColumns } from './editTable';
import { TRate } from '../../../types/commissionPolicy';
import { handleKeyDownEnterNumber } from '../../../utilities/regex';
import PercentInput from './percentInputCustom/PercentInput';

const OPTIONS_CALCULATE_TYPE = [
  { label: 'Tổng doanh thu cá nhân', value: 'VND' },
  { label: 'Tỷ lệ hoàn thành', value: '%' },
];

export const columnRate: (ColumnTypes<TRate>[number] & EditColumns)[] = [
  {
    title: 'Giá trị dưới',
    dataIndex: 'bottomPrice',
    key: 'bottomPrice',
    editable: true,
    align: 'center',
    width: '15%',
    inputType: 'text',
    inputProps: { maxLength: 13, onKeyDown: handleKeyDownEnterNumber, placeholder: 'Nhập giá trị từ' },
    dependencies: ['topPrice'],
    rules: [
      ({ getFieldValue }) => ({
        validator: async (_, value) => {
          const topPrice = getFieldValue('topPrice');
          if (topPrice && Number(value) > Number(topPrice)) {
            return Promise.reject('Vui lòng nhập giá trị dưới nhỏ hơn hoặc bằng giá trị cận trên');
          }
          return Promise.resolve();
        },
        validateTrigger: ['onChange', 'onBlur'],
      }),
    ],
  },
  {
    title: 'Giá trị tính',
    dataIndex: 'calculateType',
    key: 'calculateType',
    editable: true,
    width: '25%',
    inputType: 'select',
    align: 'center',
    optionsSelect: OPTIONS_CALCULATE_TYPE,
  },
  {
    title: 'Giá trị trên',
    dataIndex: 'topPrice',
    key: 'topPrice',
    editable: true,
    align: 'center',
    width: '15%',
    inputType: 'text',
    inputProps: { maxLength: 13, onKeyDown: handleKeyDownEnterNumber, placeholder: 'Nhập giá trị đến' },
    dependencies: ['bottomPrice'],
    rules: [
      ({ getFieldValue }) => ({
        validator: async (_, value) => {
          const bottomPrice = getFieldValue('bottomPrice');
          if (bottomPrice && Number(value) < Number(bottomPrice)) {
            return Promise.reject('Vui lòng nhập giá trị dưới nhỏ hơn hoặc bằng giá trị cận trên');
          }
          return Promise.resolve();
        },
        validateTrigger: ['onChange', 'onBlur'],
      }),
    ],
  },
  {
    title: 'Đơn vị tính',
    dataIndex: 'unit',
    key: 'unit',
    editable: true,
    width: '15%',
    inputType: 'custom',
    align: 'center',
    renderEditComponent: ({ value }) => <div style={{ textAlign: 'center' }}>{value as string}</div>,
  },
  {
    title: 'NVTV',
    dataIndex: 'userNVTV',
    key: 'userNVTV',
    editable: true,
    width: '15%',
    inputType: 'custom',
    align: 'center',
    renderEditComponent: ({ onChange, save, disabled }) => (
      <PercentInput
        placeholder="Nhập..."
        onPressEnter={save}
        onBlur={save}
        onChange={val => onChange(val)}
        suffix="%"
        disabled={disabled}
      />
    ),
  },
  {
    title: 'NVKD',
    dataIndex: 'userNVKD',
    key: 'userNVKD',
    editable: true,
    width: '15%',
    inputType: 'custom',
    align: 'center',
    renderEditComponent: ({ onChange, save, disabled }) => (
      <PercentInput
        placeholder="Nhập..."
        onPressEnter={save}
        onBlur={save}
        onChange={val => onChange(val)}
        suffix="%"
        disabled={disabled}
      />
    ),
  },
  {
    title: 'TPBH',
    dataIndex: 'userTPBH',
    key: 'userTPBH',
    editable: true,
    width: '15%',
    inputType: 'custom',
    align: 'center',
    renderEditComponent: ({ onChange, save, disabled }) => (
      <PercentInput
        placeholder="Nhập..."
        onPressEnter={save}
        onBlur={save}
        onChange={val => onChange(val)}
        suffix="%"
        disabled={disabled}
      />
    ),
  },
  {
    title: 'GĐBH',
    dataIndex: 'userGDBH',
    key: 'userGDBH',
    editable: true,
    width: '15%',
    inputType: 'custom',
    align: 'center',
    renderEditComponent: ({ onChange, save, disabled }) => (
      <PercentInput
        placeholder="Nhập..."
        onPressEnter={save}
        onBlur={save}
        onChange={val => onChange(val)}
        suffix="%"
        disabled={disabled}
      />
    ),
  },
  {
    title: 'GĐKD',
    dataIndex: 'userGDKD',
    key: 'userGDKD',
    editable: true,
    width: '15%',
    inputType: 'custom',
    align: 'center',
    renderEditComponent: ({ onChange, save, disabled }) => (
      <PercentInput
        placeholder="Nhập..."
        onPressEnter={save}
        onBlur={save}
        onChange={val => onChange(val)}
        suffix="%"
        disabled={disabled}
      />
    ),
  },
];
