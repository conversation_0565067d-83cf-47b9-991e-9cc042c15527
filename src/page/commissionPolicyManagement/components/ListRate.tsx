import { Form } from 'antd';
import React, { useEffect } from 'react';
import { ColumnTypes, Editable, EditableRecord, EditColumns } from './editTable';
import { EditableCell } from './editTable/EditableCell';
import { EditableRow } from './editTable/EditableRow';
import { useCommissionPolicyStore } from '../store';
import { v4 as uuid } from 'uuid';

interface ListRateProps<T> {
  nameField: string;
  columns: (ColumnTypes<T>[number] & EditColumns)[];
  dataSource: T[];
  arrayFieldTable: string[];
  setDataSource: React.Dispatch<React.SetStateAction<T[]>>;
}

const ListRate = <T extends EditableRecord>({ nameField, columns, dataSource, setDataSource }: ListRateProps<T>) => {
  const form = Form.useFormInstance();
  const { initialValue } = useCommissionPolicyStore();
  const defaultListRate = initialValue?.listRate;

  useEffect(() => {
    if (defaultListRate) {
      const fieldBonus = defaultListRate;
      if (Array.isArray(fieldBonus) && fieldBonus?.length > 0) {
        setDataSource(fieldBonus as unknown as T[]);
      }
    }
  }, [defaultListRate, nameField, setDataSource]);

  const handleDelete = (key: React.Key) => {
    const newData = dataSource.filter(item => item.key !== key);
    setDataSource(newData);
    form.setFieldsValue({ listRate: newData });
  };

  const handleClone = (record: T) => {
    const newData = {
      ...record,
      key: uuid(),
      isNew: true,
      bottomPrice: dataSource[dataSource?.length - 1]?.topPrice ? dataSource[dataSource?.length - 1]?.topPrice : 0,
      topPrice: 0,
    } as T;
    setDataSource([...dataSource, newData]);
  };

  const handleSave = (row: T) => {
    let newData = [...dataSource];
    const index = newData.findIndex((item: T) => row.key === item.key);
    const item = newData[index];
    newData.splice(index, 1, { ...item, ...row });
    if (newData[index + 1]) newData[index + 1].bottomPrice = newData[index]?.topPrice ? newData[index]?.topPrice : '0';
    if (index === 0)
      newData = newData.map(item => ({
        ...item,
        calculateType: row?.calculateType,
      }));
    setDataSource(newData);
    form.setFieldsValue({
      listRate: newData,
    });
  };

  const components = {
    body: {
      row: EditableRow,
      cell: EditableCell,
    },
  };

  return (
    <>
      <Editable<T>
        columns={columns}
        dataSource={dataSource}
        handleAdd={() => {}}
        handleDelete={handleDelete}
        handleClone={handleClone}
        handleSave={handleSave}
        components={components}
      />
    </>
  );
};

export default ListRate;
