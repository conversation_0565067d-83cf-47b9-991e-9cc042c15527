import { TableColumnsType, Typography } from 'antd';
import dayjs from 'dayjs';
import { FORMAT_DATE_TIME } from '../../../constants/common';
import { ICommissionPolicy, TPos } from '../../../types/commissionPolicy';

const { Text } = Typography;

enum Status {
  ACTIVED = 'Đã kích hoạt',
  UNACTIVED = 'Vô hiệu hóa',
}
interface StatusObject {
  key: string;
  value: string;
}
function getStatusObject(status: string): StatusObject {
  switch (status) {
    case Status.ACTIVED:
      return { key: 'ACTIVED', value: '#389E0D' };
    default:
      return { key: 'UNACTIVED', value: '#CF1322' };
  }
}

export const columns: TableColumnsType = [
  {
    title: 'Mã bộ chỉ tiêu KPI',
    dataIndex: 'code',
    key: 'code',
    width: 160,
    fixed: 'left',
    render: (value: string) =>
      value ? (
        <div className="cell-name">
          <Text>{value}</Text>
        </div>
      ) : (
        '-'
      ),
  },
  {
    title: 'Tên bộ chỉ tiêu K<PERSON>',
    dataIndex: 'name',
    key: 'name',
    width: 140,
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Loại chỉ tiêu',
    dataIndex: 'policyType',
    width: 140,
    key: 'policyType',
    render: (value: string) => {
      return value ? value : '-';
    },
  },
  {
    title: 'Đơn vị bán hàng',
    dataIndex: 'listPos',
    width: 140,
    key: 'listPos',
    render: (value: TPos) => {
      return value ? value?.name : '-';
    },
  },
  {
    title: 'Trạng thái',
    dataIndex: 'isActive',
    width: 200,
    key: 'isActive',
    align: 'center',
    render: (value: number) => {
      const statusObject = getStatusObject(value === 1 ? 'Đã kích hoạt' : 'Vô hiệu hóa');
      return <Text style={{ color: statusObject.value }}>{value === 1 ? 'Đã kích hoạt' : 'Vô hiệu hóa'} </Text>;
    },
  },
  {
    title: 'Ngày tạo',
    dataIndex: 'createdDate',
    key: 'createdDate',
    width: 200,

    render: (value: string, record: ICommissionPolicy) => (
      <>
        <Text>{value ? dayjs(value).format(FORMAT_DATE_TIME) : '-'}</Text>
        <br />
        <Text>{record?.createdBy?.fullName ? record.createdBy.fullName : '-'}</Text>
      </>
    ),
  },
  {
    title: 'Ngày cập nhật',
    dataIndex: 'modifiedDate',
    key: 'modifiedDate',
    width: 200,

    render: (value: string, record: ICommissionPolicy) => (
      <>
        <Text>{value ? dayjs(value).format(FORMAT_DATE_TIME) : '-'}</Text>
        <br />
        <Text>{record?.modifiedBy?.fullName ? record.modifiedBy.fullName : '-'}</Text>
      </>
    ),
  },
];
