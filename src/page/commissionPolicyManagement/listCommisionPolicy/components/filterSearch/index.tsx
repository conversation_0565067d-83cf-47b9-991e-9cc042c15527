import { Col, Form, Row, Select } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { useEffect, useMemo, useState } from 'react';
import { useLocation } from 'react-router-dom';
import DropdownFilterSearch from '../../../../../components/dropdown/dropdownFilterSearch';
import useFilter from '../../../../../hooks/filter';
import './styles.scss';
import { FORMAT_DATE_API, OPTIONS_STATUS_KPI, OPTIONS_TYPE_COMMISSION_POLICY } from '../../../../../constants/common';
import { getListPos } from '../../../../../service/commissionPolicy';
import MultiSelectLazy from '../../../../../components/select/mutilSelectLazy';
import { TEmployeeAll } from '../../../../../types/customers';
import { getEmployeeDropdownById, getListEmployeeAll } from '../../../../../service/customers';
import DatePickerFilter from '../../../../../components/datePicker/DatePickerFilter';
import { useEntitiesById } from '../../../../../hooks';
import { CommissionPolicyType } from '../../../utils';

type TFilter = {
  isActive?: number | null;
  type?: string | null;
  posId?: string;
  startDate?: string | Dayjs | null;
  endDate?: string | Dayjs | null;
  status?: string;
  createdBy?: string;
  share?: string;
};

function FilterSearch() {
  const [isOpenFilter, setIsOpenFilter] = useState(false);
  const [initialValues, setInitialValues] = useState<TFilter>();
  const [filter, setFilter] = useFilter();
  const { search } = useLocation();
  const params = useMemo(() => new URLSearchParams(search), [search]);

  const [form] = Form.useForm();

  const { entities: defaultEmployee } = useEntitiesById({
    apiQuery: getEmployeeDropdownById,
    queryKey: ['employee-dropdown'],
    params: { id: filter?.createdBy },
    labelField: 'name',
    valueField: 'id',
  });

  useEffect(() => {
    const formatData = {
      startDate: params.get('startDate') ? dayjs(params.get('startDate')) : undefined,
      endDate: params.get('endDate') ? dayjs(params.get('endDate')) : undefined,
    };
    setInitialValues(formatData);
    form.setFieldsValue(formatData);
  }, [form, params]);

  const handleSubmitFilter = (values: TFilter) => {
    const newFilter: Record<string, unknown> = {
      type:
        values?.type === CommissionPolicyType.MANAGER
          ? 'Quản lý'
          : values?.type === CommissionPolicyType.PERSONAL
            ? 'Cá nhân'
            : '',
      posId: values?.posId ? values?.posId : null,
      isActive: values?.isActive ? values?.isActive : null,
      startDate: values?.startDate ? dayjs(values?.startDate).format(FORMAT_DATE_API) : null,
      endDate: values?.endDate ? dayjs(values?.endDate).format(FORMAT_DATE_API) : null,
      createdBy: values?.createdBy ? values.createdBy : null,
    };
    setFilter({ ...filter, page: '1', ...newFilter });
    setIsOpenFilter(false);
  };

  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };
  const handleSelectEmployee = (values: TEmployeeAll[]) => {
    form.setFieldsValue({ createdBy: values.map(item => item.id).join(',') });
  };

  const handleSelectPos = (values: TEmployeeAll[]) => {
    form.setFieldsValue({ posId: values.map(item => item.id).join(',') });
  };

  const handleClearFilters = () => {
    setInitialValues(undefined);
    setIsOpenFilter(false);
  };
  return (
    <>
      <DropdownFilterSearch
        rootClassName="wrapper-filter-customers"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        showParams={true}
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        initialValues={initialValues}
        form={form}
        keySearch="searchText"
        onClearFilters={handleClearFilters}
        extraFormItems={
          <>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="Loại chỉ tiêu" name="type">
                  <Select placeholder="Chọn loại chỉ tiêu" allowClear options={OPTIONS_TYPE_COMMISSION_POLICY} />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item label="Đơn vị bán hàng" name="posId">
                  <MultiSelectLazy
                    enabled={isOpenFilter}
                    apiQuery={getListPos}
                    queryKey={['pos-dropdown']}
                    keysLabel={['name']}
                    handleListSelect={handleSelectPos}
                    placeholder="Chọn đơn vị bán hàng"
                    keysTag={'name'}
                  />
                </Form.Item>{' '}
              </Col>
              <Col span={24}>
                <Form.Item label="Người tạo" name="createdBy">
                  <MultiSelectLazy
                    enabled={isOpenFilter}
                    apiQuery={getListEmployeeAll}
                    queryKey={['employee-dropdown']}
                    keysLabel={['name', 'email']}
                    handleListSelect={handleSelectEmployee}
                    defaultValues={defaultEmployee}
                    placeholder="Chọn nhân viên"
                    keysTag={'email'}
                  />
                </Form.Item>{' '}
              </Col>
              <Col span={24}>
                <Form.Item label="Trạng thái" name="isActive">
                  <Select placeholder="Chọn trạng thái" allowClear options={OPTIONS_STATUS_KPI} />
                </Form.Item>
              </Col>
              <Col span={24}>
                <DatePickerFilter startDate="startDate" endDate="endDate" />
              </Col>
            </Row>
          </>
        }
      />
    </>
  );
}

export default FilterSearch;
