import { TableColumnsType, Typography } from 'antd';
import dayjs from 'dayjs';
import { CUSTOMER_BUSINESS, CUSTOMER_PERSONAL } from '../../../configs/path';
import { FORMAT_DATE_TIME } from '../../../constants/common';
import { ICustomers, TDataDuplicate } from '../../../types/customers';

const { Text } = Typography;
export const columns: TableColumnsType = [
  {
    title: 'Khách hàng',
    dataIndex: 'companyName',
    key: 'companyName',
    width: 160,
    fixed: 'left',
    render: (value: string) => (value ? <Text>{value}</Text> : '-'),
  },
  {
    title: 'Mã số thuế',
    dataIndex: 'taxCode',
    key: 'taxCode',
    width: 140,
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: '<PERSON><PERSON> khách hàng',
    dataIndex: 'code',
    width: 140,
    key: 'code',
    render: (value: string) => (value ? value : '-'),
  },
  // {
  //   title: 'Mã Lead',
  //   dataIndex: 'leadCode',
  //   width: 140,
  //   key: 'lead-code',
  //   render: (value: string) => (value ? value : '-'),
  // },
  {
    title: 'Số điện thoại',
    dataIndex: 'phone',
    width: 140,
    key: 'phone',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Email',
    dataIndex: 'email',
    width: 200,
    key: 'email',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Trạng thái hoạt động',
    dataIndex: 'isActive',
    width: 120,
    align: 'center',
    key: 'isActive',
    render: (value: number) =>
      value === 1 ? (
        <Text style={{ color: '#389E0D' }}>Đã kích hoạt</Text>
      ) : (
        <Text style={{ color: '#CF1322' }}>Vô hiệu hóa</Text>
      ),
  },
  {
    title: 'Trạng thái KHTN',
    dataIndex: 'status',
    width: 120,
    align: 'center',
    key: 'status',
    render: (value: string) => {
      switch (value) {
        case 'CUST_DONE':
          return <Text style={{ color: '#1677FF' }}>KHCT</Text>;
        case 'NORMAL':
          return <Text>Mới</Text>;
        case 'PENDING_CUST':
          return <Text style={{ color: '#FFA940' }}>Đang giao dịch</Text>;
        default:
          return '-';
      }
    },
  },
  // {
  //   title: 'NVKD được chia sẻ',
  //   dataIndex: 'share',
  //   width: 180,
  //   key: 'share',
  //   render: (value: TShare) =>
  //     value?.emails?.length > 0 ? value?.emails?.map(value => <Tag key={value}>{value}</Tag>) : '-',
  // },
  {
    title: 'Ngày tạo',
    dataIndex: 'createdDate',
    key: 'createdDate',
    width: 200,

    render: (value: string, record: ICustomers) => (
      <>
        <Text>{dayjs(value).format(FORMAT_DATE_TIME)}</Text>
        <br />
        <Text>{record?.createdByObj?.fullName ? record.createdByObj.fullName : '-'}</Text>
      </>
    ),
  },
  {
    title: 'Ngày cập nhật',
    dataIndex: 'updatedDate',
    key: 'updatedDate',
    width: 200,

    render: (value: string, record: ICustomers) => (
      <>
        <Text>{dayjs(value).format(FORMAT_DATE_TIME)}</Text> <br />
        <Text>{record?.updatedByObj?.fullName ? record.updatedByObj.fullName : '-'}</Text>
      </>
    ),
  },
];

export const columnsDuplicate: TableColumnsType = [
  {
    title: 'Người đại diện',
    dataIndex: 'name',
    key: 'name',
    fixed: 'left',
    render: (value: string, record: TDataDuplicate) => {
      const url = record?.type === 'individual' ? CUSTOMER_PERSONAL : CUSTOMER_BUSINESS;
      return value ? (
        <a href={`${url}/${record?.id}`} target="_blank" rel="noopener noreferrer">
          {value}
        </a>
      ) : (
        '-'
      );
    },
  },
  {
    title: 'Mã khách hàng',
    dataIndex: 'code',
    key: 'code',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Số giấy tờ',
    dataIndex: 'taxCode',
    key: 'taxCode',
    render: (value: string, record: TDataDuplicate) =>
      value ? value : record?.identities?.length > 0 ? record?.identities?.join(', ') : '-',
  },
  {
    title: 'Loại khách hàng',
    dataIndex: 'type',
    key: 'type',
    render: (value: string) => (value === 'individual' ? 'Cá nhân' : 'Doanh nghiệp'),
  },
  {
    title: 'Email',
    dataIndex: 'email',
    key: 'email',
    render: (value: string) => (value ? value : '-'),
  },
];
