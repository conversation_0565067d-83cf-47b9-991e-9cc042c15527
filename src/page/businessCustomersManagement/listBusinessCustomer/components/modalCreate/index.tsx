import { Button, Col, Form, Input, Modal, Row } from 'antd';
import React, { useState } from 'react';
import { REGEX_PHONE_VN } from '../../../../../constants/common';
import { useCreateField } from '../../../../../hooks';
import { postCreateCustomer } from '../../../../../service/customers';
import { handleErrors } from '../../../../../service/error/errorsService';
import { IModalCreateCustomer, TCreateCustomer, TDataDuplicate } from '../../../../../types/customers';
import { handleKeyDownEnterNumber } from '../../../../../utilities/regex';
import ModalCheckDuplicate from '../modalCheckDuplicate';
import './styles.scss';
import { showConfirmCancelModal } from '../../../../../components/modal/specials/ConfirmCancelModal';

const { Item } = Form;

const ModalCreateCustomer = (props: IModalCreateCustomer) => {
  const { isOpen, handleCancel } = props;
  const [form] = Form.useForm();

  const [isOpenCheckDuplicate, setIsOpenCheckDuplicate] = useState(false);
  const [dataSubmit, setDataSubmit] = useState<TCreateCustomer>();
  const [dataDuplicate, setDataDuplicate] = useState<TDataDuplicate[]>();

  const { mutateAsync: createCustomer } = useCreateField<TCreateCustomer>({
    apiQuery: postCreateCustomer,
    keyOfListQuery: ['get-customers-business'],
    checkDuplicate: true,
    isMessageError: false,
  });

  const handleSubmit = async (values: TCreateCustomer) => {
    const newData: TCreateCustomer = {
      ...values,
      type: 'business',
      continueCreate: false,
    };

    if (dataSubmit?.phone === values.phone) {
      await createCustomer({ ...newData, continueCreate: true });
      removeDataSubmit();
      handleCancel();
    } else {
      const res = await createCustomer(newData);
      const responseData = res?.data;
      if (responseData) {
        switch (responseData.statusCode) {
          case '0':
            handleCancel();
            removeDataSubmit();
            break;
          case 'DCUSE0001':
            setIsOpenCheckDuplicate(true);
            setDataSubmit(newData as TCreateCustomer);
            setDataDuplicate(responseData.data as TDataDuplicate[]);
            break;
          default:
            handleErrors(responseData);
        }
      }
    }
  };
  const handleCancelCheckDuplicate = () => {
    setIsOpenCheckDuplicate(false);
  };

  const removeDataSubmit = () => {
    setDataSubmit(undefined);
  };

  return (
    <>
      <Modal
        rootClassName="wrapper-modal-create-customer"
        title="Tạo mới khách hàng tiềm năng doanh nghiệp"
        open={isOpen}
        maskClosable={false}
        centered
        onCancel={() => {
          if (!form.isFieldsTouched()) {
            handleCancel();
            removeDataSubmit();
            return;
          }
          showConfirmCancelModal({
            onConfirm: () => {
              handleCancel();
              removeDataSubmit();
            },
          });
        }}
        destroyOnClose
        zIndex={1000}
        afterClose={() => form.resetFields()}
        footer={
          <Button type="primary" htmlType="submit" onClick={() => form.submit()}>
            Tạo mới
          </Button>
        }
      >
        <Form form={form} layout="vertical" onFinish={handleSubmit}>
          <Row gutter={16} className="content-header-form">
            <Col span={12}>
              <Item
                name={'companyName'}
                label="Tên Công ty"
                required={false}
                rules={[{ required: true, message: 'Vui lòng nhập tên Công ty', whitespace: true }]}
              >
                <Input
                  maxLength={120}
                  placeholder="Nhập Tên Công ty"
                  onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                    e.target.value = e.target.value.toUpperCase();
                  }}
                />
              </Item>
            </Col>
            <Col span={12}>
              <Item
                name={'companyShortName'}
                label="Tên ngắn"
                required={false}
                rules={[{ required: true, message: 'Vui lòng nhập tên ngắn', whitespace: true }]}
              >
                <Input maxLength={25} placeholder="Nhập Tên ngắn" />
              </Item>
            </Col>
            <Col span={12}>
              <Item
                name={'phone'}
                required={false}
                label="Số điện thoại"
                rules={[
                  { required: true, message: 'Vui lòng nhập số điện thoại' },
                  {
                    pattern: REGEX_PHONE_VN,
                    message: 'Số điện thoại phải bắt đầu bằng số 0 và từ 9 đến 15 chữ số',
                  },
                ]}
              >
                <Input maxLength={15} onKeyDown={handleKeyDownEnterNumber} placeholder="Nhập số điện thoại" />
              </Item>
            </Col>
            <Col span={12}>
              <Item
                name={'name'}
                required={false}
                label="Người đại diện"
                rules={[{ required: true, message: 'Vui lòng nhập người đại diện', whitespace: true }]}
              >
                <Input
                  maxLength={60}
                  placeholder=" Nhập tên người đại điện"
                  onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                    e.target.value = e.target.value.toUpperCase();
                  }}
                />
              </Item>
            </Col>
          </Row>
        </Form>
      </Modal>
      <ModalCheckDuplicate
        isOpen={isOpenCheckDuplicate}
        handleCancelCheckDuplicate={handleCancelCheckDuplicate}
        handleCancel={handleCancel}
        dataDuplicate={dataDuplicate}
        dataSubmit={dataSubmit as TCreateCustomer}
        removeDataSubmit={removeDataSubmit}
        type="create"
      />
    </>
  );
};

export default ModalCreateCustomer;
