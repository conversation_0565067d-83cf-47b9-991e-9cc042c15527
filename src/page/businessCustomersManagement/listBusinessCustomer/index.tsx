import { App, Button, Flex, TableColumnsType } from 'antd';
import { useMemo, useState } from 'react';
import BreadCrumbComponent from '../../../components/breadCrumb';
import ConfirmDeleteModal from '../../../components/modal/specials/ConfirmDeleteModal';
import { modalConfirm } from '../../../components/modal/specials/ModalConfirm';
import TableComponent from '../../../components/table';
import { ActionsColumns } from '../../../components/table/components/ActionsColumns';
import { CUSTOMER_BUSINESS } from '../../../configs/path';
import { useCheckPermissions, useFetch, useUpdateField } from '../../../hooks';

import { MutationFunction } from '@tanstack/react-query';
import { PERMISSION_DEMAND_CUSTOMER } from '../../../constants/permissions/demand';
import { changeStatusCustomer, getListCustomerBusiness, softDeleteCustomer } from '../../../service/customers';
import { ICustomers } from '../../../types/customers';
import DropdownDownloadAndImport from '../../personalCustomersManagement/listPersonalCustomers/components/dropdownDownloadAndImport';
import FilterSearch from '../../personalCustomersManagement/listPersonalCustomers/components/filterSearch';
import { columns } from './columns';
import ModalCreateCustomer from './components/modalCreate';
import ModalShareCare from './components/modalShareCare/ModalShareCare';
import './styles.scss';

function ListBusinessCustomers() {
  const { modal } = App.useApp();
  // const [selectedRowKeys, setSelectedRowKeys] = useState<Key[]>([]);
  const [isOpenModalShare, setIsOpenModalShare] = useState(false);
  const [isOpenModalDelete, setIsOpenModalDelete] = useState<boolean>(false);
  const [currentRecord, setCurrentRecord] = useState<ICustomers>();
  const [isOpenModalCreate, setIsOpenModalCreate] = useState(false);
  const { customerCreate, customerDelete, customerUpdate, customerChangeStatus, customerGetById } =
    useCheckPermissions(PERMISSION_DEMAND_CUSTOMER);

  const {
    data: listCustomer,
    isLoading,
    isPlaceholderData,
    isFetching,
  } = useFetch<ICustomers[]>({
    queryKeyArrWithFilter: ['get-customers-business'],
    api: getListCustomerBusiness,
  });

  const { mutateAsync: updateActive } = useUpdateField({
    apiQuery: changeStatusCustomer,
    keyOfListQuery: ['get-customers-business'],
    isMessageError: false,
    isMessageSuccess: false,
  });

  const columnActions: TableColumnsType<ICustomers> = useMemo(() => {
    return [
      ...columns,
      {
        title: 'Hành động',
        dataIndex: 'action',
        key: 'action',
        width: '100px',
        align: 'center',
        fixed: 'right',
        render: (_: unknown, record: ICustomers) => {
          const textModalConfirmActive = record?.isActive === 1 ? 'Vô hiệu hoá' : 'Kích hoạt';
          const openViewDetail = () => {
            window.open(`${CUSTOMER_BUSINESS}/${record?.id}`, '_blank', 'noopener noreferrer');
          };
          const handleActiveCustomer = () => {
            return modalConfirm({
              modal: modal,
              title: `${textModalConfirmActive} khách hàng`,
              content: `Bạn có muốn ${textModalConfirmActive} khách hàng này không?`,
              handleConfirm: async () => {
                await updateActive({ id: record?.id ?? '', isActive: record?.isActive === 1 ? 2 : 1 });
              },
            });
          };
          const handleDeleteCustomer = () => {
            setIsOpenModalDelete(true);
            setCurrentRecord(record);
          };

          return (
            <ActionsColumns
              handleViewDetail={customerGetById || customerUpdate ? openViewDetail : undefined}
              textModalConfirmActive={textModalConfirmActive}
              handleActive={customerChangeStatus ? handleActiveCustomer : undefined}
              handleDelete={customerDelete ? handleDeleteCustomer : undefined}
            />
          );
        },
      },
    ];
  }, [customerChangeStatus, customerDelete, customerGetById, customerUpdate, modal, updateActive]);

  // const onSelectChange = (selectedRowKeys: Key[]) => {
  //   setSelectedRowKeys(selectedRowKeys);
  // };

  // const rowSelection: TableRowSelection<ICustomers> = {
  //   selectedRowKeys,
  //   onChange: onSelectChange,
  // };

  // const handleOpenModalShare = () => {
  //   setIsOpenModalShare(true);
  // };

  const handleCancelModalShare = () => {
    setIsOpenModalShare(false);
  };

  const handleOpenModalCreate = () => {
    setIsOpenModalCreate(true);
  };

  const handleCancelModalCreate = () => {
    setIsOpenModalCreate(false);
  };

  return (
    <div className="wrapper-list-business-customers">
      <BreadCrumbComponent />
      <div className="header-content">
        <Flex gap={17}>
          <FilterSearch />
          {/* {selectedRowKeys.length > 0 && customerGetAll && customerUpdate && (
            <Button onClick={handleOpenModalShare}>
              <ShareAltOutlined /> Chia sẻ
            </Button>
          )} */}
        </Flex>
        <Flex gap={10}>
          <DropdownDownloadAndImport isBusiness />
          {customerCreate && (
            <Button type="primary" onClick={handleOpenModalCreate}>
              Thêm mới
            </Button>
          )}
        </Flex>
      </div>
      <div className="table-business-customers">
        <TableComponent
          queryKeyArr={['get-customers-business']}
          columns={columnActions}
          // rowSelection={rowSelection}
          loading={isLoading || isPlaceholderData || isFetching}
          dataSource={listCustomer?.data?.data?.rows ?? []}
          rowKey="id"
        />
      </div>
      <ModalCreateCustomer isOpen={isOpenModalCreate} handleCancel={handleCancelModalCreate} />
      <ModalShareCare
        onClose={handleCancelModalShare}
        isOpen={isOpenModalShare}
        onSubmitted={() => console.log('first')}
      />
      <ConfirmDeleteModal
        label="khách hàng"
        open={isOpenModalDelete}
        apiQuery={softDeleteCustomer as MutationFunction<unknown, unknown>}
        keyOfListQuery={['get-customers-business']}
        onCancel={() => setIsOpenModalDelete(false)}
        idDetail={currentRecord?.id}
        title="Xoá khách hàng"
        description="Vui lòng nhập lý do muốn xoá khách hàng này"
      />
    </div>
  );
}

export default ListBusinessCustomers;
