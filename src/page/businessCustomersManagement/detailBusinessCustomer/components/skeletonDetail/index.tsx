import { Checkbox, Col, Form, Row, Skeleton, Typography } from 'antd';

const { Item } = Form;
const { Title } = Typography;
const { Input } = Skeleton;

const SkeletonDetail = () => {
  return (
    <Form layout="vertical">
      <Row gutter={{ md: 24, lg: 40, xl: 80, xxl: 126 }}>
        <Col span={24}></Col>
        <Col xs={24} md={12}>
          <Title level={5}>Thông tin chung</Title>
          <Item label="Mã số khách hàng" required>
            <Input active block />
          </Item>
          <Row gutter={24}>
            <Col xs={24} sm={12}>
              <Item label="Tên công ty" required>
                <Input active block />
              </Item>
            </Col>
            <Col xs={24} sm={12}>
              <Item label="Tên ngắn">
                <Input active block />
              </Item>
            </Col>
            <Col xs={24} sm={12}>
              <Item required label="Số điện thoại">
                <Input active block />
              </Item>
            </Col>
            <Col xs={24} sm={12}>
              <Item label="Mã số thuế">
                <Input active block />
              </Item>
            </Col>
            <Col xs={24} sm={12} className="item-issueDate">
              <Item label="Ngày cấp mã số thuế">
                <Input active block />
              </Item>
            </Col>
            <Col span={24}>
              <Item label="Nơi cấp mã số thuế">
                <Input active block />
              </Item>
            </Col>
            <Col xs={24} sm={12}>
              <Item label="Người Đại diện" required>
                <Input active block />
              </Item>
            </Col>
            <Col xs={24} sm={12}>
              <Item label="Chức vụ">
                <Input active block />
              </Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col xs={24} sm={12}>
              <Item label="Ngày sinh" layout="horizontal" labelCol={{ span: 12 }} labelAlign="left">
                <Checkbox>Chỉ năm sinh</Checkbox>
              </Item>
            </Col>
            <Col xs={24} sm={12} className="item-birthday">
              <Input active block />
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={24}>
              <Item label="Địa chỉ email">
                <Input active block />
              </Item>
            </Col>
            <Col xs={24} sm={12}>
              <Item label="Loại giấy tờ">
                <Input active block />
              </Item>
            </Col>
            <Col xs={24} sm={12}>
              <Item label="Số giấy tờ">
                <Input active block />
              </Item>
            </Col>

            <Col xs={24} sm={12}>
              <Item label="Ngày cấp giấy tờ">
                <Input active block />
              </Item>
            </Col>
            <Col xs={24} sm={12}>
              <Item label="Nơi cấp giấy tờ">
                <Input active block />
              </Item>
            </Col>
          </Row>

          <Title level={5}>Thông tin tài khoản</Title>
          <Row gutter={24}>
            <Col xs={24} sm={12}>
              <Item label="Ngân hàng">
                <Input active block />
              </Item>
            </Col>
            <Col xs={24} sm={12}>
              <Item label="Số tài khoản">
                <Input active block />
              </Item>
            </Col>

            <Col xs={24} sm={12}>
              <Item label="Ngành nghề">
                <Input active block />
              </Item>
            </Col>
            <Col xs={24} sm={12}>
              <Item label="Nguồn thu nhập">
                <Input active block />
              </Item>
            </Col>
            <Col span={24}>
              <Item label="Thu nhập / tháng (VNĐ)">
                <Input active block />
              </Item>
            </Col>
          </Row>
          <Item label="Chia sẻ thông tin với người khác">
            <Input active block />
          </Item>

          <Title level={5}>Địa chỉ thường trú người đại diện</Title>
          <Item label="Địa chỉ">
            <Input active block />
          </Item>
          <Item>
            <Input active block />
          </Item>

          <Title level={5}>Địa chỉ liên lạc người đại diện</Title>
          <Item>
            <Checkbox>Sử dụng địa chỉ thường trú</Checkbox>
          </Item>
          <Item label="Địa chỉ">
            <Input active block />
          </Item>
          <Item>
            <Input active block />
          </Item>
        </Col>
        <Col xs={24} md={12}>
          <Title level={5} style={{ marginBottom: '35px' }}>
            Địa chỉ công ty
          </Title>
          <Item label="Địa chỉ">
            <Input active block />
          </Item>
          <Item>
            <Input active block />
          </Item>
          <Title level={5}>Ghi chú</Title>
          <Skeleton active paragraph={{ rows: 5 }} />
        </Col>
      </Row>
    </Form>
  );
};

export default SkeletonDetail;
