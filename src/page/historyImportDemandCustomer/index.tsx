import { EllipsisOutlined, LoadingOutlined } from '@ant-design/icons';
import { TableColumnsType, Typography } from 'antd';
import dayjs from 'dayjs';
import BreadCrumbComponent from '../../components/breadCrumb';
import TableComponent from '../../components/table';
import { FORMAT_DATE_TIME, OPTIONS_STATUS_IMPORT_CUSTOMERS } from '../../constants/common';
import { useFetch } from '../../hooks';
import { getListHistoryImportDemandCustomer } from '../../service/customers';
import { TListHistoryImportDemandCustomer } from '../../types/customers';
import FilterSearchHistory from './FilterSearchHistory';
import './styles.scss';

const { Text } = Typography;

const HistoryImportDemandCustomer = () => {
  const { data, isFetching } = useFetch<TListHistoryImportDemandCustomer[]>({
    api: getListHistoryImportDemandCustomer,
    queryKeyArrWithFilter: ['list-history-import-demand-customer'],
  });
  const dataSources = data?.data?.data?.rows || [];

  const columns: TableColumnsType = [
    {
      title: 'Ngày tải lên',
      dataIndex: 'createdDate',
      key: 'createdDate',
      width: '20%',
      render: (value: string, record: TListHistoryImportDemandCustomer) =>
        value || record?.processBy
          ? `${dayjs(value).format(FORMAT_DATE_TIME)} - ${record?.processByObj?.fullName || ''}`
          : '-',
    },
    {
      title: 'Loại khách hàng',
      dataIndex: 'type',
      width: '20%',
      key: 'type',
      render: (value: string) =>
        value ? (value === 'DEMAND_CUSTOMER_INVIDIUAL' ? 'Khách hàng cá nhân' : 'Khách hàng doanh nghiệp') : '-',
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      width: '15%',
      align: 'center',
      render: (value: string) => {
        const itemStatus = OPTIONS_STATUS_IMPORT_CUSTOMERS.find(item => item.value === value);
        return value ? <Text style={{ color: itemStatus?.color }}>{itemStatus?.label}</Text> : '-';
      },
    },
    {
      title: 'KHTN upload thành công',
      dataIndex: 'success',
      key: 'success',
      width: '25%',
      align: 'center',
      render: (value: number, record: TListHistoryImportDemandCustomer) => {
        if (record?.status === 'PENDING' || record?.status === 'PROCESSING') {
          return <EllipsisOutlined />;
        }
        return typeof value === 'number' ? `${value}/${record?.fail + record?.success}` : '-';
      },
    },
    {
      title: 'Log file',
      dataIndex: 'failedFileUrl',
      key: 'failedFileUrl',
      width: '35%',
      render: (value: string, record: TListHistoryImportDemandCustomer) => {
        if (record?.status === 'PENDING' || record?.status === 'PROCESSING') {
          return <LoadingOutlined style={{ color: '#1677FF', fontSize: 24 }} />;
        }
        return (
          <Typography.Link download={record?.failedFileName} href={value} target="_blank">
            {record?.failedFileName}
          </Typography.Link>
        );
      },
    },
  ];

  return (
    <div className="wrapper-list-history-import-demand">
      <BreadCrumbComponent />
      <FilterSearchHistory />
      <TableComponent
        queryKeyArr={['list-history-import-demand-customer']}
        dataSource={dataSources}
        columns={columns}
        rowKey="id"
        loading={isFetching}
      />
    </div>
  );
};

export default HistoryImportDemandCustomer;
