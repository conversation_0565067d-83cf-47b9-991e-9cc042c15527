import { Col, DatePicker, Form, Row, Select } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useMemo, useState } from 'react';
import { useLocation } from 'react-router-dom';
import DropdownFilterSearch from '../../components/dropdown/dropdownFilterSearch';
import MultiSelectLazy from '../../components/select/mutilSelectLazy';
import {
  FORMAT_DATE,
  FORMAT_DATE_API,
  OPTIONS_STATUS_IMPORT_CUSTOMERS,
  TYPE_DEMAND_CUSTOMERS,
} from '../../constants/common';
import useFilter from '../../hooks/filter';
import { getListEmployeeInternal } from '../../service/employee';
import { TEmployeeAll, TFilterHistoryCustomer } from '../../types/customers';
import { useEntitiesById } from '../../hooks';
import { getEmployeeDropdownById } from '../../service/customers';

const FilterSearchHistory = () => {
  const { search } = useLocation();
  const [form] = Form.useForm();
  const params = useMemo(() => new URLSearchParams(search), [search]);
  const [filter, setFilter] = useFilter();
  const [initialValues, setInitialValues] = useState<TFilterHistoryCustomer>();
  const [isOpenFilter, setIsOpenFilter] = useState(false);

  const { entities: defaultEmployee } = useEntitiesById({
    apiQuery: getEmployeeDropdownById,
    queryKey: ['employee-dropdown'],
    params: { id: filter?.createdBy },
    labelField: 'name',
    valueField: 'id',
  });

  useEffect(() => {
    if (params) {
      const newInitialValues = {
        createdBy: params.get('createdBy') || undefined,
        startCreatedDate: params.get('startCreatedDate') ? dayjs(params.get('startCreatedDate')) : undefined,
        endCreatedDate: params.get('endCreatedDate') ? dayjs(params.get('endCreatedDate')) : undefined,
        status: params.get('status') || undefined,
        type: params.get('type') || undefined,
      };
      form.setFieldsValue(newInitialValues);
      setInitialValues(newInitialValues);
    }
  }, [form, params]);

  const handleSubmitFilter = (values: TFilterHistoryCustomer) => {
    const newFilter: Record<string, unknown> = {
      createdBy: values?.createdBy ? values.createdBy : null,
      startCreatedDate: values?.startCreatedDate ? dayjs(values?.startCreatedDate).format(FORMAT_DATE_API) : null,
      endCreatedDate: values?.endCreatedDate ? dayjs(values?.endCreatedDate).format(FORMAT_DATE_API) : null,
      status: values?.status || null,
      type: values?.type || null,
    };
    setFilter({ ...filter, page: '1', ...newFilter });
    setIsOpenFilter(false);
  };

  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };

  const handleSelect = (values: TEmployeeAll[]) => {
    form.setFieldsValue({ createdBy: values.map(item => item.id).join(',') });
  };

  const handleClearFilters = () => {
    setInitialValues(undefined);
    setTimeout(() => {
      setIsOpenFilter(false);
    }, 100);
  };

  return (
    <>
      <DropdownFilterSearch
        rootClassName="wrapper-filter"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        showParams={true}
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        initialValues={initialValues}
        form={form}
        onClearFilters={handleClearFilters}
        extraFormItems={
          <>
            <Form.Item label="Người tải lên" name="createdBy">
              <MultiSelectLazy
                enabled={isOpenFilter}
                apiQuery={getListEmployeeInternal}
                queryKey={['employee']}
                keysTag={'email'}
                keysLabel={['name', 'email']}
                placeholder="Chọn người tải lên"
                handleListSelect={handleSelect}
                defaultValues={defaultEmployee}
              />
            </Form.Item>
            <Form.Item label="Loại khách hàng" name="type">
              <Select placeholder="Chọn loại khách hàng" allowClear options={TYPE_DEMAND_CUSTOMERS} />
            </Form.Item>
            <Form.Item label="Trạng thái" name="status">
              <Select placeholder="Chọn trạng thái" allowClear options={OPTIONS_STATUS_IMPORT_CUSTOMERS} />
            </Form.Item>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="Từ ngày" name="startCreatedDate">
                  <DatePicker
                    format={FORMAT_DATE}
                    disabledDate={current => {
                      const endCreatedDate = form.getFieldValue('endCreatedDate');
                      return (
                        (current && current > dayjs().startOf('day')) ||
                        (endCreatedDate && current > dayjs(endCreatedDate).startOf('day')) // Disable ngày sau "Đến ngày"
                      );
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Đến ngày" name="endCreatedDate">
                  <DatePicker
                    format={FORMAT_DATE}
                    disabledDate={current => {
                      const startCreatedDate = form.getFieldValue('startCreatedDate');
                      return (
                        (current && current > dayjs().startOf('day')) ||
                        (startCreatedDate && current < dayjs(startCreatedDate).startOf('day')) // Disable ngày trước "Ngày tạo"
                      );
                    }}
                  />
                </Form.Item>
              </Col>
            </Row>
          </>
        }
      />
    </>
  );
};

export default FilterSearchHistory;
