import { EllipsisOutlined, LoadingOutlined } from '@ant-design/icons';
import { TableColumnsType, Typography } from 'antd';
import dayjs from 'dayjs';
import BreadCrumbComponent from '../../components/breadCrumb';
import TableComponent from '../../components/table';
import { FORMAT_DATE_TIME, OPTIONS_STATUS_IMPORT_CUSTOMERS } from '../../constants/common';
import './styles.scss';
import { useFetch } from '../../hooks';
import { getListHistoryImportLeads } from '../../service/lead';
import { TListHistoryImportLeads } from '../../types/lead';
import FilterSearchHistoryLead from './FilterSearchHistoryLead';

const { Text } = Typography;

const HistoryImportLeads = () => {
  const { data, isFetching } = useFetch<TListHistoryImportLeads[]>({
    api: getListHistoryImportLeads,
    queryKeyArrWithFilter: ['list-history-import-leads'],
  });
  const dataSources = data?.data?.data?.rows || [];

  const columns: TableColumnsType = [
    {
      title: 'Ngày tải lên',
      dataIndex: 'createdDate',
      key: 'createdDate',
      width: '20%',
      render: (value: string, record: TListHistoryImportLeads) =>
        value || record?.processBy ? `${dayjs(value).format(FORMAT_DATE_TIME)} - ${record?.processBy?.fullName}` : '-',
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      width: '15%',
      align: 'center',
      render: (value: string) => {
        const itemStatus = OPTIONS_STATUS_IMPORT_CUSTOMERS.find(item => item.value === value);
        return value ? <Text style={{ color: itemStatus?.color }}>{itemStatus?.label}</Text> : '-';
      },
    },
    {
      title: 'Lead upload thành công',
      dataIndex: 'success',
      key: 'success',
      width: '25%',
      align: 'center',
      render: (value: number, record: TListHistoryImportLeads) => {
        if (record?.status === 'PENDING' || record?.status === 'PROCESSING') {
          return <EllipsisOutlined />;
        }
        return typeof value === 'number' ? `${value}/${record?.fail + record?.success}` : '-';
      },
    },
    {
      title: 'Log file',
      dataIndex: 'failedFileUrl',
      key: 'failedFileUrl',
      width: '35%',
      render: (value: string, record: TListHistoryImportLeads) => {
        if (record?.status === 'PENDING' || record?.status === 'PROCESSING') {
          return <LoadingOutlined style={{ color: '#1677FF', fontSize: 24 }} />;
        }
        return (
          <Typography.Link download={record?.failedFileName} href={value} target="_blank">
            {record?.failedFileName}
          </Typography.Link>
        );
      },
    },
  ];

  return (
    <div className="wrapper-list-history-import-leads">
      <BreadCrumbComponent />
      <FilterSearchHistoryLead />
      <TableComponent
        loading={isFetching}
        queryKeyArr={['list-history-import-leads']}
        dataSource={dataSources}
        columns={columns}
        rowKey="id"
      />
    </div>
  );
};

export default HistoryImportLeads;
