import { Form, Select } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { useEffect, useMemo, useState } from 'react';
import { useLocation } from 'react-router-dom';
import DatePickerFilter from '../../../../../components/datePicker/DatePickerFilter';
import DropdownFilterSearch from '../../../../../components/dropdown/dropdownFilterSearch';
import SingleSelectLazy from '../../../../../components/select/singleSelectLazy';
import { FORMAT_DATE_API, TYPE_LEAD_SOURCE } from '../../../../../constants/common';
import { useFetch } from '../../../../../hooks';
import useFilter from '../../../../../hooks/filter';
import { getDetailOfLeadSource, getListLeadSource } from '../../../../../service/leadSource';
import { ILead } from '../../../../../types/leadToDeliver';
import './styles.scss';

type TFilter = {
  isActive?: number | null;
  createdFrom?: string | Dayjs | null;
  createdTo?: string | Dayjs | null;
  isHot?: string;
  createdBy?: string;
  share?: string;
  source?: string;
};

function FilterSearch() {
  const [form] = Form.useForm();
  const [isOpenFilter, setIsOpenFilter] = useState(false);
  const [initialValues, setInitialValues] = useState<TFilter>();
  const [defaultSource, setDefaultSource] = useState<
    { label: string | React.ReactElement; value: string } | undefined
  >();
  const [filter, setFilter] = useFilter();
  const { search } = useLocation();
  const params = useMemo(() => new URLSearchParams(search), [search]);

  const { data: source } = useFetch<ILead>({
    api: () => getDetailOfLeadSource({ name: params.get('source') }),
    queryKeyArr: ['source-filter'],
    withFilter: false,
    enabled: !!isOpenFilter,
  });

  const dataSource = source?.data?.data;
  useEffect(() => {
    if (dataSource?.name && dataSource?.id) {
      setDefaultSource({ label: dataSource.name, value: dataSource.id });
    }
  }, [dataSource]);

  useEffect(() => {
    const initialValue = {
      createdFrom: params.get('createdFrom') ? dayjs(params.get('createdFrom')) : undefined,
      createdTo: params.get('createdTo') ? dayjs(params.get('createdTo')) : undefined,
      isHot: params.get('isHot') || undefined,
      exploitStatus: params.get('exploitStatus') || undefined,
      source: params.get('source') || undefined,
    };
    setInitialValues(initialValue);
    form.setFieldsValue(initialValue);
  }, [form, params]);

  const handleSubmitFilter = (values: TFilter) => {
    const newFilter: Record<string, unknown> = {
      isHot: values.isHot || null,
      isActive: values.isActive || null,
      createdFrom: values?.createdFrom ? dayjs(values?.createdFrom).format(FORMAT_DATE_API) : null,
      createdTo: values?.createdTo ? dayjs(values?.createdTo).format(FORMAT_DATE_API) : null,
      source: values?.source || null,
    };
    setFilter({ ...filter, page: '1', ...newFilter });
    setIsOpenFilter(false);
  };

  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };

  const handleSelectLeadSource = (value: ILead) => {
    form.setFieldsValue({ source: value?.name });
  };

  const handleClearFilters = () => {
    setInitialValues(undefined);
    setDefaultSource(undefined);
    setTimeout(() => {
      isOpenFilter && setIsOpenFilter(false);
    }, 100);
  };

  return (
    <>
      <DropdownFilterSearch
        rootClassName="wrapper-filter-customers"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        showParams={true}
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        initialValues={initialValues}
        onClearFilters={handleClearFilters}
        form={form}
        extraFormItems={
          <>
            <Form.Item label="Nguồn Lead" name="source">
              <SingleSelectLazy
                apiQuery={getListLeadSource}
                queryKey={['list-source']}
                enabled={isOpenFilter}
                placeholder="Chọn nguồn lead"
                keysLabel={['name']}
                handleSelect={handleSelectLeadSource}
                defaultValues={defaultSource}
              />
            </Form.Item>
            <Form.Item label="Loại Lead" name="isHot">
              <Select placeholder="Chọn loại Lead" allowClear options={TYPE_LEAD_SOURCE} />
            </Form.Item>
            <DatePickerFilter startDate="createdFrom" endDate="createdTo" />
          </>
        }
      />
    </>
  );
}

export default FilterSearch;
