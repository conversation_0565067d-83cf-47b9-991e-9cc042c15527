import { Button, Modal, Radio, Select } from 'antd';
import React from 'react';
import { useFetch, useUpdateField } from '../../../../../hooks';
import { getListOfStaff, manualDeliver } from '../../../../../service/lead';
import { ILead, TStaff } from '../../../../../types/lead';
import { TManualDeliver } from '../../../../../types/leadToDeliver';
import './styles.scss';

interface ModalAssignLeadProps {
  isOpen: boolean;
  onClose: () => void;
  selectedRows: ILead[];
}

function ModalAssignLead(props: ModalAssignLeadProps) {
  const { isOpen, selectedRows, onClose } = props;

  const [assignedStaff, setAssignedStaff] = React.useState<string>();

  const { data: dataStaff } = useFetch<TStaff[]>({
    queryKeyArrWithFilter: ['get-list-staff'],
    api: getListOfStaff,
  });

  const { mutateAsync: mutateManualDeliver } = useUpdateField({
    apiQuery: manualDeliver,
    keyOfListQuery: ['get-assign-leads'],
    messageSuccess: 'Phân bổ thành công',
  });

  const listStaffs = dataStaff?.data?.data;

  const handleManualDeliver = React.useCallback(async () => {
    const payload: TManualDeliver[] = [];
    selectedRows?.map(o => {
      payload?.push({ id: o?.id as string, assignee: assignedStaff });
      return o;
    });
    const res = await mutateManualDeliver(payload);
    if (res?.data?.statusCode === '0') {
      onClose();
    }
  }, [assignedStaff, mutateManualDeliver, onClose, selectedRows]);

  return (
    <Modal
      className="modal-share-care"
      open={isOpen}
      title="Phân bổ"
      width={380}
      onCancel={onClose}
      confirmLoading
      maskClosable={false}
      footer={
        <Button type="primary" onClick={handleManualDeliver}>
          Phân bổ
        </Button>
      }
    >
      <Radio.Group value={1} options={[{ value: 1, label: 'Phân bổ đến cá nhân' }]} />
      <Select
        allowClear
        onChange={value => {
          setAssignedStaff(value);
        }}
        options={listStaffs?.map(item => {
          return {
            listStaff: { code: item?.code, name: item?.name },
            value: item.code,
            label: item.name,
            code: item?.code,
            name: item?.name,
          };
        })}
      />
    </Modal>
  );
}

export default ModalAssignLead;
