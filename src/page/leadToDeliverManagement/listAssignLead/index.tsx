import { Button, Flex, Modal, TableColumnsType, Tag } from 'antd';
import { TableRowSelection } from 'antd/es/table/interface';
import { Key, useState } from 'react';
import BreadCrumbComponent from '../../../components/breadCrumb';
import TableComponent from '../../../components/table';
import { useDeleteField, useFetch } from '../../../hooks';
import FilterSearch from './components/filterSearch';
import './styles.scss';
import ModalAssignLead from './components/modalManualDeliver/ModalManualDeliver';
import { ILead } from '../../../types/lead';
import { deleteAssigner, getListOfLeadToDeliver } from '../../../service/lead';
import React from 'react';
import { columns } from './columns';

function ListAssignLeads() {
  // const { modal } = App.useApp();
  const [selectedRows, setSelectedRows] = useState<ILead[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<Key[]>([]);
  const [isOpenModalAssignLead, setIsOpenModalAssignLead] = useState(false);
  const [isOpenModalDeleteAssigner, setIsOpenModalDeleteAssigner] = useState<boolean>(false);
  const [currentRecord, setCurrentRecord] = useState<ILead>();

  const {
    data: listCustomer,
    isLoading,
    isPlaceholderData,
    isFetching,
  } = useFetch<ILead[]>({
    queryKeyArrWithFilter: ['get-assign-leads'],
    api: getListOfLeadToDeliver,
  });

  const { mutateAsync: deleteAssignerLead } = useDeleteField({
    apiQuery: deleteAssigner,
    keyOfListQuery: ['get-assign-leads'],
    messageSuccess: 'Gỡ phân bổ thành công',
  });

  const handleDeleteLeadAssigner = React.useCallback(async () => {
    const res = await deleteAssignerLead({ id: currentRecord?.id });
    if (res?.data?.statusCode === '0') setIsOpenModalDeleteAssigner(false);
  }, [currentRecord?.id, deleteAssignerLead]);

  const columnsTag: TableColumnsType = React.useMemo(() => {
    const newColumns = [...columns];
    newColumns.splice(6, 0, {
      title: 'Nhân viên chăm sóc',
      dataIndex: 'takeCareName',
      width: 200,
      key: 'takeCareName',
      render: (_takecareName: string, record: ILead) =>
        record?.takeCare ? (
          <Tag
            closable
            onClose={e => {
              e.preventDefault();
              setIsOpenModalDeleteAssigner(true);
              setCurrentRecord(record);
            }}
          >
            {record?.takeCare?.name || 'Somename'}
          </Tag>
        ) : (
          <></>
        ),
    });
    return newColumns;
  }, []);

  const onSelectChange = (selectedRowKeys: Key[], selectedRows: ILead[]) => {
    setSelectedRows(selectedRows);
    setSelectedRowKeys(selectedRowKeys);
  };

  const rowSelection: TableRowSelection<ILead> = {
    selectedRowKeys,
    onChange: (selectedRowKeys: Key[], selectedRows: ILead[]) => onSelectChange(selectedRowKeys, selectedRows),
  };

  const handleOpenModalAssignLead = () => {
    setIsOpenModalAssignLead(true);
  };
  const handleCancelModalAssignLead = () => {
    setIsOpenModalAssignLead(false);
  };

  return (
    <div className="wrapper-list-personal-customers">
      <BreadCrumbComponent />
      <div className="header-content">
        <Flex gap={17}>
          <FilterSearch />
          {selectedRowKeys.length > 0 && <Button onClick={handleOpenModalAssignLead}>Phân bổ</Button>}
        </Flex>
      </div>
      <div className="table-personal-customers">
        <TableComponent
          queryKeyArr={['get-assign-leads']}
          columns={columnsTag}
          rowSelection={rowSelection}
          loading={isLoading || isPlaceholderData || isFetching}
          dataSource={listCustomer?.data?.data?.rows ?? []}
          rowKey="id"
        />
      </div>
      <ModalAssignLead
        onClose={handleCancelModalAssignLead}
        isOpen={isOpenModalAssignLead}
        selectedRows={selectedRows}
      />
      <Modal
        className="modal-confirm-delete-assigner"
        open={isOpenModalDeleteAssigner}
        title={'Gỡ phân bổ Lead'}
        centered
        closable={false}
        cancelText="Huỷ"
        destroyOnClose
        style={{ textAlign: 'center' }}
        footer={[
          <Button key="cancel" className="btn-cancel" type="text" onClick={() => setIsOpenModalDeleteAssigner(false)}>
            Huỷ
          </Button>,
          <Button key="confirm" className="btn-confirm" type="primary" onClick={handleDeleteLeadAssigner}>
            Xác nhận
          </Button>,
        ]}
      >
        Bạn có muốn gỡ phân bổ Lead này không?
      </Modal>
    </div>
  );
}

export default ListAssignLeads;
