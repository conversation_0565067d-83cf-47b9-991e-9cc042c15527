import { TableColumnsType, Tag, Typography } from 'antd';
import dayjs from 'dayjs';
import { FORMAT_DATE_TIME } from '../../../constants/common';
import { ILead } from '../../../types/lead';

const { Text } = Typography;

export const columns: TableColumnsType = [
  {
    title: 'Mã Lead',
    dataIndex: 'code',
    key: 'code',
    width: 160,
    fixed: 'left',
    render: (value: string, record: ILead) =>
      value ? (
        <div className="cell-name">
          {record?.isHot ? (
            <Tag bordered={false} color="error">
              HOT
            </Tag>
          ) : null}
          <Text>{value}</Text>
        </div>
      ) : (
        '-'
      ),
  },
  {
    title: 'Nguồn Lead',
    dataIndex: 'source',
    key: 'source',
    width: 140,
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: '<PERSON><PERSON> cấu hình',
    dataIndex: ['repo', 'name'],
    width: 140,
    key: 'repoName',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Cấu hình phân bổ',
    dataIndex: ['repo', 'config', 'name'],
    width: 140,
    key: 'configsName',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Họ và tên',
    dataIndex: 'name',
    width: 140,
    key: 'name',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Số điện thoại',
    dataIndex: 'phone',
    width: 200,
    key: 'phone',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Ngày tạo',
    dataIndex: 'createdDate',
    key: 'createdDate',
    width: 200,

    render: (value: string, record: ILead) => (
      <>
        <Text>{dayjs(value).format(FORMAT_DATE_TIME)}</Text>
        <br />
        <Text>{record?.createdByObj?.fullName ? record.createdByObj.fullName : '-'}</Text>
      </>
    ),
  },
  {
    title: 'Ngày cập nhật',
    dataIndex: 'updatedDate',
    key: 'updatedDate',
    width: 200,

    render: (value: string, record: ILead) => (
      <>
        <Text>{dayjs(value).format(FORMAT_DATE_TIME)}</Text> <br />
        <Text>{record?.modifiedByObj?.fullName ? record.modifiedByObj.fullName : '-'}</Text>
      </>
    ),
  },
  {
    title: 'Ngày nhận',
    dataIndex: 'assignedDate',
    key: 'assignedDate',
    width: 200,

    render: (value: string) => (
      <>
        <Text>{dayjs(value).format(FORMAT_DATE_TIME)}</Text> <br />
      </>
    ),
  },
];
