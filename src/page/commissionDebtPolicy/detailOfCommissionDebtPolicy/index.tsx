import { Form, Spin } from 'antd';

import { useEffect } from 'react';
import { useParams } from 'react-router-dom';
import ButtonOfPageDetail from '../../../components/button/buttonOfPageDetail';
import { PERMISSION_DEBT_COMMISSION } from '../../../constants/permissions/debtCommission';
import { useCheckPermissions, useFetch, useRowFieldErrors, useUpdateField } from '../../../hooks';
import { getDetailOfCommissionDebtPolicy, putUpdateCommissionDebtPolicy } from '../../../service/commissionDebtPolicy';
import { ICommissionDebtPolicy, TPayloadCommissionDebtPolicy } from '../../../types/commissionDebtPolicy';
import { DebtRemuneration, GeneralInformation, PenaltySanctions } from '../components';
import { useCommissionDebtICommissionDebtPolicy } from '../storeDebtPolicy';
import { v4 as uuid } from 'uuid';
import { formatDataCommissionRate } from '../utils';
import BreadCrumbComponent from '../../../components/breadCrumb';

const DetailOfCommissionDebtPolicy = () => {
  const [form] = Form.useForm();
  const { id } = useParams();
  const { setIsModified, setInitialValue, initialValue, isModified, setDataPenaltySanctions } =
    useCommissionDebtICommissionDebtPolicy(state => state);
  const { policyUpdate } = useCheckPermissions(PERMISSION_DEBT_COMMISSION);
  const { clearError, hasAnyError, markError, resetErrors } = useRowFieldErrors();

  const { data, isFetching } = useFetch<ICommissionDebtPolicy>({
    api: () => id && getDetailOfCommissionDebtPolicy(id),
    queryKeyArr: ['detail-of-commission-debt-policy', id],
    enabled: !!id,
  });
  const dataDetail = data?.data?.data;

  const updateCommissionDebt = useUpdateField({
    apiQuery: putUpdateCommissionDebtPolicy,
    keyOfDetailQuery: ['detail-of-commission-debt-policy', id],
    isMessageError: false,
  });

  useEffect(() => {
    if (dataDetail) {
      const formatData = {
        ...dataDetail,
        penalty: dataDetail?.penalty || [],
        badDebt: dataDetail?.badDebt || {},
        projectDebt: dataDetail?.projectDebt || {},
      };
      form.setFieldsValue(formatData);
      setInitialValue(formatData);
      setDataPenaltySanctions(formatData?.penalty?.map(item => ({ ...item, key: uuid() })));
    }
  }, [dataDetail, form, setDataPenaltySanctions, setInitialValue]);

  const handleSubmit = async () => {
    try {
      if (hasAnyError()) {
        return;
      }
      await form.validateFields();
      const values: TPayloadCommissionDebtPolicy = form.getFieldsValue(true);
      const projectDebt = values?.projectDebt;
      const badDebt = values?.badDebt;

      // Kiểm tra và định dạng dữ liệu thù lao nợ dự án và nợ xấu
      const newData = {
        ...values,
        year: values?.year && Number(values.year),
        projectDebt: formatDataCommissionRate(projectDebt),
        badDebt: formatDataCommissionRate(badDebt),
        penalty: values?.penalty?.map(item => ({
          ...item,
          amount: item?.amount ? parseFloat(item?.amount?.toString()) : 0,
        })),
      };
      const res = await updateCommissionDebt.mutateAsync(newData);
      if (res?.data?.statusCode === '0') {
        resetErrors();
        setIsModified(false);
      }
    } catch (error) {
      console.error('Validation failed:', error);
      return;
    }
  };

  const handleCancel = () => {
    form.setFieldsValue(initialValue);
    setDataPenaltySanctions(initialValue?.penalty?.map(item => ({ ...item, key: uuid() })) || []);
    resetErrors();
    setIsModified(false);
  };

  return (
    <div>
      <Spin spinning={isFetching || updateCommissionDebt.isPending}>
        <BreadCrumbComponent titleBread={initialValue?.name} />
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          onFieldsChange={() => {
            setIsModified(true);
          }}
          disabled={!policyUpdate}
        >
          <GeneralInformation />
          <PenaltySanctions clearError={clearError} markError={markError} />
          <DebtRemuneration
            title="Thù lao nợ dự án"
            nameField="projectDebt"
            markError={markError}
            clearError={clearError}
          />
          <DebtRemuneration title="Thù lao nợ xấu" nameField="badDebt" markError={markError} clearError={clearError} />
        </Form>
        {isModified && policyUpdate && (
          <ButtonOfPageDetail
            handleSubmit={() => form.submit()}
            handleCancel={handleCancel}
            loadingSubmit={updateCommissionDebt.isPending}
          />
        )}
      </Spin>
    </div>
  );
};

export default DetailOfCommissionDebtPolicy;
