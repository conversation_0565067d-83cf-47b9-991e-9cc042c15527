import { MutationFunction } from '@tanstack/react-query';
import { <PERSON>pp, But<PERSON>, Flex } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { useMemo, useState } from 'react';
import BreadCrumbComponent from '../../../components/breadCrumb';
import ConfirmDeleteModal from '../../../components/modal/specials/ConfirmDeleteModal';
import { modalConfirm } from '../../../components/modal/specials/ModalConfirm';
import TableComponent from '../../../components/table';
import { ActionsColumns } from '../../../components/table/components/ActionsColumns';
import { PERMISSION_DEBT_COMMISSION } from '../../../constants/permissions/debtCommission';
import { useCheckPermissions, useFetch, useUpdateField } from '../../../hooks';
import {
  deleteOfCommissionDebtPolicy,
  getAllOfCommissionDebtPolicy,
  putUpdateStatusOfCommissionDebtPolicy,
} from '../../../service/commissionDebtPolicy';
import { ICommissionDebtPolicy, TFilterDebtPolicy } from '../../../types/commissionDebtPolicy';
import CreateDebtPolicy from '../createDebtPolicy';
import { useCommissionDebtICommissionDebtPolicy } from '../storeDebtPolicy';
import { columnsDebtPolicy } from './columns';
import FilterDebtPolicy from './FilterDebtPolicy';
import { COMMISSION_DEBT_POLICY } from '../../../configs/path';

const ListOfCommissionDebtPolicy = () => {
  const { modal, notification } = App.useApp();
  const { policyDelete, policyGetId, policyUpdate, policyClone, policyCreate, policyChangeStatus } =
    useCheckPermissions(PERMISSION_DEBT_COMMISSION);
  const [filterParams, setFilterParams] = useState<TFilterDebtPolicy>({});
  const [isOpenModalDelete, setIsOpenModalDelete] = useState<boolean>(false);
  const [currentRecord, setCurrentRecord] = useState<ICommissionDebtPolicy>();
  const { setActionModal, setDataPenaltySanctions, setIsModified, setDisabled } =
    useCommissionDebtICommissionDebtPolicy(state => state);

  const { data, isLoading } = useFetch<ICommissionDebtPolicy[]>({
    api: getAllOfCommissionDebtPolicy,
    queryKeyArrWithFilter: ['list-of-commission-debt-policy', filterParams],
    moreParams: { ...filterParams },
  });

  const dataSource = data?.data?.data?.rows || [];

  const { mutateAsync: updateActive } = useUpdateField({
    apiQuery: putUpdateStatusOfCommissionDebtPolicy,
    keyOfListQuery: ['list-of-commission-debt-policy', filterParams],
    isMessageError: false,
    isMessageSuccess: false,
  });
  const columnsActions: ColumnsType<ICommissionDebtPolicy> = useMemo(() => {
    return [
      ...columnsDebtPolicy,
      {
        title: 'Hành động',
        dataIndex: 'action',
        key: 'action',
        width: '100px',
        align: 'center',
        render: (_: unknown, record: ICommissionDebtPolicy) => {
          const textModalConfirmActive = record?.isActive === 1 ? 'Vô hiệu hoá' : 'Kích hoạt';
          const handleActivePolicy = () => {
            return modalConfirm({
              modal: modal,
              title: `${textModalConfirmActive} bộ chỉ tiêu KPI`,
              content: `Bạn có muốn ${textModalConfirmActive} bộ chỉ tiêu KPI này không?`,
              handleConfirm: async () => {
                const res = await updateActive({
                  id: record?.id ?? '',
                  isActive: record?.isActive === 1 ? 2 : 1,
                });
                if (res?.data?.statusCode === '0') {
                  notification.success({
                    message: `${textModalConfirmActive} bộ chỉ tiêu thành công`,
                  });
                }
              },
            });
          };
          const handleDeleteRole = () => {
            setIsOpenModalDelete(true);
            setCurrentRecord(record);
          };

          const openViewDetail = () => {
            window.open(`${COMMISSION_DEBT_POLICY}/${record?.id}`, '_blank', 'noopener,noreferrer');
          };
          const cloneRole = () => {
            setActionModal({ isOpen: true, type: 'clone', id: record?.id });
          };

          return (
            <ActionsColumns
              handleViewDetail={policyGetId || policyUpdate ? openViewDetail : undefined}
              handleCloneRole={policyClone ? cloneRole : undefined}
              textModalConfirmActive={textModalConfirmActive}
              handleActive={policyChangeStatus ? handleActivePolicy : undefined}
              handleDelete={policyDelete ? handleDeleteRole : undefined}
            />
          );
        },
      },
    ];
  }, [
    modal,
    notification,
    policyChangeStatus,
    policyClone,
    policyDelete,
    policyGetId,
    policyUpdate,
    setActionModal,
    updateActive,
  ]);

  const handleOpenCreateDebtPolicy = () => {
    setActionModal({ isOpen: true, type: 'create' });
    setDataPenaltySanctions([]);
    setIsModified(false);
    setDisabled(false);
  };

  return (
    <div className="wrapper-list-of-debt-policy">
      <BreadCrumbComponent />
      <Flex justify="space-between" style={{ marginBottom: 16 }}>
        <FilterDebtPolicy setFilterParams={setFilterParams} />
        {policyCreate && (
          <Button type="primary" onClick={handleOpenCreateDebtPolicy}>
            Thêm mới
          </Button>
        )}
      </Flex>
      <TableComponent
        columns={columnsActions}
        dataSource={dataSource}
        loading={isLoading}
        rowKey="id"
        queryKeyArr={['list-of-commission-debt-policy', filterParams]}
      />
      <ConfirmDeleteModal
        label="bộ chỉ tiêu KPI"
        fieldNameReason="reasonDelete"
        open={isOpenModalDelete}
        apiQuery={deleteOfCommissionDebtPolicy as MutationFunction<unknown, unknown>}
        keyOfListQuery={['list-of-commission-debt-policy', filterParams]}
        onCancel={() => setIsOpenModalDelete(false)}
        idDetail={currentRecord?.id}
        title="Xoá bộ chỉ tiêu KPI"
        description="Vui lòng nhập lý do muốn xoá bộ chỉ tiêu KPI"
      />
      <CreateDebtPolicy />
    </div>
  );
};

export default ListOfCommissionDebtPolicy;
