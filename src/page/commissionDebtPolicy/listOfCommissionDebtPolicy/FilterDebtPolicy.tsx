import { Col, DatePicker, Form, Row, Select } from 'antd';
import dayjs from 'dayjs';
import { Dispatch, SetStateAction, useState } from 'react';
import DropdownFilterSearch from '../../../components/dropdown/dropdownFilterSearch';
import MultiSelectLazy from '../../../components/select/mutilSelectLazy';
import { DEFAULT_PARAMS, FORMAT_DATE, FORMAT_DATE_API, OPTIONS_STATUS_PROJECT } from '../../../constants/common';
import useFilter from '../../../hooks/filter';
import { getListAllEmployeeDropdown } from '../../../service/employee';
import { sendGetListOfDropdownProjects } from '../../../service/salesPolicy';
import { TFilterDebtPolicy } from '../../../types/commissionDebtPolicy';
import { IResponseObjMultiSelect } from '../../../types/common/common';
import { TEmployeeAll } from '../../../types/customers';

const FilterDebtPolicy = ({ setFilterParams }: { setFilterParams: Dispatch<SetStateAction<TFilterDebtPolicy>> }) => {
  const [form] = Form.useForm();
  const [, setFilter] = useFilter();
  const [isOpenFilter, setIsOpenFilter] = useState(false);

  const handleSubmitFilter = (values: TFilterDebtPolicy) => {
    const newFilter: TFilterDebtPolicy = {
      startCreatedDate: values?.startCreatedDate ? dayjs(values?.startCreatedDate).format(FORMAT_DATE_API) : undefined,
      endCreatedDate: values?.endCreatedDate ? dayjs(values?.endCreatedDate).format(FORMAT_DATE_API) : undefined,
      isActive: values?.isActive ? String(values?.isActive) : undefined,
      createdBy: values?.createdBy ? String(values.createdBy) : undefined,
      project: values?.project ? String(values.project) : undefined,
    };
    setFilterParams(prev => ({ ...prev, ...newFilter }));
    setFilter(DEFAULT_PARAMS);
    setIsOpenFilter(false);
  };

  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };

  const handleClearFilters = () => {
    setTimeout(() => {
      setIsOpenFilter(false);
      setFilterParams(prev => ({ search: prev?.search }));
      setFilter({});
    }, 100);
  };

  const handleSelectEmployee = (values: { option: TEmployeeAll }[]) => {
    form.setFieldsValue({
      createdBy: values
        ?.map(item => item?.option?.id)
        .filter(item => item !== undefined)
        .join(','),
    });
  };

  const handleSearch = (searchTerm: string) => {
    const search = searchTerm || undefined;
    setFilterParams(prev => ({ ...prev, search: search }));
    setFilter(DEFAULT_PARAMS);
  };
  const handleSelectProject = (values: IResponseObjMultiSelect[]) => {
    form.setFieldsValue({
      project: values
        ?.map(item => item?.option?.id)
        .filter(item => item !== undefined)
        .join(','),
    });
  };

  return (
    <>
      <DropdownFilterSearch
        rootClassName="wrapper-filter"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        showParams={false}
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        form={form}
        onClearFilters={handleClearFilters}
        onChangeSearch={handleSearch}
        extraFormItems={
          <>
            <Form.Item label="Chọn dự án" name="project">
              <MultiSelectLazy
                gcTime={1000 * 60 * 15} // 15 phút
                apiQuery={sendGetListOfDropdownProjects}
                queryKey={['list-source']}
                enabled={isOpenFilter}
                placeholder="Chọn dự án"
                keysLabel={['name']}
                handleListSelect={handleSelectProject}
                keysTag={['name']}
              />
            </Form.Item>
            <Form.Item label="Người tạo" name="createdBy">
              <MultiSelectLazy
                gcTime={1000 * 60 * 15} // 15 phút
                enabled={isOpenFilter}
                apiQuery={getListAllEmployeeDropdown}
                queryKey={['employee-dropdown']}
                keysLabel={['username', 'name']}
                handleListSelect={handleSelectEmployee}
                placeholder="Chọn nhân viên"
                keysTag={'username'}
              />
            </Form.Item>
            <Form.Item label="Trạng thái" name="isActive">
              <Select placeholder="Chọn trạng thái" allowClear options={OPTIONS_STATUS_PROJECT} />
            </Form.Item>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="Từ ngày" name="startCreatedDate">
                  <DatePicker
                    format={FORMAT_DATE}
                    disabledDate={current => {
                      const endCreatedDate = form.getFieldValue('endCreatedDate');
                      return (
                        (current && current > dayjs().startOf('day')) ||
                        (endCreatedDate && current > dayjs(endCreatedDate).startOf('day')) // Disable ngày sau "Đến ngày"
                      );
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Đến ngày" name="endCreatedDate">
                  <DatePicker
                    format={FORMAT_DATE}
                    disabledDate={current => {
                      const startCreatedDate = form.getFieldValue('startCreatedDate');
                      return (
                        (current && current > dayjs().startOf('day')) ||
                        (startCreatedDate && current < dayjs(startCreatedDate).startOf('day')) // Disable ngày trước "Ngày tạo"
                      );
                    }}
                  />
                </Form.Item>
              </Col>
            </Row>
          </>
        }
      />
    </>
  );
};

export default FilterDebtPolicy;
