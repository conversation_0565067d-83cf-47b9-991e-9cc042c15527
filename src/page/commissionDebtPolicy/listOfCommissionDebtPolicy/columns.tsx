import { Typography } from 'antd';
import { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { FORMAT_DATE_TIME, OPTIONS_STATUS_FILTER } from '../../../constants/common';
import { ICommissionDebtPolicy } from '../../../types/commissionDebtPolicy';
import { TProjectDropdown } from '../../../types/common/common';

const { Text } = Typography;

export const columnsDebtPolicy: ColumnsType<ICommissionDebtPolicy> = [
  {
    title: 'Mã bộ chỉ tiêu KPI',
    dataIndex: 'code',
    key: 'code',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Tên bộ chỉ tiêu KPI ',
    dataIndex: 'name',
    key: 'name',
    render: (value: string) => (value ? value : '-'),
  },

  {
    title: 'Dự án',
    dataIndex: 'project',
    key: 'project',
    render: (value: TProjectDropdown) => (value?.name ? value?.name : '-'),
  },
  {
    title: 'Trạng thái',
    dataIndex: 'isActive',
    key: 'isActive',
    render: (value: number) => {
      const status = OPTIONS_STATUS_FILTER.find(item => item.value === value);
      return <Text style={{ color: status?.color }}>{status?.label}</Text>;
    },
  },
  {
    title: 'Ngày tạo',
    dataIndex: 'createdDate',
    key: 'createdDate',
    render: (value: string, record: ICommissionDebtPolicy) =>
      value ? (
        <>
          <Text>{dayjs(value).format(FORMAT_DATE_TIME)}</Text>
          <br />
          <Text>{record?.createdBy?.fullName ? record.createdBy.fullName : '-'}</Text>
        </>
      ) : (
        '-'
      ),
  },
  {
    title: 'Ngày cập nhật',
    dataIndex: 'modifiedDate',
    key: 'modifiedDate',
    render: (value: string, record: ICommissionDebtPolicy) =>
      value ? (
        <>
          <Text>{dayjs(value).format(FORMAT_DATE_TIME)}</Text>
          <br />
          <Text>{record?.modifiedBy?.fullName ? record.modifiedBy.fullName : '-'}</Text>
        </>
      ) : (
        '-'
      ),
  },
];
