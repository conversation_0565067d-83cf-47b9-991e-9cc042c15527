import { create } from 'zustand';
import { ICommissionDebtPolicy, TPenaltySanction } from '../../types/commissionDebtPolicy';

interface IActionModal {
  isOpen: boolean;
  type?: 'create' | 'clone';
  id?: string;
}

interface ICommissionDebtPolicyDefault extends ICommissionDebtPolicy {
  nameField?: keyof ICommissionDebtPolicy;
}

interface IDebtPolicy {
  disabled: boolean;
  initialValue?: ICommissionDebtPolicyDefault;
  isModified: boolean;
  actionModal: IActionModal;
  dataPenaltySanctions: TPenaltySanction[];
  setDataPenaltySanctions: (value: TPenaltySanction[]) => void;
  setDisabled: (value: boolean) => void;
  setInitialValue: (value?: ICommissionDebtPolicy) => void;
  setIsModified: (value: boolean) => void;
  setActionModal: (value: IActionModal) => void;
}

export const useCommissionDebtICommissionDebtPolicy = create<IDebtPolicy>(set => ({
  disabled: false,
  initialValue: {} as ICommissionDebtPolicy,
  isModified: false,
  actionModal: { isOpen: false, type: undefined, id: undefined },
  dataPenaltySanctions: [],
  setDataPenaltySanctions: value => set({ dataPenaltySanctions: value }),
  setDisabled: value => set({ disabled: value }),
  setInitialValue: value => set({ initialValue: value }),
  setIsModified: value => set({ isModified: value }),
  setActionModal: (value: IActionModal) => set({ actionModal: value }),
}));
