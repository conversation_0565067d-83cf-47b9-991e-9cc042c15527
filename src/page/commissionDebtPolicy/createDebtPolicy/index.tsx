import { But<PERSON>, Form, Spin } from 'antd';
import { useEffect } from 'react';
import ModalComponent from '../../../components/modal';
import { useCreateField, useFetch, useRowFieldErrors } from '../../../hooks';
import {
  getDetailOfCommissionDebtPolicy,
  postCreateOfCommissionDebtPolicy,
} from '../../../service/commissionDebtPolicy';
import { ICommissionDebtPolicy, TPayloadCommissionDebtPolicy } from '../../../types/commissionDebtPolicy';
import { DebtRemuneration, GeneralInformation, PenaltySanctions } from '../components';
import { useCommissionDebtICommissionDebtPolicy } from '../storeDebtPolicy';
import { v4 as uuid } from 'uuid';
import { formatDataCommissionRate } from '../utils';

const CreateDebtPolicy = () => {
  const [form] = Form.useForm();
  const { actionModal, setActionModal, setInitialValue, setDataPenaltySanctions } =
    useCommissionDebtICommissionDebtPolicy(state => state);
  const { clearError, hasAnyError, markError, resetErrors } = useRowFieldErrors();

  // Lấy dữ liệu từ actionModal để xác định hành động (tạo mới, chỉnh sửa, sao chép)
  const { data } = useFetch<ICommissionDebtPolicy>({
    api: () => actionModal?.id && getDetailOfCommissionDebtPolicy(actionModal?.id),
    queryKeyArr: ['detail-of-commission-debt-policy', actionModal?.id],
    enabled: !!actionModal?.id,
  });
  const dataDetail = data?.data?.data;

  // Mutation để tạo mới chính sách nợ
  const postCommissionDebtPolicy = useCreateField({
    apiQuery: postCreateOfCommissionDebtPolicy,
    keyOfListQuery: ['list-of-commission-debt-policy'],
    isMessageError: false,
  });

  // Đặt giá trị ban đầu cho form khi actionModal thay đổi
  useEffect(() => {
    if (dataDetail && actionModal?.id) {
      const formatData = {
        ...dataDetail,
        id: actionModal?.id || dataDetail?.id || '',
        code: undefined,
        penalty: dataDetail?.penalty || [],
        badDebt: dataDetail?.badDebt || {},
        projectDebt: dataDetail?.projectDebt || {},
      };
      form.setFieldsValue(formatData);
      setDataPenaltySanctions(formatData?.penalty?.map(item => ({ ...item, key: uuid() })));
      setInitialValue(formatData);
    }
  }, [actionModal?.id, dataDetail, form, setDataPenaltySanctions, setInitialValue]);

  const handleSubmit = async () => {
    try {
      await form.validateFields();
      if (hasAnyError()) {
        return;
      }

      const values: TPayloadCommissionDebtPolicy = form.getFieldsValue(true);
      const projectDebt = values?.projectDebt;
      const badDebt = values?.badDebt;

      const newData = {
        ...values,
        year: values?.year && Number(values.year),
        projectDebt: formatDataCommissionRate(projectDebt),
        badDebt: formatDataCommissionRate(badDebt),
        penalty: values?.penalty?.map(item => ({
          ...item,
          amount: item?.amount ? parseFloat(item?.amount?.toString()) : 0,
        })),
      };
      const res = await postCommissionDebtPolicy.mutateAsync(newData);

      if (res?.data?.statusCode === '0') {
        setActionModal({ isOpen: false, type: undefined, id: undefined });
        setInitialValue(undefined);
        resetErrors();
        setDataPenaltySanctions([]);
        form.resetFields();
      }
    } catch (error) {
      console.error('Validation failed:', error);
      return;
    }
  };
  return (
    <Spin spinning={false}>
      <ModalComponent
        rootClassName="wrapper-create-debt-policy"
        title="Tạo mới bộ chỉ tiêu KPI"
        open={actionModal?.isOpen}
        onCancel={() => {
          setActionModal({ isOpen: false, type: undefined, id: undefined });
          form.resetFields();
          resetErrors();
          setInitialValue(undefined);
        }}
        destroyOnClose
        footer={[
          <Button key="submit" type="primary" onClick={form.submit} loading={!!postCommissionDebtPolicy.isPending}>
            Lưu
          </Button>,
        ]}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            isActive: 1,
          }}
        >
          <GeneralInformation />
          <PenaltySanctions markError={markError} clearError={clearError} />
          <DebtRemuneration
            title="Thù lao nợ dự án"
            nameField="projectDebt"
            markError={markError}
            clearError={clearError}
          />
          <DebtRemuneration title="Thù lao nợ xấu" nameField="badDebt" markError={markError} clearError={clearError} />
        </Form>
      </ModalComponent>
    </Spin>
  );
};

export default CreateDebtPolicy;
