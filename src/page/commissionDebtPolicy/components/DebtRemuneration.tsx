import { PlusOutlined } from '@ant-design/icons';
import { Button, Checkbox, Flex, Form, Select, Space, Typography } from 'antd';
import isEqual from 'lodash/isEqual';
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { v4 as uuid } from 'uuid';
import { ColumnTypesCustom, EditTable, EditTableColumns } from '../../../components/editTable';
import CurrencyInput from '../../../components/input/CurrencyInput';
import CurrencyPercentInput from '../../../components/input/CurrencyPercentInput';
import PercentInput from '../../../components/input/PercentInput';
import { ActionsColumns } from '../../../components/table/components/ActionsColumns';
import { CALCULATE_TYPES } from '../../../constants/debtCommission';
import { TCommissionRateItem } from '../../../types/commissionDebtPolicy';
import { formatPercent } from '../../../utilities/shareFunc';
import { useCommissionDebtICommissionDebtPolicy } from '../storeDebtPolicy';
import { pickFields } from '../utils';

const { Title } = Typography;
const { Item } = Form;

interface IDebtRemunerationProps {
  title: string;
  nameField: 'projectDebt' | 'badDebt';
  markError: (rowKey: string, fieldKey: string) => void;
  clearError: (rowKey: string, fieldKey: string) => void;
}

const DebtRemuneration = ({ title, nameField, clearError, markError }: IDebtRemunerationProps) => {
  const form = Form.useFormInstance();
  const { id } = useParams();
  const [dataSource, setDataSource] = useState<TCommissionRateItem[]>([]);
  const { initialValue, actionModal, setIsModified, isModified } = useCommissionDebtICommissionDebtPolicy(
    state => state,
  );

  const dataDebtRemuneration = initialValue?.[nameField];

  // Lấy dữ liệu từ actionModal hoặc initialValue
  useEffect(() => {
    if ((id || actionModal?.id) && dataDebtRemuneration && !isModified) {
      const ratesWithKeys = dataDebtRemuneration?.listRate.map(item => ({
        ...item,
        type: dataDebtRemuneration?.type,
        key: uuid(),
      }));
      setDataSource(ratesWithKeys);
    }
  }, [actionModal?.id, dataDebtRemuneration, id, isModified]);

  const columns: (ColumnTypesCustom<TCommissionRateItem>[number] & EditTableColumns)[] = [
    {
      key: 'bottomPrice',
      title: 'Giá trị cận dưới',
      dataIndex: 'bottomPrice',
      editable: true,
      width: '22%',
      inputType: 'custom',
      renderEditComponent:
        (_, index) =>
        ({ save, disabled }) => (
          <CurrencyInput
            onChange={val => {
              if (index === 0) {
                const newDataSource = [...dataSource];
                newDataSource[0] = { ...newDataSource[0], bottomPrice: val || '' };
                //giá trị cập nhật khi thay có sự thay đổi
                updateDataSource(newDataSource);
              }
            }}
            maxLength={17}
            onBlur={save}
            onPressEnter={save}
            placeholder="Nhập giá trị cận dưới"
            suffix="VNĐ"
            disabled={disabled || index !== 0}
          />
        ),
      alwaysEditable: true,
      rules: (_, index) => [
        {
          validator: (_, value) => {
            if (!value) {
              markError(dataSource[index].key, 'bottomPrice');
              return Promise.reject('Vui lòng nhập giá trị cận dưới.');
            }
            clearError(dataSource[index].key, 'bottomPrice');
            return Promise.resolve();
          },
        },
      ],
    },
    {
      key: 'type',
      title: 'Giá trị tính',
      dataIndex: 'type',
      editable: true,
      width: '22%',
      inputType: 'custom',
      alwaysEditable: true,
      renderEditComponent:
        (_, index) =>
        ({ save, disabled }) => (
          <Select
            options={CALCULATE_TYPES}
            onChange={val => {
              if (index === 0) {
                const newDataSource = dataSource.map(item => ({
                  ...item,
                  type: val || '',
                }));
                //giá trị cập nhật khi thay có sự thay đổi
                updateDataSource(newDataSource);
                setIsModified(true);
              }
            }}
            onSelect={save}
            disabled={disabled || index !== 0}
            placeholder="Chọn giá trị tính"
          />
        ),

      rules: (_, index) => [
        {
          validator: (_, value) => {
            if (!value) {
              markError(dataSource[index].key, 'type');
              return Promise.reject('Vui lòng chọn giá trị tính.');
            }
            clearError(dataSource[index].key, 'type');
            return Promise.resolve();
          },
        },
      ],
    },
    {
      key: 'topPrice',
      title: 'Giá trị cận trên',
      dataIndex: 'topPrice',
      editable: true,
      width: '22%',
      inputType: 'custom',
      renderEditComponent:
        (_, index) =>
        ({ save, disabled }) => (
          <CurrencyInput
            onChange={val => {
              if (index !== undefined) {
                const newDataSource = dataSource.map((item, idx) => {
                  if (idx === index) return { ...item, topPrice: val || '' };
                  if (idx === index + 1) return { ...item, bottomPrice: val || '' };
                  return item;
                });
                //giá trị cập nhật khi thay có sự thay đổi
                updateDataSource(newDataSource);
              }
            }}
            maxLength={17}
            onBlur={save}
            onPressEnter={save}
            placeholder="Nhập giá trị cận trên"
            suffix="VNĐ"
            disabled={disabled}
          />
        ),
      alwaysEditable: true,
      dependencies: ['bottomPrice'],
      rules: (_, index) => [
        ({ getFieldValue }) => ({
          validator: async (_, value) => {
            const nextTopPrice = dataSource[index + 1]?.topPrice;
            const bottomPrice = getFieldValue('bottomPrice');
            if (!value || !bottomPrice) return Promise.resolve();

            const numValue = Number(value);
            const numBottomPrice = Number(bottomPrice);

            // Kiểm tra nếu giá trị cận trên nhỏ hơn hoặc bằng giá trị cận dưới
            if (numValue < numBottomPrice) {
              markError(dataSource[index].key, 'topPrice');
              return Promise.reject('Vui lòng nhập giá trị cận dưới nhỏ hơn hoặc bằng giá trị cận trên');
            }
            // Kiểm tra nếu giá trị cận trên bằng giá trị cận dưới
            else if (numValue === numBottomPrice) {
              markError(dataSource[index].key, 'topPrice');
              return Promise.reject('Khoảng giá trị cận dưới và cận trên không hợp lệ. Vui lòng nhập lại');
            } else if (nextTopPrice && numValue >= Number(nextTopPrice)) {
              clearError(dataSource[index].key, 'topPrice');
              return Promise.reject('Khoảng giá trị cận trên phải bé hơn giá trị cận trên của mục tiếp theo');
            }
            return Promise.resolve();
          },
        }),
      ],
    },
    {
      key: 'recordedCommission',
      title: 'Thù lao ghi nhận',
      dataIndex: 'recordedCommission',
      editable: true,
      width: '30%',
      inputType: 'custom',
      alwaysEditable: true,
      renderEditComponent:
        (record: unknown, index) =>
        ({ save, disabled }) => {
          const row = record as TCommissionRateItem;
          return (
            <CurrencyPercentInput
              disabled={disabled}
              allowTypeSwitch={index === 0}
              defaultInputType={index !== 0 && dataSource.length > 0 ? dataSource[0].recordedCommissionUnit : 'VNĐ'}
              externalInputType={dataSource[0]?.recordedCommissionUnit}
              onChange={val => {
                if (index !== undefined) {
                  const newDataSource = [...dataSource];
                  newDataSource[index] = { ...newDataSource[index], recordedCommission: val || '' };
                  updateDataSource(newDataSource);
                }
              }}
              onBlur={save}
              onPressEnter={save}
              maxLength={13}
              placeholder="Nhập thù lao ghi nhận"
              onTypeChange={(type, value) => {
                const rowIndex = dataSource.findIndex(item => item.key === row.key);
                // Cập nhật giá trị thù lao ghi nhận theo loại
                if (rowIndex >= 0) {
                  const newData = [...dataSource];
                  const updatedData = newData.map((item, idx) => {
                    const currentValue = idx === rowIndex ? value : item.recordedCommission;
                    return {
                      ...item,
                      recordedCommissionUnit: type,
                      recordedCommission:
                        type === '%'
                          ? formatPercent(currentValue?.toString()?.slice(0, 3) || '')
                          : currentValue?.toString()?.split('.')[0] || '',
                    };
                  });

                  updateDataSource(updatedData);
                  setIsModified(true);
                }
              }}
            />
          );
        },
      rules: (_, index) => [
        {
          validator: (_, value) => {
            if (!value) {
              markError(dataSource[index].key, 'recordedCommission');
              return Promise.reject('Vui lòng nhập giá trị thù lao ghi nhận.');
            }
            clearError(dataSource[index].key, 'recordedCommission');
            return Promise.resolve();
          },
        },
      ],
    },
    {
      dataIndex: 'action',
      key: 'action',
      width: '100px',
      align: 'center',
      render: (_, record, index) => {
        const handleDelete = () => {
          const newData = [...dataSource];
          newData.splice(index, 1);
          updateDataSource(newData);
          setIsModified(true);
        };

        const handleClone = () => {
          const newItem = { ...record, bottomPrice: record?.topPrice, topPrice: '', key: uuid() };
          const newData = [...dataSource];
          newData.splice(index + 1, 0, newItem);
          updateDataSource(newData);
        };

        return <ActionsColumns handleCloneRole={handleClone} handleDelete={handleDelete} />;
      },
    },
  ];

  // Hàm cập nhật dữ liệu state và form
  const updateDataSource = (newDataSource: TCommissionRateItem[]) => {
    setDataSource(newDataSource);
    form.setFieldsValue({
      [`${nameField}`]: { listRate: newDataSource },
    });
  };

  // Hàm thêm một mục mới vào cuối danh sách
  const handleAdd = () => {
    const lastItem = dataSource[dataSource.length - 1];
    const newBottomPrice = lastItem ? lastItem.topPrice : 0;
    const FirstType = dataSource?.[0]?.type;

    const newData: TCommissionRateItem = {
      key: uuid(),
      unit: 'VNĐ',
      recordedCommissionUnit: 'VNĐ',
      topPrice: 0,
      bottomPrice: newBottomPrice,
      recordedCommission: 0,
      type: FirstType || 'all',
    };

    const newDataSource = [...dataSource, newData];
    setDataSource(newDataSource);
    form.setFieldsValue({
      [`${nameField}`]: { listRate: newDataSource },
    });
  };

  const handleSave = () => {
    const cleanedDataSource = dataSource.map(pickFields);
    const cleanedListRate = dataDebtRemuneration?.listRate?.map(pickFields);
    const isDifferent = !isEqual(cleanedDataSource, cleanedListRate);
    if (isDifferent) {
      setIsModified(true);
    }
  };

  return (
    <>
      <Title level={5} style={{ marginTop: 8 }}>
        {title}
      </Title>
      <Flex justify="space-between" align="center">
        <Space>
          <Item name={[nameField, 'isProgressive']} valuePropName="checked">
            <Checkbox>Tính luỹ tiến</Checkbox>
          </Item>
          <Item name={[nameField, 'isVAT']} valuePropName="checked">
            <Checkbox>VAT</Checkbox>
          </Item>
        </Space>
        <Space align="start" size="middle">
          <Item name={[nameField, 'rate']}>
            <PercentInput
              suffix="%"
              isDefault={false}
              placeholder={nameField === 'projectDebt' ? 'Nhập hệ số thù lao nợ dự án' : 'Nhập hệ số thù lao nợ xấu'}
              style={{ minWidth: 220 }}
            />
          </Item>
          <Button type="default" icon={<PlusOutlined />} onClick={handleAdd}>
            Thêm tỷ lệ hoa hồng
          </Button>
        </Space>
      </Flex>
      <Item
        name={nameField}
        rules={[
          {
            required: true,
            validator: () => {
              const debtRemuneration = form.getFieldValue(nameField);
              const listRate = debtRemuneration?.listRate || [];
              const hasInvalid = listRate?.some((item: TCommissionRateItem) =>
                Object.entries(item)?.some(
                  ([key, value]) =>
                    key !== 'key' && (value === undefined || value === null || value === '' || value === 0),
                ),
              );
              if ((Array.isArray(listRate) && !listRate.length) || hasInvalid) {
                return Promise.reject(
                  `Vui lòng nhập đủ thông tin tỷ lệ hoa hồng nợ ${nameField === 'projectDebt' ? 'dự án' : 'Xấu'} `,
                );
              }
              return Promise.resolve();
            },
          },
        ]}
      >
        <EditTable columns={columns} dataSource={dataSource} handleSave={handleSave} />
      </Item>
    </>
  );
};

export default DebtRemuneration;
