import { Col, Form, Row, Typography } from 'antd';
import isEqual from 'lodash/isEqual';
import { ColumnTypesCustom, EditTable, EditTableColumns } from '../../../components/editTable';
import CurrencyPercentInput from '../../../components/input/CurrencyPercentInput';
import { CALCULATE_TYPES } from '../../../constants/debtCommission';
import { TPenaltySanction } from '../../../types/commissionDebtPolicy';
import { useCommissionDebtICommissionDebtPolicy } from '../storeDebtPolicy';

const { Title } = Typography;
const { Item } = Form;

interface IPenaltySanctionProps {
  markError: (rowKey: string, fieldKey: string) => void;
  clearError: (rowKey: string, fieldKey: string) => void;
}

const PenaltySanctions = (props: IPenaltySanctionProps) => {
  const { markError, clearError } = props;
  const form = Form.useFormInstance();

  const { setIsModified, setDataPenaltySanctions, dataPenaltySanctions } = useCommissionDebtICommissionDebtPolicy(
    state => state,
  );

  const columns: (ColumnTypesCustom<TPenaltySanction>[number] & EditTableColumns)[] = [
    {
      key: 'name',
      title: 'Nhóm tuổi nợ',
      dataIndex: 'name',
      editable: true,
      width: '30%',
      inputType: 'text',
      alwaysEditable: true,
      disabled: true,
    },
    {
      key: 'amount',
      title: 'Giá trị phạt',
      dataIndex: 'amount',
      editable: true,
      width: '40%',
      inputType: 'custom',
      alwaysEditable: true,
      renderEditComponent:
        (record: unknown) =>
        ({ onChange, save, disabled }) => {
          const row = record as TPenaltySanction;
          return (
            <CurrencyPercentInput
              disabled={disabled}
              onPressEnter={save}
              onBlur={save}
              defaultInputType={row?.unit || 'VNĐ'}
              onChange={val => onChange(val)}
              maxLength={13}
              placeholder="Giá trị phạt"
              onTypeChange={(type, value) => {
                const rowIndex = dataPenaltySanctions.findIndex(item => item.key === row.key);
                if (rowIndex >= 0) {
                  const newData = [...dataPenaltySanctions];
                  newData[rowIndex].unit = type;
                  newData[rowIndex].amount = Number(value);
                  setDataPenaltySanctions(newData);
                  form.setFieldsValue({
                    [`penalty`]: newData,
                  });
                  setIsModified(true);
                }
              }}
            />
          );
        },
      rules: (_, index) => [
        {
          validator: async (_, value) => {
            const numValue = Number(value);
            if (isNaN(numValue) || numValue <= 0) {
              markError(dataPenaltySanctions[index].key, 'amount');
              return Promise.reject('Vui lòng nhập giá trị phạt.');
            } else {
              clearError(dataPenaltySanctions[index].key, 'amount');
              return Promise.resolve();
            }
          },
        },
      ],
    },
    {
      key: 'type',
      title: 'Tính theo',
      dataIndex: 'type',
      editable: true,
      width: '30%',
      inputType: 'select',
      optionsSelect: CALCULATE_TYPES,
      selectProps: { placeholder: 'Tính theo' },
      alwaysEditable: true,
      rules: [{ required: true, message: 'Vui lòng chọn tính theo' }],
    },
  ];

  // Hàm lưu dữ liệu khi chỉnh sửa
  const handleSave = (row: TPenaltySanction) => {
    const newData = [...(dataPenaltySanctions || [])];
    const index = newData.findIndex((item: TPenaltySanction) => row.key === item.key);
    const item = newData[index];
    newData.splice(index, 1, { ...item, ...row });
    setDataPenaltySanctions(newData);
    form.setFieldsValue({
      [`penalty`]: newData,
    });
    if (!isEqual(newData, dataPenaltySanctions)) setIsModified(true);
  };

  return (
    <Row gutter={{ md: 24, lg: 40 }}>
      <Col span={24}>
        <Title level={5}>Chế tài phạt</Title>
      </Col>
      <Col xs={24} md={12}>
        <Item
          name={'penalty'}
          rules={[
            {
              validator: () => {
                const penaltySanctions = form.getFieldValue('penalty');
                const hasInvalid = penaltySanctions?.some((item: TPenaltySanction) =>
                  Object.entries(item)?.some(([, value]) => value === undefined || value === null || value === 0),
                );
                if (!penaltySanctions || hasInvalid) {
                  return Promise.reject('Vui lòng nhập đủ chế tài phạt');
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <EditTable columns={columns} dataSource={dataPenaltySanctions || []} handleSave={handleSave} />
        </Item>
      </Col>
    </Row>
  );
};

export default PenaltySanctions;
