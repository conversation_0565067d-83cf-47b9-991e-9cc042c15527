import { Button, Col, Form, Input, Modal, Row, Select } from 'antd';
import ModalComponent from '../../../components/modal';
import {
  OPTIONS_CONSTRUCTION_PROGRESS_PROJECT,
  OPTIONS_PROJECT_TYPE,
  OPTIONS_STATUS_PROJECT,
} from '../../../constants/common';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { Project } from '../../../types/project/project';
import { useCreateField, useFetch } from '../../../hooks';
import { sendCreateProject } from '../../../service/project';
import SearchableInfiniteScrollSelect from '../../../components/select/SelectScroll';
import { getListInvestor } from '../../../service/investor';
import { QueryKey } from '@tanstack/react-query';
import { handleKeyDownBlockSpecialCharacters } from '../../../utilities/regex';

export interface InvestorType {
  id: string;
  name: string;
}
interface FormModalProjectProps {
  visible: boolean;
  onClose: () => void;
  keyQuery: QueryKey;
}

const FormModalProject = ({ visible, onClose, keyQuery }: FormModalProjectProps) => {
  const [form] = Form.useForm();
  const [investorOptions, setInvestorOptions] = useState<InvestorType[]>([]);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [page, setPage] = useState(1);

  const createProject = useCreateField<Project>({
    keyOfDetailQuery: keyQuery,
    apiQuery: sendCreateProject,
    label: 'dự án',
    isMessageError: false,
  });

  const {
    data: dataInvestors,
    isFetching,
    isLoading: isLoadingInvestor,
  } = useFetch<InvestorType[]>({
    queryKeyArrWithFilter: ['investor-list', page, searchTerm],
    api: getListInvestor,
    moreParams: { page: page, search: searchTerm },
  });

  const totalPages = dataInvestors?.data?.data?.totalPages || 0;

  const listInvestor = useMemo(() => {
    return dataInvestors?.data?.data?.rows || [];
  }, [dataInvestors]);

  useEffect(() => {
    setInvestorOptions(prev => {
      const newItems = listInvestor.filter(item => !prev.some(existing => existing.id === item.id));
      return [...prev, ...newItems];
    });
  }, [listInvestor]);

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setPage(1);
    setInvestorOptions([]);
  };

  const handleSelectChange = (value: { value: string; label: string } | null) => {
    form.setFieldsValue({
      investor: {
        id: value ? value.value : null,
      },
    });
    if (!value) {
      setSearchTerm('');
      form.setFieldsValue({ investor: null });
      form.validateFields(['investor']);
    }
  };

  const handleLoadMore = useCallback(() => {
    setPage(prev => prev + 1);
  }, []);

  const handleCancel = useCallback(() => {
    if (form.isFieldsTouched()) {
      Modal.confirm({
        title: 'Xác nhận hủy',
        content: 'Dữ liệu đang chưa được lưu, bạn có chắc muốn hủy dữ liệu đang nhập không?',
        cancelText: 'Quay lại',
        onOk: () => {
          form.resetFields();
          onClose();
        },
        okButtonProps: {
          type: 'default',
        },
        cancelButtonProps: {
          type: 'primary',
        },
      });
    } else {
      form.resetFields();
      onClose();
    }
  }, [form, onClose]);

  const handleFinish = async (values: Project) => {
    const transformedValues = {
      ...values,
      type: Array.isArray(values.type) ? values.type.join(',') : values.type, // Chuyển mảng thành chuỗi "01,02"
    };
    const res = await createProject.mutateAsync(transformedValues);
    const statusCode = res?.data?.statusCode;
    if (statusCode === '0') {
      form.resetFields();
      onClose();
    }
  };

  return (
    <ModalComponent
      title="Tạo mới dự án"
      open={visible}
      onCancel={handleCancel}
      destroyOnClose
      footer={
        <Button type="primary" htmlType="submit" onClick={() => form.submit()}>
          Lưu
        </Button>
      }
    >
      <Form
        form={form}
        layout="vertical"
        colon={false}
        labelAlign="left"
        initialValues={{
          status: OPTIONS_STATUS_PROJECT[0]?.value,
          source: 'CRM',
        }}
        onFinish={handleFinish}
      >
        <Row>
          <Col span={12}>
            <Row gutter={[24, 0]}>
              <Col span={12}>
                <Form.Item
                  label="Loại dự án"
                  name="type"
                  rules={[{ required: true, message: 'Vui lòng chọn loại dự án' }]}
                >
                  <Select
                    options={OPTIONS_PROJECT_TYPE.map(item => {
                      return {
                        value: item.value,
                        label: item.label,
                      };
                    })}
                    allowClear
                    placeholder="Chọn loại dự án"
                    mode="multiple"
                    showSearch={false}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Trạng thái" name="status" required>
                  <Select
                    placeholder="Trạng thái"
                    options={OPTIONS_STATUS_PROJECT.map(item => {
                      return {
                        value: item.value,
                        label: item.label,
                      };
                    })}
                  />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item
                  label="Chủ đầu tư"
                  name="investor"
                  rules={[{ required: true, message: 'Vui lòng chọn tên chủ đầu tư' }]}
                >
                  <SearchableInfiniteScrollSelect
                    options={investorOptions?.map(item => ({
                      value: item.id,
                      label: item.name,
                    }))}
                    onLoadMore={handleLoadMore}
                    onSearch={handleSearch}
                    isLoading={isLoadingInvestor}
                    hasMore={page < totalPages}
                    placeholder="Chọn chủ đầu tư"
                    allowClear
                    onChange={handleSelectChange}
                    isFetching={isFetching}
                    labelInValue
                    onClear={() => form.setFieldsValue({ investor: null })}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Nguồn" name="source">
                  <Input disabled value="CRM" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Tiến độ xây dựng"
                  name="currentStatus"
                  rules={[{ required: true, message: 'Vui lòng chọn tiến độ xây dựng' }]}
                >
                  <Select
                    options={OPTIONS_CONSTRUCTION_PROGRESS_PROJECT.map(item => {
                      return {
                        value: item.value,
                        label: item.label,
                      };
                    })}
                    allowClear
                    placeholder="Chọn tiến độ xây dựng"
                  />
                </Form.Item>
              </Col>
              <Col span={17}>
                <Form.Item
                  label="Tên dự án"
                  name="name"
                  required
                  rules={[
                    {
                      validator: (_, value) => {
                        if (!value || value.trim() === '') {
                          return Promise.reject(new Error('Vui lòng nhập tên dự án'));
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <Input
                    maxLength={50}
                    placeholder="Nhập tên dự án"
                    onBlur={e => {
                      form.setFieldsValue({ name: e.target.value.trim() });
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={7}>
                <Form.Item label="Mã dự án" name="code" rules={[{ required: true, message: 'Vui lòng nhập mã dự án' }]}>
                  <Input
                    placeholder="Nhập mã dự án"
                    maxLength={20}
                    onKeyDown={e => {
                      if (e.key === ' ') {
                        e.preventDefault();
                      }
                      handleKeyDownBlockSpecialCharacters(e);
                    }}
                  />
                </Form.Item>
              </Col>
            </Row>
          </Col>
        </Row>
      </Form>
    </ModalComponent>
  );
};

export default FormModalProject;
