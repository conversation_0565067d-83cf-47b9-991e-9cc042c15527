import { <PERSON>, Image, Button, Flex, Dropdown, Tag, Typography } from 'antd';
import { BookOutlined, MoreOutlined } from '@ant-design/icons';
import type { MenuProps } from 'antd';
import FPTLogo from '../../../../src/assets/images/Default_Logo_Project.png';
import { OPTIONS_PROJECT_TYPE } from '../../../constants/common';
import { OPERATE_SELL_PROGRAM, PROJECTS_MANAGEMENT } from '../../../configs/path';
import { Project } from '../../../types/project/project';
import { useNavigate } from 'react-router-dom';
import { useSellProgramStore } from '../../Store';

interface ProjectCardProps {
  data: Project;
}
const { Title } = Typography;

const ProjectCard: React.FC<ProjectCardProps> = ({ data }) => {
  const navigate = useNavigate();
  const setSalesProgramIds = useSellProgramStore(state => state.ArrSetSalesProgramIds);
  const isButtonDisabled = !(data.urlEsalekit && data.esalekit);
  const menu: MenuProps['items'] = [
    {
      key: '1',
      label: <a href={`${PROJECTS_MANAGEMENT}/${data._id}`}>Xem chi tiết</a>,
    },
    ...(data.source === 'CRM'
      ? [
          {
            key: '2',
            label: data.status === '01' ? <a href="">Vô hiệu hóa</a> : <a href="">Kích hoạt</a>,
          },
        ]
      : []),
  ];
  return (
    <Card
      cover={
        <div className="image-container">
          <Image
            src={data?.imageUrl ? `${import.meta.env.VITE_S3_IMAGE_URL}/${data?.imageUrl}` : FPTLogo}
            alt="alt"
            preview={false}
            width="100%"
            height={220}
            style={{ objectFit: 'cover' }}
          />
          <Dropdown menu={{ items: menu }} placement="bottomRight" overlayStyle={{ width: 160 }}>
            <Button icon={<MoreOutlined />} className="button-overlay" />
          </Dropdown>
          {data?.status === '01' ? (
            <Tag className="tag-active-overlay" color="green">
              Đang hoạt động
            </Tag>
          ) : (
            <Tag className="tag-active-overlay" color="red">
              <span style={{ color: '#F5222D' }}> Vô hiệu hóa</span>
            </Tag>
          )}
        </div>
      }
    >
      <Title level={5}>{data?.name}</Title>
      <div className="type-project">
        <div style={{ marginBottom: 8 }}>
          <span className="text">Mã dự án: </span> <span className="type">{data?.code}</span>
        </div>
        <span className="text">Loại hình: </span>
        <span className="type">
          {data?.type
            ?.split(',')
            .map(typeValue => OPTIONS_PROJECT_TYPE.find(option => option.value === typeValue)?.label)
            .filter(Boolean)
            .join(', ')}
        </span>
      </div>
      <Flex wrap gap={16}>
        <Button
          type="primary"
          htmlType="button"
          disabled={data.salesProgramIds && data.salesProgramIds.length === 0}
          onClick={() => {
            if (data.salesProgramIds && data.salesProgramIds.length > 0) {
              setSalesProgramIds(data.salesProgramIds);
            }
            navigate(`${PROJECTS_MANAGEMENT}${OPERATE_SELL_PROGRAM}/${data._id}`);
          }}
        >
          Quản lý bán hàng
        </Button>

        <Button icon={<BookOutlined />} htmlType="button" disabled={isButtonDisabled}>
          E - Sale kit
        </Button>
      </Flex>
    </Card>
  );
};

export default ProjectCard;
