.project-list_page {
  .name-project {
    margin-bottom: 8px;
    font-size: 16px;
    font-weight: 600;
  }

  .type-project {
    margin-bottom: 24px;
    font-size: 12px;
    font-weight: 400;

    .type {
      font-size: 12px;
    }
  }

  .image-container {
    position: relative;
    display: inline-block;
  }

  .button-overlay {
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: 1;
  }
}

.project-card {
  display: flex;
  align-items: center;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  justify-content: space-between;
  padding-left: 16px;
  @media (max-width: 768px) {
    display: flex;
    flex-direction: column-reverse;
    padding: 20px;
    justify-content: center;
  }

  .project-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;

    .ant-typography-secondary {
      font-size: 12px;
      color: #8c8c8c;

      .text-type {
        color: rgba(0, 0, 0, 0.88);
        line-height: 20px;
      }
    }
  }

  .project-actions {
    display: flex;
    gap: 40px;
    margin-top: 8px;
    align-items: center;
  }

  .project-image {
    width: 180px;
    height: 110px;
    overflow: hidden;
    margin-left: 20px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}

.edit-block {
  .input-edit-block {
    width: 130px;
    height: 22px;
    margin-top: 4px;
    font-size: 12px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    background: #fafafa;
    border-radius: 4px;
  }
}

.PropertyUnit {
  .button-container {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    flex-wrap: wrap;

    @media (max-width: 1320px) {
      align-items: center;
      margin-top: 10px;
    }
    .ant-btn-icon {
      color: #00000073;
    }
  }
  .scroll-table {
    display: flex !important;
    flex-direction: row !important;
    overflow: auto !important;
    width: 100% !important;
    box-sizing: border-box !important;
  }
  .info-floor {
    display: flex;
    align-items: center;
    color: #00000040;
    margin: 24px 0;
    .anticon-arrow-right,
    .anticon-arrow-down {
      font-size: 13px;
      margin-left: 10px;
    }
    .text-arrow {
      font-size: 12px;
      line-height: 20px;
      margin-right: 14px;
    }
    .ant-btn {
      padding: 0 23px;
      border: none;
    }
    .active {
      border: none;
      background-color: #0000000f;
      &:hover {
        background-color: #0000000f;
      }
    }
  }
}

.importPropertyUnitModal {
  .spaceImport {
    width: 100%;
    margin-top: 20px;
    .buttonImportPropertyUnit {
      margin: 10px 0px;
    }
    .ant-upload-list-item-name {
      color: #1677ff;
    }
  }

  .ant-tooltip-content {
    .ant-tooltip-inner {
      display: none;
    }
  }
  .ant-space-item {
    .ant-upload-wrapper {
      .ant-upload-list {
        .ant-upload-list-item {
          border-radius: 8px;
          margin-bottom: 20px;
        }
      }
    }
  }
}

.modalAddRoleProject {
  .ant-modal-content {
    padding: 0px;
  }
  .listRole {
    max-height: 300px;
    overflow: auto;
  }
}

.ModalFormFolder {
  .ant-modal-title {
    font-size: 20px;
    line-height: 28px;
  }
}
