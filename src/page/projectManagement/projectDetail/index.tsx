import './styles.scss';
import { <PERSON>ton, Spin, Tabs, TabsProps, Typography } from 'antd';
import { BookOutlined } from '@ant-design/icons';
import BlockInfo from './component/blockDetailTab';
import GeneralTab from './component/generalTab';
import BreadCrumbComponent from '../../../components/breadCrumb';
import { useLocation, useParams, Location, useSearchParams } from 'react-router-dom';
import { useFetch } from '../../../hooks';
import { DetailProject } from '../../../types/project/project';
import { getDetailProject } from '../../../service/project';
import { OPTIONS_PROJECT_TYPE } from '../../../constants/common';
import FPTLogo from '../../../../src/assets/images/Default_Logo_Project.png';
import SellProgram from './component/sellProgramTab';
import PropertyUnit from '../../operateSellProgram/propertyOperateSellProgram/component';
import Document from './component/document';
import OrgchartTab from './component/orgchartTab';
import { useState } from 'react';
import Role from './component/role';
import FormReportTab from './component/formReportTab';

const { Title, Text } = Typography;

const ProjectDetail = () => {
  const id = useParams();
  const idProject = id?.id;
  const location = useLocation() as Location & { state?: { activeTab?: string } };

  const { data: dataDetailProjects, isLoading } = useFetch<DetailProject>({
    queryKeyArr: ['detail-project', idProject],
    api: () => getDetailProject(idProject),
    withFilter: false,
    enabled: !!id,
    cacheTime: 10,
  });
  const dataDetailProject = dataDetailProjects?.data?.data;
  const isButtonDisabled = !(dataDetailProject?.urlEsalekit && dataDetailProject?.esalekit);
  const [searchParams, setSearchParams] = useSearchParams();

  const [activeTab, setActiveTab] = useState(location.state?.activeTab || searchParams.get('tab') || '1');
  const handleTabChange = (key: string) => {
    setActiveTab(key);
    setSearchParams({ tab: key });
  };

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: 'Thông tin chung',
      children: <GeneralTab data={dataDetailProject} />,
    },
    {
      key: '2',
      label: 'Thông tin Block',
      children: <BlockInfo />,
    },
    {
      key: '3',
      label: 'Đơn vị bán hàng',
      children: <OrgchartTab data={dataDetailProject} />,
    },
    {
      key: '4',
      label: 'Chương trình bán hàng',
      children: <SellProgram dataProject={dataDetailProject} />,
    },
    {
      key: '5',
      label: 'Bảng hàng',
      children: <PropertyUnit dataProject={dataDetailProject} />,
    },
    {
      key: '6',
      label: 'Tài liệu dự án',
      children: <Document />,
    },
    {
      key: '7',
      label: 'Cấu hình',
      children: '',
    },
    {
      key: '8',
      label: 'Vai trò dự án',
      children: <Role />,
    },
    {
      key: '9',
      label: 'Biểu mẫu',
      children: <FormReportTab dataProject={dataDetailProject} />,
    },
  ];

  return (
    <Spin spinning={isLoading}>
      <BreadCrumbComponent titleBread={`${dataDetailProject?.name}`} />
      <div className="project-card">
        <div className="project-info">
          <Title level={5}>{dataDetailProject?.name}</Title>
          <Text type="secondary">
            Loại hình:{' '}
            <span className="text-type">
              {dataDetailProject?.type
                ?.split(',')
                .map(typeValue => OPTIONS_PROJECT_TYPE.find(option => option.value === typeValue)?.label)
                .filter(Boolean)
                .join(', ')}
            </span>
          </Text>
        </div>
        <div className="project-actions">
          <Button type="link" icon={<BookOutlined />} disabled={isButtonDisabled}>
            E - Sale kit
          </Button>
        </div>
        <div className="project-image">
          <img
            src={
              dataDetailProject?.imageUrl
                ? `${import.meta.env.VITE_S3_IMAGE_URL}/${dataDetailProject?.imageUrl}`
                : FPTLogo
            }
            alt="Project"
          />
        </div>
      </div>
      <div className="project_detail-page">
        <Tabs activeKey={activeTab} onChange={handleTabChange} items={items} tabPosition="top" />
      </div>
    </Spin>
  );
};

export default ProjectDetail;
