import React, { useState } from 'react';
import { Tag, Input } from 'antd';
import { PlusOutlined } from '@ant-design/icons';

interface EditableTagProps {
  label: string;
  idRow: string;
  updateMasterBlock: (idRow: string, value: string, label: string) => void;
  // setIsUpdateState: (value: boolean) => void;
}

const AddBlock: React.FC<EditableTagProps> = ({ label, idRow, updateMasterBlock }) => {
  const [editing, setEditing] = useState(false);
  const [value, setValue] = useState('');

  const stopEditing = () => {
    setEditing(false);
    updateMasterBlock(idRow, value.trim(), label);
    if (value) {
      // setIsUpdateState(true);
    }
    setValue('');
  };

  const tagName = label === 'rooms' ? `Tạo thêm phòng` : `Tạo thêm tầng`;
  const placeholde = label === 'rooms' ? `Nhập tên phòng` : `Nhập tên tầng`;
  return (
    <div>
      {editing ? (
        <Input
          value={value}
          onChange={e => setValue(e.target.value)}
          onBlur={stopEditing}
          onPressEnter={stopEditing}
          placeholder={placeholde}
          autoFocus
          style={{ width: '130px', height: '22px', marginRight: '8px' }}
        />
      ) : (
        <Tag
          icon={<PlusOutlined />}
          onClick={() => setEditing(!editing)}
          style={{
            cursor: 'pointer',
            width: '130px',
            height: '22px',
            border: '1px dashed #d9d9d9',
            background: 'transparent',
            padding: '0',
            textAlign: 'center',
            marginInlineEnd: '0',
          }}
          className="site-tag-plus"
        >
          {tagName}
        </Tag>
      )}
    </div>
  );
};
export default AddBlock;
