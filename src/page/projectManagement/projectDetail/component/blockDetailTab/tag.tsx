import React, { useState } from 'react';
import { Input } from 'antd';
import { CloseOutlined } from '@ant-design/icons';

interface EditableTagProps {
  children: string;
  tagKey: string;
  onEdit: (tagKey: string, idRow: string, newName: string, label: string) => void;
  onRemove: (tagKey: string, idRow: string) => void;
  idRow: string;
  label: string;
  value: string;
}

const EditTag: React.FC<EditableTagProps> = ({ children, tagKey, onEdit, onRemove, idRow, value, label }) => {
  const [valueTags, setValueTags] = useState(value);

  const stopEditing = (_value: string) => {
    if (!_value.trim()) {
      setValueTags(value.trim());
      return;
    }
    if (_value.trim() !== children) {
      onEdit(tagKey, idRow, _value.trim(), label);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    if (value.trim().length > 15) return;
    setValueTags(value);
  };

  const handleRemove = () => {
    onRemove(tagKey, idRow);
  };

  return (
    <div className="edit-block">
      <Input
        onBlur={e => {
          stopEditing(e.target.value);
        }}
        onPressEnter={e => {
          stopEditing(e.currentTarget.value);
        }}
        maxLength={15}
        onChange={handleInputChange}
        autoFocus
        value={valueTags}
        suffix={<CloseOutlined onClick={handleRemove} style={{ color: 'rgba(0,0,0,.45)', fontSize: '10px' }} />}
        className="input-edit-block"
      />
    </div>
  );
};

export default EditTag;
