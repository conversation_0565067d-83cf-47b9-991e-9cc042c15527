import { CloseOutlined, PlusOutlined } from '@ant-design/icons';
import { <PERSON>ton, Card, Col, Form, FormInstance, Input, InputNumber, Row, Select, Switch } from 'antd';
import dayjs from 'dayjs';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import ButtonOfPageDetail from '../../../../../components/button/buttonOfPageDetail';
import MyDatePicker from '../../../../../components/datePicker';
import TagInput from '../../../../../components/input/tagInput/TagInput';
import SearchableInfiniteScrollSelect from '../../../../../components/select/SelectScroll';
import SelectAddress from '../../../../../components/selectAddress';
import FormUploadImage from '../../../../../components/upload/FormUploadImage';
import { PROJECTS_MANAGEMENT } from '../../../../../configs/path';
import {
  OPTIONS_CONSTRUCTION_PROGRESS_PROJECT,
  OPTIONS_PROJECT_TYPE,
  OPTIONS_STATUS_PROJECT,
} from '../../../../../constants/common';
import { useFetch, useUpdateField } from '../../../../../hooks';
import { getBanks } from '../../../../../service/bank';
import { getListInvestor } from '../../../../../service/investor';
import { updateProject } from '../../../../../service/project';
import { DetailProject, IBankProject, NumericValue } from '../../../../../types/project/project';
import { formatNumber, parseNumericField } from '../../../../../utilities/regex';
import { InvestorType } from '../../../projectCreate';
import './index.scss';

interface Bank {
  bankCode: string;
  bankName: string;
}

interface CommonFormProps {
  data?: DetailProject;
  onFormInstanceChange?: (formInstance: FormInstance) => void;
}

const GeneralTab: React.FC<CommonFormProps> = ({ data }) => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const [investorOptions, setInvestorOptions] = useState<InvestorType[]>([]);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [page, setPage] = useState(1);
  const [selectedDate, setSelectedDate] = useState<dayjs.Dayjs | null>(null);
  const [isFormChanged, setIsFormChanged] = useState<boolean>(false);

  const {
    data: dataInvestors,
    isFetching,
    isLoading: isLoadingInvestor,
  } = useFetch<InvestorType[]>({
    queryKeyArrWithFilter: ['investor-list', page, searchTerm],
    api: getListInvestor,
    moreParams: { page: page, search: searchTerm },
  });
  const { data: dataBanks, isLoading } = useFetch<Bank[]>({
    queryKeyArrWithFilter: ['get-list-banks'],
    api: getBanks,
    moreParams: { branchIsActvie: true },
  });
  const onUpdateTabGeneralProject = useUpdateField({
    keyOfListQuery: ['detail-project'],
    messageSuccess: 'Chỉnh sửa thông tin thành công!',
    apiQuery: updateProject,
    path: PROJECTS_MANAGEMENT,
    isMessageError: false,
  });

  const totalPages = dataInvestors?.data?.data?.totalPages || 0;
  const listBank = dataBanks?.data?.data || [];

  const listInvestor = useMemo(() => {
    return dataInvestors?.data?.data?.rows || [];
  }, [dataInvestors]);

  useEffect(() => {
    setInvestorOptions(prev => {
      const newItems = listInvestor.filter(item => !prev.some(existing => existing.id === item.id));
      return [...prev, ...newItems];
    });
  }, [listInvestor]);

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setPage(1);
    setInvestorOptions([]);
  };

  const handleSelectChange = (value: { value: string; label: string } | null) => {
    form.setFieldsValue({
      investor: {
        id: value ? value.value : null,
      },
    });
    if (!value) {
      setSearchTerm('');
      form.setFieldsValue({
        investor: null,
      });
    }
  };

  const handleLoadMore = useCallback(() => {
    setPage(prev => prev + 1);
  }, []);

  const handleDateChange = (date: dayjs.Dayjs | null) => {
    setSelectedDate(date);
    form.setFieldsValue({ expectedDate: date });
  };

  const handleCancel = () => {
    if (isFormChanged) {
      navigate(PROJECTS_MANAGEMENT);
    } else {
      navigate(PROJECTS_MANAGEMENT);
    }
  };

  // Cập nhật value vào form
  useEffect(() => {
    if (data) {
      const numericFields = Object.keys(data)
        .filter(key => /Area|Percent/.test(key))
        .reduce<Record<string, number | undefined>>(
          (acc, key) => ({
            ...acc,
            [key]: parseNumericField(data[key as keyof DetailProject] as NumericValue),
          }),
          {},
        );
      const investorValue = data.investor
        ? {
            value: data.investor.id,
            label: data.investor.name,
          }
        : undefined;

      form.setFieldsValue({
        ...data,
        ...numericFields,
        type: data.type.split(','),
        expectedDate: data?.expectedDate ? dayjs(data?.expectedDate) : null,
        investor: investorValue,
        addressObject: data?.addressObject,
        banks:
          (data.banks?.length ?? 0) > 0
            ? (data.banks ?? []).map(bank => ({
                bankCode: bank.code,
                accountNumber: bank.accountNumber,
              }))
            : [{ bankCode: undefined, accountNumber: '' }],
      });
    }
  }, [data, form]);

  const handleSubmitGeneral = async (value: DetailProject) => {
    const transformedValues = {
      ...value,
      id: data?.id || '',
      investor: { id: value?.investor?.id || data?.investor?.id || '' },
      numOfBlocks: Number(value.numOfBlocks) || null,
      numberOfPropertys: Number(value.numberOfPropertys) || null,
      banks: value.banks?.map((item: IBankProject) => {
        return {
          code: item.bankCode || '',
          name: listBank.find(bank => bank.bankCode === item.bankCode)?.bankName || '',
          accountNumber: item.accountNumber,
        };
      }),
    };
    const res = await onUpdateTabGeneralProject.mutateAsync(transformedValues);
    const statusCode = res?.data?.statusCode;
    if (statusCode === '0') {
      navigate(PROJECTS_MANAGEMENT);
    }
  };
  return (
    <>
      <Form form={form} layout="vertical" onFinish={handleSubmitGeneral} onValuesChange={() => setIsFormChanged(true)}>
        <Row gutter={126}>
          <Col xs={24} lg={12}>
            <Row gutter={[12, 12]}>
              <Col xs={24} md={12} lg={12}>
                <Form.Item
                  label="Loại dự án"
                  name="type"
                  required
                  rules={[{ required: true, message: 'Vui lòng chọn loại dự án' }]}
                >
                  <Select
                    options={OPTIONS_PROJECT_TYPE.map(item => {
                      return {
                        value: item.value,
                        label: item.label,
                      };
                    })}
                    allowClear
                    placeholder="Loại dự án"
                    mode="multiple"
                    showSearch={false}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12} lg={12}>
                <Form.Item
                  label="Trạng thái"
                  name="status"
                  rules={[{ required: true, message: 'Vui lòng chọn trạng thái' }]}
                >
                  <Select
                    disabled={data?.source !== 'CRM'}
                    placeholder="Trạng thái"
                    options={OPTIONS_STATUS_PROJECT.map(item => {
                      return {
                        value: item.value,
                        label: item.label,
                      };
                    })}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12} lg={12}>
                <Form.Item label="Nguồn" name="source">
                  <Input disabled value="CRM" defaultValue={'CRM'} />
                </Form.Item>
              </Col>
              <Col xs={24} md={12} lg={12}>
                <Form.Item
                  label="Tiến độ xây dựng"
                  name="currentStatus"
                  rules={[{ required: true, message: 'Vui lòng chọn tiến độ xây dựng' }]}
                >
                  <Select
                    options={OPTIONS_CONSTRUCTION_PROGRESS_PROJECT.map(item => {
                      return {
                        value: item.value,
                        label: item.label,
                      };
                    })}
                    allowClear
                    placeholder="Tiến độ xây dựng"
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={16} lg={16}>
                <Form.Item
                  label="Tên dự án"
                  name="name"
                  required
                  rules={[
                    {
                      validator: (_, value) => {
                        if (!value || value.trim() === '') {
                          return Promise.reject(new Error('Vui lòng nhập tên dự án'));
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <Input
                    disabled={data?.source !== 'CRM'}
                    placeholder="Tên dự án"
                    maxLength={50}
                    onBlur={e => {
                      form.setFieldsValue({ name: e.target.value.trim() });
                    }}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={8} lg={8}>
                <Form.Item label="Mã dự án" name="code" rules={[{ required: true, message: 'Vui lòng nhập mã dự án' }]}>
                  <Input placeholder="Mã dự án" disabled />
                </Form.Item>
              </Col>
              <Col xs={24} md={12} lg={12}>
                <Form.Item label="Tên ngắn" name="shortName">
                  <Input placeholder="Nhập tên ngắn" maxLength={50} />
                </Form.Item>
              </Col>
              <Col xs={24} md={12} lg={12}>
                <Form.Item label="Tên thương mại" name="namePublic">
                  <Input placeholder="Nhập tên thương mại" maxLength={150} />
                </Form.Item>
              </Col>
              <Col xs={24} md={12} lg={12}>
                <Form.Item
                  label="Chủ đầu tư"
                  name="investor"
                  rules={[{ required: true, message: 'Vui lòng chọn chủ đầu tư' }]}
                >
                  <SearchableInfiniteScrollSelect
                    options={investorOptions?.map(item => {
                      return {
                        value: item.id,
                        label: item.name,
                      };
                    })}
                    onLoadMore={handleLoadMore}
                    onSearch={handleSearch}
                    isLoading={isLoadingInvestor}
                    hasMore={page < totalPages}
                    placeholder="Chọn chủ đầu tư"
                    allowClear
                    onChange={handleSelectChange}
                    isFetching={isFetching}
                    labelInValue
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12} lg={12}>
                <Form.Item label="Tên pháp lý" name="legalName">
                  <Input placeholder="Nhập tên pháp lý" maxLength={150} />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item label="Tài khoản giao dịch" required>
                  <Card className="border-account-transactions">
                    <Form.List name="banks">
                      {(fields, { add, remove }) => (
                        <>
                          {fields.map(({ key, name, ...restField }) => (
                            <Row align="middle" gutter={16} key={key}>
                              <Col span={11}>
                                <Form.Item
                                  {...restField}
                                  name={[name, 'bankCode']}
                                  label="Ngân hàng"
                                  rules={[{ required: true, message: 'Vui lòng chọn ngân hàng' }]}
                                >
                                  <Select
                                    placeholder="Chọn ngân hàng"
                                    allowClear
                                    filterOption={(input, option) =>
                                      typeof option?.label === 'string'
                                        ? option.label.toLowerCase().includes(input.toLowerCase())
                                        : false
                                    }
                                    showSearch
                                    loading={isLoading}
                                    options={listBank.map(item => ({
                                      value: item.bankCode,
                                      label: item.bankName,
                                    }))}
                                  />
                                </Form.Item>
                              </Col>
                              <Col span={11}>
                                <Form.Item
                                  {...restField}
                                  name={[name, 'accountNumber']}
                                  label="Số tài khoản"
                                  rules={[{ required: true, message: 'Vui lòng nhập số tài khoản' }]}
                                  normalize={value => value?.trim()}
                                >
                                  <Input placeholder="Nhập số tài khoản" maxLength={50} />
                                </Form.Item>
                              </Col>
                              <Col span={2}>
                                {fields.length > 1 && (
                                  <CloseOutlined className="icon-close-bank" onClick={() => remove(name)} />
                                )}
                              </Col>
                            </Row>
                          ))}
                          <Col span={22}>
                            <Form.Item>
                              <Button
                                type="dashed"
                                onClick={() => add({ bankCode: undefined, accountNumber: '' })}
                                block
                                icon={<PlusOutlined />}
                              >
                                Thêm tài khoản giao dịch
                              </Button>
                            </Form.Item>
                          </Col>
                        </>
                      )}
                    </Form.List>
                  </Card>
                </Form.Item>
              </Col>
              <Col xs={24} md={12} lg={12}>
                <Form.Item label="Đơn vị thiết kế" name="designUnit">
                  <Input placeholder="Nhập đơn vị thiết kế" maxLength={150} />
                </Form.Item>
              </Col>
              <Col xs={24} md={12} lg={12}>
                <Form.Item label="Đơn vị xây dựng" name="builidingUnit">
                  <Input placeholder="Nhập đơn vị xây dựng" maxLength={150} />
                </Form.Item>
              </Col>
              <Col xs={24} md={24} lg={24}>
                <Form.Item label="Địa chỉ" name="addressObject">
                  <SelectAddress parentName="addressObject" address={form.getFieldValue('addressObject')} />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item name="address">
                  <Input placeholder="Nhập địa chỉ chi tiết" maxLength={255} />
                </Form.Item>
              </Col>
              <Col xs={24} md={12} lg={12}>
                <Form.Item label="Link dự án" name="urlProject">
                  <Input placeholder="Nhập link dự án" maxLength={250} />
                </Form.Item>
              </Col>
              <Col xs={24} md={12} lg={12}>
                <Form.Item label="Số lượng tòa/ Phân khu" name="numOfBlocks">
                  <InputNumber
                    placeholder="Nhập số lượng tòa/ phân khu"
                    formatter={formatNumber}
                    maxLength={3}
                    min={0}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12} lg={12}>
                <Form.Item label="Số lượng căn hộ" name="numberOfPropertys">
                  <InputNumber placeholder="Nhập số lượng căn hộ" formatter={formatNumber} maxLength={5} min={0} />
                </Form.Item>
              </Col>
              <Col xs={24} md={12} lg={12}>
                <Form.Item label="Ngày bàn giao dự kiến" name="expectedDate">
                  <MyDatePicker
                    placeholder="Chọn ngày bàn giao dự kiến"
                    onDateChange={handleDateChange}
                    value={selectedDate}
                    disabledDate={current => current && current < dayjs().startOf('day')}
                  />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item layout="horizontal" label="Hiển thị E-Sale Kit " name="esalekit">
                  <Switch style={{ marginLeft: 95 }} />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item label="Link E-Sale Kit" name="urlEsalekit">
                  <Input placeholder="Nhập link E-Sale Kit" maxLength={250} />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item layout="horizontal" label="Hiển thị Mobile " name="displayMobile">
                  <Switch style={{ marginLeft: 110 }} />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item label="Tiện ích nội khu" name="innerUtility">
                  <TagInput
                    value={form.getFieldValue('innerUtility') || []}
                    onChange={newTags => form.setFieldsValue({ innerUtility: newTags })}
                    placeholder="Thêm tiện ích nội khu"
                  />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item label="Tiện ích ngoại khu" name="outerUtility">
                  <TagInput
                    value={form.getFieldValue('outerUtility') || []}
                    onChange={newTags => form.setFieldsValue({ outerUtility: newTags })}
                    placeholder="Thêm tiện ích ngoại khu"
                  />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item label="Ưu điểm nổi bật" name="outstandingAdv">
                  <TagInput
                    value={form.getFieldValue('outstandingAdv') || []}
                    onChange={newTags => form.setFieldsValue({ outstandingAdv: newTags })}
                    placeholder="Thêm ưu điểm nổi bật"
                  />
                </Form.Item>
              </Col>
              <Col>
                <FormUploadImage
                  path="project"
                  defaultImage={data?.imageUrl}
                  reset={false}
                  fieldName={'imageUrl'}
                  label="Hình ảnh dự án"
                  fileSize={2}
                  textType="Upload ảnh tỉ lệ 16:9, Tối đa 2 MB"
                />
              </Col>
            </Row>
          </Col>
          <Col xs={24} lg={12}>
            <Row
              style={{
                marginBottom: 40,
              }}
            >
              <Col span={24}>
                <Form.Item name="description" label="Mô tả dự án">
                  <Input.TextArea placeholder="Textarea" style={{ height: 236 }} maxLength={255} />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col xs={24} md={12} lg={12}>
                <Form.Item name="projectArea" label="Diện tích dự án (m2)">
                  <InputNumber min={0} placeholder="Nhập diện tích dự án" formatter={formatNumber} maxLength={20} />
                </Form.Item>
              </Col>
              <Col xs={24} md={12} lg={12}>
                <Form.Item name="constructionArea" label="Diện tích xây dựng (m2)">
                  <InputNumber min={0} placeholder="Nhập diện tích xây dựng" formatter={formatNumber} maxLength={20} />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col xs={24} md={12} lg={12}>
                <Form.Item name="constructionPercent" label="Mật độ xây dựng (%)">
                  <InputNumber min={0} placeholder="Nhập mật độ xây dựng" formatter={formatNumber} maxLength={10} />
                </Form.Item>
              </Col>
              <Col xs={24} md={12} lg={12}>
                <Form.Item name="constructionFloorArea" label="Diện tích sàn xây dựng (m2)">
                  <InputNumber
                    min={0}
                    placeholder="Nhập diện tích sàn xây dựng"
                    formatter={formatNumber}
                    maxLength={20}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col xs={24} md={12} lg={12}>
                <Form.Item name="parkArea" label="Diện tích công viên (m2)">
                  <InputNumber min={0} placeholder="Nhập diện tích công viên" formatter={formatNumber} maxLength={20} />
                </Form.Item>
              </Col>
              <Col xs={24} md={12} lg={12}>
                <Form.Item name="greenPercent" label="Mật độ phủ xanh (%)">
                  <InputNumber min={0} placeholder="Nhập mật độ phủ xanh" formatter={formatNumber} maxLength={10} />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col xs={24} md={12} lg={12}>
                <Form.Item name="greenAreaOnBuilding" label="Diện tích cây xanh công trình (m2)">
                  <InputNumber
                    min={0}
                    placeholder="Nhập diện tích cây xanh công trình"
                    formatter={formatNumber}
                    maxLength={20}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12} lg={12}>
                <Form.Item name="trafficArea" label="Diện tích giao thông (m2)">
                  <InputNumber
                    min={0}
                    placeholder="Nhập diện tích giao thông"
                    formatter={formatNumber}
                    maxLength={20}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col xs={24} md={12} lg={12}>
                <Form.Item name="amountRegistration" label="Số tiền đăng ký">
                  <InputNumber min={0} placeholder="Nhập số tiền đăng ký" formatter={formatNumber} maxLength={15} />
                </Form.Item>
              </Col>
            </Row>
          </Col>
        </Row>
      </Form>
      <ButtonOfPageDetail handleSubmit={() => form.submit()} handleCancel={handleCancel} isShowModal={isFormChanged} />
    </>
  );
};

export default GeneralTab;
