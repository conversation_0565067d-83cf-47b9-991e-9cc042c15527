import {
  But<PERSON>,
  Col,
  Dropdown,
  Flex,
  Form,
  FormProps,
  Input,
  Typography,
  Modal,
  Row,
  TableColumnsType,
  Upload,
} from 'antd';
import Breadcrumb, { ItemType } from 'antd/es/breadcrumb/Breadcrumb';
import React, { useCallback, useMemo } from 'react';
import { FolderOutlined, FileTextOutlined, PlusOutlined, UploadOutlined, MoreOutlined } from '@ant-design/icons';
import { IDocumentProject, IFormDocumentProject, IitemDocumentProject } from '../../../../../types/project/project';
import { useParams } from 'react-router-dom';
import { useCreateField, useDeleteField, useFetch, useUpdateField } from '../../../../../hooks';
import {
  addItemsProjectDocument,
  addProjectDocument,
  deleteItemProjectDocument,
  deleteProjectDocument,
  getDocumentProjectItems,
  getProjectDocument,
  updateProjectDocument,
} from '../../../../../service/project';
import TableComponent from '../../../../../components/table';
import { uploadMedia } from '../../../../../service/upload';
import { RcFile } from 'antd/es/upload';
import { AxiosError } from 'axios';

const { confirm } = Modal;
interface IItemTypeBreadcrumb extends ItemType {
  label?: string;
}

const Document: React.FC = () => {
  const { id: projectID } = useParams<{ id: string }>();
  const [form] = Form.useForm();
  const [isFormChanged, setIsFormChanged] = React.useState<boolean>(false);
  const [stateShowModal, setStateShowModal] = React.useState<boolean>(false);
  const [stateIdIteamSelect, setStateIdIteamSelect] = React.useState<string>('');
  const [stateIsLoadingUpload, setStateIsLoadingUpload] = React.useState<boolean>(false);
  const [stateBreadcrumb, setStateBreadcrumb] = React.useState<IItemTypeBreadcrumb[]>([
    {
      title: 'Danh sách tài liệu dự án',
    },
  ]);

  const {
    data: dataProjectDocument,
    isFetching,
    refetch: refetchProjectDocument,
  } = useFetch<IDocumentProject[]>({
    queryKeyArr: ['getDocumentProject', projectID],
    api: getProjectDocument,
    moreParams: { projectID },
  });

  const { data: dataProjectDocumentItems, isFetching: isFetchingProjectDocumentItems } = useFetch<IDocumentProject>({
    queryKeyArr: ['getDocumentProjectItems', stateIdIteamSelect],
    api: getDocumentProjectItems,
    moreParams: { documentId: stateIdIteamSelect },
    enabled: !!stateIdIteamSelect,
  });

  const listProjectDocumentItems = useMemo(() => {
    const data = dataProjectDocumentItems?.data.data;
    const items = data?.items || ([] as IitemDocumentProject[]);
    return items.map(item => ({
      ...item,
      type: 'file',
    }));
  }, [dataProjectDocumentItems]);

  const listProjectDocument = useMemo(() => {
    return dataProjectDocument?.data?.data || [];
  }, [dataProjectDocument]);

  const { mutateAsync: handleCreateDocumentProject, isPending: isLoadingCreate } = useCreateField({
    apiQuery: addProjectDocument,
    keyOfDetailQuery: ['getDocumentProject', projectID],
    label: 'thư mục',
    isMessageError: false,
  });

  const { mutateAsync: handleCreateFolderDocumentProject } = useCreateField({
    apiQuery: addProjectDocument,
    keyOfDetailQuery: ['getDocumentProject', projectID],
    // label: 'tài liệu',
    isMessageError: false,
    messageSuccess: 'Tải nhập tài liệu thành công',
  });

  const { mutateAsync: handleUpdateDocumentProject, isPending: isLoadingUpdate } = useUpdateField({
    apiQuery: updateProjectDocument,
    keyOfDetailQuery: ['getDocumentProject', projectID],
    label: 'tài liệu',
    // label: 'thư mục',
    messageSuccess: 'Đổi tên thành công!',
    isMessageError: false,
  });

  const { mutateAsync: handleAddItemsDocumentProject } = useUpdateField({
    apiQuery: addItemsProjectDocument,
    keyOfDetailQuery: ['getDocumentProjectItems', stateIdIteamSelect],
    // label: 'tài liệu',
    isMessageError: false,
    messageSuccess: 'Tải nhập tài liệu thành công',
    isMessageSuccess: true,
  });

  const { mutateAsync: handleDeleteDocumentProject, isPending: isLoadingDelete } = useDeleteField({
    apiQuery: deleteProjectDocument,
    keyOfDetailQuery: ['getDocumentProject', projectID],
    label: 'tài liệu',
  });

  const { mutateAsync: handleDeleteFileDocumentProject } = useDeleteField({
    apiQuery: deleteProjectDocument,
    keyOfDetailQuery: ['getDocumentProject', projectID],
    label: 'tài liệu',
  });

  const { mutateAsync: handleDeleteItemDocumentProject } = useDeleteField({
    apiQuery: deleteItemProjectDocument,
    keyOfDetailQuery: ['getDocumentProjectItems', stateIdIteamSelect],
    label: 'tài liệu',
  });

  const handleConfirmDeleteDocumentProject = async (id: string, type: string) => {
    try {
      if (stateIdIteamSelect)
        return await handleDeleteItemDocumentProject({
          documentId: stateIdIteamSelect,
          itemId: id,
        });
      if (type === 'file') return await handleDeleteFileDocumentProject({ id });
      return await handleDeleteDocumentProject({ id });
    } catch (error) {
      console.log('');
    } finally {
      Modal.destroyAll();
    }
  };

  const handleViewDetailDocumentProject = async (item: IitemDocumentProject) => {
    setStateIdIteamSelect(item.id);
    setStateBreadcrumb([
      {
        title: <span style={{ cursor: 'pointer' }}>Danh sách tài liệu dự án</span>,
        onClick: () => {
          setStateBreadcrumb([
            {
              title: 'Danh sách tài liệu dự án',
            },
          ]);
          setStateIdIteamSelect('');
          refetchProjectDocument();
        },
      },
      {
        title: (
          <>
            <FolderOutlined />
            <span> {item.name}</span>
          </>
        ),
        label: item.name,
      },
    ]);
  };

  const downloadFile = (path: string, fileName: string) => {
    const link = document.createElement('a');
    link.href = `${import.meta.env.VITE_S3_IMAGE_URL}/${path}`;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const formatFileSize = (size: number) => {
    if (size >= 1024 * 1024 * 1024) {
      return `${(size / (1024 * 1024 * 1024)).toFixed(1)} GB`;
    }
    if (size >= 1024 * 1024) {
      return `${(size / (1024 * 1024)).toFixed(1)} MB`;
    }
    if (size >= 1024) {
      return `${(size / 1024).toFixed(1)} KB`;
    }
    return `${size} B`; // Nếu file rất nhỏ (< 1KB)
  };

  const columns: TableColumnsType<IDocumentProject> = [
    {
      title: '',
      dataIndex: 'name',
      key: 'name',
      width: '60%',
      render: (text: string, record) => (
        <Flex align="center">
          {record.type === 'folder' && (
            <div
              style={{
                width: '32px',
                height: '32px',
                display: 'flex',
                alignItems: 'center',
                border: '1px solid #d9d9d9',
                justifyContent: 'center',
                borderRadius: '2px',
              }}
            >
              <FolderOutlined style={{ fontSize: '16px' }} />
            </div>
          )}

          {record.type === 'file' && (
            <div
              style={{
                width: '32px',
                height: '32px',
                display: 'flex',
                alignItems: 'center',
                border: '1px solid #d9d9d9',
                justifyContent: 'center',
                borderRadius: '2px',
              }}
            >
              <FileTextOutlined style={{ fontSize: '16px' }} />
            </div>
          )}

          <span style={{ marginLeft: '10px' }}>{text}</span>
        </Flex>
      ),
    },
    {
      title: 'Dung lượng',
      dataIndex: 'sizeFile',
      key: 'sizeFile',
      width: '20%',
      align: 'center',
      render: (text: number, record) => {
        let sizeFile = text; // Mặc định lấy size từ dataIndex

        // Nếu là folder, tính tổng dung lượng của tất cả items
        if (record.type === 'folder') {
          sizeFile = record.items.reduce(
            (total: number, item: IFormDocumentProject) => total + (item['sizeFile'] ? item['sizeFile'] : 0) || 0,
            0,
          );
        }

        return sizeFile > 0 ? <Typography.Text>{formatFileSize(sizeFile)}</Typography.Text> : <></>;
      },
    },
    {
      title: 'Hành động',
      dataIndex: 'address',
      key: 'address',
      width: '20%',
      align: 'center',
      render: (_, record) => {
        let items = [
          {
            label: 'Xem chi tiết',
            key: 1,
            onClick: () => handleViewDetailDocumentProject(record),
          },
          {
            label: 'Đổi tên',
            key: 2,
            onClick: () => {
              form.setFieldsValue({
                name: record.name,
                id: record.id,
              });
              setStateShowModal(true);
            },
          },
          {
            label: 'Xoá',
            key: 3,
            onClick: async () => {
              confirm({
                title: 'Xác nhận xoá tài liệu',
                content: (
                  <>
                    <Row>
                      <Col span={24}>
                        <Typography.Text>
                          Tài liệu đã xoá và sẽ không thể phục hồi, bạn có chắc chắn muốn xoá không?
                        </Typography.Text>
                      </Col>
                    </Row>
                    <Row>
                      <Col span={24}>
                        <Flex gap="small" justify="end" style={{ marginTop: '16px' }}>
                          <Button
                            onClick={() => {
                              Modal.destroyAll();
                            }}
                            type="primary"
                          >
                            Hủy
                          </Button>
                          <Button
                            onClick={() => {
                              handleConfirmDeleteDocumentProject(record.id, record.type);
                            }}
                            loading={isLoadingDelete}
                          >
                            Xoá tài liệu
                          </Button>
                        </Flex>
                      </Col>
                    </Row>
                  </>
                ),
                footer: null,
              });
            },
          },
        ];
        if (record.type === 'file') {
          items = [
            {
              label: 'Tải xuống',
              key: 1,
              onClick: () => downloadFile(record.path, record.name),
            },
            {
              label: 'Xoá',
              key: 3,
              onClick: async () => {
                confirm({
                  title: 'Xác nhận xoá tài liệu',
                  content: (
                    <>
                      <Row>
                        <Col span={24}>
                          <Typography.Text>
                            Tài liệu đã xoá và sẽ không thể phục hồi, bạn có chắc chắn muốn xoá không?
                          </Typography.Text>
                        </Col>
                      </Row>
                      <Row>
                        <Col span={24}>
                          <Flex gap="small" justify="end" style={{ marginTop: '16px' }}>
                            <Button
                              onClick={() => {
                                Modal.destroyAll();
                              }}
                              type="primary"
                            >
                              Hủy
                            </Button>
                            <Button
                              onClick={() => {
                                handleConfirmDeleteDocumentProject(record.id, record.type);
                              }}
                              loading={isLoadingDelete}
                            >
                              Xoá tài liệu
                            </Button>
                          </Flex>
                        </Col>
                      </Row>
                    </>
                  ),
                  footer: null,
                });
              },
            },
          ];
        }
        return (
          <Dropdown
            menu={{
              items,
            }}
            placement="bottomRight"
            overlayStyle={{ width: 150 }}
          >
            <Button type="text" style={{ padding: '4px 8px' }}>
              <MoreOutlined style={{ fontSize: '20px', cursor: 'pointer' }} />
            </Button>
          </Dropdown>
        );
      },
    },
  ];

  const onFinish: FormProps<IFormDocumentProject>['onFinish'] = async values => {
    try {
      if (!values.name?.trim()) {
        return form.setFields([
          {
            name: 'name',
            errors: ['Vui lòng nhập tên thư mục'],
          },
        ]);
      }
      const params = {
        ...values,
        projectID,
        type: 'folder',
      };
      if (!values.id) {
        setStateShowModal(!stateShowModal);
        setIsFormChanged(false);
        form.resetFields();
        return await handleCreateDocumentProject(params);
      } else {
        setStateShowModal(!stateShowModal);
        setIsFormChanged(false);
        form.resetFields();
        return await handleUpdateDocumentProject(params);
      }
    } catch (error) {
      console.log('');
    }
  };

  const _confirm = () => {
    confirm({
      title: 'Không thể upload file',
      icon: null,
      content: 'Kích thước file vượt quá giới hạn 100MB',
      footer: [
        <Flex style={{ marginTop: '10px' }} gap="small" justify="end">
          <Button
            key="submit"
            type="primary"
            size="small"
            onClick={() => {
              Modal.destroyAll();
            }}
            style={{ width: '50%' }}
          >
            Đóng
          </Button>
        </Flex>,
      ],
    });
  };

  const handleCancel = useCallback(() => {
    if (isFormChanged) {
      Modal.confirm({
        title: 'Xác nhận hủy',
        content: 'Dữ liệu đang chưa được lưu, bạn có chắc muốn huỷ dữ liệu đang nhập không?',
        cancelText: 'Quay lại',
        okButtonProps: {
          type: 'default',
        },
        cancelButtonProps: {
          type: 'primary',
        },
        onOk: () => {
          setStateShowModal(false);
          setIsFormChanged(false);
          form.resetFields();
        },
      });
    } else {
      setStateShowModal(false);
      setIsFormChanged(false);
      form.resetFields();
    }
  }, [isFormChanged]);

  return (
    <div>
      <Row>
        <Col span={24}>
          <Breadcrumb items={stateBreadcrumb} />
        </Col>
      </Row>
      <Row>
        <Col span={24}>
          <Flex gap="small" wrap justify="flex-end" align="center">
            <Upload
              listType="picture"
              maxCount={1}
              showUploadList={false} // Ẩn danh sách file đã upload
              disabled={stateIsLoadingUpload}
              customRequest={async ({ file, onSuccess, onError }) => {
                try {
                  setStateIsLoadingUpload(true);
                  const fileData = file as RcFile;
                  const isLtFile = fileData.size / 1024 / 1024 > 100;
                  if (isLtFile) {
                    _confirm();
                    return;
                  }
                  const resp = await uploadMedia(
                    fileData,
                    stateBreadcrumb[1]
                      ? `${projectID}/projects-document/${stateBreadcrumb[1].label}`
                      : `${projectID}/projects-document`,
                  );
                  const data = resp.data.data;
                  if (stateIdIteamSelect)
                    return handleAddItemsDocumentProject({
                      name: fileData.name,
                      sizeFile: fileData.size,
                      path: data.key || data.Key,
                      id: stateIdIteamSelect,
                    });
                  await handleCreateFolderDocumentProject({
                    name: fileData.name,
                    projectID,
                    type: 'file',
                    sizeFile: fileData.size,
                    path: data.key || data.Key,
                  });
                  onSuccess?.('ok');
                } catch (error: unknown) {
                  onError?.(error as AxiosError);
                } finally {
                  setStateIsLoadingUpload(false);
                }
              }}
            >
              <Button loading={stateIsLoadingUpload} icon={<UploadOutlined />}>
                Tải nhập file
              </Button>
            </Upload>
            {!stateIdIteamSelect && (
              <Button
                onClick={() => {
                  setStateShowModal(!stateShowModal);
                }}
                icon={<PlusOutlined />}
                type="primary"
              >
                Tạo mới Folder
              </Button>
            )}
          </Flex>
        </Col>
      </Row>
      <Row style={{ marginTop: '16px' }}>
        <Col span={24}>
          <TableComponent
            queryKeyArr={['getDocumentProject', projectID]}
            columns={columns}
            loading={isFetching || isFetchingProjectDocumentItems}
            dataSource={stateIdIteamSelect ? listProjectDocumentItems : listProjectDocument}
            rowKey="id"
            isPagination={false}
          />
        </Col>
      </Row>

      <Modal className="ModalFormFolder" title="Tên thư mục" open={stateShowModal} closeIcon={false} footer={null}>
        <Form
          form={form}
          layout="vertical"
          name="basic"
          initialValues={{ remember: true }}
          onValuesChange={() => setIsFormChanged(!isFormChanged)}
          onFinish={onFinish}
        >
          <Form.Item<IFormDocumentProject>
            name="name"
            rules={[
              { required: true, message: 'Vui lòng nhập tên thư mục' },
              { max: 200, message: 'Tên thư mục không được vượt quá 200 ký tự' },
            ]}
          >
            <Input maxLength={200} placeholder="Nhập tên thư mục" />
          </Form.Item>
          <Form.Item<IFormDocumentProject> name="id" style={{ display: 'none' }}>
            <Input placeholder="Nhập tên thư mục" />
          </Form.Item>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Button
                onClick={() => {
                  handleCancel();
                }}
                style={{ width: '100%', backgroundColor: '#f0f0f0' }}
              >
                Hủy
              </Button>
            </Col>
            <Col span={12}>
              <Button
                htmlType="submit"
                loading={isLoadingUpdate || isLoadingCreate}
                type="primary"
                style={{ width: '100%' }}
              >
                Tạo
              </Button>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
};

export default Document;
