import { TableColumnsType, Typography } from 'antd';
import { useNavigate, useParams } from 'react-router-dom';
import InputSearch from '../../../../../components/input/InputSearch';
import TableComponent from '../../../../../components/table';
import { useFetch } from '../../../../../hooks';
import { getListFormReport } from '../../../../../service/formReport';
import './styles.scss';
import { FORM_REPORT, PROJECTS_MANAGEMENT } from '../../../../../configs/path';
import { useState } from 'react';
import { FormReport } from '../../../../../types/formReport';
import { useFormReportStore, useProjectStore } from '../../store';
import { DetailProject } from '../../../../../types/project/project';
const { Text } = Typography;
interface CommonFormProps {
  dataProject?: DetailProject;
}
const FormReportTab: React.FC<CommonFormProps> = ({ dataProject }) => {
  const navigate = useNavigate();
  const { setFormData } = useFormReportStore();
  const { setProject } = useProjectStore();
  const { id } = useParams();
  const projectId = id;

  const [search, setSearch] = useState<string>('');

  const handleOnChangeSearch = (value: unknown) => {
    setSearch(value as string);
  };

  const { data, isLoading, isPlaceholderData, isFetching } = useFetch<FormReport[]>({
    queryKeyArr: ['get-form-report', projectId, search],
    api: getListFormReport,
    moreParams: { id: projectId, search: search, page: -1, pageSize: null },
  });
  const formReports = data?.data?.data || [];

  const columns: TableColumnsType<FormReport> = [
    {
      title: 'Mã biểu mẫu',
      dataIndex: 'code',
      key: 'code',
      width: 130,
      render: value => {
        return <Text>{value}</Text>;
      },
    },
    {
      title: 'Tên biểu mẫu',
      key: 'name',
      dataIndex: 'name',
      width: 410,
      render: value => {
        return <Text>{value}</Text>;
      },
    },
    {
      title: 'URL',
      dataIndex: 'url',
      key: 'url',
      width: 447,
      align: 'left',
      render: value => {
        return (
          <a href={value} target="_blank">
            {value}
          </a>
        );
      },
    },
    {
      title: 'Số lượng tệp',
      key: 'fileCount',
      dataIndex: 'fileCount',
      align: 'center',
      width: 101,
      render: value => {
        return <Text>{value}</Text>;
      },
    },
    {
      dataIndex: 'action',
      key: 'action',
      align: 'center',
      width: 115,
      render: (_, record) => (
        <a
          onClick={() => {
            setFormData(record);
            if (dataProject) {
              setProject(dataProject);
            }
            navigate(`${PROJECTS_MANAGEMENT}/${projectId}${FORM_REPORT}/${record.id}`);
          }}
        >
          Xem chi tiết
        </a>
      ),
    },
  ];

  return (
    <div className="form_report-tab">
      <div className="header-content" style={{ paddingTop: 24 }}>
        <InputSearch showParams={false} placeholder="Tìm theo mã hoặc tên biểu mẫu" onChange={handleOnChangeSearch} />
      </div>
      <TableComponent
        rowKey={'id'}
        className="form-report-table"
        queryKeyArr={['get-form-report', projectId, search]}
        columns={columns}
        dataSource={formReports}
        loading={isLoading || isPlaceholderData || isFetching}
        isPagination={false}
      />
    </div>
  );
};

export default FormReportTab;
