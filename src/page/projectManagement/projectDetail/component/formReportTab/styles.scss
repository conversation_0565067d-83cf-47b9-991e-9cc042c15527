.form-report-table {
  width: calc(100vw - 240px);
  overflow-y: hidden;

  .ant-table {
    scrollbar-color: unset !important;
  }
  .ant-table-thead {
    & tr > th {
      padding: 12.5px 12px;
    }
  }
  .ant-table-tbody {
    & > tr > td {
      padding: 12.5px 12px;
    }
  }
}
@media screen and (max-width: 1200px) {
  .ant-table-wrapper {
    width: calc(100vw - 120px);
  }
}

@media screen and (max-width: 900px) {
  .ant-table-wrapper {
    width: calc(100vw - 48px);
  }
}

.form_report-detail {
  width: 100%;
  padding: 16px;

  .responsive-form {
    width: 100%;

    .responsive-input {
      width: 100%;
      max-width: 400px;

      .ant-input {
        width: 100%;
      }
    }

    .ant-form-item {
      margin-bottom: 16px;
    }
  }

  @media screen and (max-width: 1200px) {
    .responsive-form {
      .responsive-input {
        max-width: 300px;
      }
    }
  }

  @media screen and (max-width: 900px) {
    .responsive-form {
      .responsive-input {
        max-width: 200px;
      }
    }
  }

  @media screen and (max-width: 600px) {
    .ant-col-12 {
      width: 100%;
    }
    .responsive-form {
      .responsive-input {
        max-width: 100%;
      }
    }
  }
}
