import React, { useMemo, useState } from 'react';
import { Select, Checkbox, Button } from 'antd';
const { Option } = Select;
import './styles.scss';
import { getListOrgchartProject } from '../../../../../../service/project';
import { SalesUnitType } from '../../sellProgramTab/modalCreateSellProgram';
import { useFetch } from '../../../../../../hooks';
import { useParams } from 'react-router-dom';
import debounce from 'lodash/debounce';

interface UnitDropdownProps {
  hasEditPermission?: boolean;
  value?: string[]; // Giá trị được chọn từ bên ngoài
  onChange?: (value: string[]) => void; // Callback khi giá trị thay đổi
  onUpdate?: (value: string[]) => Promise<void>; // Callback để gọi API update
}

const UnitDropdown: React.FC<UnitDropdownProps> = ({
  hasEditPermission = true,
  value = [], // Mặc định là mảng rỗng nếu không truyền
  onChange,
  onUpdate,
}) => {
  const [open, setOpen] = useState(false); // Thêm state để kiểm soát dropdown
  const [search, setSearch] = useState('');
  const { projectId } = useParams();

  const { data: dataSaleUnitOfProject } = useFetch<SalesUnitType[]>({
    queryKeyArr: ['search-lst-orgChat', projectId, search],
    api: getListOrgchartProject,
    moreParams: { id: projectId, search: search },
  });

  const dateSaleUnit = dataSaleUnitOfProject?.data.data || [];
  const handleSelectChange = (newValue: string[]) => {
    if (onChange) {
      onChange(newValue); // Cập nhật dữ liệu local ngay khi thay đổi
    }
  };

  const handleAddSelectChange = async () => {
    if (onChange) {
      onChange(value);
    }
    if (onUpdate) {
      await onUpdate(value);
    }
    setOpen(false); // Đóng dropdown sau khi click "Thêm đơn vị"
  };
  // Debounce onSearch
  const debounceSearch = useMemo(
    () =>
      debounce((value: string) => {
        setSearch(value);
      }, 300),
    [],
  );
  const tagRender = (props: any) => {
    const { value: unitId } = props;
    const unit = dateSaleUnit.find(u => u.id === unitId);
    const displayLabel = unit ? `${unit.code} - ${unit.nameVN}` : unitId;

    const onPreventMouseDown = (event: React.MouseEvent<HTMLSpanElement>) => {
      event.preventDefault();
      event.stopPropagation();
    };

    if (value.length > 1) {
      if (value.indexOf(unitId) < 1) {
        return (
          <span className="tag-item" onMouseDown={onPreventMouseDown}>
            {displayLabel}
          </span>
        );
      } else if (value.indexOf(unitId) === 1) {
        const remainingCount = value.length - 1;
        return <span className="tag-remaining">+{remainingCount}...</span>;
      }
      return <></>;
    }

    return (
      <span className="tag-item-single" onMouseDown={onPreventMouseDown}>
        {displayLabel}
      </span>
    );
  };

  return (
    <div>
      <Select
        status={value.length === 0 ? 'error' : ''}
        mode="multiple"
        placeholder="Chọn những đơn vị sử dụng"
        value={value} // Sử dụng giá trị từ props
        onChange={handleSelectChange}
        className="unit-select"
        disabled={!hasEditPermission}
        tagRender={tagRender}
        showSearch
        filterOption={false}
        onSearch={debounceSearch}
        onFocus={() => {
          // Đảm bảo dropdown mở khi user click vào
          setSearch(''); // có thể reset nếu muốn
        }}
        popupClassName="custom-dropdown"
        open={open} // Kiểm soát trạng thái mở/đóng của dropdown
        onDropdownVisibleChange={visible => setOpen(visible)} // Cập nhật state khi dropdown hiển thị/ẩn
        dropdownRender={menu => (
          <div>
            <div className="checkbox-all">
              <Checkbox
                checked={value.length === dateSaleUnit.length}
                onChange={e => {
                  if (e.target.checked) {
                    const unitIds = dateSaleUnit.map(unit => unit.id);
                    handleSelectChange(unitIds);
                  } else {
                    handleSelectChange([]);
                  }
                }}
                className="checkbox-all-input"
              />
              <div className="checkbox-all-content">
                <span>Chọn tất cả</span>
                <span className="item-count">{dateSaleUnit.length} items</span>
              </div>
            </div>
            <div className="divider" />
            {menu}
            <div className="divider" />
            <div className="button-wrapper">
              <Button disabled={value?.length <= 0} className="add-button" onClick={handleAddSelectChange}>
                Thêm đơn vị
              </Button>
            </div>
          </div>
        )}
      >
        {dateSaleUnit.map(unit => (
          <Option key={unit.id} value={unit.id}>
            <div className="option-item">
              <Checkbox checked={value.includes(unit.id)} />
              <span className="option-label">{`${unit.code} - ${unit.nameVN}`}</span>
            </div>
          </Option>
        ))}
      </Select>
    </div>
  );
};

export default UnitDropdown;
