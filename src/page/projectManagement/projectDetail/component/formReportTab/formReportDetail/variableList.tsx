import { TableColumnsType, Typography } from 'antd';
import InputSearch from '../../../../../../components/input/InputSearch';
import TableComponent from '../../../../../../components/table';
import { useFetch } from '../../../../../../hooks';
import { getListVariable } from '../../../../../../service/formReport';
import { Variable } from '../../../../../../types/formReport';
import { useFormReportStore, useProjectStore } from '../../../store';
import { useState } from 'react';
const { Text } = Typography;

const VariableList = () => {
  const { formData } = useFormReportStore();
  const { project } = useProjectStore();
  const projectId = project?.id;
  const formId = formData?.id;

  const [search, setSearch] = useState<string>('');

  const handleOnChangeSearch = (value: unknown) => {
    setSearch(value as string);
  };
  const { data, isLoading, isPlaceholderData, isFetching } = useFetch<Variable[]>({
    queryKeyArr: ['get-variable', projectId, formId, search],
    api: getListVariable,
    moreParams: {
      projectId: projectId,
      formId: formId,
      page: -1,
      pageSize: null,
      search: search,
    },
  });
  const variables = data?.data?.data?.rows || [];

  const columns: TableColumnsType<Variable> = [
    {
      title: 'Mã biến',
      dataIndex: 'code',
      key: 'code',
      width: 170,
      render: value => {
        return <Text style={{ marginLeft: 5 }}>{value}</Text>;
      },
    },
    {
      title: 'Tên biến',
      key: 'name',
      dataIndex: 'name',
      render: value => {
        return <Text style={{ marginLeft: 5 }}>{value}</Text>;
      },
    },
  ];
  return (
    <div
      style={{
        marginBottom: 0,
        width: '100%',
        marginTop: 28,
      }}
    >
      {/* Thanh tìm kiếm */}
      <InputSearch
        showParams={false}
        onChange={handleOnChangeSearch}
        placeholder="Tra cứu biến theo mã hoặc tên biến"
      />

      {/* Bảng dữ liệu */}
      <TableComponent
        rowKey={'id'}
        queryKeyArr={['get-variable', projectId, formId, search]}
        className="table_form-report"
        columns={columns}
        dataSource={variables}
        isPagination={false}
        style={{ height: 170 }}
        loading={isLoading || isPlaceholderData || isFetching}
        scroll={variables && variables.length > 0 ? { x: 0, y: 120 } : { x: 0 }}
      />
    </div>
  );
};

export default VariableList;
