import { Input } from 'antd';
import React, { useState } from 'react';
import { FileSample } from '../../../../../../types/formReport';

interface InptNameFileSampleProps {
  name: string;
  projectId: string;
  formId: string;
  initialRecord: FileSample;
  handleUpdateFileSample: (projectId: string, formId: string, payload: FileSample) => Promise<void>;
}

const InptNameFileSample: React.FC<InptNameFileSampleProps> = ({
  name,
  projectId,
  formId,
  initialRecord,
  handleUpdateFileSample,
}) => {
  const [value, setValue] = useState(name);
  const [isEnterPressed, setIsEnterPressed] = useState(false);

  const handleOnChange = async (inputValue: string) => {
    const trimmedValue = inputValue.trim();

    if (!trimmedValue || trimmedValue === name.trim()) {
      setValue(name.trim());
      return;
    }

    setValue(trimmedValue);
    const updatedRecord: FileSample = { ...initialRecord, fileName: trimmedValue };

    await handleUpdateFileSample(projectId, formId, updatedRecord);
  };

  return (
    <div>
      <Input
        status={value ? '' : 'error'}
        maxLength={255}
        value={value}
        autoFocus
        onChange={e => setValue(e.target.value)}
        placeholder="Nhập tên biểu mẫu"
        onBlur={e => {
          if (!isEnterPressed) {
            handleOnChange(e.target.value);
          }
          setIsEnterPressed(false);
        }}
        onPressEnter={e => {
          setIsEnterPressed(true);
          handleOnChange(e.currentTarget.value);
        }}
        style={{ lineHeight: '20px' }}
      />
    </div>
  );
};

export default InptNameFileSample;
