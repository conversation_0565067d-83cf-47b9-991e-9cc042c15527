.unit-select {
  width: 100%;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 1);
}

.tag-item {
  display: inline-block;
  padding: 0 8px;
  margin: 4px;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 4px;
  color: #000;
}

.tag-item-single {
  display: inline-block;
  padding: 0 8px;
  margin: 4px;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 2px;
  color: #000;
}

.tag-remaining {
  display: inline-block;
  padding: 0 8px;
  margin: 4px;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 4px;
  color: #000;
}

.tag-close {
  margin-left: 8px;
  cursor: pointer;
  color: #999;
}

.custom-dropdown {
  .ant-select-item-option {
    .ant-select-item-option-state {
      display: none !important;
    }
  }
  .ant-select-dropdown {
    padding: 0 !important; // Loại bỏ padding mặc định của dropdown
  }
  .ant-select-item {
    padding: 10px 12px;
  }
}

.checkbox-all {
  padding: 8px 12px;
  display: flex;
}

.checkbox-all-input {
  border-radius: 4px;
}

.checkbox-all-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-left: 8px;
}

.item-count {
  margin-right: 20px;
}

.divider {
  border-top: 1px solid #f0f0f0;
}

.button-wrapper {
  margin-top: 8px;
  margin-bottom: 4px;
  text-align: right;
}

.add-button {
  border-radius: 4px;
}

.option-item {
  display: flex;
  align-items: center;
}

.option-label {
  margin-left: 8px;
}

.responsive-form {
  .ant-form-item {
    margin-bottom: 16px; // Khoảng cách giữa các field

    .ant-input {
      &.responsive-input {
        width: 100%; // Chiếm toàn bộ chiều rộng của container
        max-width: 480px; // Giới hạn tối đa để tránh quá rộng trên màn lớn
        min-width: 0; // Đảm bảo thu nhỏ được trên màn nhỏ
        box-sizing: border-box; // Bao gồm padding và border trong width
      }

      // Style cho trạng thái disabled hoặc readOnly
      &:disabled,
      &[readonly] {
        cursor: not-allowed;
        color: rgba(0, 0, 0, 0.25);
        background-color: rgba(0, 0, 0, 0.04);
      }

      // Style cho URL input
      &.responsive-input[readonly] {
        cursor: pointer;
        &:hover {
          border-color: #40a9ff;
        }
      }
    }
  }

  // Responsive cho các kích thước màn hình
  @media (max-width: 768px) {
    .ant-form-item {
      .ant-input.responsive-input {
        max-width: 100%; // Loại bỏ giới hạn trên màn nhỏ
      }
    }
  }

  @media (max-width: 576px) {
    .ant-form-item {
      margin-bottom: 12px; // Giảm khoảng cách trên màn rất nhỏ
    }
  }
}
.table_form-report {
  .ant-empty .ant-empty-image {
    height: 70px;
  }
}
