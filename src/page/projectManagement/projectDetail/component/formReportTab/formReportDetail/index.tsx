import { App, But<PERSON>, Col, Form, Input, Modal, Row, TableColumnsType } from 'antd';
import BreadCrumbComponent from '../../../../../../components/breadCrumb';
import TableComponent from '../../../../../../components/table';
import { useDeleteField, useFetch, useUpdateField } from '../../../../../../hooks';
import { deleteFileSample, getDetailFormReport, updateFileSample } from '../../../../../../service/formReport';
import { useParams } from 'react-router-dom';
import VariableList from './variableList';
import { useEffect, useMemo, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { FileSample, FormReport } from '../../../../../../types/formReport';
import { PlusOutlined } from '@ant-design/icons';
import FileUpload from '../../../../../../components/upload/FileUpload';
import { ActionsColumns } from '../../../../../../components/table/components/ActionsColumns';
import { useFormReportStore, useProjectStore } from '../../../store';
import InptNameFileSample from './inptNameFileSample';
import UnitDropdown from './unitDropdown';
import _ from 'lodash';

const FormReportDetail = () => {
  const { modal } = App.useApp();
  const { formData } = useFormReportStore();
  const { project } = useProjectStore();
  const { projectId, formId } = useParams();

  const [currentFileSample, setCurrentFileSample] = useState<FileSample[]>([]);

  const { data } = useFetch<FormReport[]>({
    queryKeyArr: ['get-file-sample', projectId, formId],
    api: getDetailFormReport,
    moreParams: {
      projectId: projectId,
      formId: formId,
      page: -1,
      pageSize: null,
      search: '',
    },
  });

  const fileSamples = useMemo(() => data?.data?.data?.[0]?.files ?? [], [data]);

  const { mutateAsync: update } = useUpdateField({
    apiQuery: updateFileSample,
    keyOfDetailQuery: ['get-file-sample'],
    label: 'tệp mẫu',
    isMessageError: false,
  });

  // const { mutateAsync: create } = useCreateField({
  //   apiQuery: createFileSample,
  //   keyOfDetailQuery: ['get-file-sample'],
  //   label: 'tệp mẫu',
  // });

  const { mutateAsync: remove } = useDeleteField({
    apiQuery: deleteFileSample,
    keyOfDetailQuery: ['get-file-sample'],
    label: 'tệp mẫu',
  });

  useEffect(() => {
    setCurrentFileSample([{ id: 'new', status: '01' }, ...fileSamples]);
  }, [fileSamples]);

  const handleAddNew = async () => {
    const newRecord: FileSample = {
      id: uuidv4(),
      fileName: '',
      fileUrl: '',
      orgUsing: [],
      status: '01',
      projectId,
      formId,
      isNew: true,
    };
    try {
      setCurrentFileSample(prevData => [prevData[0], newRecord, ...prevData.slice(1)]);
    } catch (error) {
      console.log(error);
    }
  };

  const handleUpdateFileSample = async (projectId: string, formId: string, payload: FileSample) => {
    // Gọi update với object phù hợp với wrapper
    updateRecordById(payload.id || uuidv4(), payload);

    if (
      _.isEmpty(payload.fileName) ||
      _.isEmpty(payload.fileUrl) ||
      payload.orgUsing?.length === 0 ||
      _.isEmpty(payload.originalname)
    )
      return;

    await update({
      projectId,
      formId,
      ...payload,
    });
    // Cập nhật state local
  };
  const updateRecordById = (id: string, changes: Partial<FileSample>) => {
    setCurrentFileSample(prev => prev.map(item => (item.id === id ? { ...item, ...changes } : item)));
  };
  const handleUploadSuccess = (record: FileSample) => async (fileSample: Partial<FileSample>) => {
    updateRecordById(record.id || uuidv4(), {
      fileUrl: fileSample.fileUrl,
      originalname: fileSample.originalname,
    });
    if (record.id !== 'new' && fileSample.fileUrl && projectId && formId) {
      const updatedPayload: FileSample = {
        ...record,
        fileUrl: fileSample.fileUrl,
        status: record.status || '01',
        originalname: fileSample.originalname,
      };
      if (
        _.isEmpty(updatedPayload.fileName) ||
        _.isEmpty(updatedPayload.fileUrl) ||
        updatedPayload.orgUsing?.length === 0 ||
        _.isEmpty(updatedPayload.originalname)
      )
        return;
      await update({
        projectId,
        formId,
        ...record,
        ...updatedPayload,
      });
      // updateRecordById(record.id || uuidv4(), {
      //   fileUrl: fileSample.fileUrl,
      //   originalname: fileSample.originalname,
      // });
    }
  };

  const handleOrgUsingChange = (recordId?: string) => async (newOrgUsing: string[]) => {
    updateRecordById(recordId || uuidv4(), { orgUsing: newOrgUsing });
  };

  const deleteFile = async (record: FileSample) => {
    record = {
      ...record,
      projectId: projectId,
      formId: formId,
    };
    if (!record.isNew) await remove(record);
    setCurrentFileSample(prev => prev.filter(file => file.id !== record.id));
  };

  const handleOrgUsingUpdate = (record: FileSample) => async (newOrgUsing: string[]) => {
    updateRecordById(record.id || uuidv4(), { orgUsing: newOrgUsing });
    if (record.id !== 'new' && projectId && formId) {
      const updatedPayload: FileSample = {
        ...record,
        orgUsing: newOrgUsing,
      };

      if (
        _.isEmpty(updatedPayload.fileName) ||
        _.isEmpty(updatedPayload.fileUrl) ||
        updatedPayload.orgUsing?.length === 0 ||
        _.isEmpty(updatedPayload.originalname)
      )
        return;
      await update({
        projectId,
        formId,
        ...updatedPayload,
      });
      // updateRecordById(record.id || uuidv4(), { orgUsing: newOrgUsing });
    }
  };

  const columns: TableColumnsType<FileSample> = [
    {
      title: 'Tên tệp mẫu',
      dataIndex: 'fileName',
      key: 'fileName',
      width: 265,
      render: (_, record) => {
        return record.id === 'new' ? (
          <Button
            icon={<PlusOutlined />}
            iconPosition={'end'}
            onClick={handleAddNew}
            style={{ width: '100%', justifyContent: 'space-between' }}
          >
            Tạo tệp mẫu mới
          </Button>
        ) : (
          <InptNameFileSample
            name={record.fileName || ''}
            projectId={projectId || ''}
            formId={formId || ''}
            initialRecord={record}
            handleUpdateFileSample={handleUpdateFileSample}
          />
        );
      },
    },
    {
      title: 'File',
      key: 'fileUrl',
      dataIndex: 'fileUrl',
      width: 265,
      render: (_, record) => {
        return (
          record.id !== 'new' && (
            <FileUpload
              fileUrl={record.fileUrl}
              originalname={record.originalname}
              maxFileSizeMB={10}
              uploadPath="property/project"
              onUploadSuccess={handleUploadSuccess(record)}
            />
          )
        );
      },
    },
    {
      title: 'Đơn vị sử dụng',
      dataIndex: 'orgUsing',
      key: 'orgUsing',
      width: 480,
      align: 'left',
      render: (_, record) => {
        return (
          record.id !== 'new' && (
            <UnitDropdown
              hasEditPermission={true}
              value={record.orgUsing || []}
              onChange={handleOrgUsingChange(record.id)}
              onUpdate={handleOrgUsingUpdate(record)}
            />
          )
        );
      },
    },
    {
      title: 'Trạng thái',
      key: 'status',
      dataIndex: 'status',
      align: 'center',
      width: 120,
      render: (_, record) => {
        return (
          record.id !== 'new' && (
            <span
              style={{
                color: record?.status === '01' ? 'rgba(56, 158, 13, 1)' : 'rgba(255, 77, 79, 1)',
              }}
            >
              {record?.status === '01' ? 'Đã kích hoạt' : 'Vô hiệu'}
            </span>
          )
        );
      },
    },
  ];

  const columnActions: TableColumnsType<FileSample> = useMemo(() => {
    return [
      ...columns,
      {
        dataIndex: 'action',
        key: 'action',
        width: 75,
        align: 'center',
        fixed: 'right',
        render: (_: unknown, record: FileSample) => {
          const textModalConfirmActive = record?.status === '01' ? 'Vô hiệu hoá' : 'Kích hoạt';

          const handleChangeStatus = async () => {
            if (
              record.fileName?.trim().length === 0 ||
              record.fileUrl?.trim().length === 0 ||
              !record.orgUsing ||
              record.originalname?.trim().length === 0
            )
              return;
            await update({
              projectId,
              formId,
              ...record,
              status: record?.status === '01' ? '02' : '01',
            });
          };
          const handleDeleteFileSample = () => {
            !record.isNew
              ? Modal.confirm({
                  title: 'Xác nhận xóa tệp mẫu',
                  content: 'Bạn có chắc muốn xóa tệp mẫu này không?',
                  cancelText: 'Quay lại',
                  onOk: () => deleteFile(record),
                  okButtonProps: { type: 'default' },
                  cancelButtonProps: { type: 'primary' },
                })
              : deleteFile(record);
          };

          return record.id !== 'new' ? (
            <ActionsColumns
              textModalConfirmActive={textModalConfirmActive}
              {...(!record.isNew && { handleActive: handleChangeStatus })}
              handleDelete={handleDeleteFileSample}
            />
          ) : (
            ''
          );
        },
      },
    ];
  }, [modal]);

  return (
    <div className="form_report-detail">
      <BreadCrumbComponent
        customItems={[
          project?.name && project?.id ? { label: project.name, path: `/projects/${project.id}` } : { label: '-' },
          project?.id
            ? { label: 'Danh sách biểu mẫu', path: `/projects/${project.id}?tab=9` }
            : { label: 'Danh sách biểu mẫu' },
        ]}
        titleBread={formData?.name}
      />

      <Row gutter={[16, 0]}>
        <Col span={12}>
          <Form layout="vertical" initialValues={formData} className="responsive-form">
            <Form.Item label="Mã biểu mẫu" name="code">
              <Input disabled className="responsive-input" />
            </Form.Item>

            <Form.Item label="Tên biểu mẫu" name="name">
              <Input disabled className="responsive-input" />
            </Form.Item>

            <Form.Item label="URL" name="url">
              <a href={formData?.url} target="_blank" rel="noopener noreferrer">
                <Input readOnly className="responsive-input" value={formData?.url} />
              </a>
            </Form.Item>
          </Form>
        </Col>
        <Col span={12}>
          <VariableList />
        </Col>
      </Row>

      <TableComponent
        rowKey="id"
        queryKeyArr={['get-file-sample']}
        className="table_form-report"
        columns={columnActions}
        dataSource={currentFileSample}
        isPagination={false}
        style={{ marginTop: 60 }}
        scroll={{ x: 0 }}
      />
    </div>
  );
};

export default FormReportDetail;
