import {
  Button,
  Input,
  Modal,
  Select,
  TableColumnsType,
  Tag,
  Checkbox,
  Spin,
  notification,
  Form,
  Row,
  Col,
} from 'antd';
import {
  UpdateOrgchartProjectPayload,
  DetailProject,
  ExternalOrgcharts,
  ListĐTHT,
  ListĐVBH,
  OrgchartProject,
  SelectValueDVHB,
} from '../../../../../types/project/project';
import { v4 as uuidv4 } from 'uuid';
import TableComponent from '../../../../../components/table';
import { useCreateField, useDeleteField, useFetch, useUpdateField } from '../../../../../hooks';
import {
  getListOrgchartProject,
  getListĐTHT,
  getListĐVBH,
  sendDeleteOrgChartProject,
  sendUpdateOrgchartProject,
  sendUpdateRootPos,
} from '../../../../../service/project';
import './styles.scss';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import SingleSelectLazy from '../../../../../components/select/singleSelectLazy';

interface CommonFormProps {
  data?: DetailProject;
}

const OrgchartTab: React.FC<CommonFormProps> = () => {
  const { id } = useParams();
  const projectId = id || '';

  const PAGE_SIZE = 10;

  const [searchPartnerShip, setSearchPartnerShip] = useState<string>('');
  const [searchSaleUnit, setSearchSaleUnit] = useState<string>('');
  const [listSaleUnit, setListSaleUnit] = useState<ListĐVBH[]>([]);
  const [page, setPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [orgchartProject, setOrgchartProject] = useState<OrgchartProject[]>([]);
  const [selectedRow, setSelectedRow] = useState<string | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedUnits, setSelectedUnits] = useState<string[]>([]);
  const [partnerCodeId, setPartnerCodeId] = useState<string>('');
  const [currentListĐTHT, setCurrentListĐTHT] = useState<ListĐTHT[]>();

  // Fetch danh sách ĐVBH
  const {
    data: listĐVBH,
    isLoading: loadingSaleUnit,
    refetch,
  } = useFetch<ListĐVBH[]>({
    queryKeyArrWithFilter: ['list-ĐVBH', projectId, searchSaleUnit, page],
    api: getListĐVBH,
    moreParams: { id: projectId, search: searchSaleUnit, page, limit: PAGE_SIZE },
  });

  // Fetch danh sách ĐTHT
  const { data: listĐTHT, isLoading: loadingPartnerShip } = useFetch<ListĐTHT[]>({
    queryKeyArrWithFilter: ['list-ĐTHT', partnerCodeId || '', searchPartnerShip],
    api: getListĐTHT,
    enabled: !!partnerCodeId, // Chỉ fetch khi partnerCodeId có giá trị hợp lệ
    moreParams: { partnerCode: partnerCodeId, search: searchPartnerShip },
  });

  // Fetch dữ liệu cho các orgchart projects
  const {
    data: orgchartData,
    isLoading,
    isPlaceholderData,
  } = useFetch<OrgchartProject[]>({
    queryKeyArr: ['orgchart-tab', projectId],
    api: getListOrgchartProject,
    moreParams: { id: projectId },
  });

  // Hooks xử lý các trường hợp tạo và xóa đơn vị bán hàng
  const createOrgchartProject = useCreateField({
    keyOfDetailQuery: ['orgchart-tab', projectId],
    apiQuery: sendUpdateOrgchartProject,
    messageSuccess: 'Thêm mới đơn vị bán hàng thành công',
  });

  const deleteOrgchartProject = useDeleteField({
    keyOfDetailQuery: ['orgchart-tab', projectId],
    apiQuery: sendDeleteOrgChartProject,
    messageSuccess: 'Xóa đơn vị bán hàng thành công',
    isShowMessage: false,
    isMessageError: false,
  });

  // Hooks xử lý các trường hợp tạo và xóa đối tác hợp tác
  const updatePartnerCode = useUpdateField({
    keyOfDetailQuery: ['orgchart-tab', projectId],
    apiQuery: sendUpdateOrgchartProject,
    messageSuccess: 'Cập nhật đối tác hợp tác thành công',
  });

  const updateRootPos = useUpdateField({
    keyOfDetailQuery: ['orgchart-tab-rootPos', projectId],
    apiQuery: sendUpdateRootPos,
    messageSuccess: 'Thêm đơn vị thu hồi thành công',
    isMessageError: false,
  });

  const deletePartnerCode = useUpdateField({
    keyOfDetailQuery: ['orgchart-tab', projectId],
    apiQuery: sendUpdateOrgchartProject,
    messageSuccess: 'Xóa đối tác hợp tác thành công',
  });

  const orgchartList = useMemo(() => orgchartData?.data?.data || [], [orgchartData]);

  useEffect(() => {
    if (orgchartList) {
      // Thêm ID khởi tạo cho dòng đầu tiên trong orgchartProject
      setOrgchartProject([
        ...orgchartList,
        { id: uuidv4(), nameVN: '', code: '', externalOrgcharts: [] }, // Chỉ thêm ID khởi tạo ở đây
      ]);
    }
  }, [orgchartList]);

  // Xử lý khi cuộn đến cuối dropdown
  const handlePopupScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;

    // Khi cuộn xuống cuối dropdown, load thêm dữ liệu
    if (scrollTop + clientHeight >= scrollHeight - 10 && !loadingSaleUnit && hasMore) {
      setPage(prevPage => prevPage + 1);
    }

    // Khi cuộn lên đầu, reset tìm kiếm để hiển thị dữ liệu gốc
    if (scrollTop <= 10 && !loadingSaleUnit && page > 1) {
      setPage(1);
      setSearchSaleUnit('');
    }
  };

  // Cập nhật lại danh sách khi có dữ liệu mới
  useEffect(() => {
    if (listĐVBH?.data?.data?.rows) {
      setListSaleUnit(prevList => {
        // Loại bỏ các bản ghi đã có trong listSaleUnit trước đó để tránh trùng lặp
        const newList =
          page === 1
            ? [...(listĐVBH?.data?.data?.rows || [])] // Lúc mới bắt đầu, set lại danh sách
            : [
                ...prevList,
                ...(listĐVBH?.data?.data?.rows || []).filter(
                  item => !prevList.some(existingItem => existingItem.id === item.id),
                ), // Chỉ thêm những item chưa có
              ];

        return newList;
      });

      // Kiểm tra có còn dữ liệu để load tiếp không
      setHasMore(listĐVBH.data.data.rows.length >= PAGE_SIZE);
    }
  }, [listĐVBH, page]);

  // Nếu có ID của ĐVBH thì hiển thị danh sách ĐTHT của ĐVBH đó
  useEffect(() => {
    if (partnerCodeId && listĐTHT?.data?.data?.rows) {
      setCurrentListĐTHT(listĐTHT.data.data.rows);
    }
  }, [partnerCodeId, listĐTHT]);

  const handleSearch = useCallback((value: string) => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    searchTimeoutRef.current = setTimeout(() => {
      setSearchSaleUnit(value);
      setPage(1);
      setHasMore(true);
      setListSaleUnit([]);
    }, 300);
  }, []);

  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);
  useEffect(() => {
    if (searchSaleUnit !== '') {
      refetch();
    }
  }, [searchSaleUnit, refetch]);

  const handleSearchPartnerShip = (e: React.ChangeEvent<HTMLInputElement>) => {
    const keyword = e.target.value.toLowerCase();
    setSearchPartnerShip(keyword);
  };

  // Xử lý khi chọn ĐVBH
  const handleSelectĐVHB = async (value: SelectValueDVHB) => {
    try {
      if (!value) return;

      const orgchartId = value.value; // ID thật của ĐVBH
      const selectedUnit = value.label; // Thông tin ĐVBH

      // Kiểm tra nếu `orgchartId` là ID khởi tạo
      if (orgchartId === 'uuidv4') {
        // Nếu là ID khởi tạo, không gửi request và chỉ cập nhật dữ liệu ở UI
        setOrgchartProject(prev => [
          ...prev.filter(item => item.id !== ''), // Giữ lại phần tử cũ không có ID khởi tạo
          {
            id: orgchartId,
            nameVN: selectedUnit.nameVN,
            code: selectedUnit.code,
            externalOrgcharts: prev.find(item => item.id === orgchartId)?.externalOrgcharts || [], // Giữ lại externalOrgcharts của phần tử cũ
          }, // Thêm phần tử mới
        ]);
        return;
      }

      const orgchartIds = [...orgchartProject.map(item => item.id)];

      // Kiểm tra xem ID này có tồn tại trong danh sách orgchartIds không, tránh duplicate
      if (orgchartIds.includes(orgchartId)) {
        return;
      }

      // Tìm phần tử cũ (nếu có) để lấy externalOrgcharts
      const currentRow = orgchartProject.find(item => item.id === orgchartId);
      const externalOrgcharts = currentRow ? currentRow.externalOrgcharts.map(item => item.id) : [];

      // Thêm ID mới vào mảng internalOrgcharts, cùng với externalOrgcharts cũ nếu có
      const updatedInternalOrgcharts = [
        ...orgchartProject.map(item => ({
          id: item.id,
          externalOrgchartIds: item.externalOrgcharts?.map(e => e.id) || [], // Giữ lại externalOrgcharts
        })),
        {
          id: orgchartId,
          externalOrgchartIds: externalOrgcharts, // Giữ lại externalOrgcharts từ phần tử cũ
        },
      ];

      // Cập nhật payload với các ID mới trong internalOrgcharts và externalOrgcharts
      const payload: UpdateOrgchartProjectPayload = {
        projectId: projectId,
        internalOrgcharts: updatedInternalOrgcharts,
      };

      // Thực hiện API gọi tạo orgchart project
      await createOrgchartProject.mutateAsync(payload);

      refetch();

      // Cập nhật lại orgchartProject với thông tin ĐVBH đã chọn và giữ lại externalOrgcharts
      setOrgchartProject(prev => [
        ...prev, // Giữ lại các phần tử cũ
        {
          id: orgchartId,
          nameVN: selectedUnit.nameVN,
          code: selectedUnit.code,
          externalOrgcharts: prev.find(item => item.id === orgchartId)?.externalOrgcharts || [], // Giữ lại externalOrgcharts của phần tử cũ
        }, // Thêm phần tử mới với externalOrgcharts đã có
      ]);
    } catch (error) {
      console.error('Error selecting DVHB:', error);
    }
  };

  // Xử lý xóa đơn vị bán hàng
  const handleDeleteOrgchartProject = async (orgchartId: string) => {
    const payload = { projectId, orgchartId };
    const response = await deleteOrgchartProject.mutateAsync(payload);

    const { statusCode, data } = response?.data || {};

    if (statusCode === 'FORMFILE0003') {
      const formNames = Array.isArray(data)
        ? data.map((item: { formDetails: { code: string } }) => item.formDetails.code).join(', ')
        : 'Không xác định';
      return notification.error({ message: `Đã tồn tại đơn vị trong Biểu mẫu ${formNames}` });
    }

    if (statusCode === 'SPROGRAM0006') {
      return notification.error({ message: `Đã tồn tại đơn vị trong CTBH, vui lòng kiểm tra!` });
    }
  };

  // Xử lý mở popup chọn ĐTHT
  const showPopup = (id: string) => {
    const currentRow = orgchartProject.find(item => item.id === id);
    if (currentRow) {
      setSelectedUnits(currentRow.externalOrgcharts.map(org => org.id));
    }
    setSelectedRow(id);
    setIsModalVisible(true);
  };

  // Xử lý thay đổi checkbox
  const handleCheckboxChange = (unitId: string, checked: boolean) => {
    setSelectedUnits(prev => (checked ? [...prev, unitId] : prev.filter(id => id !== unitId)));
  };

  // Lưu đơn vị ĐTHT đã chọn
  const handleSaveUnits = async () => {
    if (selectedRow) {
      // Lấy phần tử có id là selectedRow
      const currentRow = orgchartProject.find(item => item.id === selectedRow);

      if (currentRow) {
        // Tạo payload, chỉ lấy danh sách ĐTHT từ selectedUnits (không giữ lại các ID cũ)
        const payload = {
          projectId: projectId,
          internalOrgcharts: orgchartProject.map(item =>
            item.id === selectedRow
              ? {
                  id: item.id,
                  externalOrgchartIds: selectedUnits, // Chỉ lấy ID từ danh sách mới
                }
              : {
                  id: item.id,
                  externalOrgchartIds: item.externalOrgcharts.map(orgchart => orgchart.id),
                },
          ),
        };

        // Gửi payload lên API
        await updatePartnerCode.mutateAsync(payload);

        // Cập nhật lại orgchartProject
        setOrgchartProject(prev =>
          prev.map(item =>
            item.id === selectedRow
              ? {
                  ...item,
                  externalOrgcharts:
                    currentListĐTHT
                      ?.filter(unit => selectedUnits.includes(unit.id)) // Chỉ lấy đơn vị còn được chọn
                      .map(unit => ({
                        id: unit.id,
                        partnershipName: unit.partnershipName,
                        partnershipCode: unit.partnershipCode,
                      })) || [],
                }
              : item,
          ),
        );
      }
    }

    // Đóng modal sau khi lưu
    setIsModalVisible(false);
  };

  // Xóa đơn vị ĐTHT
  const handleRemoveUnit = useCallback(
    async (rowId: string, unit: ExternalOrgcharts) => {
      try {
        // Lọc lại orgchartProject để loại bỏ đơn vị đã được xóa
        const updatedOrgchartProject = orgchartProject.map(item =>
          item.id === rowId
            ? {
                ...item,
                externalOrgcharts: item.externalOrgcharts.filter(orgchart => orgchart.id !== unit.id), // Giữ lại các đơn vị khác
              }
            : item,
        );

        // Tạo payload với dữ liệu đã cập nhật từ orgchartProject
        const payload = {
          projectId,
          internalOrgcharts: updatedOrgchartProject.map(item => ({
            id: item.id,
            externalOrgchartIds: item.externalOrgcharts.map(orgchart => orgchart.id), // Cập nhật externalOrgchartIds
          })),
        };

        // Gửi request API để xóa ĐTHT
        await deletePartnerCode.mutateAsync(payload);

        // Cập nhật lại orgchartProject và selectedUnits sau khi xóa
        setOrgchartProject(updatedOrgchartProject);
        setSelectedUnits(prev => prev.filter(id => id !== unit.id));
      } catch (error) {
        console.error('Error removing unit:', error);
      }
    },
    [deletePartnerCode, orgchartProject, projectId],
  );

  // Hàm xử lý khi chọn đơn vị bán hàng có thể thu hồi
  const onSelectOrgchartProjectAddRootPos = (value: SelectValueDVHB) => {
    if (value && value.value) {
      const payload = {
        projectId: projectId,
        rootPosId: value.value,
      };
      updateRootPos.mutateAsync(payload);
    }
  };

  const columns: TableColumnsType<OrgchartProject> = [
    {
      title: 'Tên ĐVBH',
      dataIndex: 'nameVN',
      key: 'nameVN',
      width: 491,
      render: value =>
        value ? (
          <p>{value}</p>
        ) : (
          <Select
            placeholder="Thêm đơn vị bán hàng"
            allowClear
            filterOption={false} // Không dùng lọc client-side, để lọc bằng API
            options={listSaleUnit?.map((item: ListĐVBH) => ({
              value: item.id,
              label: item.code + ' - ' + item.nameVN,
            }))}
            onChange={handleSelectĐVHB}
            style={{ width: 250 }}
            labelInValue
            showSearch
            onSearch={handleSearch} // Gọi API khi tìm kiếm
            onPopupScroll={handlePopupScroll} // Gọi API khi cuộn
            loading={loadingSaleUnit}
            dropdownRender={menu => (
              <>
                {menu}
                {loadingSaleUnit && (
                  <div style={{ textAlign: 'center', padding: 10 }}>
                    <Spin size="small" />
                  </div>
                )}
              </>
            )}
            onFocus={() => {
              setPage(1);
              setSearchSaleUnit('');
            }}
          />
        ),
    },
    {
      title: 'Mã ĐVBH',
      key: 'code',
      dataIndex: 'code',
      width: 140,
      render: value => <p>{value}</p>,
    },
    {
      title: 'Tên ĐTHT (F2)',
      key: 'externalOrgcharts',
      dataIndex: 'externalOrgcharts',
      render: (_, record) =>
        record.nameVN ? (
          <>
            {record.externalOrgcharts?.map(partner => (
              <Tag
                key={partner.partnershipCode}
                closable
                onClose={() => handleRemoveUnit(record.id, partner)}
                style={{
                  fontSize: 12,
                  border: '1px solid rgba(0, 0, 0, 0.15)',
                  borderRadius: '4px',
                  backgroundColor: 'rgba(0, 0, 0, 0.02)',
                  margin: '0 5px 5px 0',
                }}
              >
                {partner.partnershipName}
              </Tag>
            ))}

            <Button
              color="default"
              size="small"
              style={{
                gap: 4,
                padding: '6px 12px',
                fontSize: 12,
                borderRadius: 4,
                height: 22,
                margin: '0 5px 5px 0',
              }}
              onClick={() => {
                setPartnerCodeId(record.id);
                showPopup(record.id);
              }}
            >
              Thêm ĐTHT +
            </Button>
          </>
        ) : null,
    },
    {
      key: 'action',
      align: 'center',
      width: 100,
      render: (_, record) =>
        record.nameVN ? (
          <Button type="link" danger onClick={() => handleDeleteOrgchartProject(record?.id)}>
            Xóa
          </Button>
        ) : null,
    },
  ];

  return (
    <>
      <div className="orgchart-tab">
        <div className="orgchart-tab__header">
          <Row gutter={24}>
            <Col xs={24} md={8} lg={8}>
              <Form.Item layout="vertical" name="" label="Đơn vị có thể thu hồi">
                <Select
                  placeholder="Chọn đơn vị có thể thu hồi"
                  allowClear
                  filterOption={false} // Không dùng lọc client-side, để lọc bằng API
                  options={listSaleUnit?.map((item: ListĐVBH) => ({
                    value: item.id,
                    label: item.code + ' - ' + item.nameVN,
                  }))}
                  onChange={onSelectOrgchartProjectAddRootPos}
                  labelInValue
                  showSearch
                  onSearch={handleSearch}
                  onPopupScroll={handlePopupScroll}
                  loading={loadingSaleUnit}
                  dropdownRender={menu => (
                    <>
                      {menu}
                      {loadingSaleUnit && (
                        <div style={{ textAlign: 'center', padding: 10 }}>
                          <Spin size="small" />
                        </div>
                      )}
                    </>
                  )}
                  onFocus={() => {
                    setPage(1);
                    setSearchSaleUnit('');
                  }}
                />
              </Form.Item>
            </Col>
            <Col xs={24} md={8} lg={8}>
              <Form.Item layout="vertical" name="" label="POS thị trường">
                <SingleSelectLazy placeholder="Chọn thị trường" />
              </Form.Item>
            </Col>
          </Row>
        </div>
        <TableComponent
          queryKeyArr={['orgchart-tab', projectId]}
          columns={columns}
          dataSource={orgchartProject}
          loading={isLoading || isPlaceholderData}
          rowKey="id"
          isPagination={false}
        />
      </div>

      {/* Popup thêm đơn vị ĐTHT */}
      <Modal
        title="Thêm đơn vị ĐTHT"
        open={isModalVisible}
        onOk={handleSaveUnits}
        onCancel={() => {
          setIsModalVisible(false);
          setSearchPartnerShip('');
        }}
        footer={null}
        width={400}
      >
        <Input
          placeholder="Tìm kiếm đơn vị ĐTHT"
          style={{ marginBottom: 10 }}
          value={searchPartnerShip}
          onChange={handleSearchPartnerShip}
        />
        {currentListĐTHT ? (
          currentListĐTHT.length > 0 ? (
            currentListĐTHT.map((unit: ListĐTHT) => (
              <div key={unit.id}>
                <Checkbox
                  checked={selectedUnits.includes(unit.id)}
                  onChange={e => handleCheckboxChange(unit.id, e.target.checked)}
                >
                  {`${unit.partnershipCode} - ${unit.partnershipName}`}
                </Checkbox>
              </div>
            ))
          ) : (
            <div style={{ textAlign: 'center', color: 'gray' }}>Không có ĐTHT thuộc đơn vị bán hàng</div>
          )
        ) : (
          <Spin spinning={loadingPartnerShip}></Spin>
        )}
        <div style={{ textAlign: 'end' }}>
          <Button type="primary" onClick={handleSaveUnits} style={{ marginTop: 10 }}>
            Thêm đơn vị
          </Button>
        </div>
      </Modal>
    </>
  );
};

export default OrgchartTab;
