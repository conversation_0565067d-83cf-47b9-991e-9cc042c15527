import React, { useState } from 'react';
import { Button, Upload } from 'antd';
import { FileTextOutlined, FolderOutlined, PlusOutlined, UploadOutlined } from '@ant-design/icons';
import TableComponent from '../../../../../components/table';
import ConfirmCreateModal from '../../../../../components/modal/specials/ConfirmCreateModal';
import { MutationFunction } from '@tanstack/react-query';
import { ColumnType } from 'antd/es/table';
import { DetailProject, DocumentProject } from '../../../../../types/project/project';
import { getListDocumentProject } from '../../../../../service/project';
import { useFetch } from '../../../../../hooks';
import { uploadMedia } from '../../../../../service/upload';
import { AxiosError } from 'axios';
import { RcFile } from 'antd/es/upload';
interface CommonFormProps {
  data?: DetailProject;
}

const DocumentTab: React.FC<CommonFormProps> = ({ data }) => {
  const { data: folder } = useFetch<DocumentProject[]>({
    queryKeyArrWithFilter: ['document-tab', data?.id || ''],
    api: getListDocumentProject,
    moreParams: { id: data?.id },
  });

  const folderList = folder?.data?.data?.rows;
  const [expandedFolders, setExpandedFolders] = useState<Record<string, boolean>>({});

  // Hàm toggle mở rộng folder
  const toggleFolder = (folderId: string) => {
    setExpandedFolders(prev => ({
      ...prev,
      [folderId]: !prev?.[folderId], // Đảo trạng thái mở rộng
    }));
  };

  // Chuyển đổi dữ liệu thành bảng
  const tableDocumentProject = folderList?.flatMap(folder => {
    const folderRow = {
      key: folder.id,
      name: (
        <div onClick={() => toggleFolder(folder.id)} style={{ cursor: 'pointer' }}>
          <FolderOutlined
            style={{
              marginRight: 8,
              padding: 8,
              border: '1px solid rgba(0, 0, 0, 0.15)',
              borderRadius: 6,
            }}
          />
          <span>{folder.name}</span>
        </div>
      ),
      id: folder.id,
      isFolder: true,
      actions: (
        <a href={folder?.id} target="_blank" rel="noopener noreferrer">
          📥 Tải xuống
        </a>
      ),
    };

    // Nếu folder đang mở, thêm các file bên dưới
    const fileRows =
      expandedFolders?.[folder?.id] && folder.projectDocumentFiles.length > 0
        ? folder.projectDocumentFiles.map(file => ({
            key: file.id,
            name: (
              <>
                <FileTextOutlined
                  style={{
                    marginRight: 8,
                    padding: 8,
                    border: '1px solid rgba(0, 0, 0, 0.15)',
                    borderRadius: 6,
                  }}
                />
                `${file.key.split('/').pop()}
              </>
            ),
            id: file.id,
            isFolder: false,
            actions: (
              <a href={file.location} target="_blank" rel="noopener noreferrer">
                📥 Tải xuống
              </a>
            ),
          }))
        : [];

    return [folderRow, ...fileRows];
  });

  const columns: ColumnType<DocumentProject>[] = [
    {
      title: 'File',
      dataIndex: 'name',
      key: 'name',
      width: 786,
      render: (value: string, record: DocumentProject) =>
        record?.isFolder ? <span>{value}</span> : <span>{value}</span>,
    },
    {
      title: 'Dung Lượng',
      dataIndex: 'size',
      key: 'size',
      width: 206,
    },
    {
      title: 'Thao tác',
      dataIndex: 'actions',
      key: 'actions',
      width: 206,
    },
  ];

  const [isOpenModalCreate, setIsOpenModalCreate] = useState<boolean>(false);
  return (
    <div>
      {/* Breadcrumb */}

      <div style={{ textAlign: 'end', marginBottom: '16px' }}>
        <Upload
          maxCount={1}
          showUploadList={false}
          name="logo"
          customRequest={async ({ file, onSuccess, onError }) => {
            try {
              const resp = await uploadMedia(file as RcFile, 'document/file');
              console.log(resp);
              onSuccess?.('ok');
            } catch (error: unknown) {
              onError?.(error as AxiosError);
            }
          }}
        >
          <Button icon={<UploadOutlined />} style={{ marginRight: 8 }}>
            Tải nhập file
          </Button>
        </Upload>
        <Button icon={<PlusOutlined />} type="primary" onClick={() => setIsOpenModalCreate(true)}>
          Tạo mới Folder
        </Button>
      </div>
      <TableComponent dataSource={tableDocumentProject} queryKeyArr={['document-tab']} columns={columns} />

      <ConfirmCreateModal
        open={isOpenModalCreate}
        apiQuery={{} as MutationFunction<unknown, unknown>}
        keyOfListQuery={['document-tab', data?.id || '']}
        onCancel={() => setIsOpenModalCreate(false)}
        title="Tên thư mục"
      />
    </div>
  );
};

export default DocumentTab;
