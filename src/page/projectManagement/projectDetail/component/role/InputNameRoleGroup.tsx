import { Input } from 'antd';
import { useEffect, useState } from 'react';

interface InputNameRoleGroupProps {
  value: string;
  rowId: string;
  handleInputNameRoleGroup: (value: string, rowId: string) => void;
}

const InputNameRoleGroup: React.FC<InputNameRoleGroupProps> = ({ value, rowId, handleInputNameRoleGroup }) => {
  const [stateName, setStateName] = useState(value);

  useEffect(() => {
    setStateName(value);
  }, [value]);

  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setStateName(e.target.value);
  };

  const handleInputName = () => {
    const _stateName = stateName.trim();
    if (_stateName && value !== _stateName) {
      handleInputNameRoleGroup(_stateName, rowId);
    }
    if (rowId === '0') {
      setStateName('');
    }
  };

  return (
    <Input
      onBlur={handleInputName}
      onPressEnter={handleInputName}
      onChange={onChange}
      value={stateName}
      placeholder="Tạo tên nhóm vai trò"
      maxLength={255}
    />
  );
};
export default InputNameRoleGroup;
