import { Button, Checkbox, Col, Flex, Input, List, Modal, Row, Typography } from 'antd';
import { FetchResponse } from '../../../../../hooks';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { debounce } from 'lodash';
import { IInternalEmployee } from '../../../../../types/project/project';
import { getListEmployeeInternal } from '../../../../../service/employee';
import { EmployeeInternal } from '../../../../../types/employee/employee';
import { useInfiniteQuery } from '@tanstack/react-query';
import VirtualList from 'rc-virtual-list';

interface modalAddRoleProps {
  isModalOpen: boolean;
  handleOk: () => void;
  handleCancel: () => void;
  stateInternalEployeeOrigin: IInternalEmployee[];
  handleAddEployeeToRoleGroup: (roleIds: IInternalEmployee[]) => void;
}

interface FilterRole {
  search: string;
  page: number;
}

type InfiniteResponse = {
  pages: FetchResponse<EmployeeInternal[]>[];
  pageParams: number[];
};

const PAGE_SIZE = 10;
const CONTAINER_HEIGHT = 400;

const ModalAddInternalEmployee: React.FC<modalAddRoleProps> = ({
  isModalOpen,
  stateInternalEployeeOrigin,
  handleOk,
  handleCancel,
  handleAddEployeeToRoleGroup,
}) => {
  const [stateFilter, setStateFilter] = useState<FilterRole>({
    search: '',
    page: 1,
  } as FilterRole);

  const [checkedInternalEployees, setCheckedRoles] = useState<IInternalEmployee[]>([]);

  useEffect(() => {
    setCheckedRoles(stateInternalEployeeOrigin);
  }, [stateInternalEployeeOrigin]);

  // const { data: dataInternalEmployee, isFetching } = useFetch<EmployeeInternal[]>({
  //   api: getListEmployeeInternal,
  //   queryKeyArrWithFilter: ['employee-internal', stateFilter],
  //   moreParams: {
  //     page: stateFilter.page,
  //     search: stateFilter.search,
  //   },
  // });

  const onScroll = (e: React.UIEvent<HTMLElement, UIEvent>) => {
    if (Math.abs(e.currentTarget.scrollHeight - e.currentTarget.scrollTop - CONTAINER_HEIGHT) <= 1) {
      fetchNextPage();
    }
  };

  const { data: dataInternalEmployee, fetchNextPage } = useInfiniteQuery<
    FetchResponse<EmployeeInternal[]>, // Kiểu dữ liệu mỗi trang
    Error, // Kiểu lỗi
    InfiniteResponse, // Kiểu dữ liệu của toàn bộ useInfiniteQuery
    [string, FilterRole] // Query Key
  >({
    queryKey: ['employee-internal', stateFilter],
    queryFn: async ({ pageParam = 1 }) =>
      getListEmployeeInternal({
        page: pageParam,
        search: stateFilter.search,
        pageSize: PAGE_SIZE,
      }),

    getNextPageParam: lastPage => {
      const currentPage = lastPage.data?.data?.page ?? 0;
      const totalPages = lastPage.data?.data?.totalPages ?? 0;
      return currentPage < totalPages ? currentPage + 1 : undefined;
    },

    initialPageParam: 1,
  });

  const listDataInternalEmployee = useMemo(() => {
    const data = dataInternalEmployee?.pages.flatMap(page => page?.data?.data?.rows || []) || [];
    return data.map(item => {
      return {
        id: item.id,
        email: item.email,
        accountId: item.accountId,
        name: item.name,
      };
    }) as IInternalEmployee[];
  }, [dataInternalEmployee]);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debounceSearch = useCallback(
    debounce((value: string) => {
      setStateFilter(prev => ({ ...prev, search: value }));
    }, 500), // Thời gian debounce (300ms)
    [],
  );

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    debounceSearch(value); // Gọi debounce function
  };

  const handleCheckRole = (checked: boolean, role: IInternalEmployee) => {
    if (checked) {
      setCheckedRoles([...checkedInternalEployees, role]);
    } else {
      setCheckedRoles(checkedInternalEployees.filter(item => item.id !== role.id));
    }
  };

  const _handleAddRoleToRoleGroup = () => {
    if (JSON.stringify(checkedInternalEployees) !== JSON.stringify(stateInternalEployeeOrigin)) {
      const eployees = checkedInternalEployees.map(item => {
        return {
          id: item.id,
          accountId: item.accountId,
          email: item.email,
          name: item.name,
        };
      });
      handleAddEployeeToRoleGroup(eployees);
    }
  };

  const isCheckbox = (internalEmployee: IInternalEmployee) => {
    return checkedInternalEployees.some(checkedInternalEployee => checkedInternalEployee.id === internalEmployee.id);
  };

  return (
    <>
      <Modal
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        closeIcon={<></>}
        footer={null}
        className="modalAddRoleProject"
      >
        <Row>
          <Col span={24}>
            <Input placeholder="Tìm kiếm thành viên" onChange={handleSearch} />
          </Col>
        </Row>
        <Row>
          <Col span={24}>
            {/* <List
              loading={isFetching}
              bordered
              dataSource={listDataInternalEmployee}
              style={{ border: 'none' }}
              className="listRole"
              renderItem={internalEmployee => (
                <List.Item
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    paddingInline: '12px',
                    borderBlockEnd: 'none',
                    backgroundColor: isCheckbox(internalEmployee) ? '#e6f7ff' : 'white',
                  }}
                >
                  <Checkbox
                    checked={isCheckbox(internalEmployee)}
                    onChange={e => handleCheckRole(e.target.checked, internalEmployee)}
                  >
                    <Typography.Text>
                      {internalEmployee.email} - {internalEmployee.name}
                    </Typography.Text>
                  </Checkbox>
                </List.Item>
              )}
            /> */}
            <List>
              <VirtualList
                data={listDataInternalEmployee}
                height={CONTAINER_HEIGHT}
                itemHeight={47}
                itemKey="id"
                onScroll={onScroll}
              >
                {(internalEmployee: IInternalEmployee) => (
                  <List.Item
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      paddingInline: '12px',
                      borderBlockEnd: 'none',
                      backgroundColor: isCheckbox(internalEmployee) ? '#e6f7ff' : 'white',
                    }}
                  >
                    <Checkbox
                      checked={isCheckbox(internalEmployee)}
                      onChange={e => handleCheckRole(e.target.checked, internalEmployee)}
                    >
                      <Typography.Text>
                        {internalEmployee.email} - {internalEmployee.name}
                      </Typography.Text>
                    </Checkbox>
                  </List.Item>
                )}
              </VirtualList>
            </List>
          </Col>
        </Row>
        <Flex
          justify="flex-end"
          align="center"
          style={{ padding: '8px 5px', borderTop: '1px solid rgba(0, 0, 0, 0.15)' }}
        >
          <Button disabled={!checkedInternalEployees.length} onClick={_handleAddRoleToRoleGroup}>
            Thêm thành viên
          </Button>
        </Flex>
      </Modal>
    </>
  );
};

export default ModalAddInternalEmployee;
