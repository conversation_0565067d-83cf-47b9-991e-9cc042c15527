import { But<PERSON>, Checkbox, Col, Flex, Input, List, Modal, Row, Typography } from 'antd';
import { FetchResponse } from '../../../../../hooks';
import { IManagementRole } from '../../../../userRolesManagement';
import { getListUserRoles } from '../../../../../service/roles';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { debounce } from 'lodash';
import { IRoles } from '../../../../../types/project/project';
import VirtualList from 'rc-virtual-list';
import { useInfiniteQuery } from '@tanstack/react-query';

interface modalAddRoleProps {
  isModalOpen: boolean;
  handleOk: () => void;
  handleCancel: () => void;
  stateRoleOrigin: IRoles[];
  handleAddRoleToRoleGroup: (roleIds: string[]) => void;
}

interface FilterRole {
  search: string;
  page: number;
  isActive: boolean;
}

type InfiniteResponse = {
  pages: FetchResponse<IManagementRole[]>[];
  pageParams: number[];
};

const ModalAddRole: React.FC<modalAddRoleProps> = ({
  isModalOpen,
  stateRoleOrigin,
  handleOk,
  handleCancel,
  handleAddRoleToRoleGroup,
}) => {
  const [stateFilter, setStateFilter] = useState<FilterRole>({
    search: '',
    page: 1,
    isActive: true,
  } as FilterRole);

  const [checkedRoles, setCheckedRoles] = useState<IRoles[]>([]);

  useEffect(() => {
    setCheckedRoles(stateRoleOrigin);
  }, [stateRoleOrigin]);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debounceSearch = useCallback(
    debounce((value: string) => {
      setStateFilter(prev => ({ ...prev, search: value }));
    }, 500), // Thời gian debounce (300ms)
    [],
  );

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    debounceSearch(value); // Gọi debounce function
  };

  const handleCheckRole = (checked: boolean, role: IRoles) => {
    if (checked) {
      setCheckedRoles([...checkedRoles, role]);
    } else {
      setCheckedRoles(checkedRoles.filter(item => item.id !== role.id));
    }
  };

  const _handleAddRoleToRoleGroup = () => {
    if (JSON.stringify(checkedRoles) !== JSON.stringify(stateRoleOrigin)) {
      const roleIds = checkedRoles.map(item => item.id);
      handleAddRoleToRoleGroup(roleIds);
    }
  };

  const isCheckbox = (role: IRoles) => {
    return checkedRoles.some(checkedRole => checkedRole.id === role.id);
  };

  const PAGE_SIZE = 10;
  const CONTAINER_HEIGHT = 400;

  const onScroll = (e: React.UIEvent<HTMLElement, UIEvent>) => {
    if (Math.abs(e.currentTarget.scrollHeight - e.currentTarget.scrollTop - CONTAINER_HEIGHT) <= 1) {
      fetchNextPage();
    }
  };

  const { data: dataRole, fetchNextPage } = useInfiniteQuery<
    FetchResponse<IManagementRole[]>, // Kiểu dữ liệu mỗi trang
    Error, // Kiểu lỗi
    InfiniteResponse, // Kiểu dữ liệu của toàn bộ useInfiniteQuery
    [string, FilterRole] // Query Key
  >({
    queryKey: ['user-roles', stateFilter],
    queryFn: async ({ pageParam = 1 }) =>
      getListUserRoles({
        page: pageParam,
        search: stateFilter.search,
        isActive: stateFilter.isActive,
        pageSize: PAGE_SIZE,
      }),

    getNextPageParam: lastPage => {
      const currentPage = lastPage.data?.data?.page ?? 0;
      const totalPages = lastPage.data?.data?.totalPages ?? 0;
      return currentPage < totalPages ? currentPage + 1 : undefined;
    },

    initialPageParam: 1,
  });

  const listDataRole = useMemo(() => {
    const data = dataRole?.pages.flatMap(page => page?.data?.data?.rows || []) || [];
    return data.map(item => {
      return {
        id: item.id,
        name: item.name,
      };
    }) as IRoles[];
  }, [dataRole]);

  return (
    <>
      <Modal
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        closeIcon={<></>}
        footer={null}
        className="modalAddRoleProject"
      >
        <Row>
          <Col span={24}>
            <Input placeholder="Tìm kiếm vai trò" onChange={handleSearch} />
          </Col>
        </Row>

        <Row>
          <Col span={24}>
            <List>
              <VirtualList
                data={listDataRole}
                height={CONTAINER_HEIGHT}
                itemHeight={47}
                itemKey="id"
                onScroll={onScroll}
              >
                {(role: IRoles) => (
                  <List.Item
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      paddingInline: '12px',
                      borderBlockEnd: 'none',
                      backgroundColor: isCheckbox(role) ? '#e6f7ff' : 'white',
                    }}
                  >
                    <Checkbox checked={isCheckbox(role)} onChange={e => handleCheckRole(e.target.checked, role)}>
                      <Typography.Text>{role.name}</Typography.Text>
                    </Checkbox>
                  </List.Item>
                )}
              </VirtualList>
            </List>
            {/* <List
              loading={isFetching}
              bordered
              dataSource={listDataRole}
              style={{ border: 'none' }}
              renderItem={role => (
                <List.Item
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    paddingInline: '12px',
                    borderBlockEnd: 'none',
                    backgroundColor: isCheckbox(role) ? '#e6f7ff' : 'white',
                  }}
                >
                  <Checkbox checked={isCheckbox(role)} onChange={e => handleCheckRole(e.target.checked, role)}>
                    <Typography.Text>{role.name}</Typography.Text>
                  </Checkbox>
                </List.Item>
              )}
            /> */}
          </Col>
        </Row>
        <Flex
          justify="flex-end"
          align="center"
          style={{ padding: '8px 5px', borderTop: '1px solid rgba(0, 0, 0, 0.15)' }}
        >
          <Button disabled={!checkedRoles.length} onClick={_handleAddRoleToRoleGroup}>
            Thêm vai trò con
          </Button>
        </Flex>
      </Modal>
    </>
  );
};

export default ModalAddRole;
