import React, { useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';
import { IInternalEmployee, IRoleGroup, IRoles } from '../../../../../types/project/project';
import {
  addEployeeToRoleGroup,
  addRoleGroupProject,
  addRoleToRoleGroup,
  deleteRoleGroupProject,
  getRoleGroupProject,
  removeEployeeInRoleGroup,
  removeRoleInRoleGroup,
  updateRoleGroupProject,
} from '../../../../../service/roles';
import { useCreateField, useDeleteField, useFetch, useUpdateField } from '../../../../../hooks';
import { Button, Col, Flex, Modal, Row, TableColumnsType, Tag, Typography } from 'antd';
import TableComponent from '../../../../../components/table';
import { PlusOutlined } from '@ant-design/icons';
import InputNameRoleGroup from './InputNameRoleGroup';
import ModalAddRole from './modalAddRole';
import ModalAddInternalEmployee from './modalAddInternalEmployee';

const { confirm } = Modal;

const Role: React.FC = () => {
  const { id: projectID } = useParams<{ id: string }>();
  const [stateRoleOrigin, setStateRoleOrigin] = useState<IRoles[]>([]);
  const [stateInternalEployeeOrigin, setStateInternalEmployeeOrigin] = useState<IInternalEmployee[]>([]);
  const [stateRoleGroupID, setStateRoleGroupID] = useState<string>('');
  const [stateIsModalAddRole, setStateIsModalAddRole] = useState<boolean>(false);
  const [stateIsModalAddInternalEmployee, setStateIsModalAddInternalEmployee] = useState<boolean>(false);

  const { data: dataRoleProject, isFetching } = useFetch<IRoleGroup[]>({
    queryKeyArr: ['getRoleProject', projectID],
    api: getRoleGroupProject,
    moreParams: { projectID },
  });

  const { mutateAsync: handleAddRoleGroupProject, isPending: isLoadingAddRoleGroupProject } = useCreateField({
    apiQuery: addRoleGroupProject,
    keyOfDetailQuery: ['getRoleProject', projectID],
    messageSuccess: 'Tạo mới vai trò dự án thành công!',
    isMessageError: false,
  });

  const { mutateAsync: _handleAddRoleToRoleGroup } = useCreateField({
    apiQuery: addRoleToRoleGroup,
    keyOfDetailQuery: ['getRoleProject', projectID],
    messageSuccess: 'Thêm mới vai trò vào nhóm thành công!',
    isMessageError: false,
  });

  const { mutateAsync: handleDeleteRoleGroupProject, isPending: isLoadingDeleteRoleGroup } = useDeleteField({
    apiQuery: deleteRoleGroupProject,
    keyOfDetailQuery: ['getRoleProject', projectID],
    messageSuccess: 'Xoá vai trò dự án thành công!',
    isMessageError: false,
  });

  const { mutateAsync: handleUpdateRoleGroupProject } = useUpdateField({
    apiQuery: updateRoleGroupProject,
    keyOfDetailQuery: ['getRoleProject', projectID],
    label: 'vai trò',
    messageSuccess: 'Chỉnh sửa vai trò dự án thành công!',
    isMessageError: false,
  });

  const { mutateAsync: _handleAddEployeeToRoleGroup } = useUpdateField({
    apiQuery: addEployeeToRoleGroup,
    keyOfDetailQuery: ['getRoleProject', projectID],
    label: 'thành viên',
    messageSuccess: 'Thêm mới thành viên vào nhóm thành công!',
    isMessageError: false,
  });

  const listRoleProject = useMemo(() => {
    const data = dataRoleProject?.data?.data || [];
    return [
      {
        id: '0',
        name: '',
        roles: [],
        origin: '',
        internalEmployee: [],
        softDelete: false,
        createdDate: new Date().toISOString(),
        modifiedDate: new Date().toISOString(),
      },
      ...data,
    ];
  }, [dataRoleProject]);

  const handleInputNameRoleGroup = (value: string, rowId: string) => {
    if (rowId === '0') return handleAddRoleGroupProject({ projectID, name: value });
    return handleUpdateRoleGroupProject({ roleGroupID: rowId, name: value });
  };

  const handleAddRole = (roles: IRoles[], id: string) => {
    setStateRoleOrigin(roles.map(role => ({ id: role.id, name: role.name })));
    setStateIsModalAddRole(true);
    setStateRoleGroupID(id);
  };

  const handleAddRoleToRoleGroup = (roleIds: string[]) => {
    _handleAddRoleToRoleGroup({ roleGroupID: stateRoleGroupID, roleId: roleIds });
    setStateIsModalAddRole(false);
  };

  const handleAddEployeeToRoleGroup = (employees: IInternalEmployee[]) => {
    _handleAddEployeeToRoleGroup({ roleGroupID: stateRoleGroupID, internalEmployee: employees });
    setStateIsModalAddInternalEmployee(false);
  };

  const { mutateAsync: handleRemoveRoleInRoleGroup, isPending: isLoadingRemoveRoleInRoleGroup } = useDeleteField({
    apiQuery: removeRoleInRoleGroup,
    keyOfDetailQuery: ['getRoleProject', projectID],
    label: 'vai trò con',
  });

  const { mutateAsync: _handleRemoveEployeeInRoleGroup, isPending: isLoadingRemoveEployeeInRoleGroup } = useDeleteField(
    {
      apiQuery: removeEployeeInRoleGroup,
      keyOfDetailQuery: ['getRoleProject', projectID],
      label: 'thành viên',
    },
  );

  const preventDeleteRole = (e: React.MouseEvent<HTMLElement>, rowId: string, roleId: string) => {
    e.preventDefault();
    handleRemoveRoleInRoleGroup({ roleGroupID: rowId, roleId });
  };

  const preventDeleteEployee = (e: React.MouseEvent<HTMLElement>, rowId: string, employeeId: string) => {
    e.preventDefault();
    _handleRemoveEployeeInRoleGroup({ roleGroupID: rowId, employeeId: employeeId });
  };

  const columns: TableColumnsType<IRoleGroup> = [
    {
      title: 'Tên nhóm vai trò',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: IRoleGroup) => {
        if (record.id === '0') {
          return (
            <InputNameRoleGroup rowId={record.id} value={text} handleInputNameRoleGroup={handleInputNameRoleGroup} />
          );
        }
        return (
          <InputNameRoleGroup rowId={record.id} value={text} handleInputNameRoleGroup={handleInputNameRoleGroup} />
        );
      },
    },
    {
      title: 'Vai trò con',
      dataIndex: 'roles',
      key: 'roles',
      render: (roles: IRoles[], record: IRoleGroup) => {
        if (record.id === '0') {
          return <></>;
        }
        return (
          <>
            <Button
              icon={<PlusOutlined />}
              size="small"
              type="dashed"
              style={{ marginInlineEnd: '8px', height: '23px', marginBlockEnd: '4px' }}
              onClick={() => {
                handleAddRole(roles, record.id);
              }}
            >
              Thêm vai trò con
            </Button>
            {roles.map(role => (
              <Tag
                onClose={e => {
                  preventDeleteRole(e, record.id, role.id);
                }}
                closeIcon
                key={role.id}
                style={{ margin: '4px 0px', marginInlineEnd: '8px' }}
              >
                {role.name}
              </Tag>
            ))}
          </>
        );
      },
    },
    {
      title: 'Thành viên',
      dataIndex: 'internalEmployee',
      key: 'internalEmployee',
      render: (employees: IInternalEmployee[], record: IRoleGroup) => {
        if (record.id === '0') {
          return <></>;
        }
        return (
          <>
            <Button
              icon={<PlusOutlined />}
              size="small"
              type="dashed"
              style={{ marginInlineEnd: '8px', height: '23px', marginBlockEnd: '4px' }}
              onClick={() => {
                setStateInternalEmployeeOrigin(employees || []);
                setStateIsModalAddInternalEmployee(true);
                setStateRoleGroupID(record.id);
              }}
            >
              Thêm thành viên
            </Button>
            {employees?.map(employee => (
              <Tag
                onClose={e => {
                  preventDeleteEployee(e, record.id, employee.id);
                }}
                closeIcon
                key={employee.id}
                style={{ margin: '4px 0px', marginInlineEnd: '8px' }}
              >
                {employee.email}
              </Tag>
            ))}
          </>
        );
      },
    },
    {
      title: 'Hành động',
      key: 'action',
      align: 'center',
      width: '176px',
      render: (_employees: string[], record: IRoleGroup) => {
        if (record.id === '0') {
          return <></>;
        }
        return (
          <>
            <Button
              onClick={async () => {
                confirm({
                  title: 'Xác nhận xoá vai trò dự án',
                  content: (
                    <>
                      <Row>
                        <Col span={24}>
                          <Typography.Text>
                            Xóa nhóm vai trò có thể gây ảnh hưởng tới phân quyền dữ liệu, bạn có muốn tiếp tục xóa?
                          </Typography.Text>
                        </Col>
                      </Row>
                      <Row>
                        <Col span={24}>
                          <Flex gap="small" justify="end" style={{ marginTop: '16px' }}>
                            <Button
                              onClick={() => {
                                Modal.destroyAll();
                              }}
                              type="primary"
                            >
                              Hủy
                            </Button>
                            <Button
                              onClick={async () => {
                                await handleDeleteRoleGroupProject({ roleGroupID: record.id });
                                Modal.destroyAll();
                              }}
                              loading={isLoadingDeleteRoleGroup}
                            >
                              Xóa vai trò
                            </Button>
                          </Flex>
                        </Col>
                      </Row>
                    </>
                  ),
                  footer: null,
                });
              }}
              type="link"
              danger
            >
              Xóa
            </Button>
          </>
        );
      },
    },
  ];

  return (
    <div>
      <Row>
        <Col span={24}>
          <TableComponent
            queryKeyArr={['getRoleProject', projectID]}
            columns={columns}
            loading={
              isFetching ||
              isLoadingRemoveRoleInRoleGroup ||
              isLoadingRemoveEployeeInRoleGroup ||
              isLoadingAddRoleGroupProject
            }
            dataSource={listRoleProject}
            rowKey="id"
            isPagination={false}
          />
        </Col>
      </Row>
      {stateIsModalAddRole && (
        <ModalAddRole
          isModalOpen={stateIsModalAddRole}
          handleOk={() => {
            setStateIsModalAddRole(false);
          }}
          stateRoleOrigin={stateRoleOrigin}
          handleCancel={() => {
            setStateIsModalAddRole(false);
          }}
          handleAddRoleToRoleGroup={handleAddRoleToRoleGroup}
        />
      )}
      {stateIsModalAddInternalEmployee && (
        <ModalAddInternalEmployee
          isModalOpen={stateIsModalAddInternalEmployee}
          handleOk={() => {
            setStateIsModalAddInternalEmployee(false);
          }}
          handleCancel={() => {
            setStateIsModalAddInternalEmployee(false);
          }}
          stateInternalEployeeOrigin={stateInternalEployeeOrigin}
          handleAddEployeeToRoleGroup={handleAddEployeeToRoleGroup}
        />
      )}
    </div>
  );
};

export default Role;
