import {
  But<PERSON>,
  Col,
  DatePicker,
  Form,
  Input,
  Modal,
  Row,
  Select,
  Spin,
  TableColumnsType,
  Tag,
  Typography,
} from 'antd';
import Checkbox, { CheckboxChangeEvent } from 'antd/es/checkbox';
import dayjs, { Dayjs } from 'dayjs';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import BreadCrumbComponent from '../../../../../components/breadCrumb';
import ButtonOfPageDetail from '../../../../../components/button/buttonOfPageDetail';
import { daysOfWeek, defaultWorkingTimes, OPTIONS_STATUS_SALE_PROGRAM } from '../../../../../constants/common';
import { useFetch, useUpdateField } from '../../../../../hooks';
import {
  getBlockByProjectId,
  getListOrgchartProject,
  getListĐTHT,
  getProjectSaleProgramById,
  sendUpdateProjectSaleProgram,
} from '../../../../../service/project';
import {
  CycleWorkingTime,
  IBlock,
  ListĐTHT,
  ProjectSaleProgram,
  SelectValueDVHB,
  WorkingTime,
  WorkingTimeConvert,
} from '../../../../../types/project/project';
import { handleKeyDownBlockSpecialCharacters } from '../../../../../utilities/regex';
import {
  SkeletonFormCheckBox,
  SkeletonFormInput,
  SkeletonFormInputNumber,
  SkeletonFormSelect,
  SkeletonFormSwitch,
  SkeletonFormTimePickerRangePicker,
} from '../../../../../components/skeletonForm';
import TableComponent from '../../../../../components/table';
import { SalesUnitType } from './modalCreateSellProgram';
import { debounce } from 'lodash';
import { PROJECTS_MANAGEMENT } from '../../../../../configs/path';
import { useSellProgramStore } from '../../../../Store';

const { Title } = Typography;

const DetailSellProgram = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [isWorkingTime, setIsWorkingTime] = useState(false);
  const [countByWorkingHours, setCountByWorkingHours] = useState(false);
  const [isCustomerConfirmRequired, setIsCustomerConfirmRequired] = useState(false);
  const [workingTimeConvert, setWorkingTimeConvert] = useState<WorkingTimeConvert[]>(defaultWorkingTimes);
  const [cycleWorkingTime, setCycleWorkingTime] = useState<CycleWorkingTime>({
    fromDay: '',
    toDay: '',
    timeRange: [null, null],
  });

  const [selectedValue, setSelectedValue] = useState<SelectValueDVHB | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [searchPartnerShip, setSearchPartnerShip] = useState<string>('');
  const [currentListĐTHT, setCurrentListĐTHT] = useState<ListĐTHT[]>();
  const [partnerCodeId, setPartnerCodeId] = useState<string>('');
  const [selectedUnits, setSelectedUnits] = useState<string[]>([]);
  const [selectedRow, setSelectedRow] = useState<string | null>(null);
  const [searchSaleUnit, setSearchSaleUnit] = useState<string>('');
  const salesProgramIds = useSellProgramStore(state => state.ArrSalesProgramIds);

  const { id: projectId } = useParams<{ id: string }>();

  const [isFormChanged, setIsFormChanged] = useState<boolean>(false);

  const { data: detailSaleProgram, isLoading } = useFetch<ProjectSaleProgram>({
    queryKeyArr: ['detail-investor'],
    api: () => salesProgramIds && getProjectSaleProgramById(salesProgramIds[0]),
    withFilter: false,
    enabled: !!salesProgramIds,
    cacheTime: 10,
  });
  const dataSaleProgram = detailSaleProgram?.data?.data;

  const { data: listĐTHT, isLoading: loadingPartnerShip } = useFetch<ListĐTHT[]>({
    queryKeyArrWithFilter: ['list-ĐTHT', partnerCodeId || '', searchPartnerShip],
    api: getListĐTHT,
    enabled: !!partnerCodeId, // Chỉ fetch khi partnerCodeId có giá trị hợp lệ
    moreParams: { partnerCode: partnerCodeId, search: searchPartnerShip },
  });
  const { data: dataBlocks } = useFetch<IBlock[]>({
    queryKeyArr: ['getBlockByProjectId', projectId],
    api: () => getBlockByProjectId({ idProject: projectId }),
    withFilter: false,
  });
  const { data: dataSaleUnitOfProject, isLoading: LoadingListSaleUnitOfProject } = useFetch<SalesUnitType[]>({
    queryKeyArr: ['orgchart-tab', projectId, searchSaleUnit],
    api: getListOrgchartProject,
    moreParams: { id: projectId, search: searchSaleUnit },
  });
  const listSaleUnitOfProject = dataSaleUnitOfProject?.data?.data;

  const [orgChartProject, setOrgChartProject] = useState<SalesUnitType[]>([
    { id: '', nameVN: '', code: '', externalOrgcharts: [] },
  ]);

  useEffect(() => {
    if (dataSaleProgram?.internalOrgcharts) {
      const orgCharts =
        dataSaleProgram.internalOrgcharts.length === 0
          ? [{ id: '', nameVN: '', code: '', externalOrgcharts: [] }]
          : [...dataSaleProgram.internalOrgcharts, { id: '', nameVN: '', code: '', externalOrgcharts: [] }];
      setOrgChartProject(orgCharts);
    }
  }, [dataSaleProgram?.internalOrgcharts]);

  const handleTimeChange = (index: number, value: [Dayjs | null, Dayjs | null] | null) => {
    const updatedRange: [Dayjs | null, Dayjs | null] = value ?? [null, null];
    setWorkingTimeConvert(prev => {
      const newWorkingTime = [...prev];
      newWorkingTime[index].timeRange = updatedRange;
      return newWorkingTime;
    });
  };

  const handleCycleWorkingTimeChange = (field: string, value: unknown[] | string) => {
    setCycleWorkingTime(prevState => ({
      ...prevState,
      [field]: value,
    }));
  };

  const [checkboxValues, setCheckboxValues] = useState({
    lockConfirmable: false,
    saleUnitLockConfirmable: false,
    combineSaleUnitConfirmable: false,
    allowSendCustomerStartPriority: false,
    isSuccessTransactionConfirmable: false,
    notSetPriority: false,
    customerConfirmRequired: false,
    allowBookingPriority: false,
    allowViewAllUnitProcessing: false,
    hasPopupAdminConfirm: false,
    holdPropertyWhenUnregister: false,
    unCountPermissionNumPropertyOfUnit: false,
    allowViewPricePublic: false,
    skipCountHasCustomer: false,
  });

  const handleCheckboxChange = (e: CheckboxChangeEvent) => {
    const { name, checked } = e.target;
    if (name) {
      setCheckboxValues(prevState => ({
        ...prevState,
        [name]: checked,
      }));
    }
  };

  const updateProjectSaleProgram = useUpdateField<ProjectSaleProgram>({
    keyOfListQuery: ['project-sale-program', projectId],
    apiQuery: sendUpdateProjectSaleProgram,
    label: 'chương trình bán hàng',
    isMessageError: false,
  });

  const handleSwitchChange = (checked: boolean) => {
    setCountByWorkingHours(checked);
  };
  const handleCustomerConfirmChange = (e: CheckboxChangeEvent) => {
    const { checked } = e.target;
    setIsCustomerConfirmRequired(checked);
    setCheckboxValues(prev => ({
      ...prev,
      customerConfirmRequired: checked,
    }));
  };

  const handleCancel = () => {
    if (isFormChanged) {
      Modal.confirm({
        title: 'Xác nhận hủy',
        content: 'Dữ liệu đang chưa được lưu, bạn có chắc muốn huỷ dữ liệu đang nhập không?',
        cancelText: 'Quay lại',
        onOk: () => {
          navigate(`/projects/${projectId}?tab=4`, {
            replace: true,
          });
        },
        okButtonProps: {
          type: 'default',
        },
        cancelButtonProps: {
          type: 'primary',
        },
      });
    } else {
      navigate(`/projects/${projectId}?tab=4`, {
        replace: true,
      });
    }
  };

  // chuyển đổi dạng data hiển thị thành dạng data gửi xuống Backend
  const convertDataWorkingTime = (data: WorkingTimeConvert[]) => {
    return data.map(item => ({
      name: item.name,
      startTime: item.timeRange[0]?.format('HH:mm:ss') || null,
      endTime: item.timeRange[1]?.format('HH:mm:ss') || null,
    }));
  };

  const handleDateChange = (date: Dayjs | null) => {
    form.setFieldsValue({ openingTimeSale: date });
    form.setFieldsValue({ endTimeSale: null });
  };
  const handleFinish = async (values: ProjectSaleProgram) => {
    let workingTime: WorkingTime[] = [];
    if (isWorkingTime) {
      const { fromDay, toDay, timeRange } = cycleWorkingTime;

      const startIdx = daysOfWeek.indexOf(fromDay);
      const endIdx = daysOfWeek.indexOf(toDay);

      if (startIdx !== -1 && endIdx !== -1 && startIdx <= endIdx) {
        // danh sách các ngày đã chọn.
        const selectedDays = daysOfWeek.slice(startIdx, endIdx + 1);
        workingTime = selectedDays.map(day => ({
          name: day,
          startTime: timeRange[0]?.format('HH:mm:ss') || null,
          endTime: timeRange[1]?.format('HH:mm:ss') || null,
        }));
      }
    } else {
      workingTime = convertDataWorkingTime(workingTimeConvert) as WorkingTime[];
    }
    const internalOrgCharts = orgChartProject
      .filter(item => item.id !== '')
      .map(item => ({
        id: item.id,
        externalOrgchartIds: item.externalOrgcharts?.map(org => org.id) || [],
      }));
    const [openingTimeSale, endTimeSale] = values.saleTimeRange || [null, null];

    const transformedValues = {
      ...values,
      id: salesProgramIds[0] || '',
      project: { id: projectId ?? '' },
      isWorkingTime,
      workingTime: workingTime,
      sharedPos: values.sharedPos || [],
      externalOrgPos: values.externalOrgPos || [],
      priceStatus: values.priceStatus || [],
      countByWorkingHours: countByWorkingHours,
      internalOrgcharts: internalOrgCharts,
      blocks: values.blocks,
      openingTimeSale: openingTimeSale ? openingTimeSale.format('YYYY-MM-DD') : null,
      endTimeSale: endTimeSale ? endTimeSale.format('YYYY-MM-DD') : null,
      ...checkboxValues,
    } as unknown as ProjectSaleProgram;
    const res = await updateProjectSaleProgram.mutateAsync(transformedValues);
    const statusCode = res?.data?.statusCode;
    if (statusCode === '0') {
      form.resetFields();
      navigate(`/projects/${projectId}`, {
        state: { activeTab: '4', refresh: true },
        replace: true,
      });
    }
  };
  const handleSearch = useMemo(
    () =>
      debounce((value: string) => {
        setSearchSaleUnit(value);
      }, 300),
    [],
  );

  useEffect(() => {
    if (dataSaleProgram) {
      form.setFieldsValue({
        ...dataSaleProgram,
        projectName: dataSaleProgram.project?.name,
        saleTimeRange: [
          dataSaleProgram.openingTimeSale ? dayjs(dataSaleProgram.openingTimeSale) : null,
          dataSaleProgram.endTimeSale ? dayjs(dataSaleProgram.endTimeSale) : null,
        ],
        blocks: dataSaleProgram.blocks?.map(block => (typeof block === 'string' ? block : block.id)) || [],
      });
      setIsWorkingTime(dataSaleProgram?.isWorkingTime || false);
      setIsCustomerConfirmRequired(dataSaleProgram.customerConfirmRequired || false);
      setCountByWorkingHours(dataSaleProgram?.countByWorkingHours || false);
      setCheckboxValues({
        lockConfirmable: dataSaleProgram.lockConfirmable || false,
        saleUnitLockConfirmable: dataSaleProgram.saleUnitLockConfirmable || false,
        combineSaleUnitConfirmable: dataSaleProgram.combineSaleUnitConfirmable || false,
        allowSendCustomerStartPriority: dataSaleProgram.allowSendCustomerStartPriority || false,
        isSuccessTransactionConfirmable: dataSaleProgram.isSuccessTransactionConfirmable || false,
        notSetPriority: dataSaleProgram.notSetPriority || false,
        customerConfirmRequired: dataSaleProgram.customerConfirmRequired || false,
        allowBookingPriority: dataSaleProgram.allowBookingPriority || false,
        allowViewAllUnitProcessing: dataSaleProgram.allowViewAllUnitProcessing || false,
        hasPopupAdminConfirm: dataSaleProgram.hasPopupAdminConfirm || false,
        holdPropertyWhenUnregister: dataSaleProgram.holdPropertyWhenUnregister || false,
        unCountPermissionNumPropertyOfUnit: dataSaleProgram.unCountPermissionNumPropertyOfUnit || false,
        allowViewPricePublic: dataSaleProgram.allowViewPricePublic || false,
        skipCountHasCustomer: dataSaleProgram.skipCountHasCustomer || false,
      });
    }
  }, [dataSaleProgram, form]);

  useEffect(() => {
    if (!dataSaleProgram?.workingTime) return;
    if (!dataSaleProgram?.isWorkingTime) {
      // Convert workingtime từ backend để sang dạng hiển thị ở frontend
      const workingTimedata: WorkingTimeConvert[] = dataSaleProgram.workingTime.map((item: WorkingTime) => ({
        name: item.name,
        timeRange: [
          item.startTime ? dayjs(item.startTime, 'HH:mm:ss') : null,
          item.endTime ? dayjs(item.endTime, 'HH:mm:ss') : null,
        ],
      }));

      // map data nhận về từ backend với mảng cố định 7 ngày để lấy ra kết quả cuối cùng.
      // có thể thay đổi tên ở đây để map
      const updatedWorkingTime: WorkingTimeConvert[] = defaultWorkingTimes.map(day => {
        const workingDay = workingTimedata.find(item => item.name === day.name);
        return workingDay || day; // Nếu không map với dữ liệu API trả về, giữ giá trị mặc định từ mảng cố định
      });

      setWorkingTimeConvert(updatedWorkingTime); // Cập nhật state
    } else {
      const fromDay = dataSaleProgram?.workingTime[0]?.name; // lấy ngày đầu tiên
      const toDay = dataSaleProgram?.workingTime[dataSaleProgram?.workingTime.length - 1]?.name; // lấy ngày cuối
      const timeRange: [Dayjs | null, Dayjs | null] = [
        dataSaleProgram?.workingTime[0]?.startTime
          ? dayjs(dataSaleProgram?.workingTime[0]?.startTime, 'HH:mm:ss')
          : null,
        dataSaleProgram?.workingTime[0]?.endTime ? dayjs(dataSaleProgram?.workingTime[0]?.endTime, 'HH:mm:ss') : null,
      ];
      setCycleWorkingTime({ fromDay, toDay, timeRange });
    }
  }, [dataSaleProgram]);

  // Xử lý mở popup chọn ĐTHT
  const showPopup = useCallback(
    (id: string) => {
      const currentRow = orgChartProject.find(item => item.id === id);
      if (currentRow) {
        setSelectedUnits(currentRow.externalOrgcharts?.map(org => org.id) || []);
      }
      setSelectedRow(id);
      setIsModalVisible(true);
    },
    [orgChartProject],
  );

  // Xử lý thay đổi checkbox
  const handleCheckbox = useCallback((unitId: string, checked: boolean) => {
    setSelectedUnits(prev => (checked ? [...prev, unitId] : prev.filter(id => id !== unitId)));
  }, []);
  // Nếu có ID của ĐVBH thì hiển thị danh sách ĐTHT của ĐVBH đó
  useEffect(() => {
    if (partnerCodeId && listĐTHT?.data?.data?.rows) {
      setCurrentListĐTHT(listĐTHT.data.data.rows);
    }
  }, [partnerCodeId, listĐTHT]);

  const handleSearchPartnerShip = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const keyword = e.target.value.toLowerCase();
    setSearchPartnerShip(keyword);
  }, []);

  const handleRemoveUnit = (rowId: string, unitId: string) => {
    setOrgChartProject(prev =>
      prev.map(item =>
        item.id === rowId
          ? {
              ...item,
              externalOrgcharts: item.externalOrgcharts?.filter(unit => unit.id !== unitId) || [],
            }
          : item,
      ),
    );
  };

  //handleDeleteOrgchartProject
  const handleDeleteOrgchartProject = (id: string) => {
    setOrgChartProject(prev => prev.filter(item => item.id !== id));
  };

  const handleSelectĐVHB = async (value: SelectValueDVHB, option: unknown) => {
    if (!value) return;

    const orgChartId = value.value; // ID thật của ĐVBH
    const selectedUnit = option; // Thông tin ĐVBH

    // Nếu là ID khởi tạo, không gửi request và chỉ cập nhật dữ liệu ở UI
    setOrgChartProject(prev => [
      ...prev.filter(item => item.id !== ''), // Giữ lại phần tử cũ không có ID khởi tạo
      {
        id: orgChartId,
        nameVN: (selectedUnit as SalesUnitType).nameVN,
        code: (selectedUnit as SalesUnitType).code,
        externalOrgcharts: prev.find(item => item.id === orgChartId)?.externalOrgcharts || [], // Giữ lại externalOrgcharts của phần tử cũ
      }, // Thêm phần tử mới
      { id: '', nameVN: '', code: '', externalOrgcharts: [] },
    ]);
    setSelectedValue(null);
  };
  const filteredOptions = listSaleUnitOfProject?.filter(item => !orgChartProject.some(org => org.id === item.id));

  const handleSaveUnits = async () => {
    if (selectedRow) {
      // Lấy phần tử có id là selectedRow
      const currentRow = orgChartProject.find(item => item.id === selectedRow);

      if (currentRow) {
        setOrgChartProject(prev =>
          prev.map(item =>
            item.id === selectedRow
              ? {
                  ...item,
                  externalOrgcharts:
                    currentListĐTHT
                      ?.filter(unit => selectedUnits.includes(unit.id)) // Chỉ lấy đơn vị còn được chọn
                      .map(unit => ({
                        id: unit.id,
                        partnershipName: unit.partnershipName,
                        partnershipCode: unit.partnershipCode,
                      })) || [],
                }
              : item,
          ),
        );
      }
    }
    setIsModalVisible(false);
    setSearchPartnerShip('');
  };

  const columns: TableColumnsType<SalesUnitType> = [
    {
      title: 'Tên ĐVBH',
      dataIndex: 'nameVN',
      key: 'nameVN',
      width: 491,
      render: value =>
        value ? (
          <p>{value}</p>
        ) : (
          <Select
            placeholder="Thêm đơn vị bán hàng"
            allowClear
            filterOption={false} // Không dùng lọc client-side, để lọc bằng API
            options={filteredOptions?.map((item: SalesUnitType) => ({
              value: item.id,
              label: item.code + ' - ' + item.nameVN,
              ...item,
            }))}
            onChange={handleSelectĐVHB}
            style={{ width: 250 }}
            labelInValue
            showSearch
            onSearch={handleSearch}
            value={selectedValue}
            loading={LoadingListSaleUnitOfProject}
            dropdownRender={menu => (
              <>
                {menu}
                {LoadingListSaleUnitOfProject && (
                  <div style={{ textAlign: 'center', padding: 10 }}>
                    <Spin size="small" />
                  </div>
                )}
              </>
            )}
          />
        ),
    },
    {
      title: 'Mã ĐVBH',
      key: 'code',
      dataIndex: 'code',
      width: 140,
      render: value => <p>{value}</p>,
    },
    {
      title: 'Tên ĐTHT (F2)',
      key: 'externalOrgchartIds',
      dataIndex: 'externalOrgchartIds',
      render: (_, record) =>
        record.nameVN ? (
          <>
            {record.externalOrgcharts?.map(partner => (
              <Tag
                key={partner.partnershipCode}
                closable
                onClose={() => handleRemoveUnit(record.id, partner.id)}
                style={{
                  fontSize: 12,
                  border: '1px solid rgba(0, 0, 0, 0.15)',
                  borderRadius: '4px',
                  backgroundColor: 'rgba(0, 0, 0, 0.02)',
                  margin: '0 5px 5px 0',
                }}
              >
                {partner.partnershipName}
              </Tag>
            ))}

            <Button
              color="default"
              size="small"
              style={{
                gap: 4,
                padding: '6px 12px',
                fontSize: 12,
                borderRadius: 4,
                height: 22,
                margin: '0 5px 5px 0',
              }}
              onClick={() => {
                setPartnerCodeId(record.id);
                showPopup(record.id);
              }}
            >
              Thêm ĐTHT +
            </Button>
          </>
        ) : null,
    },
    {
      key: 'action',
      align: 'center',
      width: 100,
      render: (_, record) =>
        record.nameVN ? (
          <Button type="link" danger onClick={() => handleDeleteOrgchartProject(record.id)}>
            Xóa
          </Button>
        ) : null,
    },
  ];

  return (
    <Spin spinning={isLoading}>
      <BreadCrumbComponent
        titleBread={`${dataSaleProgram?.name}`}
        customItems={[
          {
            label: `${dataSaleProgram?.project?.name}`,
            path: `${PROJECTS_MANAGEMENT}/${dataSaleProgram?.project.id}`,
          },
        ]}
        noMenu={false}
      />
      <Form
        form={form}
        layout="vertical"
        colon={false}
        labelAlign="left"
        onFinish={handleFinish}
        onValuesChange={() => setIsFormChanged(true)}
      >
        <Row gutter={126}>
          <Col xs={24} lg={12}>
            <Row gutter={[24, 0]}>
              <Col xs={24} md={16} lg={16}>
                <Form.Item label="Dự án" name="projectName" required>
                  <SkeletonFormInput loading={isLoading} disabled placeholder="Nhập tên dự án" />
                </Form.Item>
              </Col>
              <Col xs={24} md={8} lg={8}>
                <Form.Item
                  label="Trạng thái"
                  name="status"
                  rules={[{ required: true, message: 'Vui lòng chọn trạng thái' }]}
                >
                  <SkeletonFormSelect
                    placeholder="Chọn trạng thái"
                    options={OPTIONS_STATUS_SALE_PROGRAM.map(item => {
                      return {
                        value: item.value,
                        label: item.label,
                      };
                    })}
                    loading={isLoading}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={16} lg={16}>
                <Form.Item
                  label="Tên chương trình"
                  name="name"
                  rules={[
                    {
                      required: true,
                      validator: (_, value) => {
                        if (!value || value.trim() === '') {
                          return Promise.reject(new Error('Vui lòng nhập tên chương trình'));
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <SkeletonFormInput
                    placeholder="Nhập tên chương trình"
                    maxLength={50}
                    onBlur={e => {
                      form.setFieldsValue({ name: e.target.value.trim() });
                    }}
                    loading={isLoading}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={8} lg={8}>
                <Form.Item
                  label="Mã chương trình"
                  name="code"
                  rules={[{ required: true, message: 'Vui lòng nhập mã chương trình' }]}
                >
                  <SkeletonFormInput
                    placeholder="Nhập mã chương trình"
                    onKeyDown={e => {
                      if (e.key === ' ') {
                        e.preventDefault();
                      }
                      handleKeyDownBlockSpecialCharacters(e);
                    }}
                    maxLength={20}
                    loading={isLoading}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12} lg={12}>
                <Form.Item label="Thời gian" name="saleTimeRange">
                  <DatePicker.RangePicker
                    placeholder={['Chọn ngày bắt đầu', 'Chọn ngày kết thúc']}
                    format={'DD/MM/YYYY'}
                    allowEmpty={[false, true]}
                    onChange={dates => {
                      handleDateChange(dates ? dates[0] : null);
                      if (dates) {
                        form.setFieldsValue({
                          openingTimeSale: dates[0],
                          endTimeSale: dates[1],
                        });
                      } else {
                        form.setFieldsValue({
                          openingTimeSale: null,
                          endTimeSale: null,
                        });
                      }
                    }}
                    value={
                      form.getFieldValue('openingTimeSale') && form.getFieldValue('endTimeSale')
                        ? [dayjs(form.getFieldValue('openingTimeSale')), dayjs(form.getFieldValue('endTimeSale'))]
                        : null
                    }
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12} lg={12}>
                <Form.Item label="Block" name="blocks" rules={[{ required: true, message: 'Vui lòng chọn block' }]}>
                  <Select
                    allowClear
                    placeholder="Chọn block"
                    mode="multiple"
                    options={dataBlocks?.data?.data?.map(item => ({ value: item.id, label: item.block }))}
                  />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item label="Tự động trả về sản phẩm đăng ký (phút)" name="dwellTime">
                  <SkeletonFormInputNumber
                    loading={isLoading}
                    placeholder="Nhập tự động trả về sản phẩm đăng ký (phút)"
                    min={0}
                    maxLength={3}
                    step={1}
                    parser={value => Math.floor(Number(value))}
                  />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item label="Thời gian book lại sản phẩm đăng ký (phút)" name="dwellTimeReBooking">
                  <SkeletonFormInputNumber
                    loading={isLoading}
                    placeholder="Nhập thời gian book lại sản phẩm đăng ký (phút)"
                    min={0}
                    maxLength={3}
                    step={1}
                    parser={value => Math.floor(Number(value))}
                  />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item label="Tự động trả về sản phẩm đã xác nhận (phút)" name="dwellTimeBookingPriority">
                  <SkeletonFormInputNumber
                    loading={isLoading}
                    placeholder="Nhập tự động trả về sản phẩm đã xác nhận (phút)"
                    min={0}
                    maxLength={3}
                    step={1}
                    parser={value => Math.floor(Number(value))}
                  />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item label="Chiết khấu" name="discount">
                  <SkeletonFormInputNumber loading={isLoading} placeholder="Nhập chiết khẩu" min={0} maxLength={2} />
                </Form.Item>
              </Col>
              <Col>
                <Form.Item layout="horizontal" label="Thời gian làm việc" name="countByWorkingHours">
                  <SkeletonFormSwitch loading={isLoading} style={{ marginLeft: 20 }} onChange={handleSwitchChange} />
                </Form.Item>
              </Col>
              {countByWorkingHours && (
                <>
                  <Col span={24}>
                    <Form.Item>
                      <SkeletonFormCheckBox
                        loading={isLoading}
                        checked={isWorkingTime}
                        onChange={value => setIsWorkingTime(value.target.checked)}
                      >
                        Theo chu kỳ
                      </SkeletonFormCheckBox>
                    </Form.Item>
                  </Col>
                  {isWorkingTime ? (
                    <>
                      <Col span={12}>
                        <Form.Item label="Ngày làm việc trong tuần">
                          <SkeletonFormSelect
                            loading={isLoading}
                            allowClear
                            placeholder="Chọn từ thứ"
                            value={cycleWorkingTime.fromDay || null}
                            options={daysOfWeek.map(day => ({ value: day, label: day }))}
                            onChange={value => handleCycleWorkingTimeChange('fromDay', value as string)}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item>
                          <SkeletonFormSelect
                            loading={isLoading}
                            allowClear
                            placeholder="Chọn đến thứ"
                            style={{ marginTop: 30 }}
                            value={cycleWorkingTime.toDay || null}
                            options={daysOfWeek.map(day => ({ value: day, label: day }))}
                            onChange={value => handleCycleWorkingTimeChange('toDay', value as string)}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={24}>
                        <Form.Item label="Giờ làm việc">
                          <SkeletonFormTimePickerRangePicker
                            loading={isLoading}
                            placeholder={['Chọn giờ bắt đầu', 'Chọn giờ kết thúc']}
                            value={
                              cycleWorkingTime.timeRange[0] && cycleWorkingTime.timeRange[1]
                                ? [dayjs(cycleWorkingTime.timeRange[0]), dayjs(cycleWorkingTime.timeRange[1])]
                                : [null, null]
                            }
                            onChange={value => {
                              if (value && value[0] && value[1]) {
                                handleCycleWorkingTimeChange('timeRange', [value[0], value[1]]);
                              } else {
                                handleCycleWorkingTimeChange('timeRange', [null, null]);
                              }
                            }}
                          />
                        </Form.Item>
                      </Col>
                    </>
                  ) : (
                    <>
                      {workingTimeConvert.map((day, index) => (
                        <Col span={24} key={day.name}>
                          <Form.Item label={daysOfWeek[index]}>
                            <SkeletonFormTimePickerRangePicker
                              loading={isLoading}
                              placeholder={['Chọn giờ bắt đầu', 'Chọn giờ kết thúc']}
                              value={day.timeRange}
                              format="HH:mm:ss"
                              onChange={value => handleTimeChange(index, value)}
                            />
                          </Form.Item>
                        </Col>
                      ))}
                    </>
                  )}
                </>
              )}
            </Row>
          </Col>
          <Col xs={24} lg={12} style={{ marginTop: 20 }}>
            <Form.Item name="lockConfirmable" valuePropName="checked">
              <SkeletonFormCheckBox loading={isLoading} name="lockConfirmable" onChange={handleCheckboxChange}>
                Bổ sung hồ sơ sau
              </SkeletonFormCheckBox>
            </Form.Item>
            <Form.Item name="saleUnitLockConfirmable" valuePropName="checked">
              <SkeletonFormCheckBox loading={isLoading} name="saleUnitLockConfirmable" onChange={handleCheckboxChange}>
                Đơn vị bán hàng xác nhận giao dịch
              </SkeletonFormCheckBox>
            </Form.Item>
            <Form.Item name="combineSaleUnitConfirmable" valuePropName="checked">
              <SkeletonFormCheckBox
                loading={isLoading}
                name="combineSaleUnitConfirmable"
                onChange={handleCheckboxChange}
              >
                Gộp xác nhận và xác nhận bổ sung hồ sơ sau
              </SkeletonFormCheckBox>
            </Form.Item>
            <Form.Item name="allowSendCustomerStartPriority" valuePropName="checked">
              <SkeletonFormCheckBox
                loading={isLoading}
                name="allowSendCustomerStartPriority"
                onChange={handleCheckboxChange}
              >
                Cho phép gửi HS KH khi bắt đầu ưu tiên
              </SkeletonFormCheckBox>
            </Form.Item>
            <Form.Item name="isSuccessTransactionConfirmable" valuePropName="checked">
              <SkeletonFormCheckBox
                loading={isLoading}
                name="isSuccessTransactionConfirmable"
                onChange={handleCheckboxChange}
              >
                Gửi email xác nhận giao dịch thành công
              </SkeletonFormCheckBox>
            </Form.Item>
            <Form.Item name="notSetPriority" valuePropName="checked">
              <SkeletonFormCheckBox loading={isLoading} name="notSetPriority" onChange={handleCheckboxChange}>
                Chương trình bán hàng không ráp ưu tiên
              </SkeletonFormCheckBox>
            </Form.Item>
            <Form.Item name="customerConfirmRequired" valuePropName="checked">
              <SkeletonFormCheckBox
                loading={isLoading}
                name="customerConfirmRequired"
                checked={checkboxValues.customerConfirmRequired}
                onChange={handleCustomerConfirmChange}
              >
                Gửi sms cho khách hàng xác thực
              </SkeletonFormCheckBox>
            </Form.Item>
            {isCustomerConfirmRequired && (
              <>
                <Form.Item
                  name="customerSmsExpiredTime"
                  label="Thời gian hiệu lực sms"
                  rules={[{ required: true, message: 'Vui lòng nhập thời gian hiệu lực sms' }]}
                >
                  <SkeletonFormInputNumber
                    loading={isLoading}
                    placeholder="Nhập thời gian hiệu lực sms"
                    maxLength={3}
                  />
                </Form.Item>
                <Form.Item
                  name="customerSmsContent"
                  label="Nội dung sms gửi khách hàng xác nhận"
                  required
                  rules={[
                    {
                      validator: (_, value) => {
                        if (!value || value.trim() === '') {
                          return Promise.reject(new Error('Vui lòng nhập nội dung sms gửi khách hàng xác nhận'));
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <SkeletonFormInput
                    loading={isLoading}
                    placeholder="Nhập nội dung sms gửi khách hàng xác nhận"
                    maxLength={255}
                    onBlur={e => {
                      form.setFieldsValue({ customerSmsContent: e.target.value.trim() });
                    }}
                  />
                </Form.Item>
                <Form.Item
                  name="customerSmsContentSuccess"
                  label="Nội dung sms gửi khách hàng xác nhận thành công"
                  required
                  rules={[
                    {
                      validator: (_, value) => {
                        if (!value || value.trim() === '') {
                          return Promise.reject(
                            new Error('Vui lòng nhập nội dung sms gửi khách hàng xác nhận thành công'),
                          );
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <SkeletonFormInput
                    loading={isLoading}
                    placeholder="Nhập nội dung sms gửi khách hàng xác nhận thành công"
                    maxLength={255}
                    onBlur={e => {
                      form.setFieldsValue({ customerSmsContentSuccess: e.target.value.trim() });
                    }}
                  />
                </Form.Item>
              </>
            )}
            <Form.Item name="allowBookingPriority" valuePropName="checked">
              <SkeletonFormCheckBox loading={isLoading} name="allowBookingPriority" onChange={handleCheckboxChange}>
                Đặt chỗ chọn sản phẩm
              </SkeletonFormCheckBox>
            </Form.Item>
            <Form.Item name="allowViewAllUnitProcessing" valuePropName="checked">
              <SkeletonFormCheckBox
                loading={isLoading}
                name="allowViewAllUnitProcessing"
                onChange={handleCheckboxChange}
              >
                Đơn vị bán hàng nhìn thấy sản phẩm của ĐVBH khác đã đăng ký
              </SkeletonFormCheckBox>
            </Form.Item>
            <Form.Item name="hasPopupAdminConfirm" valuePropName="checked">
              <SkeletonFormCheckBox loading={isLoading} name="hasPopupAdminConfirm" onChange={handleCheckboxChange}>
                Đơn vị bán hàng xác nhận giao dịch
              </SkeletonFormCheckBox>
            </Form.Item>
            <Form.Item name="holdPropertyWhenUnregister" valuePropName="checked">
              <SkeletonFormCheckBox
                loading={isLoading}
                name="holdPropertyWhenUnregister"
                onChange={handleCheckboxChange}
              >
                Cho phép ĐVBH giữ sp giao đích danh khi trả về
              </SkeletonFormCheckBox>
            </Form.Item>
            <Form.Item name="unCountPermissionNumPropertyOfUnit" valuePropName="checked">
              <SkeletonFormCheckBox
                loading={isLoading}
                name="unCountPermissionNumPropertyOfUnit"
                onChange={handleCheckboxChange}
              >
                Không tính số lượng quyền khi giao sản phẩm đích danh
              </SkeletonFormCheckBox>
            </Form.Item>
            <Form.Item name="allowViewPricePublic" valuePropName="checked">
              <SkeletonFormCheckBox loading={isLoading} name="allowViewPricePublic" onChange={handleCheckboxChange}>
                Đơn vị bán hàng thấy giá không cần đăng ký
              </SkeletonFormCheckBox>
            </Form.Item>
            <Form.Item name="skipCountHasCustomer" valuePropName="checked">
              <SkeletonFormCheckBox loading={isLoading} name="skipCountHasCustomer" onChange={handleCheckboxChange}>
                Không đếm thời gian trả về khi đã nhập khách hàng
              </SkeletonFormCheckBox>
            </Form.Item>
          </Col>
          <Col span={24}>
            <Title style={{ marginTop: 32, marginBottom: 16 }} level={5}>
              Đơn vị bán hàng
            </Title>
            <TableComponent
              columns={columns}
              dataSource={orgChartProject}
              rowKey="id"
              isPagination={false}
              queryKeyArr={[]}
            />
            <Modal
              title="Thêm đơn vị ĐTHT"
              open={isModalVisible}
              onOk={handleSaveUnits}
              onCancel={() => {
                setIsModalVisible(false);
                setSearchPartnerShip('');
              }}
              footer={null}
              width={400}
            >
              <Input
                placeholder="Tìm kiếm đơn vị ĐTHT"
                style={{ marginBottom: 10 }}
                value={searchPartnerShip}
                onChange={handleSearchPartnerShip}
              />
              {currentListĐTHT ? (
                currentListĐTHT.length > 0 ? (
                  currentListĐTHT.map((unit: ListĐTHT) => (
                    <div key={unit.id}>
                      <Checkbox
                        checked={selectedUnits.includes(unit.id)}
                        onChange={e => handleCheckbox(unit.id, e.target.checked)}
                      >
                        {`${unit.partnershipCode} - ${unit.partnershipName}`}
                      </Checkbox>
                    </div>
                  ))
                ) : (
                  <div style={{ textAlign: 'center', color: 'gray' }}>Không có ĐTHT thuộc đơn vị bán hàng</div>
                )
              ) : (
                <Spin spinning={loadingPartnerShip}></Spin>
              )}
              <div style={{ textAlign: 'end' }}>
                <Button type="primary" style={{ marginTop: 10 }} onClick={handleSaveUnits}>
                  Thêm đơn vị
                </Button>
              </div>
            </Modal>
          </Col>
        </Row>
      </Form>
      <ButtonOfPageDetail handleSubmit={() => form.submit()} handleCancel={handleCancel} isShowModal={false} />
    </Spin>
  );
};

export default DetailSellProgram;
