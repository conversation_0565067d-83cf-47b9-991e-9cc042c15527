import { Button, TableColumnsType } from 'antd';
import dayjs from 'dayjs';
import { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import InputSearch from '../../../../../components/input/InputSearch';
import TableComponent from '../../../../../components/table';
import { ActionsColumns } from '../../../../../components/table/components/ActionsColumns';
import { OPERATE_SELL_PROGRAM, PROJECTS_MANAGEMENT, SALES_PROGRAM } from '../../../../../configs/path';
import { useFetch } from '../../../../../hooks';
import { getListProjectSaleProgram } from '../../../../../service/project';
import { DetailProject, ProjectSaleProgram } from '../../../../../types/project/project';
import FormModalSellProgram from './modalCreateSellProgram';
import { useSellProgramStore } from '../../../../Store';

const statusColors = {
  active: '#52C41A',
  inactive: '#CF1322',
};
const getStatusProjectSaleProgram = (status: string) => (
  <p style={{ color: status === '01' ? statusColors.active : statusColors.inactive }}>
    {status === '01' ? 'Đang hoạt động' : 'Ngưng hoạt động'}
  </p>
);
const formatDate = (date: string) => {
  const formattedDate = dayjs(date).format('DD/MM/YYYY');

  return (
    <>
      <div>{date ? formattedDate : ''}</div>
    </>
  );
};
interface CommonFormProps {
  dataProject?: DetailProject;
}

const SellProgram: React.FC<CommonFormProps> = ({ dataProject }) => {
  const { id } = useParams();
  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
  const toggleModal = (isVisible: boolean) => setIsModalVisible(isVisible);
  const navigate = useNavigate();
  const setSalesProgramIds = useSellProgramStore(state => state.ArrSetSalesProgramIds);

  const { data, isLoading } = useFetch<ProjectSaleProgram[]>({
    queryKeyArrWithFilter: ['project-sale-program', id],
    api: getListProjectSaleProgram,
    moreParams: { projectId: id || '' },
  });
  const dataProjectSaleProgram = (data?.data?.data?.rows || []).map(item => ({
    ...item,
    key: item.id,
  }));

  const columns: TableColumnsType<ProjectSaleProgram> = [
    {
      title: 'Mã chương trình',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: 'Tên chương trình bán hàng',
      dataIndex: 'name',
      key: 'name',
      width: '40%',
    },
    {
      title: 'Ngày bắt đầu',
      dataIndex: 'openingTimeSale',
      key: 'openingTimeSale',
      render: formatDate,
    },
    {
      title: 'Ngày kết thúc',
      dataIndex: 'endTimeSale',
      key: 'endTimeSale',
      render: formatDate,
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      render: getStatusProjectSaleProgram,
      align: 'center',
    },
    {
      title: '',
      dataIndex: 'action',
      key: 'action',
      align: 'center',
      render: (_, record) => {
        return (
          <ActionsColumns
            handleViewDetail={() => {
              setSalesProgramIds([record.id]);
              navigate(`${PROJECTS_MANAGEMENT}${SALES_PROGRAM}/${dataProject?.id}`);
            }}
            handleViewInsuranceProgram={() => {
              setSalesProgramIds([record.id]);
              if (dataProject?.id) {
                navigate(`${PROJECTS_MANAGEMENT}${OPERATE_SELL_PROGRAM}/${dataProject?.id}`);
              }
            }}
          />
        );
      },
    },
  ];

  return (
    <div>
      <div className="header-content">
        <InputSearch placeholder="Tìm theo mã chương trình, tên chương trình" />
        <Button type="default" onClick={() => toggleModal(true)}>
          + Thêm chương trình bán hàng
        </Button>
      </div>
      <TableComponent
        dataSource={dataProjectSaleProgram}
        columns={columns}
        queryKeyArr={['project-sale-program', id]}
        loading={isLoading}
      />
      <FormModalSellProgram
        dataProject={dataProject}
        visible={isModalVisible}
        onClose={() => toggleModal(false)}
        keyQuery={['project-sale-program', id]}
      />
    </div>
  );
};

export default SellProgram;
