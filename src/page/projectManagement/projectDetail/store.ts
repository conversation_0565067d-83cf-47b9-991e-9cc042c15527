// src/stores/formReportStore.ts
import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { FormReport } from '../../../types/formReport';
import { DetailProject } from '../../../types/project/project';

// Đ<PERSON><PERSON> nghĩa kiểu cho state
interface FormReportState {
  formData?: FormReport; // Dữ liệu form từ FormReportTab
  setFormData: (data: FormReport) => void; // Hàm để set formData
  clearFormData: () => void; // Hàm để xóa formData
}

// Tạo store với persist dùng sessionStorage
export const useFormReportStore = create<FormReportState>()(
  persist(
    set => ({
      formData: undefined, // Khởi tạo rõ ràng, tránh gán {}
      setFormData: data => set({ formData: data }),
      clearFormData: () => set({ formData: undefined }), // Reset về undefined
    }),
    {
      name: 'form-report-store', // Key trong sessionStorage
      storage: createJSONStorage(() => sessionStorage), // Lưu vào sessionStorage
    },
  ),
);

interface ProjectState {
  project?: DetailProject;
  setProject: (data: DetailProject) => void;
  clearFormData: () => void;
}

// Tạo store với persist dùng sessionStorage
export const useProjectStore = create<ProjectState>()(
  persist(
    set => ({
      project: undefined, // Khởi tạo rõ ràng là undefined
      setProject: data => set({ project: data }),
      clearFormData: () => set({ project: undefined }), // Reset về undefined
    }),
    {
      name: 'project-store', // Key trong sessionStorage
      storage: createJSONStorage(() => sessionStorage), // Lưu vào sessionStorage
    },
  ),
);
