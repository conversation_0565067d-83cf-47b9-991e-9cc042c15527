.project_list-page {
  .type-project {
    margin-bottom: 24px;
    font-size: 12px;
    font-weight: 400;
    line-height: 20px;
    .type {
      font-size: 12px;
    }
    .text {
      color: #00000040;
    }
  }

  .image-container {
    position: relative;
    display: inline-block;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    border-top: 1px solid rgba(0, 0, 0, 0.015);
    border-left: 1px solid rgba(0, 0, 0, 0.015);
    border-right: 1px solid rgba(0, 0, 0, 0.015);
  }

  .button-overlay {
    position: absolute;
    top: 16px;
    right: 16px;
    z-index: 1;
  }
  .tag-active-overlay {
    position: absolute;
    top: 16px;
    left: 16px;
    z-index: 1;
  }
  .ant-card-body {
    padding: 24px 16px;
  }
  .sort-dropdown {
    .ant-select {
      width: 202px;
      .ant-select-selector {
        &::before {
          display: flex;
          align-items: center;
          content: 'Sắp xếp theo: ';
          color: rgba(0, 0, 0, 0.25);
          margin-right: 8px;
        }
      }
      .anticon {
        color: rgba(0, 0, 0, 0.88);
      }
    }
  }
}
