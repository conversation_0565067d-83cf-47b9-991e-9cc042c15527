import './styles.scss';
import { Col, Row, Select, Spin } from 'antd';
import { DownOutlined } from '@ant-design/icons';
import BreadCrumbComponent from '../../components/breadCrumb';
import InputSearch from '../../components/input/InputSearch';
import ProjectCard from './projectCard';
import { useState, useEffect, useCallback } from 'react';
import FormModalProject from './projectCreate';
import { useInfiniteQuery } from '@tanstack/react-query';
import { getListProject } from '../../service/project';

const ProjectList = () => {
  const { Option } = Select;
  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
  const [sortOrder, setSortOrder] = useState<string>('');
  const [search, setSearch] = useState<string | unknown>('');
  const toggleModal = (isVisible: boolean) => setIsModalVisible(isVisible);

  // set lại giá trị sortOrder khi thay đổi.
  const handleSortChange = (value: string) => {
    setSortOrder(value === 'latest' ? '' : 'asc');
  };

  // set lại giá trị search khi tìm kiếm.
  function handleSearch(value: unknown) {
    setSearch(value);
  }

  // fetch data danh sách dự án
  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, isLoading } = useInfiniteQuery({
    queryKey: ['getListProject', search, sortOrder],
    queryFn: ({ pageParam = 1 }) =>
      getListProject({
        page: pageParam,
        pageSize: 12,
        search,
        sortOrder,
      }).then(res => res.data),
    initialPageParam: 1,
    staleTime: 5 * 60 * 1000, // có thể cache trong 5 phút, sau 5 phút sẽ gọi API fetch lại dữ liệu.
    getNextPageParam: lastPage => {
      return lastPage?.data?.rows?.length ? lastPage?.data?.page + 1 : undefined;
    },
  });

  // Lấy danh sách các dự án từ dữ liệu trả về.
  const projects = data?.pages?.flatMap(page => page?.data?.rows) || [];

  // next page khi cuộn tới cuối trang.
  const loadMore = useCallback(() => {
    if (!isLoading && !isFetchingNextPage && hasNextPage) {
      fetchNextPage();
    }
  }, [isLoading, isFetchingNextPage, hasNextPage, fetchNextPage]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      entries => {
        if (entries[0].isIntersecting) {
          loadMore();
        }
      },
      { threshold: 1.0 },
    );
    const sentinelRef = document.getElementById('sentinel');
    if (sentinelRef) {
      observer.observe(sentinelRef);
    }
    return () => {
      if (sentinelRef) {
        observer.unobserve(sentinelRef);
      }
    };
  }, [loadMore]);

  return (
    <>
      <div className="project_list-page">
        <BreadCrumbComponent />
        <div className="header-content">
          <div className="header-content">
            <InputSearch
              style={{ width: 283 }}
              placeholder="Tìm kiếm theo tên, mã dự án"
              onChange={handleSearch}
              showParams={false}
            />
            <div className="sort-dropdown">
              <Select defaultValue="latest" suffixIcon={<DownOutlined />} onChange={handleSortChange}>
                <Option value="latest">Mới nhất</Option>
                <Option value="oldest">Cũ nhất</Option>
              </Select>
            </div>
          </div>
          {/* <Button type="primary" icon={<PlusOutlined />} onClick={() => toggleModal(true)}>
            Tạo mới dự án
          </Button> */}
        </div>
        <Spin spinning={isLoading || isFetchingNextPage}>
          {projects.length > 0 ? (
            <>
              <Row gutter={[24, 24]}>
                {projects.map((p, index) => (
                  <Col key={`${p._id}-${index}`} xs={24} sm={12} md={8} lg={8}>
                    <ProjectCard data={p} />
                  </Col>
                ))}
              </Row>
              {hasNextPage && !isFetchingNextPage && (
                <div id="sentinel">
                  <Spin />
                </div>
              )}
            </>
          ) : (
            <div>
              <h1>Dữ liệu đang trống!</h1>
            </div>
          )}
        </Spin>
        <FormModalProject
          keyQuery={['getListProject', search, sortOrder]}
          visible={isModalVisible}
          onClose={() => toggleModal(false)}
        />
      </div>
    </>
  );
};

export default ProjectList;
