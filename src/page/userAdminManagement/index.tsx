import { App, But<PERSON>, TableColumnsType, Typography } from 'antd';
import './styles.scss';
import { useNavigate } from 'react-router-dom';
import InputSearch from '../../components/input/InputSearch';
import { useFetch } from '../../hooks';
import { getListUserAdmin } from '../../service/employee';
import TableComponent from '../../components/table';
import { ListOfUserAdminProvider } from './context/ListOfUserAdminContext';
import BreadCrumbComponent from '../../components/breadCrumb';
import { USER_ADMIN_MANAGEMENT } from '../../configs/path';
import { ActionsColumns } from '../../components/table/components/ActionsColumns';
import { modalConfirm } from '../../components/modal/specials/ModalConfirm';

const { Text } = Typography;
export interface UserAdminType {
  id?: string | null;
  [key: string]: unknown;
  email?: string;
  code?: string;
  name?: string;
  pos?: {
    name: string;
  };
  role?: {
    name: string;
  };
  active?: boolean;
}
const ListUserAdmin = () => {
  const navigate = useNavigate();
  const { modal } = App.useApp();
  const { data, isLoading } = useFetch<UserAdminType[]>({
    queryKeyArrWithFilter: ['user-admin'],
    api: getListUserAdmin,
  });
  const listUserAdmin =
    data?.data?.data?.rows?.map((user: UserAdminType) => ({
      ...user,
      key: user.id,
    })) ?? [];

  const columns: TableColumnsType<UserAdminType> = [
    {
      title: 'Tên tài khoản',
      dataIndex: 'email',
      key: 'email',
      width: '15%',
    },
    {
      title: 'Mã tài khoản',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: 'Công ty',
      dataIndex: ['pos', 'code'],
      key: 'pos',
    },
    {
      title: 'Vai trò',
      dataIndex: ['role', 'name'],
      key: 'role',
    },
    {
      title: 'Trạng thái',
      dataIndex: 'active',
      align: 'center',
      key: 'active',
      render: value => (value ? <Text type="success">Đã kích hoạt</Text> : <Text type="danger">Vô hiệu hoá</Text>),
    },
    {
      title: 'Hành động',
      key: 'action',
      align: 'center',
      render: (_, record) => {
        const textModalConfirmActive = record?.active ? 'Vô hiệu hoá' : 'Kích hoạt';
        const handleActiveUserAdmin = () => {
          return modalConfirm({
            modal,
            title: `${textModalConfirmActive} user admin`,
            content: `Bạn chắc chắn muốn ${textModalConfirmActive} user admin này?`,
          });
        };
        const openViewDetail = () => {};
        return (
          <ActionsColumns
            handleViewDetail={openViewDetail}
            textModalConfirmActive={textModalConfirmActive}
            handleActive={handleActiveUserAdmin}
          />
        );
      },
    },
  ];

  return (
    <ListOfUserAdminProvider>
      <BreadCrumbComponent />
      <div className="header-content">
        <InputSearch keySearch="query" />
        <Button type="primary" onClick={() => navigate(`${USER_ADMIN_MANAGEMENT}/create`)}>
          Tạo mới
        </Button>
      </div>
      <TableComponent columns={columns} dataSource={listUserAdmin} queryKeyArr={['user-admin']} loading={isLoading} />
    </ListOfUserAdminProvider>
  );
};

export default ListUserAdmin;
