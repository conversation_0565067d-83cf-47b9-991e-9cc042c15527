import { App, But<PERSON>, Col, Form, Input, Row, Select, Switch } from 'antd';
import { useNavigate } from 'react-router-dom';
import { UserAdminType } from '..';
import BreadCrumbComponent from '../../../components/breadCrumb';
import { modalConfirm } from '../../../components/modal/specials/ModalConfirm';
import { useFetch, useUpdateField } from '../../../hooks';
import { getListUserRoles } from '../../../service/roles';
import { getListUnits } from '../../../service/units';
import { sendUpdatePassWordUser } from '../../../service/employee';
import { Units } from '../../../types/units/units';
import { IManagementRole } from '../../userRolesManagement';
import './styles.scss';

interface FormValues {
  active: boolean;
  email: string;
  phone: string;
  pos: {
    id: string;
    name: string;
  };
  roleCanAssign: string[];
  roleId: string;
}

const DetailAndCreateUserAdmin = () => {
  const navigate = useNavigate();
  const { modal } = App.useApp();

  const { data: dataRole } = useFetch<IManagementRole[]>({
    queryKeyArrWithFilter: ['user-roles'],
    api: getListUserRoles,
    withFilter: false,
  });

  const { data: companyData } = useFetch<Units[]>({
    queryKeyArrWithFilter: ['units'],
    api: getListUnits,
    withFilter: false,
    moreParams: { type: 'ORG' },
  });

  const { mutateAsync: updatePassWord } = useUpdateField<UserAdminType>({
    keyOfListQuery: ['user-reset-password'],
    apiQuery: sendUpdatePassWordUser,
    messageSuccess: 'Đã gửi link đặt lại mật khẩu tới email tài khoản đăng ký!',
    messageError: 'Khôi phục không thành công',
  });

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    const { key } = event;
    if (!/^[0-9]$/.test(key) && !['Backspace', 'Delete', 'Tab', 'ArrowLeft', 'ArrowRight'].includes(key)) {
      event.preventDefault();
    }
  };
  const handleUpdatePassWord = () => {
    return modalConfirm({
      modal,
      title: `Đặt lại mật khẩu`,
      content: `Bạn chắc chắn muốn đặt lại mật khẩu user này?`,
      handleConfirm: () =>
        updatePassWord({
          id: '581f2148-9dfb-405c-a72d-37dd0e259b59',
        }),
    });
  };

  const handleSubmit = (values: FormValues) => {
    console.log('abc', values);
  };
  return (
    <div className="form-user-admin">
      <BreadCrumbComponent titleBread="Tạo mới user admin" />
      <Form layout="horizontal" onFinish={handleSubmit} labelCol={{ span: 8 }} labelAlign="left">
        <h3>Thông tin chi tiết</h3>
        <Row className="height-form">
          <Col xxl={8} xl={12} md={24}>
            <Row>
              <Col span={24}>
                <Form.Item
                  label="Tên tài khoản"
                  name="username"
                  rules={[{ required: true, message: 'Vui lòng nhập tên tài khoản' }]}
                >
                  <Input placeholder="Tên tài khoản" />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item label="Mã tài khoản" name="accountCode">
                  <Input disabled placeholder="Mã tài khoản" />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item
                  label="Số điện thoại"
                  name="phoneNumber"
                  rules={[{ required: true, message: 'Vui lòng nhập số điện thoại' }]}
                >
                  <Input onKeyDown={handleKeyDown} placeholder="Số điện thoại" />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item label="Vai trò" name="role">
                  <Select
                    allowClear
                    placeholder="Chọn vai trò"
                    options={dataRole?.data?.data?.rows?.map((item: IManagementRole) => {
                      return {
                        value: item.id,
                        label: item.name,
                      };
                    })}
                  ></Select>
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item label="Công ty" name="pos">
                  <Select
                    allowClear
                    placeholder="Chọn công ty"
                    options={companyData?.data.map(item => {
                      return {
                        value: item.id,
                        label: item.name,
                      };
                    })}
                  />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item label="Trạng thái" name="status" valuePropName="checked">
                  <Switch />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item label="Vai trò được gán" name="assignedRole">
                  <Select
                    allowClear
                    mode="multiple"
                    placeholder="Chọn vai trò"
                    options={dataRole?.data?.data?.rows?.map((item: IManagementRole) => {
                      return {
                        value: item.id,
                        label: item.name,
                      };
                    })}
                  />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Button onClick={handleUpdatePassWord} type="default">
                  Khôi phục mật khẩu
                </Button>
              </Col>
            </Row>
          </Col>
        </Row>
        <div className="btn-submit">
          <Form.Item>
            <Button onClick={() => navigate(-1)}>Hủy</Button>
            <Button style={{ marginLeft: '12px' }} type="primary" htmlType="submit">
              Lưu
            </Button>
          </Form.Item>
        </div>
      </Form>
    </div>
  );
};
export default DetailAndCreateUserAdmin;
