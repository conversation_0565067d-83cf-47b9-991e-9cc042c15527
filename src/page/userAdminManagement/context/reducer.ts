//đ<PERSON>nh nghĩa các action có thể thực hiện được đang để test, sửa lại theo yêu cầu

import { ListOfUserAdminState } from './state';

// payload là dữ liệu truyền vào để update state, đang để string để test
export type ListOfUserAdminAction = { type: 'increment'; payload: string } | { type: 'decrement' };

export const reducer = (state: ListOfUserAdminState, action: ListOfUserAdminAction): ListOfUserAdminState => {
  switch (action.type) {
    case 'increment':
      return state;
    default:
      return state;
  }
};
