import { createContext, useReducer, ReactNode, Dispatch } from 'react';
import { ListOfUserAdminAction, reducer } from './reducer';
import { initialListOfUserAdminState, ListOfUserAdminState } from './state';

interface ListOfUserAdminContextProps {
  state: ListOfUserAdminState;
  dispatch: Dispatch<ListOfUserAdminAction>;
}
interface ListOfUserAdminProviderProps {
  children: ReactNode;
}

export const ListOfUserAdminContext = createContext<ListOfUserAdminContextProps | undefined>(undefined);

export const ListOfUserAdminProvider = ({ children }: ListOfUserAdminProviderProps) => {
  const [state, dispatch] = useReducer(reducer, initialListOfUserAdminState);

  return <ListOfUserAdminContext.Provider value={{ state, dispatch }}>{children}</ListOfUserAdminContext.Provider>;
};
