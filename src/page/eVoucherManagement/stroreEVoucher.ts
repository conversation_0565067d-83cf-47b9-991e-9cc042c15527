import { create } from 'zustand';
import { IEVoucher } from '../../types/eVoucher';

interface IEVoucherStore {
  disabled: boolean;
  initialValue: IEVoucher | undefined;
  isModified: boolean;
  setDisabled: (value: boolean) => void;
  setInitialValue: (value?: IEVoucher) => void;
  setIsModified: (value: boolean) => void;
}

export const useEVoucherStore = create<IEVoucherStore>(set => ({
  disabled: false,
  initialValue: undefined,
  isModified: false,
  setDisabled: value => set({ disabled: value }),
  setInitialValue: value => set({ initialValue: value }),
  setIsModified: value => set({ isModified: value }),
}));
