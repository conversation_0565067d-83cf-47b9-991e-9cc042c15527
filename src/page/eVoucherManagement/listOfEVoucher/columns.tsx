import { TableColumnsType, Typography } from 'antd';
import dayjs from 'dayjs';
import { FORMAT_DATE_TIME, OPTIONS_TYPE_E_VOUCHER } from '../../../constants/common';
import { IEVoucher } from '../../../types/eVoucher';

const { Text } = Typography;

enum Status {
  ACTIVED = 'Đã kích hoạt',
  UNACTIVED = 'Vô hiệu hóa',
}
interface StatusObject {
  key: string;
  value: string;
}
function getStatusObject(status: string): StatusObject {
  switch (status) {
    case Status.ACTIVED:
      return { key: 'ACTIVED', value: '#389E0D' };
    default:
      return { key: 'UNACTIVED', value: '#CF1322' };
  }
}

export const columns: TableColumnsType = [
  {
    title: 'Tên chương trình',
    dataIndex: 'nameOfHandover',
    key: 'nameOfHandover',
    width: 160,
    fixed: 'left',
    render: (value: string) =>
      value ? (
        <div className="cell-name">
          <Text>{value}</Text>
        </div>
      ) : (
        '-'
      ),
  },
  {
    title: 'Tên voucher',
    dataIndex: 'title',
    key: 'title',
    width: 140,
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Loại',
    dataIndex: 'typeEVoucher',
    width: 140,
    key: 'typeEVoucher',
    render: (value: string) => (value ? OPTIONS_TYPE_E_VOUCHER?.find(o => o?.value === value)?.label : '-'),
  },
  {
    title: 'Thời hạn sử dụng',
    dataIndex: 'endTime',
    width: 140,
    key: 'endTime',
    render: (value: string) => <Text>{dayjs(value).format(FORMAT_DATE_TIME)}</Text>,
  },
  {
    title: 'Số lượng phát hành',
    dataIndex: 'amount',
    width: 160,
    key: 'amount',
    render: (value: number) => (value ? value : '-'),
  },
  {
    title: 'Số lượng đã phân phối',
    dataIndex: 'amountOfApply',
    width: 180,
    key: 'amountOfApply',
    render: (value: number) => (value ? value : '-'),
  },
  {
    title: 'Số lượng có hiệu lực',
    dataIndex: 'amountEffective',
    width: 180,
    key: 'amountEffective',
    render: (value: number) => (value ? value : '-'),
  },
  {
    title: 'Số lượng đã sử dụng',
    dataIndex: 'amountOfUsed',
    width: 180,
    key: 'amountOfUsed',
    render: (value: number) => (value ? value : '-'),
  },
  {
    title: 'Ngày tạo',
    dataIndex: 'createdDate',
    key: 'createdDate',
    width: 200,

    render: (value: string, record: IEVoucher) => (
      <>
        <Text>{dayjs(value).format(FORMAT_DATE_TIME)}</Text>
        <br />
        <Text>{record?.createdBy ? `${record?.createdBy?.userName} - ${record?.createdBy?.fullName}` : '-'}</Text>
      </>
    ),
  },
  {
    title: 'Ngày duyệt',
    dataIndex: 'approvedDate',
    key: 'approvedDate',
    width: 200,

    render: (value: string, record: IEVoucher) => (
      <>
        <Text>{dayjs(value).format(FORMAT_DATE_TIME)}</Text> <br />
        <Text>{record?.approvedBy ? `${record?.approvedBy?.userName} - ${record?.approvedBy?.fullName}` : '-'}</Text>
      </>
    ),
  },
  {
    title: 'Ngày cập nhật',
    dataIndex: 'updatedDate',
    key: 'updatedDate',
    width: 200,

    render: (value: string, record: IEVoucher) => (
      <>
        <Text>{value ? dayjs(value).format(FORMAT_DATE_TIME) : '-'}</Text>
        <br />
        <Text>{record?.updatedBy ? `${record?.updatedBy?.userName} - ${record?.updatedBy?.fullName}` : '-'}</Text>
      </>
    ),
  },
  {
    title: 'Trạng thái',
    dataIndex: 'isActive',
    width: 200,
    key: 'isActive',
    align: 'center',
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    render: (value: number) => {
      const statusObject = getStatusObject(value === 1 ? 'Đã kích hoạt' : 'Vô hiệu hóa');
      return <Text style={{ color: statusObject.value }}>{value === 1 ? 'Đã kích hoạt' : 'Vô hiệu hóa'} </Text>;
    },
  },
  {
    title: 'Trạng thái duyệt',
    dataIndex: 'applyStatus',
    width: 200,
    key: 'applyStatus',
    align: 'center',
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    render: (value: string) => {
      return <Text>{value === 'APPROVED' ? 'Đã duyệt' : 'Chờ duyệt'} </Text>;
    },
  },
];
