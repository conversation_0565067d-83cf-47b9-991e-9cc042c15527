import { Col, DatePicker, Form, Row } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useLocation } from 'react-router-dom';
import useFilter from '../../../../../hooks/filter';
import { DEFAULT_PARAMS, FORMAT_DATE } from '../../../../../constants/common';
import DropdownFilterSearch from '../../../../../components/dropdown/dropdownFilterSearch';
import React from 'react';

const { RangePicker } = DatePicker;

type FilterEVoucher = {
  startDate?: string | Dayjs;
  endDate?: string | Dayjs;
};

const FilterEVoucher = () => {
  const { search } = useLocation();
  const [form] = Form.useForm();
  const params = useMemo(() => new URLSearchParams(search), [search]);
  const [filter, setFilter] = useFilter();
  const [initialValues, setInitialValues] = useState<FilterEVoucher>();
  const [isOpenFilter, setIsOpenFilter] = useState(false);
  const rangePickerRef = useRef<React.ComponentRef<typeof DatePicker.RangePicker>>(null);
  const [dates, setDates] = useState<[Dayjs | null, Dayjs | null]>([null, null]);

  useEffect(() => {
    if (params) {
      const initialValue = {
        startDate: params.get('startDate') ? dayjs(params.get('startDate')) : undefined,
        endDate: params.get('endDate') ? dayjs(params.get('endDate')) : undefined,
      };
      setInitialValues(initialValue);
      form.setFieldsValue(initialValue);
    }
  }, [form, params]);

  const handleSubmitFilter = (values: FilterEVoucher) => {
    const newFilter: Record<string, unknown> = {
      startDate: values?.startDate ? dayjs(values?.startDate).format(FORMAT_DATE) : null,
      endDate: values?.endDate ? dayjs(values?.endDate).format(FORMAT_DATE) : null,
    };
    setFilter({ ...filter, page: '1', ...newFilter });
    setIsOpenFilter(false);
  };

  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };

  const handleClearFilters = () => {
    setInitialValues(undefined);
    setFilter({ ...DEFAULT_PARAMS, search: params.get('search') || '' });
    setTimeout(() => {
      setIsOpenFilter(false);
    }, 100);
  };

  const handleChangeSearch = (e: unknown) => {
    const searchTerm = typeof e === 'string' ? e : '';
    setFilter({ ...filter, page: '1', search: searchTerm });
  };

  const handleCalendarChange = React.useCallback(
    (value: [Dayjs | null, Dayjs | null] | null) => {
      // Khai báo hàm xử lý thay đổi ngày trên lịch, memoized bằng useCallback
      if (!value) {
        // Kiểm tra nếu không có giá trị (người dùng xóa ngày)
        if (dates[0] !== null || dates[1] !== null) {
          // Nếu dates hiện tại có ít nhất 1 giá trị
          setDates([null, null]); // Xóa cả hai ngày trong state
        }
        return; // Thoát hàm sau khi xử lý trường hợp null
      }

      const newStart = value[0]; // Gán ngày bắt đầu mới từ value
      const newEnd = value[1]; // Gán ngày kết thúc mới từ value

      if (
        (newStart?.isSame(dates[0], 'day') || (!newStart && !dates[0])) && // So sánh ngày bắt đầu mới với hiện tại hoặc cả hai null
        (newEnd?.isSame(dates[1], 'day') || (!newEnd && !dates[1])) // So sánh ngày kết thúc mới với hiện tại hoặc cả hai null
      ) {
        return; // Thoát nếu không có thay đổi, tránh cập nhật thừa
      }

      setDates([newStart, newEnd]); // Cập nhật state dates với giá trị mới
    },
    [dates],
  );

  const handleChangeRangePicker = React.useCallback(
    (value: [Dayjs | null, Dayjs | null] | null) => {
      // Khai báo hàm xử lý onChange của RangePicker
      if (!value) {
        // Kiểm tra nếu không có giá trị
        if (dates[0] !== null || dates[1] !== null) {
          // Nếu dates có giá trị
          setDates([null, null]); // Xóa dates về [null, null]
        }
        return; // Thoát hàm
      }

      const newStart = value[0]; // Lấy ngày bắt đầu mới
      const newEnd = value[1]; // Lấy ngày kết thúc mới

      if (
        (newStart?.isSame(dates[0], 'day') || (!newStart && !dates[0])) && // Kiểm tra ngày bắt đầu có thay đổi không
        (newEnd?.isSame(dates[1], 'day') || (!newEnd && !dates[1])) // Kiểm tra ngày kết thúc có thay đổi không
      ) {
        return; // Thoát nếu không thay đổi
      }

      setDates([newStart, newEnd]); // Cập nhật dates với giá trị mới
    },
    [dates],
  );

  return (
    <>
      <DropdownFilterSearch
        rootClassName="wrapper-filter"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        showParams={false}
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        initialValues={initialValues}
        defaultValueSearch={params.get('search') || ''}
        keyInputSearch={`search`}
        form={form}
        onChangeSearch={handleChangeSearch}
        onClearFilters={handleClearFilters}
        extraFormItems={
          <>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="Từ ngày" name="startDate">
                  <DatePicker
                    format={FORMAT_DATE}
                    disabledDate={current => {
                      const endDate = form.getFieldValue('endDate');
                      return (
                        (current && current > dayjs().startOf('day')) ||
                        (endDate && current > dayjs(endDate).startOf('day')) // Disable ngày sau "Đến ngày"
                      );
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Đến ngày" name="endDate">
                  <DatePicker
                    format={FORMAT_DATE}
                    disabledDate={current => {
                      const startDate = form.getFieldValue('startDate');
                      return (
                        (current && current > dayjs().startOf('day')) ||
                        (startDate && current < dayjs(startDate).startOf('day')) // Disable ngày trước "Ngày tạo"
                      );
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item
                  label="Thời gian duyệt"
                  name="applicationPeriod"
                  required
                  rules={[
                    {
                      validator: async (_, value: [Dayjs | null, Dayjs | null] | null) => {
                        if (!value || (!value[0] && !value[1])) {
                          return Promise.reject(new Error('Vui lòng chọn thời gian'));
                        }
                        if (!value[0]) {
                          return Promise.reject(new Error('Vui lòng chọn ngày bắt đầu'));
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <RangePicker
                    ref={rangePickerRef}
                    value={dates}
                    onCalendarChange={handleCalendarChange}
                    onChange={handleChangeRangePicker}
                    placeholder={['Từ ngày', 'Đến ngày']}
                    format="DD/MM/YYYY"
                    allowClear={true}
                  />
                </Form.Item>
              </Col>
            </Row>
          </>
        }
      />
    </>
  );
};

export default FilterEVoucher;
