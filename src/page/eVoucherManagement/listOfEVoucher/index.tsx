import { App, Button, Flex, notification, TableColumnsType } from 'antd';
import { useMemo } from 'react';
import BreadCrumbComponent from '../../../components/breadCrumb';
import TableComponent from '../../../components/table';
import { useFetch, useUpdateField } from '../../../hooks';
import './styles.scss';
import React from 'react';
import { columns } from './columns';
import FilterSearch from './components/filterSearch';
import CreateModalEVoucher from '../createEVoucher';
import { E_VOUCHER } from '../../../configs/path';
import { IEVoucher } from '../../../types/eVoucher';
import { modalConfirm } from '../../../components/modal/specials/ModalConfirm';
import { ActionsColumns } from '../../../components/table/components/ActionsColumns';
import ConfirmDeleteModal from '../../../components/modal/specials/ConfirmDeleteModal';
import { MutationFunction, useMutation } from '@tanstack/react-query';
import { ExportOutlined } from '@ant-design/icons';
import { changeStatusEVoucher, deleteEVoucher, exportEVoucher, getListEVoucher } from '../../../service/evoucher';
import { downloadArrayBufferFile } from '../../../utilities/shareFunc';

function ListEVouchers() {
  const { modal } = App.useApp();

  // const {
  //   create: checkCreate,
  //   // changeStatus: checkChangeStatus,
  // } = useCheckPermissions(PERMISSION_COMMISSION);

  const {
    data: listEVoucher,
    isLoading,
    isPlaceholderData,
    isFetching,
  } = useFetch<IEVoucher[]>({
    queryKeyArrWithFilter: ['get-list-e-voucher'],
    api: getListEVoucher,
  });

  const { mutateAsync: updateActive } = useUpdateField({
    apiQuery: changeStatusEVoucher,
    keyOfListQuery: ['get-list-e-voucher'],
    isShowMessage: false,
    isMessageSuccess: false,
  });

  const [isModalVisible, setIsModalVisible] = React.useState<boolean>(false);
  const [isOpenModalDelete, setIsOpenModalDelete] = React.useState<boolean>(false);
  const [currentRecord, setCurrentRecord] = React.useState<IEVoucher>();

  const toggleModal = (isVisible: boolean) => setIsModalVisible(isVisible);

  const columnActions: TableColumnsType<IEVoucher> = useMemo(() => {
    return [
      ...columns,
      {
        title: 'Hành động',
        dataIndex: 'action',
        key: 'action',
        width: '100px',
        align: 'center',
        fixed: 'right',
        render: (_: unknown, record: IEVoucher) => {
          const openViewDetail = () => {
            window.open(`${E_VOUCHER}/${record?.id}`, '_blank', 'noopener noreferrer');
          };
          const handleDeleteEVoucher = () => {
            setIsOpenModalDelete(!isOpenModalDelete);
            setCurrentRecord(record);
          };

          const textModalConfirmActive = record?.isActive === 1 ? 'Vô hiệu hoá' : 'Kích hoạt';
          const handleActiveEVoucher = () => {
            return modalConfirm({
              modal: modal,
              title: `${textModalConfirmActive} e-voucher`,
              content: `Bạn có muốn ${textModalConfirmActive} e-voucher này không?`,
              handleConfirm: async () => {
                const res = await updateActive({
                  id: record?.id ?? '',
                  isActive: record?.isActive === 1 ? 2 : 1,
                });
                if (res?.data?.statusCode === '0') {
                  notification.success({
                    message: record?.isActive === 1 ? 'Vô hiệu hóa thành công' : 'Kích hoạt thành công',
                  });
                  return Promise.resolve();
                } else {
                  notification.error({
                    message: record?.isActive === 1 ? 'Vô hiệu hóa không thành công' : 'Kích hoạt không thành công',
                  });
                  return Promise.reject();
                }
              },
            });
          };

          return (
            <ActionsColumns
              handleViewDetail={openViewDetail}
              textModalConfirmActive={textModalConfirmActive}
              handleActive={handleActiveEVoucher}
              handleDelete={handleDeleteEVoucher}
              // moreActions={[
              //   {
              //     label: 'Duyệt',
              //     key: 'approveEVoucher',
              //     onClick: () => handleApproveEVoucher(record),
              //     disabled: false,
              //   },
              // ]}
            />
          );
        },
      },
    ];
  }, [isOpenModalDelete, modal, updateActive]);

  const exportHistoryMutation = useMutation({
    mutationFn: () => exportEVoucher(),
  });

  const handleSubmitExport = async () => {
    try {
      const response = await exportHistoryMutation.mutateAsync();
      downloadArrayBufferFile({ data: response.data, fileName: `Bao-cao-E_VOUCHER.xlsx` });
    } catch (error) {
      notification.error({ message: 'Xuất dữ liệu thất bại.' });
    }
  };

  return (
    <div className="wrapper-list-e-voucher">
      <BreadCrumbComponent />
      <div className="header-content">
        <Flex gap={17}>
          <FilterSearch />
        </Flex>
        <Flex gap={10}>
          <Button type="default" onClick={handleSubmitExport}>
            <ExportOutlined />
            Xuất excel
          </Button>
          <Button
            type="primary"
            onClick={() => {
              toggleModal(true);
            }}
          >
            Thêm mới
          </Button>
        </Flex>
      </div>
      <div className="table-business-e-voucher">
        <TableComponent
          queryKeyArr={['get-list-e-voucher']}
          columns={columnActions}
          loading={isLoading || isPlaceholderData || isFetching}
          dataSource={listEVoucher?.data?.data?.rows ?? []}
          rowKey="id"
        />
      </div>
      <ConfirmDeleteModal
        label="e-voucher"
        open={isOpenModalDelete}
        apiQuery={deleteEVoucher as MutationFunction<unknown, unknown>}
        keyOfListQuery={['get-list-e-voucher']}
        onCancel={() => setIsOpenModalDelete(false)}
        idDetail={currentRecord?.id}
        title="Xoá e-voucher"
        description="Vui lòng nhập lý do muốn xoá e-voucher"
      />
      <CreateModalEVoucher
        keyQuery={['get-list-e-voucher']}
        visible={isModalVisible}
        onClose={() => {
          toggleModal(false);
        }}
      />
    </div>
  );
}

export default ListEVouchers;
