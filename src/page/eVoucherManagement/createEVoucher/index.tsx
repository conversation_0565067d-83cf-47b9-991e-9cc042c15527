import { Button, Form, Modal, Tabs, TabsProps } from 'antd';
import { useEffect, useState } from 'react';
import { useCreateField } from '../../../hooks';
import './styles.scss';
import React from 'react';
import ModalComponent from '../../../components/modal';
import { QueryKey } from '@tanstack/react-query';
import { IEVoucher, TBusinessPartner, TCreateEVoucher } from '../../../types/eVoucher';
import { useLocation } from 'react-router-dom';
import ApplicbleRecipient from '../component/applicableRecipientsTab';
import { createEVoucher } from '../../../service/evoucher';
import { TAttachment } from '../../../types/proposal';
import ReleasePeriod from '../component/releasePeriodTab';

interface CreateModalEVoucherProps {
  visible: boolean;
  onClose: () => void;
  keyQuery: QueryKey;
}

const CreateModalEVoucher = ({ visible, onClose }: CreateModalEVoucherProps) => {
  const [form] = Form.useForm();

  const location = useLocation();

  const [isModified, setIsModified] = useState(false);
  const [isCreating, setIsCreating] = useState(false);

  const [initialValue, setInitialValue] = useState<IEVoucher>();
  const [actived, setActived] = React.useState(1);
  const [avatarImage, setAvatarImage] = React.useState<TAttachment>();
  const [detailImage, setDetailImage] = React.useState<TAttachment>();

  const [listPartner, setListPartner] = React.useState<TBusinessPartner[]>([]);

  const postCreateEVoucher = useCreateField<TCreateEVoucher>({
    keyOfListQuery: ['get-list-e-voucher'],
    apiQuery: createEVoucher,
    label: 'E-voucher',
  });

  const [activeTab, setActiveTab] = useState((location.state as { activeTab?: string })?.activeTab || '1');
  const handleTabChange = (key: string) => {
    setActiveTab(key);
  };

  // cảnh báo khi thoát trang khi chưa lưu dữ liệu
  useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  const handleCancel = React.useCallback(() => {
    if (form.isFieldsTouched()) {
      Modal.confirm({
        title: 'Xác nhận hủy',
        content: 'Dữ liệu đang chưa được lưu, bạn có chắc muốn hủy dữ liệu đang nhập không?',
        cancelText: 'Quay lại',
        onOk: () => {
          form.resetFields();
          setInitialValue(undefined);
          setListPartner([]);
          onClose();
        },
        okButtonProps: {
          type: 'default',
        },
        cancelButtonProps: {
          type: 'primary',
        },
      });
      setIsModified(false);
      setActiveTab('1');
    } else {
      form.resetFields();
      setIsModified(false);
      setListPartner([]);
      setActiveTab('1');

      onClose();
    }
  }, [form, onClose]);

  const handleFinish = async (values: TCreateEVoucher) => {
    const transformedValues = {
      ...values,
      amount: Number(values?.amount),
      isActive: actived,
      businessPartner: listPartner?.map(o => ({ ...o, merchantLimitAmount: Number(o?.merchantLimitAmount) })),
      avatarImage: avatarImage?.url,
      detailImage: detailImage?.url,
      maxAmountReduced: Number(values?.maxAmountReduced),
      promotionValue: Number(values?.promotionValue),
      applyNvkdAmount: Number(values?.applyNvkdAmount),
      employeeApplyAmount: Number(values?.employeeApplyAmount),
      employeeApplyCommonAmount: Number(values?.employeeApplyCommonAmount),
      applyNvkd: values?.applyNvkd?.map(o => ({ ...o, amount: Number(values?.applyNvkdAmount) })),
      employeeApply: values?.employeeApply?.map(o => ({ ...o, amount: Number(values?.employeeApplyAmount) })),
      employeeApplyCommon: values?.employeeApplyCommon?.map(o => ({
        ...o,
        amount: Number(values?.employeeApplyCommonAmount),
      })),
    };

    try {
      setIsCreating(true);
      const res = await postCreateEVoucher.mutateAsync(transformedValues);
      if (res?.data?.statusCode === '0') {
        form.resetFields();
        setIsModified(false);
        onClose();
      }
      setIsCreating(false);
    } catch (error) {
      setIsCreating(false);
      console.error('Error creating e-voucher policy:', error);
    }
  };

  const validateForm = () => {
    setIsModified(true);
  };

  useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [form, isModified]);

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: 'Đợt phát hành',
      children: (
        <ReleasePeriod
          data={undefined}
          form={form}
          listPartner={listPartner}
          actived={actived}
          setListPartner={setListPartner}
          setActived={setActived}
          avatarImage={avatarImage}
          detailImage={detailImage}
          setAvatarImage={setAvatarImage}
          setDetailImage={setDetailImage}
        />
      ),
    },
    {
      key: '2',
      label: 'Đối tượng áp dụng',
      children: <ApplicbleRecipient />,
    },
  ];

  return (
    <ModalComponent
      title="Tạo mới E-voucher"
      open={visible}
      onCancel={handleCancel}
      destroyOnClose
      footer={
        <Button type="primary" htmlType="submit" onClick={() => form.submit()} loading={isCreating}>
          Lưu
        </Button>
      }
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleFinish}
        onValuesChange={validateForm}
        initialValues={initialValue ? initialValue : { period: 'day' }}
        className="space-y-6"
      >
        <Tabs activeKey={activeTab} onChange={handleTabChange} items={items} tabPosition="top" />
      </Form>
    </ModalComponent>
  );
};

export default CreateModalEVoucher;
