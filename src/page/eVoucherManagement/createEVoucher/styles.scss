.wrapper-detail-personal-proposal {
  .item-gender {
    .ant-row {
      flex-wrap: nowrap;
      .ant-form-item-label {
        flex: 0 0 25% !important;
      }
    }
  }
  .item-birthday .ant-picker {
    width: 100%;
  }
}
.input-link {
  color: #1677ff !important;
  background-color: rgba(0, 0, 0, 0.04) !important;
}
.attachment-file .ant-table-thead > tr > th {
  background-color: white !important; /* <PERSON><PERSON><PERSON> x<PERSON>h d<PERSON> */
}
// .custom-table .ant-table {
//   table-layout: fixed;
// }

// .custom-table .ant-table-thead > tr > th,
// .custom-table .ant-table-tbody > tr > td {
//   vertical-align: middle;
//   white-space: nowrap;
// }

.partner-table .ant-form-item {
  margin: 0 !important;
}
