import { Checkbox, Col, Form, Row, Typography } from 'antd';
import React, { useEffect } from 'react';

import ApplyNvkd from '../applyNvkd';
import EmployeeApply from '../employeeApply';
import EmployeeApplyCommon from '../employeeApplyCommon';
import { useEVoucherStore } from '../../stroreEVoucher';

const { Title } = Typography;

interface ApplicbleRecipientProps {
  setIsModified?: (value: boolean) => void;
}

const ApplicbleRecipient: React.FC<ApplicbleRecipientProps> = () => {
  const form = Form.useFormInstance();
  const { initialValue } = useEVoucherStore();
  const [checkedApplyNvkd, setCheckedApplyNvkd] = React.useState<boolean>(initialValue?.applyNvkd?.length !== 0);
  const [checkedEmployeeApply, setCheckedEmployeeApply] = React.useState<boolean>(
    initialValue?.employeeApply?.length !== 0,
  );
  const [checkedEmployeeApplyCommon, setCheckedEmployeeApplyCommon] = React.useState<boolean>(
    initialValue?.employeeApplyCommon?.length !== 0,
  );

  useEffect(() => {
    if (initialValue) {
      setCheckedApplyNvkd(initialValue?.applyNvkd?.length !== 0);
      setCheckedEmployeeApply(initialValue?.employeeApply?.length !== 0);
      setCheckedEmployeeApplyCommon(initialValue?.employeeApplyCommon?.length !== 0);
    }
  }, [initialValue]);

  return (
    <>
      <Row gutter={{ md: 24, lg: 40, xl: 80, xxl: 126 }}>
        <Col xs={24} md={12}>
          {/*Áp dụng trực tiếp*/}
          <Row gutter={{ md: 24 }}>
            <Col xs={24} md={1}>
              <Checkbox
                onChange={e => {
                  if (!e?.target?.checked) {
                    form?.setFieldValue('applyNvkd', []);
                    form?.setFieldValue('applyNvkdPos', []);
                  }
                  setCheckedApplyNvkd(e?.target?.checked);
                }}
                checked={checkedApplyNvkd}
              />
            </Col>
            <Col xs={24} md={23}>
              <Title level={5}>Áp dụng trực tiếp</Title>
            </Col>
            {checkedApplyNvkd && <ApplyNvkd />}
          </Row>
          {/*Phân phối qua sàn*/}
          <Row gutter={{ md: 24 }}>
            <Col xs={24} md={1}>
              <Checkbox
                onChange={e => {
                  if (!e?.target?.checked) {
                    form?.setFieldValue('employeeApply', []);
                    form?.setFieldValue('employeeApplyPos', []);
                  }
                  setCheckedEmployeeApply(e?.target?.checked);
                }}
                checked={checkedEmployeeApply}
              />
            </Col>
            <Col xs={24} md={23}>
              <Title level={5}>Phân phối qua sàn</Title>
            </Col>

            {checkedEmployeeApply && (
              <>
                <EmployeeApply />
              </>
            )}
          </Row>
          {/*Khai báo thông tin*/}
          <Row gutter={{ md: 24 }}>
            <Col xs={24} md={1}>
              <Checkbox
                onChange={e => {
                  if (!e?.target?.checked) {
                    form?.setFieldValue('employeeApplyCommon', []);
                    form?.setFieldValue('employeeApplyCommonPos', []);
                    form?.setFieldValue('listSelfCreatedVoucher', []);
                  }
                  setCheckedEmployeeApplyCommon(e?.target?.checked);
                }}
                checked={checkedEmployeeApplyCommon}
              />
            </Col>
            <Col xs={24} md={23}>
              <Title level={5}>Khai báo thông tin</Title>
            </Col>
            {checkedEmployeeApplyCommon && (
              <>
                <EmployeeApplyCommon />
              </>
            )}
          </Row>
        </Col>
      </Row>
    </>
  );
};
export default ApplicbleRecipient;
