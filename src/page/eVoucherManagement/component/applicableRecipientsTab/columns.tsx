import { TableColumnsType, Typography } from 'antd';
import { TPos } from '../../../../types/eVoucher';

const { Text } = Typography;

export const applyNvkdColumns: TableColumnsType = [
  {
    title: 'STT',
    dataIndex: 'index',
    key: 'index',
    width: 80,
    fixed: 'left',
    render: (_value, _record, index) => index + 1,
  },
  {
    title: 'NVKD',
    dataIndex: 'name',
    width: '150px',
    key: 'name',
    render: (value: string) => {
      return <Text>{value || '-'}</Text>;
    },
  },
  {
    title: 'Số điện thoại',
    width: '150px',
    dataIndex: 'phone',
    key: 'phone',
    render: (value: string) => {
      return <Text>{value || '-'}</Text>;
    },
  },
  {
    title: 'Email',
    width: '150px',
    dataIndex: 'email',
    key: 'email',
    render: (value: string) => {
      return <Text>{value || '-'}</Text>;
    },
  },
  {
    title: 'Zone/Pos',
    width: '150px',
    dataIndex: 'pos',
    key: 'pos',
    render: (value: TPos) => {
      return <Text>{value?.name || '-'}</Text>;
    },
  },
];
export const employeeApplyColumns: TableColumnsType = [
  {
    title: 'STT',
    dataIndex: 'index',
    key: 'index',
    width: 80,
    fixed: 'left',
    render: (_value, _record, index) => index + 1,
  },
  {
    title: 'NVKD',
    dataIndex: 'name',
    width: '150px',
    key: 'name',
    render: (value: string) => {
      return <Text>{value || '-'}</Text>;
    },
  },
  {
    title: 'Số điện thoại',
    width: '150px',
    dataIndex: 'phone',
    key: 'phone',
    render: (value: string) => {
      return <Text>{value || '-'}</Text>;
    },
  },
  {
    title: 'Email',
    width: '150px',
    dataIndex: 'email',
    key: 'email',
    render: (value: string) => {
      return <Text>{value || '-'}</Text>;
    },
  },
  {
    title: 'Đơn vị',
    width: '150px',
    dataIndex: 'pos',
    key: 'pos',
    render: (value: TPos) => {
      return <Text>{value?.name || '-'}</Text>;
    },
  },
];
export const employeeApplyCommonColumns: TableColumnsType = [
  {
    title: 'STT',
    dataIndex: 'index',
    key: 'index',
    width: 80,
    fixed: 'left',
    render: (_value, _record, index) => index + 1,
  },
  {
    title: 'NVKD',
    dataIndex: 'name',
    width: '150px',
    key: 'name',
    render: (value: string) => {
      return <Text>{value || '-'}</Text>;
    },
  },
  {
    title: 'Số điện thoại',
    width: '150px',
    dataIndex: 'phone',
    key: 'phone',
    render: (value: string) => {
      return <Text>{value || '-'}</Text>;
    },
  },
  {
    title: 'Email',
    width: '150px',
    dataIndex: 'email',
    key: 'email',
    render: (value: string) => {
      return <Text>{value || '-'}</Text>;
    },
  },
  {
    title: 'Đơn vị',
    width: '150px',
    dataIndex: 'pos',
    key: 'pos',
    render: (value: TPos) => {
      return <Text>{value?.name || '-'}</Text>;
    },
  },
];
