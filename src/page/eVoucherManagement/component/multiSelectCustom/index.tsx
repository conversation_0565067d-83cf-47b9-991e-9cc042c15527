import { useMemo } from 'react';
import { Input, Popover, Checkbox, Button, Tag, Form, Tooltip, Spin } from 'antd';
import { SearchOutlined, FilterOutlined } from '@ant-design/icons';
import './styles.scss';
import { FetchResponse, TDataList, useScrollHandlerLazyLoading } from '../../../../hooks';
import { FetchNextPageOptions, InfiniteData, InfiniteQueryObserverResult } from '@tanstack/react-query';
import { debounce } from 'lodash';
import { normalizeString } from '../../../../utilities/regex';

type SelectionMultipleOptions = {
  id: string;
  code?: string;
  name?: string;
};

interface MultipleSelectorCustomProps<T> {
  fieldName: string;
  tempSelected: T[];
  setTempSelected: React.Dispatch<React.SetStateAction<T[]>>;
  data?:
    | InfiniteData<
        FetchResponse<
          TDataList<
            {
              [key: string]: unknown;
            }[]
          >
        >,
        unknown
      >
    | undefined;
  fetchNextPage: (options?: FetchNextPageOptions) => Promise<
    InfiniteQueryObserverResult<
      InfiniteData<
        FetchResponse<
          TDataList<
            {
              [key: string]: unknown;
            }[]
          >
        >,
        unknown
      >,
      Error
    >
  >;
  hasNextPage: boolean;
  isFetchingNextPage: boolean;
  isLoading: boolean;
  search: string;
  setSearch: React.Dispatch<React.SetStateAction<string>>;
  setIsModified?: React.Dispatch<React.SetStateAction<boolean>>;
  addLabel?: string;
}

const MultipleSelectorCustom = <T extends SelectionMultipleOptions>(props: MultipleSelectorCustomProps<T>) => {
  const form = Form.useFormInstance();

  const {
    fieldName,
    tempSelected,
    setTempSelected,
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    search,
    setSearch,
    setIsModified,
    addLabel,
  } = props;

  const listSelected = Form?.useWatch(fieldName, form);

  const scrollHandlerLazy = useScrollHandlerLazyLoading({
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
  });

  const flatData: T[] = useMemo(
    () => (data?.pages.flatMap(page => page?.data?.rows ?? page?.data) || []) as T[],
    [data?.pages],
  );

  const listData = useMemo(() => flatData?.map(i => ({ ...i, label: i?.name, value: i?.id })), [flatData]);
  const filterData = useMemo(() => {
    return listData?.filter(e => e?.label?.toLowerCase().includes(search?.toLowerCase() ?? '')) as T[];
  }, [listData, search]);

  const isAllChecked = filterData?.every(val => tempSelected?.includes(val));
  const isIndeterminate = tempSelected?.some(val => filterData?.includes(val)) && !isAllChecked; //Check hiển thị cho total select item

  const toggleSelectAll = (checked: boolean) => {
    if (checked) {
      const newSelection = Array.from(new Set([...(tempSelected ?? []), ...(filterData || [])]));
      setTempSelected(newSelection);
      setIsModified && setIsModified(true);
    } else {
      setTempSelected(prev => prev.filter(val => !filterData?.includes(val)));
      setIsModified && setIsModified(true);
    }
  };

  const handleRemove = (value: string) => {
    const newVal = tempSelected.filter(v => v?.id !== value);
    setTempSelected(newVal);
    form?.setFieldValue(fieldName, newVal);
    setIsModified && setIsModified(true);
  };

  const handleAdd = () => {
    const newSelection = Array.from(new Set([...tempSelected, ...(tempSelected ?? [])]));
    setTempSelected(newSelection);
    form?.setFieldValue(fieldName, newSelection);
    setIsModified && setIsModified(true);
  };

  const handleSearch = debounce((value: string) => {
    setSearch(value ? normalizeString(value) : '');
  }, 500);

  const content = (
    <div style={{ width: 250 }}>
      <Checkbox
        indeterminate={isIndeterminate}
        checked={isAllChecked}
        onChange={e => toggleSelectAll(e.target.checked)}
      >
        {`${filterData?.length} items`}
      </Checkbox>

      <Input
        size="middle"
        placeholder="Tìm kiếm nhân viên"
        prefix={<SearchOutlined />}
        value={search}
        onChange={e => handleSearch(e?.target?.value)}
        style={{ margin: '8px 0' }}
      />
      <Spin spinning={isLoading || isFetchingNextPage}>
        <div className="custom-select-group" style={{ height: 200, overflowY: 'auto' }} onScroll={scrollHandlerLazy}>
          <Checkbox.Group
            value={tempSelected.map(emp => emp.id)}
            onChange={vals => {
              setTempSelected(filterData.filter(emp => vals.includes(emp.id)) as T[]);
            }}
          >
            <div className="contents-select-group">
              {filterData?.map(emp => (
                <Checkbox key={emp.id} value={emp.id}>
                  {emp.name}
                </Checkbox>
              ))}
            </div>
          </Checkbox.Group>
        </div>
      </Spin>
      {addLabel ? (
        <div className="btn-submit">
          <Button type="default" size="middle" block onClick={handleAdd}>
            {addLabel}
          </Button>
        </div>
      ) : (
        <></>
      )}
    </div>
  );

  return (
    <>
      <Popover content={content} trigger="click" placement="bottomLeft">
        <Input
          readOnly
          placeholder="Search"
          suffix={
            <>
              <FilterOutlined style={{ marginRight: 8 }} />
              <SearchOutlined />
            </>
          }
        />
      </Popover>

      {listSelected ? (
        <>
          <div className="shown-tag">
            {listSelected.slice(0, 2).map((val: T) => {
              return (
                <Tag key={val?.id} closable onClose={() => handleRemove(val?.id)}>
                  {val?.name}
                </Tag>
              );
            })}
            {listSelected.length > 2 && (
              <Tooltip
                title={listSelected
                  .slice(2)
                  .map((val: T) => val?.name || val)
                  .join(', ')}
              >
                <Tag>+{listSelected.length - 2}...</Tag>
              </Tooltip>
            )}
          </div>{' '}
        </>
      ) : (
        <></>
      )}
    </>
  );
};

export default MultipleSelectorCustom;
