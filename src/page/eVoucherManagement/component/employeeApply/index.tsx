import { Button, Col, Form, Input, Radio, Row, Table, TableColumnsType, Typography } from 'antd';
import MultipleSelectorCustom from '../multiSelectCustom';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useInfiniteQuery } from '@tanstack/react-query';
import { FetchResponse, TDataList } from '../../../../hooks';
import { TBusinessPartner } from '../../../../types/eVoucher';
import { handleKeyDownEnterNumber } from '../../../../utilities/regex';
import { DeleteOutlined } from '@ant-design/icons';
import { employeeApplyColumns } from '../columns';
import { useEVoucherStore } from '../../stroreEVoucher';
import { TPos } from '../../../../types/commissionPolicy';
import { getEmployees, getOrgCharts } from '../../../../service/evoucher';
import { DEFAULT_PARAMS_PAGESIZE } from '../../../../constants/common';

const { Item } = Form;
const { Text } = Typography;

const EmployeeApply: React.FC = () => {
  const form = Form.useFormInstance();

  const amountEmployeeApply = Form.useWatch('amountOfPosDistribution', form);
  const formCurrentListEmployeeApply = Form.useWatch('employeeApply', form);
  const formCurrentListEmployeeApplyPos = Form.useWatch('employeeApplyPos', form);

  const { initialValue } = useEVoucherStore();
  const defaultListEmployeeApply = useRef(initialValue?.employeeApply);
  const defaultListEmployeeApplyPos = useRef(initialValue?.employeeApply?.map(item => item?.pos));

  const {
    data: dataEmployees,
    fetchNextPage: fetchNextPageEmployee,
    hasNextPage: hasNextPageEmployee,
    isFetchingNextPage: isFetchingNextPageEmployee,
    isLoading: isLoadingEmployee,
  } = useInfiniteQuery<FetchResponse<TDataList<{ [key: string]: unknown }[]>>>({
    queryKey: ['get-employees', formCurrentListEmployeeApplyPos],
    queryFn: ({ pageParam = 1 }) =>
      getEmployees({
        page: pageParam as number,
        pageSize: DEFAULT_PARAMS_PAGESIZE,
        search,
        orgcharts: formCurrentListEmployeeApplyPos?.map((o: TPos) => o?.id)?.join(','),
      }).then(res => res.data as FetchResponse<TDataList<{ [key: string]: unknown }[]>>),
    initialPageParam: 1,
    staleTime: 50000,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    getNextPageParam: (lastPage: any) => {
      const hasMore = lastPage?.data?.rows?.length > 0 && lastPage?.data?.totalPages > lastPage?.data?.page;
      return hasMore ? Number(lastPage?.data?.page) + 1 : undefined;
    },
  });

  const {
    data: dataPos,
    fetchNextPage: fetchNextPagePos,
    hasNextPage: hasNextPagePos,
    isFetchingNextPage: isFetchingNextPagePos,
    isLoading: isLoadingPos,
  } = useInfiniteQuery<FetchResponse<TDataList<{ [key: string]: unknown }[]>>>({
    queryKey: ['get-pos'],
    queryFn: ({ pageParam = 1 }) =>
      getOrgCharts({ page: pageParam as number, pageSize: DEFAULT_PARAMS_PAGESIZE, search }).then(
        res => res.data as FetchResponse<TDataList<{ [key: string]: unknown }[]>>,
      ),
    initialPageParam: 1,
    staleTime: 50000,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    getNextPageParam: (lastPage: any) => {
      const hasMore = lastPage?.data?.rows?.length > 0 && lastPage?.data?.totalPages > lastPage?.data?.page;
      return hasMore ? Number(lastPage?.data?.page) + 1 : undefined;
    },
  });

  const [listEmployeeApplyPos, setListEmployeeApplyPos] = useState<TPos[]>([]);
  const [listEmployeeApply, setListEmployeeApply] = useState<TBusinessPartner[]>([]);
  const [search, setSearch] = useState<string>('');

  useEffect(() => {
    if (
      Array.isArray(defaultListEmployeeApply?.current) &&
      defaultListEmployeeApply?.current &&
      defaultListEmployeeApply?.current?.length > 0
    ) {
      setListEmployeeApply(defaultListEmployeeApply?.current);
    }
    if (
      Array.isArray(defaultListEmployeeApplyPos?.current) &&
      defaultListEmployeeApplyPos?.current &&
      defaultListEmployeeApplyPos?.current?.length > 0
    ) {
      const employeeApplyPos = defaultListEmployeeApplyPos?.current
        ?.map(item => item)
        .filter((pos, index, array) => pos && array.findIndex(p => p?.id === pos?.id) === index);
      setListEmployeeApplyPos(employeeApplyPos as TPos[]);
    }
  }, [defaultListEmployeeApply, defaultListEmployeeApplyPos, initialValue]);

  const handleRemove = useCallback(
    (value: string) => {
      const newVal = listEmployeeApply.filter(v => v?.id !== value);
      setListEmployeeApply(newVal);
      form?.setFieldValue('employeeApply', newVal);
    },
    [form, listEmployeeApply],
  );

  const actionEmployeeApplyColumns: TableColumnsType = useMemo(() => {
    return [
      ...employeeApplyColumns,
      {
        title: 'Số lượng',
        width: '100px',
        dataIndex: 'amount',
        key: 'amount',
        render: () => {
          return <Text>{amountEmployeeApply || '-'}</Text>;
        },
      },
      {
        dataIndex: 'action',
        align: 'center',
        width: '80px',
        render: (_, record: TBusinessPartner) => {
          return (
            <Text style={{ color: '#1677FF', cursor: 'pointer' }} onClick={() => handleRemove(record?.id)}>
              Xóa
            </Text>
          );
        },
      },
    ];
  }, [amountEmployeeApply, handleRemove]);

  return (
    <>
      <Col xs={24} md={24}>
        <Item
          name={'amountOfPosDistribution'}
          label="Số lượng"
          rules={[
            ({ getFieldValue }) => ({
              validator: async (_, value) => {
                const amount = getFieldValue('amount');

                if (amount && Number(value) > Number(amount)) {
                  return Promise.reject('Vui lòng nhập số lượng nhỏ hơn hoặc bằng số lượng phát hành');
                }
                if (!value || value.toString().trim() === '') {
                  return Promise.reject(new Error('Vui lòng nhập số lượng'));
                }
                return Promise.resolve();
              },
              validateTrigger: ['onChange'],
            }),
          ]}
        >
          <Input onKeyDown={handleKeyDownEnterNumber} placeholder="Nhập số lượng" maxLength={13} />
        </Item>
      </Col>
      <Col xs={24} md={24}>
        <Item name={'confirmBeforeUse'} label="Xác nhận e-voucher trước khi sử dụng">
          <Radio.Group
            options={[
              { label: 'Có', value: 'yes' },
              { label: 'Không', value: 'no' },
            ]}
            defaultValue="yes"
          />
        </Item>
      </Col>
      <Col xs={24} md={24}>
        <Item name="employeeApplyPos" label="Đơn vị xác nhận">
          <MultipleSelectorCustom<TPos>
            fieldName="employeeApplyPos"
            tempSelected={listEmployeeApplyPos}
            setTempSelected={setListEmployeeApplyPos}
            data={dataPos}
            fetchNextPage={fetchNextPagePos}
            hasNextPage={hasNextPagePos}
            isFetchingNextPage={isFetchingNextPagePos}
            isLoading={isLoadingPos}
            search={search}
            setSearch={setSearch}
            addLabel="Thêm đơn vị"
          />
        </Item>
      </Col>
      <Col xs={24} md={24}>
        <Item
          name="employeeApply"
          label="Nhân viên xác nhận"
          rules={[{ required: true, message: 'Vui lòng chọn nhân viên' }]}
        >
          <MultipleSelectorCustom<TBusinessPartner>
            fieldName="employeeApply"
            tempSelected={listEmployeeApply}
            setTempSelected={setListEmployeeApply}
            data={dataEmployees}
            fetchNextPage={fetchNextPageEmployee}
            hasNextPage={hasNextPageEmployee}
            isFetchingNextPage={isFetchingNextPageEmployee}
            isLoading={isLoadingEmployee}
            search={search}
            setSearch={setSearch}
            addLabel="Thêm nhân viên"
          />
        </Item>
      </Col>
      <Col xs={24} md={12} style={{ marginBottom: '16px' }}>
        <Row gutter={{ md: 24, lg: 10 }}>
          <Col xs={24} md={16}>
            <Button
              icon={<DeleteOutlined />}
              onClick={() => {
                setListEmployeeApply([]);
                form?.setFieldValue('employeeApply', []);
              }}
            >
              Xóa tất cả
            </Button>
          </Col>
        </Row>
      </Col>
      <Col xs={24} md={24} style={{ marginBottom: '16px' }}>
        <Table
          className=""
          dataSource={formCurrentListEmployeeApply}
          columns={actionEmployeeApplyColumns}
          pagination={false}
        />
      </Col>
    </>
  );
};

export default EmployeeApply;
