import { Button, Col, Form, Input, Row, Table, TableColumnsType, Typography } from 'antd';
import MultipleSelectorCustom from '../multiSelectCustom';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useInfiniteQuery } from '@tanstack/react-query';
import { FetchResponse, TDataList } from '../../../../hooks';
import { TBusinessPartner } from '../../../../types/eVoucher';
import { handleKeyDownEnterNumber } from '../../../../utilities/regex';
import { DeleteOutlined } from '@ant-design/icons';
import { applyNvkdColumns } from '../columns';
import { useEVoucherStore } from '../../stroreEVoucher';
import { TPos } from '../../../../types/commissionPolicy';
import { getEmployees, getOrgCharts } from '../../../../service/evoucher';
import { DEFAULT_PARAMS_PAGESIZE } from '../../../../constants/common';

const { Item } = Form;
const { Text } = Typography;

const ApplyNvkd: React.FC = () => {
  const form = Form.useFormInstance();

  const amountApplyNvkd = Form.useWatch('applyNvkdAmount', form);
  const formCurrentListApplyNvkd = Form.useWatch('applyNvkd', form);
  const formCurrentListApplyNvkdPos = Form.useWatch('applyNvkdPos', form);

  const { initialValue } = useEVoucherStore();
  const defaultListApplyNvkd = useRef(initialValue?.applyNvkd);
  const defaultListApplyNvkdPos = useRef(initialValue?.applyNvkd?.map(item => item?.pos));

  const {
    data: dataEmployees,
    fetchNextPage: fetchNextPageEmployee,
    hasNextPage: hasNextPageEmployee,
    isFetchingNextPage: isFetchingNextPageEmployee,
    isLoading: isLoadingEmployee,
  } = useInfiniteQuery<FetchResponse<TDataList<{ [key: string]: unknown }[]>>>({
    queryKey: ['get-employees', formCurrentListApplyNvkdPos],
    queryFn: ({ pageParam = 1 }) =>
      getEmployees({
        page: pageParam as number,
        pageSize: DEFAULT_PARAMS_PAGESIZE,
        search,
        orgcharts: formCurrentListApplyNvkdPos?.map((o: TPos) => o?.id)?.join(','),
      }).then(res => res.data as FetchResponse<TDataList<{ [key: string]: unknown }[]>>),
    initialPageParam: 1,
    staleTime: 50000,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    getNextPageParam: (lastPage: any) => {
      const hasMore = lastPage?.data?.rows?.length > 0 && lastPage?.data?.totalPages > lastPage?.data?.page;
      return hasMore ? Number(lastPage?.data?.page) + 1 : undefined;
    },
  });

  const {
    data: dataPos,
    fetchNextPage: fetchNextPagePos,
    hasNextPage: hasNextPagePos,
    isFetchingNextPage: isFetchingNextPagePos,
    isLoading: isLoadingPos,
  } = useInfiniteQuery<FetchResponse<TDataList<{ [key: string]: unknown }[]>>>({
    queryKey: ['get-pos'],
    queryFn: ({ pageParam = 1 }) =>
      getOrgCharts({ page: pageParam as number, pageSize: DEFAULT_PARAMS_PAGESIZE, search }).then(
        res => res.data as FetchResponse<TDataList<{ [key: string]: unknown }[]>>,
      ),
    initialPageParam: 1,
    staleTime: 50000,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    getNextPageParam: (lastPage: any) => {
      const hasMore = lastPage?.data?.rows?.length > 0 && lastPage?.data?.totalPages > lastPage?.data?.page;
      return hasMore ? Number(lastPage?.data?.page) + 1 : undefined;
    },
  });

  const [listApplyNvkdPos, setListApplyNvkdPos] = useState<TPos[]>([]);
  const [listApplyNvkd, setListApplyNvkd] = useState<TBusinessPartner[]>([]);
  const [search, setSearch] = useState<string>('');

  useEffect(() => {
    if (
      Array.isArray(defaultListApplyNvkd?.current) &&
      defaultListApplyNvkd?.current &&
      defaultListApplyNvkd?.current?.length > 0
    ) {
      setListApplyNvkd(defaultListApplyNvkd?.current);
    }
    if (
      Array.isArray(defaultListApplyNvkdPos?.current) &&
      defaultListApplyNvkdPos?.current &&
      defaultListApplyNvkdPos?.current?.length > 0
    ) {
      const applyNvkdPos = defaultListApplyNvkdPos?.current
        ?.map(item => item)
        .filter((pos, index, array) => pos && array.findIndex(p => p?.id === pos?.id) === index);
      setListApplyNvkdPos(applyNvkdPos as TPos[]);
    }
  }, [defaultListApplyNvkd, defaultListApplyNvkdPos]);

  const handleRemove = useCallback(
    (value: string) => {
      const newVal = listApplyNvkd.filter(v => v?.id !== value);
      setListApplyNvkd(newVal);
      form?.setFieldValue('applyNvkd', newVal);
    },
    [form, listApplyNvkd],
  );

  const actionApplyNvkdColumns: TableColumnsType = useMemo(() => {
    return [
      ...applyNvkdColumns,
      {
        title: 'Số lượng',
        width: '100px',
        dataIndex: 'amount',
        key: 'amount',
        render: () => {
          return <Text>{amountApplyNvkd || '-'}</Text>;
        },
      },
      {
        dataIndex: 'action',
        align: 'center',
        width: '80px',
        render: (_, record: TBusinessPartner) => {
          return (
            <Text style={{ color: '#1677FF', cursor: 'pointer' }} onClick={() => handleRemove(record?.id)}>
              Xóa
            </Text>
          );
        },
      },
    ];
  }, [amountApplyNvkd, handleRemove]);

  return (
    <>
      <Col xs={24} md={24}>
        <Item name="applyNvkdPos" label="Đơn vị bán hàng">
          <MultipleSelectorCustom
            fieldName="applyNvkdPos"
            tempSelected={listApplyNvkdPos}
            setTempSelected={setListApplyNvkdPos}
            data={dataPos}
            fetchNextPage={fetchNextPagePos}
            hasNextPage={hasNextPagePos}
            isFetchingNextPage={isFetchingNextPagePos}
            isLoading={isLoadingPos}
            search={search}
            setSearch={setSearch}
            addLabel="Thêm đơn vị"
          />
        </Item>
      </Col>
      <Col xs={24} md={24}>
        <Item
          name="applyNvkd"
          label="Nhân viên kinh doanh"
          rules={[{ required: true, message: 'Vui lòng chọn nhân viên' }]}
        >
          <MultipleSelectorCustom<TBusinessPartner>
            fieldName="applyNvkd"
            tempSelected={listApplyNvkd}
            setTempSelected={setListApplyNvkd}
            data={dataEmployees}
            fetchNextPage={fetchNextPageEmployee}
            hasNextPage={hasNextPageEmployee}
            isFetchingNextPage={isFetchingNextPageEmployee}
            isLoading={isLoadingEmployee}
            search={search}
            setSearch={setSearch}
            addLabel="Thêm nhân viên"
          />
        </Item>
      </Col>
      <Col xs={24} md={24}>
        <Item
          name={'applyNvkdAmount'}
          label="Số lượng"
          required
          rules={[
            ({ getFieldValue }) => ({
              validator: async (_, value) => {
                const amount = getFieldValue('amount');

                if (amount && Number(value) > Number(amount)) {
                  return Promise.reject('Vui lòng nhập số lượng nhỏ hơn hoặc bằng số lượng phát hành');
                }
                if (!value || value.toString().trim() === '') {
                  return Promise.reject(new Error('Vui lòng nhập số lượng'));
                }
                return Promise.resolve();
              },
              validateTrigger: ['onChange'],
            }),
          ]}
        >
          <Input onKeyDown={handleKeyDownEnterNumber} placeholder="Nhập số lượng" maxLength={13} />
        </Item>
      </Col>
      <Col xs={24} md={12} style={{ marginBottom: '16px' }}>
        <Row gutter={{ md: 24, lg: 10 }}>
          <Col xs={24} md={16}>
            <Button
              icon={<DeleteOutlined />}
              onClick={() => {
                setListApplyNvkd([]);
                form?.setFieldValue('applyNvkd', []);
              }}
            >
              Xóa tất cả
            </Button>
          </Col>
        </Row>
      </Col>
      <Col xs={24} md={24} style={{ marginBottom: '16px' }}>
        <Table className="" dataSource={formCurrentListApplyNvkd} columns={actionApplyNvkdColumns} pagination={false} />
      </Col>
    </>
  );
};

export default ApplyNvkd;
