import { Button, Col, Form, Radio, Row, Table, TableColumnsType, Typography } from 'antd';
import MultipleSelectorCustom from '../multiSelectCustom';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { TBusinessPartner } from '../../../../types/eVoucher';
import { DeleteOutlined } from '@ant-design/icons';
import { employeeApplyCommonColumns } from '../columns';
import { useEVoucherStore } from '../../stroreEVoucher';
import { TPos } from '../../../../types/commissionPolicy';
import { useInfiniteQuery } from '@tanstack/react-query';
import { FetchResponse, TDataList } from '../../../../hooks';
import { DEFAULT_PARAMS_PAGESIZE } from '../../../../constants/common';
import { getEmployees, getOrgCharts } from '../../../../service/evoucher';

const { Item } = Form;
const { Text } = Typography;

const EmployeeApplyCommon: React.FC = () => {
  const form = Form.useFormInstance();

  const formCurrentListEmployeeApplyCommon = Form.useWatch('employeeApplyCommon', form);
  const formCurrentListEmployeeApplyCommonPos = Form.useWatch('employeeApplyCommonPos', form);

  const { initialValue } = useEVoucherStore();
  const defaultListEmployeeApplyCommon = useRef(initialValue?.employeeApplyCommon);
  const defaultListEmployeeApplyCommonPos = useRef(initialValue?.employeeApplyCommon?.map(item => item?.pos));
  const defaultListSelfCreatedVoucher = useRef(initialValue?.listSelfCreatedVoucher);

  const {
    data: dataEmployees,
    fetchNextPage: fetchNextPageEmployee,
    hasNextPage: hasNextPageEmployee,
    isFetchingNextPage: isFetchingNextPageEmployee,
    isLoading: isLoadingEmployee,
  } = useInfiniteQuery<FetchResponse<TDataList<{ [key: string]: unknown }[]>>>({
    queryKey: ['get-employees', formCurrentListEmployeeApplyCommonPos],
    queryFn: ({ pageParam = 1 }) =>
      getEmployees({
        page: pageParam as number,
        pageSize: DEFAULT_PARAMS_PAGESIZE,
        search,
        orgcharts: formCurrentListEmployeeApplyCommonPos?.map((o: TPos) => o?.id)?.join(','),
      }).then(res => res.data as FetchResponse<TDataList<{ [key: string]: unknown }[]>>),
    initialPageParam: 1,
    staleTime: 50000,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    getNextPageParam: (lastPage: any) => {
      const hasMore = lastPage?.data?.rows?.length > 0 && lastPage?.data?.totalPages > lastPage?.data?.page;
      return hasMore ? Number(lastPage?.data?.page) + 1 : undefined;
    },
  });

  const {
    data: dataPos,
    fetchNextPage: fetchNextPagePos,
    hasNextPage: hasNextPagePos,
    isFetchingNextPage: isFetchingNextPagePos,
    isLoading: isLoadingPos,
  } = useInfiniteQuery<FetchResponse<TDataList<{ [key: string]: unknown }[]>>>({
    queryKey: ['get-pos'],
    queryFn: ({ pageParam = 1 }) =>
      getOrgCharts({ page: pageParam as number, pageSize: DEFAULT_PARAMS_PAGESIZE, search }).then(
        res => res.data as FetchResponse<TDataList<{ [key: string]: unknown }[]>>,
      ),
    initialPageParam: 1,
    staleTime: 50000,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    getNextPageParam: (lastPage: any) => {
      const hasMore = lastPage?.data?.rows?.length > 0 && lastPage?.data?.totalPages > lastPage?.data?.page;
      return hasMore ? Number(lastPage?.data?.page) + 1 : undefined;
    },
  });

  const [listEmployeeApplyCommonPos, setListEmployeeApplyCommonPos] = useState<TPos[]>([]);
  const [listEmployeeApplyCommon, setListEmployeeApplyCommon] = useState<TBusinessPartner[]>([]);
  const [listSelfCreatedVoucher, setListSelfCreatedVoucher] = useState<TPos[]>([]);
  const [search, setSearch] = useState<string>('');

  useEffect(() => {
    if (
      Array.isArray(defaultListEmployeeApplyCommon?.current) &&
      defaultListEmployeeApplyCommon?.current &&
      defaultListEmployeeApplyCommon?.current?.length > 0
    ) {
      setListEmployeeApplyCommon(defaultListEmployeeApplyCommon?.current);
    }
    if (
      Array.isArray(defaultListEmployeeApplyCommonPos?.current) &&
      defaultListEmployeeApplyCommonPos?.current &&
      defaultListEmployeeApplyCommonPos?.current?.length > 0
    ) {
      const employeeApplyPosCommon = defaultListEmployeeApplyCommonPos?.current
        ?.map(item => item)
        .filter((pos, index, array) => pos && array.findIndex(p => p?.id === pos?.id) === index);
      setListEmployeeApplyCommonPos(employeeApplyPosCommon as TPos[]);
    }
    if (
      Array.isArray(defaultListSelfCreatedVoucher?.current) &&
      defaultListSelfCreatedVoucher?.current &&
      defaultListSelfCreatedVoucher?.current?.length > 0
    ) {
      setListSelfCreatedVoucher(defaultListSelfCreatedVoucher?.current as TPos[]);
    }
  }, [defaultListEmployeeApplyCommon, defaultListEmployeeApplyCommonPos, defaultListSelfCreatedVoucher]);

  const handleRemove = useCallback(
    (value: string) => {
      const newVal = listEmployeeApplyCommon.filter(v => v?.id !== value);
      setListEmployeeApplyCommon(newVal);
      form?.setFieldValue('employeeApplyCommon', newVal);
    },
    [form, listEmployeeApplyCommon],
  );

  const actionEmployeeApplyCommonColumns: TableColumnsType = useMemo(() => {
    return [
      ...employeeApplyCommonColumns,
      {
        dataIndex: 'action',
        align: 'center',
        width: '80px',
        render: (_, record: TBusinessPartner) => {
          return (
            <Text style={{ color: '#1677FF', cursor: 'pointer' }} onClick={() => handleRemove(record?.id)}>
              Xóa
            </Text>
          );
        },
      },
    ];
  }, [handleRemove]);

  return (
    <>
      <Col xs={24} md={24}>
        <Item name="listSelfCreatedVoucher" label="Đơn vị sử dụng">
          <MultipleSelectorCustom<TPos>
            fieldName="listSelfCreatedVoucher"
            tempSelected={listSelfCreatedVoucher}
            setTempSelected={setListSelfCreatedVoucher}
            data={dataPos}
            fetchNextPage={fetchNextPagePos}
            hasNextPage={hasNextPagePos}
            isFetchingNextPage={isFetchingNextPagePos}
            isLoading={isLoadingPos}
            search={search}
            setSearch={setSearch}
            addLabel="Thêm đơn vị"
          />
        </Item>
      </Col>
      <Col xs={24} md={24}>
        <Item name={'confirmBeforeUseCommon'} label="Xác nhận e-voucher trước khi sử dụng">
          <Radio.Group
            options={[
              { label: 'Có', value: 'yes' },
              { label: 'Không', value: 'no' },
            ]}
            defaultValue="yes"
          />
        </Item>
      </Col>
      <Col xs={24} md={24}>
        <Item name="employeeApplyCommonPos" label="Đơn vị xác nhận">
          <MultipleSelectorCustom<TPos>
            fieldName="employeeApplyCommonPos"
            tempSelected={listEmployeeApplyCommonPos}
            setTempSelected={setListEmployeeApplyCommonPos}
            data={dataPos}
            fetchNextPage={fetchNextPagePos}
            hasNextPage={hasNextPagePos}
            isFetchingNextPage={isFetchingNextPagePos}
            isLoading={isLoadingPos}
            search={search}
            setSearch={setSearch}
            addLabel="Thêm đơn vị"
          />
        </Item>
      </Col>
      <Col xs={24} md={24}>
        <Item
          name="employeeApplyCommon"
          label="Nhân viên xác nhận"
          rules={[{ required: true, message: 'Vui lòng chọn nhân viên' }]}
        >
          <MultipleSelectorCustom<TBusinessPartner>
            fieldName="employeeApplyCommon"
            tempSelected={listEmployeeApplyCommon}
            setTempSelected={setListEmployeeApplyCommon}
            data={dataEmployees}
            fetchNextPage={fetchNextPageEmployee}
            hasNextPage={hasNextPageEmployee}
            isFetchingNextPage={isFetchingNextPageEmployee}
            isLoading={isLoadingEmployee}
            search={search}
            setSearch={setSearch}
            addLabel="Thêm nhân viên"
          />
        </Item>
      </Col>
      <Col xs={24} md={12} style={{ marginBottom: '16px' }}>
        <Row gutter={{ md: 24, lg: 10 }}>
          <Col xs={24} md={16}>
            <Button
              icon={<DeleteOutlined />}
              onClick={() => {
                setListEmployeeApplyCommon([]);
                form?.setFieldValue('employeeApplyCommon', []);
              }}
            >
              Xóa tất cả
            </Button>
          </Col>
        </Row>
      </Col>
      <Col xs={24} md={24} style={{ marginBottom: '16px' }}>
        <Table
          className=""
          dataSource={formCurrentListEmployeeApplyCommon}
          columns={actionEmployeeApplyCommonColumns}
          pagination={false}
        />
      </Col>
    </>
  );
};

export default EmployeeApplyCommon;
