import { Button, Space } from 'antd';

interface Props {
  handleSubmit?: () => void;
  handleApprove?: () => void;
  loadingSubmit?: boolean;
  isShowModal?: boolean;
}

const ButtonOfPageDetail = (props: Props) => {
  const { handleSubmit, handleApprove, loadingSubmit } = props;

  return (
    <div className="buttons-detail-footer">
      <Space className="space-button" size={'middle'}>
        {handleApprove && (
          <Button size="small" type="primary" onClick={handleApprove}>
            Du<PERSON>ệt
          </Button>
        )}
        {handleSubmit && (
          <Button size="small" type="default" onClick={handleSubmit} loading={loadingSubmit}>
            <PERSON><PERSON><PERSON> thay đổi
          </Button>
        )}
      </Space>
    </div>
  );
};

export default ButtonOfPageDetail;
