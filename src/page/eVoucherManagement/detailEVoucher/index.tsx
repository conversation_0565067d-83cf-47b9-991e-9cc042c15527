import { useLocation, useParams, useSearchParams } from 'react-router-dom';
import BreadCrumbComponent from '../../../components/breadCrumb';
import { App, Form, notification, Tabs, TabsProps } from 'antd';
import { IEVoucher, TBusinessPartner } from '../../../types/eVoucher';
import React from 'react';
import { useFetch, useUpdateField } from '../../../hooks';
import { applyEVoucher, getDetailEVoucher, updateEVoucher } from '../../../service/evoucher';
import dayjs from 'dayjs';
import ButtonOfPageDetail from './component/buttonOfPageDetail';
import { TAttachment } from '../../../types/proposal';
import { modalConfirm } from '../../../components/modal/specials/ModalConfirm';
import ApplicbleRecipient from '../component/applicableRecipientsTab';
import ReleasePeriod from '../component/releasePeriodTab';
import { useEVoucherStore } from '../stroreEVoucher';

const DetailEVoucher = () => {
  const { id } = useParams();
  const [form] = Form.useForm();
  const location = useLocation();

  const { modal } = App.useApp();

  const { initialValue, setInitialValue, isModified, setIsModified } = useEVoucherStore();

  const [actived, setActived] = React.useState(1);

  const [searchParams, setSearchParams] = useSearchParams();

  const [avatarImage, setAvatarImage] = React.useState<TAttachment>();
  const [detailImage, setDetailImage] = React.useState<TAttachment>();

  const [listPartner, setListPartner] = React.useState<TBusinessPartner[]>([]);

  const [activeTab, setActiveTab] = React.useState(
    (location.state as { activeTab?: string })?.activeTab || searchParams.get('tab') || '1',
  );

  const { data: dataDetail } = useFetch<IEVoucher>({
    api: () => id && getDetailEVoucher(id),
    queryKeyArr: ['get-detail-e-voucher', id],
    enabled: !!id,
    cacheTime: 10,
  });

  const { mutateAsync: approveEVoucherMutate } = useUpdateField({
    apiQuery: applyEVoucher,
    keyOfListQuery: ['get-detail-e-voucher', id],
    isShowMessage: false,
    isMessageSuccess: false,
  });

  const { mutateAsync } = useUpdateField({
    apiQuery: updateEVoucher,
    keyOfListQuery: ['get-detail-e-voucher', id],
    keyOfDetailQuery: ['get-detail-e-voucher', id],
  });

  const dataEVoucher = dataDetail?.data?.data;

  const handleTabChange = (key: string) => {
    setActiveTab(key);
    setSearchParams({ tab: key });
  };

  const handleFinish = async () => {
    await form.validateFields();
    const values = form.getFieldsValue(true);

    const transformedValues = {
      ...values,
      id: dataEVoucher?.id,
      amount: Number(values?.amount),
      isActive: actived,
      businessPartner: listPartner?.map(o => ({ ...o, merchantLimitAmount: Number(o?.merchantLimitAmount) })),
      avatarImage: avatarImage?.url,
      detailImage: detailImage?.url,
      maxAmountReduced: Number(values?.maxAmountReduced),
      promotionValue: Number(values?.promotionValue),
      applyNvkdAmount: Number(values?.applyNvkdAmount),
      employeeApplyAmount: Number(values?.employeeApplyAmount),
      employeeApplyCommonAmount: Number(values?.employeeApplyCommonAmount),
      applyNvkd: values?.applyNvkd?.map((o: TBusinessPartner) => ({ ...o, amount: Number(values?.applyNvkdAmount) })),
      employeeApply: values?.employeeApply?.map((o: TBusinessPartner) => ({
        ...o,
        amount: Number(values?.employeeApplyAmount),
      })),
      employeeApplyCommon: values?.employeeApplyCommon?.map((o: TBusinessPartner) => ({
        ...o,
        amount: Number(values?.employeeApplyCommonAmount),
      })),
      amountOfUsed: null,
    };

    try {
      const res = await mutateAsync(transformedValues);
      if (res?.data?.statusCode === '0') {
        form.resetFields();
        setIsModified(false);
      }
    } catch (error) {
      console.error('Error creating e-voucher policy:', error);
    }
  };

  // cảnh báo khi thoát trang khi chưa lưu dữ liệu
  React.useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  React.useEffect(() => {
    if (dataEVoucher) {
      const applyNvkdPos = dataEVoucher?.applyNvkd
        ?.map(item => item?.pos)
        .filter((pos, index, array) => pos && array.findIndex(p => p?.id === pos?.id) === index);
      const employeeApplyPos = dataEVoucher?.employeeApply
        ?.map(item => item?.pos)
        .filter((pos, index, array) => pos && array.findIndex(p => p?.id === pos?.id) === index);
      const employeeApplyCommonPos = dataEVoucher?.employeeApplyCommon
        ?.map(item => item?.pos)
        .filter((pos, index, array) => pos && array.findIndex(p => p?.id === pos?.id) === index);

      const initialData = {
        ...dataEVoucher,
        businessPartner: dataEVoucher?.businessPartner?.map(item => ({
          ...item,
          merchantLimitAmount: item?.merchantLimitAmount?.toString(),
        })),
        endDate: dayjs(dataEVoucher?.endDate),
        startDate: dayjs(dataEVoucher?.startDate),
        endTime: dataEVoucher?.endTime ? dayjs(dataEVoucher?.endTime, 'HH:mm') : '',
        startTime: dataEVoucher?.startTime ? dayjs(dataEVoucher?.startTime, 'HH:mm') : '',
        applyNvkdAmount: dataEVoucher?.applyNvkd ? dataEVoucher?.applyNvkd[0]?.amount : undefined,
        avatarImage: {
          uid: '1',
          name: dataEVoucher?.avatarImage,
          status: 'done',
          url: `${import.meta.env.VITE_S3_IMAGE_URL}/${dataEVoucher?.avatarImage}`,
        },
        detailImage: {
          uid: '1',
          name: dataEVoucher?.detailImage,
          status: 'done',
          url: `${import.meta.env.VITE_S3_IMAGE_URL}/${dataEVoucher?.detailImage}`,
        },
        applyNvkdPos: applyNvkdPos,
        employeeApplyPos: employeeApplyPos,
        employeeApplyCommonPos: employeeApplyCommonPos,
      };
      setAvatarImage({
        uid: '1',
        name: dataEVoucher?.avatarImage,
        url: `${import.meta.env.VITE_S3_IMAGE_URL}/${dataEVoucher?.avatarImage}`,
      });
      setDetailImage({
        uid: '1',
        name: dataEVoucher?.detailImage,
        url: `${import.meta.env.VITE_S3_IMAGE_URL}/${dataEVoucher?.detailImage}`,
      });
      form.setFieldsValue(initialData);
      setListPartner(dataEVoucher?.businessPartner || []);
      setInitialValue(initialData as unknown as IEVoucher);
    }
  }, [dataEVoucher, form, setInitialValue]);

  React.useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  const validateForm = () => {
    setIsModified(true);
  };

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: 'Đợt phát hành',
      children: initialValue && (
        <ReleasePeriod
          data={undefined}
          listPartner={listPartner}
          actived={actived}
          setListPartner={setListPartner}
          setActived={setActived}
          avatarImage={avatarImage}
          detailImage={detailImage}
          setDetailImage={setDetailImage}
          setAvatarImage={setAvatarImage}
          setIsModified={setIsModified}
        />
      ),
    },
    {
      key: '2',
      label: 'Đối tượng áp dụng',
      children: initialValue && <ApplicbleRecipient setIsModified={setIsModified} />,
    },
  ];

  const handleApprove = () => {
    if (actived === 2)
      return notification.error({
        message: 'Thiết lập voucher đang bị vô hiệu hóa',
      });
    else
      return modalConfirm({
        modal: modal,
        title: `Duyệt đợt phát hành Evoucher`,
        content: `Bạn có muốn duyệt đợt phát hành Evoucher không?`,
        handleConfirm: async () => {
          const res = await approveEVoucherMutate({
            id: id ?? '',
          });
          if (res?.data?.statusCode === '0') {
            notification.success({
              message: 'Phê duyệt thành công',
            });
            return Promise.resolve();
          } else {
            notification.error({
              message: 'Phê duyệt không thành công',
            });
            return Promise.reject();
          }
        },
      });
  };

  return (
    <div className="wrapper-detail-personal-customer">
      <BreadCrumbComponent titleBread={dataEVoucher?.name} />
      {initialValue && (
        <Form
          form={form}
          layout="vertical"
          onFinish={handleFinish}
          onValuesChange={validateForm}
          initialValues={initialValue}
          className="space-y-6"
        >
          <Tabs activeKey={activeTab} onChange={handleTabChange} items={items} tabPosition="top" />
        </Form>
      )}
      {<ButtonOfPageDetail handleSubmit={isModified ? () => form.submit() : undefined} handleApprove={handleApprove} />}
    </div>
  );
};

export default DetailEVoucher;
