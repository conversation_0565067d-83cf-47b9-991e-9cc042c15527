import { Flex, TableColumnsType } from 'antd';
import { useMemo, useState } from 'react';
import BreadCrumbComponent from '../../../components/breadCrumb';
import TableComponent from '../../../components/table';
import { ActionsColumns } from '../../../components/table/components/ActionsColumns';
import { useFetch } from '../../../hooks';

import { ICustomers } from '../../../types/customers';
import { columns } from './columns';
import ModalCreateCustomer from './components/modalCreate';
import ModalShareCare from './components/modalShareCare/ModalShareCare';
import './styles.scss';
import { OFFICIAL_CUSTOMER_BUSINESS } from '../../../configs/path';
import { getListOfficialCustomerBusiness } from '../../../service/officialCustomers';
import FilterSearch from './components/filterSearch';

function ListOfficialBusinessCustomers() {
  // const [selectedRows, setSelectedRows] = useState<ICustomers[]>([]);
  const [isOpenModalShare, setIsOpenModalShare] = useState(false);
  const [isOpenModalCreate, setIsOpenModalCreate] = useState(false);

  const {
    data: listCustomer,
    isLoading,
    isPlaceholderData,
    isFetching,
  } = useFetch<ICustomers[]>({
    queryKeyArrWithFilter: ['get-official-customers-business'],
    api: getListOfficialCustomerBusiness,
  });

  const columnActions: TableColumnsType<ICustomers> = useMemo(() => {
    return [
      ...columns,
      {
        title: 'Hành động',
        dataIndex: 'action',
        key: 'action',
        width: '100px',
        align: 'center',
        fixed: 'right',
        render: (_: unknown, record: ICustomers) => {
          const openViewDetail = () => {
            window.open(`${OFFICIAL_CUSTOMER_BUSINESS}/${record?.id}`, '_blank', 'noopener noreferrer');
          };

          return <ActionsColumns handleViewDetail={openViewDetail} />;
        },
      },
    ];
  }, []);

  const handleCancelModalShare = () => {
    setIsOpenModalShare(false);
  };

  const handleCancelModalCreate = () => {
    setIsOpenModalCreate(false);
  };
  return (
    <div className="wrapper-list-business-customers">
      <BreadCrumbComponent />
      <div className="header-content">
        <Flex gap={17}>
          <FilterSearch />
        </Flex>
        {/* <Flex gap={10}>
          <Dropdown menu={{ items: [] }}>
            <Button>
              <Space>
                Tải / nhập dữ liệu
                <DownOutlined />
              </Space>
            </Button>
          </Dropdown>
          <Button type="primary" onClick={handleOpenModalCreate}>
            Thêm mới
          </Button>
        </Flex> */}
      </div>
      <div className="table-business-customers">
        <TableComponent
          queryKeyArr={['get-official-customers-business']}
          columns={columnActions}
          loading={isLoading || isPlaceholderData || isFetching}
          dataSource={listCustomer?.data?.data?.rows ?? []}
          rowKey="id"
        />
      </div>
      <ModalCreateCustomer isOpen={isOpenModalCreate} handleCancel={handleCancelModalCreate} />
      <ModalShareCare
        onClose={handleCancelModalShare}
        isOpen={isOpenModalShare}
        onSubmitted={() => console.log('first')}
      />
    </div>
  );
}

export default ListOfficialBusinessCustomers;
