import { TableColumnsType, Typography } from 'antd';
import dayjs from 'dayjs';
import { OFFICIAL_CUSTOMER_BUSINESS } from '../../../configs/path';
import { FORMAT_DATE_TIME } from '../../../constants/common';
import { ICustomers, TDataDuplicate } from '../../../types/customers';

const { Text } = Typography;
export const columns: TableColumnsType = [
  {
    title: 'Khách hàng',
    dataIndex: 'companyName',
    key: 'companyName',
    width: 160,
    fixed: 'left',
    render: (value: string) => (value ? <Text>{value}</Text> : '-'),
  },
  {
    title: 'Mã số thuế',
    dataIndex: 'taxCode',
    key: 'taxCode',
    width: 140,
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Mã khách hàng',
    dataIndex: 'code',
    width: 140,
    key: 'code',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Số điện thoại',
    dataIndex: 'phone',
    width: 140,
    key: 'phone',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Email',
    dataIndex: 'email',
    width: 200,
    key: 'email',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Ngày tạo',
    dataIndex: 'createdDate',
    key: 'createdDate',
    width: 200,

    render: (value: string, record: ICustomers) => (
      <>
        <Text>{dayjs(value).format(FORMAT_DATE_TIME)}</Text>
        <br />
        <Text>{record?.createdByObj?.fullName ? record.createdByObj.fullName : '-'}</Text>
      </>
    ),
  },
  {
    title: 'Ngày cập nhật',
    dataIndex: 'updatedDate',
    key: 'updatedDate',
    width: 200,

    render: (value: string, record: ICustomers) => (
      <>
        <Text>{dayjs(value).format(FORMAT_DATE_TIME)}</Text> <br />
        <Text>{record?.updatedByObj?.fullName ? record.updatedByObj.fullName : '-'}</Text>
      </>
    ),
  },
];

export const columnsDuplicate: TableColumnsType = [
  {
    title: 'Khách hàng',
    dataIndex: 'companyName',
    key: 'companyName',
    fixed: 'left',
    render: (value: string, record: TDataDuplicate) =>
      value ? (
        <a href={`${OFFICIAL_CUSTOMER_BUSINESS}/${record?.id}`} target="_blank" rel="noopener noreferrer">
          {value}
        </a>
      ) : (
        '-'
      ),
  },
  {
    title: 'Mã khách hàng',
    dataIndex: 'code',
    key: 'code',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Số giấy tờ',
    dataIndex: 'taxCode',
    key: 'taxCode',
    render: (value: string, record: TDataDuplicate) =>
      value ? value : record?.identities?.length > 0 ? record?.identities?.join(', ') : '-',
  },
  {
    title: 'Loại khách hàng',
    dataIndex: 'type',
    key: 'type',
    render: (value: string) => (value === 'individual' ? 'Cá nhân' : 'Doanh nghiệp'),
  },
  {
    title: 'Email',
    dataIndex: 'email',
    key: 'email',
    render: (value: string) => (value ? value : '-'),
  },
];
