import { Form } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { useEffect, useMemo, useState } from 'react';
import { useLocation } from 'react-router-dom';
import DatePickerFilter from '../../../../../components/datePicker/DatePickerFilter';
import DropdownFilterSearch from '../../../../../components/dropdown/dropdownFilterSearch';
import useFilter from '../../../../../hooks/filter';
import './styles.scss';

type TFilter = {
  isActive?: number | null;
  startCreatedDate?: string | Dayjs | null;
  endCreatedDate?: string | Dayjs | null;
  status?: string;
  createdBy?: string;
  share?: string;
};

function FilterSearch() {
  const [form] = Form.useForm();
  const [isOpenFilter, setIsOpenFilter] = useState(false);
  const [initialValues, setInitialValues] = useState<TFilter>();
  const [filter, setFilter] = useFilter();
  const { search } = useLocation();
  const params = useMemo(() => new URLSearchParams(search), [search]);

  useEffect(() => {
    const formatData = {
      startCreatedDate: params.get('startCreatedDate') ? dayjs(params.get('startCreatedDate')) : undefined,
      endCreatedDate: params.get('endCreatedDate') ? dayjs(params.get('endCreatedDate')) : undefined,
    };
    setInitialValues(formatData);
    form.setFieldsValue(formatData);
  }, [form, params]);

  const handleSubmitFilter = (values: TFilter) => {
    const newFilter: Record<string, unknown> = {
      startCreatedDate: values?.startCreatedDate ? dayjs(values?.startCreatedDate).format('YYYY-MM-DD') : null,
      endCreatedDate: values?.endCreatedDate ? dayjs(values?.endCreatedDate).format('YYYY-MM-DD') : null,
    };
    setFilter({ ...filter, page: '1', ...newFilter });
    setIsOpenFilter(false);
  };

  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };

  const handleClearFilters = () => {
    setInitialValues(undefined);
    setTimeout(() => {
      setIsOpenFilter(false);
    }, 100);
  };

  return (
    <>
      <DropdownFilterSearch
        rootClassName="wrapper-filter-customers"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        showParams={true}
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        initialValues={initialValues}
        form={form}
        onClearFilters={handleClearFilters}
        extraFormItems={
          <>
            <DatePickerFilter startDate="startCreatedDate" endDate="endCreatedDate" />
          </>
        }
      />
    </>
  );
}

export default FilterSearch;
