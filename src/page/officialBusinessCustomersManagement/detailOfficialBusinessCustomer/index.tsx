import { Form, Tabs } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import BreadCrumbComponent from '../../../components/breadCrumb';
import { OptionType } from '../../../components/select/selectEmployees';
import { AddressType } from '../../../components/selectAddress';
import { FORMAT_DATE } from '../../../constants/common';
import { useFetch } from '../../../hooks';
import { getProvinces } from '../../../service/address';
import { ICustomers, IDocumentCustomer, TIdentities } from '../../../types/customers';
import './styles.scss';
import { getDetailOfficialCustomer } from '../../../service/officialCustomers';
import TabGeneralInformation from './components/tabGeneralInformation';
import DocumentComponent from '../../../components/document';
import {
  addCustomerDocument,
  addItemsCustomerDocument,
  deleteCustomerDocument,
  deleteItemCustomerDocument,
  getCustomerDocument,
  getDocumentCustomerItems,
  updateCustomerDocument,
} from '../../../service/customers';
import TabTransactionInformation from './components/tabTransactionInformation';

const DetailPersonalCustomer = () => {
  const { id } = useParams();
  const [form] = Form.useForm();

  const cloneAddress = Form.useWatch(['info', 'cloneAddress'], form);
  const address = Form.useWatch(['address'], form);

  const [initialValues, setInitialValues] = useState<ICustomers>();
  const [yearOnly, setYearOnly] = useState(false);
  const [isModified, setIsModified] = useState(false);
  const [valuesShareEmail, setValuesShareEmail] = useState<OptionType[]>();

  const { data: dataProvinces } = useFetch<AddressType[]>({
    queryKeyArrWithFilter: ['get-province'],
    api: getProvinces,
  });

  const { data: dataDetail } = useFetch<ICustomers>({
    api: () => id && getDetailOfficialCustomer(id),
    queryKeyArr: ['get-detail-official-customer-business', id],
    enabled: !!id,
    cacheTime: 10,
  });
  const dataOfficailCustomer = dataDetail?.data?.data;

  // cảnh báo khi thoát trang khi chưa lưu dữ liệu
  useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  useEffect(() => {
    if (dataOfficailCustomer) {
      const initialData = {
        ...dataOfficailCustomer,
        bankInfo: Array.isArray(dataOfficailCustomer?.bankInfo) ? dataOfficailCustomer.bankInfo : [],
        info: {
          ...dataOfficailCustomer.info,
          birthday: dataOfficailCustomer?.info?.birthday
            ? dayjs(dataOfficailCustomer.info.birthday, FORMAT_DATE)
            : null,
          birthdayYear: dataOfficailCustomer?.info?.birthdayYear
            ? dayjs(dataOfficailCustomer.info.birthdayYear, 'YYYY')
            : null,
          onlyYear: dataOfficailCustomer?.info?.birthdayYear ? true : false,
        },
        company: {
          ...dataOfficailCustomer?.company,
          issueDate: dataOfficailCustomer?.company?.issueDate
            ? dayjs(dataOfficailCustomer?.company?.issueDate, FORMAT_DATE)
            : null,
        },
        personalInfo: {
          ...dataOfficailCustomer.personalInfo,
          income: dataOfficailCustomer?.personalInfo?.income
            ? dataOfficailCustomer?.personalInfo?.income?.toString()
            : undefined,
          identities: dataOfficailCustomer?.personalInfo?.identities?.map((item: TIdentities) => ({
            ...item,
            date: item.date ? dayjs(item.date, FORMAT_DATE) : null,
          })),
        },
        identities: {
          ...dataOfficailCustomer?.identities,
          date: dataOfficailCustomer?.identities?.date
            ? dayjs(dataOfficailCustomer?.identities?.date, FORMAT_DATE)
            : null,
        },
        address: {
          ...dataOfficailCustomer?.info?.address,
        },
        rootAddress: {
          ...dataOfficailCustomer?.info?.rootAddress,
        },
      };
      setInitialValues(initialData as ICustomers);
      setYearOnly(!!dataOfficailCustomer.info?.birthdayYear);
      setValuesShareEmail(dataOfficailCustomer?.share?.emails?.map(item => ({ label: item, value: item })) || []);
    }
  }, [dataOfficailCustomer, form]);

  useEffect(() => {
    if (cloneAddress) {
      form.setFieldsValue({
        rootAddress: form.getFieldValue('address'),
      });
    }
  }, [cloneAddress, form, address]);

  useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  const itemsTabOfficialCustomerBusiness = [
    {
      key: '1',
      label: 'Thông tin khách hàng',
      children: (
        <TabGeneralInformation
          dataDetail={dataDetail}
          dataProvinces={dataProvinces}
          initialValues={initialValues}
          valuesShareEmail={valuesShareEmail}
          yearOnly={yearOnly}
          isModified={isModified}
          setIsModified={setIsModified}
          setYearOnly={setYearOnly}
          setValuesShareEmail={setValuesShareEmail}
        />
      ),
    },
    {
      key: '2',
      label: 'Tài liệu',
      children: (
        <DocumentComponent<IDocumentCustomer>
          // modelId={id}
          keyQueryList={'getDocument'}
          keyQueryItems={'getDocumentItems'}
          modelIdFieldName={'customer'}
          getDocument={getCustomerDocument}
          getDocumentItems={getDocumentCustomerItems}
          addDocument={addCustomerDocument}
          updateDocument={updateCustomerDocument}
          addItemsDocument={addItemsCustomerDocument}
          deleteDocument={deleteCustomerDocument}
          deleteItemDocument={deleteItemCustomerDocument}
        />
      ),
    },
    { key: '3', label: 'Thông tin giao dịch', children: <TabTransactionInformation /> },
  ];

  return (
    <div className="wrapper-detail-personal-customer">
      <BreadCrumbComponent titleBread={dataOfficailCustomer?.company?.shortName} />
      <Tabs defaultActiveKey="1" onChange={() => {}} items={itemsTabOfficialCustomerBusiness} />
    </div>
  );
};

export default DetailPersonalCustomer;
