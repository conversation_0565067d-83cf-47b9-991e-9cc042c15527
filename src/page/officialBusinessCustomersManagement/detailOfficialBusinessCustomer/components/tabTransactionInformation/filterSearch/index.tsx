import { Col, DatePicker, Form, Row } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { useEffect, useMemo, useState } from 'react';
import { useLocation } from 'react-router-dom';
import './styles.scss';
import useFilter from '../../../../../../hooks/filter';
import { TEmployeeAll } from '../../../../../../types/customers';
import DropdownFilterSearch from '../../../../../../components/dropdown/dropdownFilterSearch';
import MultiSelectLazy from '../../../../../../components/select/mutilSelectLazy';
import { getListEmployeeAll } from '../../../../../../service/customers';

type TFilter = {
  isActive?: number | null;
  startCreatedDate?: string | Dayjs | null;
  endCreatedDate?: string | Dayjs | null;
  status?: string;
  createdBy?: string;
  share?: string;
};

function FilterSearch() {
  const [isOpenFilter, setIsOpenFilter] = useState(false);
  const [initialValues, setInitialValues] = useState<TFilter>();
  const [filter, setFilter] = useFilter();
  const { search } = useLocation();
  const params = useMemo(() => new URLSearchParams(search), [search]);

  useEffect(() => {
    setInitialValues({
      status: params.get('status') || undefined,
      isActive: params.get('isActive') ? Number(params.get('isActive')) : undefined,
      startCreatedDate: params.get('startCreatedDate') ? dayjs(params.get('startCreatedDate')) : undefined,
      endCreatedDate: params.get('endCreatedDate') ? dayjs(params.get('endCreatedDate')) : undefined,
    });
  }, [params]);

  const handleSubmitFilter = (values: TFilter) => {
    const newFilter: Record<string, unknown> = {
      status: values.status || null,
      isActive: values.isActive || null,
      startCreatedDate: values?.startCreatedDate ? dayjs(values?.startCreatedDate).format('YYYY-MM-DD') : null,
      endCreatedDate: values?.endCreatedDate ? dayjs(values?.endCreatedDate).format('YYYY-MM-DD') : null,
    };
    setFilter({ ...filter, page: '1', ...newFilter });
    setIsOpenFilter(false);
  };

  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };
  const handleSelectEmployee = (values: TEmployeeAll[]) => {
    console.log('values :', values);
  };
  const handleSelectEmployeeShare = (values: TEmployeeAll[]) => {
    console.log('values :', values);
  };
  return (
    <>
      <DropdownFilterSearch
        rootClassName="wrapper-filter-customers"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        showParams={true}
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        initialValues={initialValues}
        extraFormItems={
          <>
            <Form.Item label="Tên dự án" name="createdBy">
              <MultiSelectLazy
                enabled={isOpenFilter}
                apiQuery={getListEmployeeAll}
                queryKey={['project-dropdown']}
                keysLabel={['name']}
                handleListSelect={handleSelectEmployee}
                // defaultValues={{ label: employee?.name, value: employee?.id }}
                placeholder="Chọn tên dự án"
                keysTag={'name'}
              />
            </Form.Item>
            <Form.Item label="Loại giao dịch" name="-">
              <MultiSelectLazy
                enabled={isOpenFilter}
                apiQuery={getListEmployeeAll}
                queryKey={['transaction-dropdown']}
                keysLabel={['name']}
                handleListSelect={handleSelectEmployeeShare}
                // defaultValues={{ label: employee?.name, value: employee?.id }}
                placeholder="Chọn loại giao dịch"
                keysTag={'name'}
              />
            </Form.Item>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="Tạo từ ngày" name="startCreatedDate">
                  <DatePicker
                    format="DD/MM/YYYY"
                    disabledDate={current => current && current > dayjs().startOf('day')}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Đến ngày" name="endCreatedDate">
                  <DatePicker
                    format="DD/MM/YYYY"
                    disabledDate={current => current && current > dayjs().startOf('day')}
                  />
                </Form.Item>
              </Col>
            </Row>
          </>
        }
      />
    </>
  );
}

export default FilterSearch;
