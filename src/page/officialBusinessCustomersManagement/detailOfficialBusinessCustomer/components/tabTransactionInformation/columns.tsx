import { TableColumnsType, Typography } from 'antd';
import dayjs from 'dayjs';
import { ICustomers, TTransitionHistory, TTransitionInformation } from '../../../../../types/customers';
import { FORMAT_DATE_TIME } from '../../../../../constants/common';
import { OPERATE_SELL_PROGRAM, PRODUCT_DETAIL, PROJECTS_MANAGEMENT } from '../../../../../configs/path';

const { Text } = Typography;
export const columnsProductsTab: TableColumnsType = [
  {
    title: 'Tên dự án',
    dataIndex: 'nameProject',
    key: 'nameProject',
    width: 200,
    render: (text: string) => text,
  },
  {
    title: 'Mã sản phẩm',
    dataIndex: ['propertyUnit', 'code'],
    key: 'code',
    width: 150,
    render: (text: string, record: TTransitionInformation) => (
      <Typography.Link
        onClick={() => {
          window.open(
            `${PROJECTS_MANAGEMENT}${PRODUCT_DETAIL}/${record?.propertyUnit?._id}`,
            '_blank',
            'noopener noreferrer',
          );
        }}
      >
        {text}
      </Typography.Link>
    ),
  },
  {
    title: 'Block',
    dataIndex: ['propertyUnit', 'block'],
    key: 'block',
    width: 150,
  },
  {
    title: 'Tầng',
    dataIndex: ['propertyUnit', 'floor'],
    key: 'floor',
    width: 150,
  },
  {
    title: 'Tình trạng sản phẩm',
    dataIndex: 'status',
    key: 'status',
    width: 180,
    render: value => value?.code,
  },
  {
    title: 'Giá chưa VAT',
    dataIndex: ['propertyUnit', 'price'],
    key: 'price',
    render: (price: number) => <Text>{price}</Text>,
    width: 180,
  },
  {
    title: '',
    key: 'expand',
    className: 'expand-column',
    width: 60,
    align: 'center',
  },
];
export const columnsHistoryTab: TableColumnsType = [
  {
    title: 'Tên dự án',
    dataIndex: 'nameProject',
    key: 'nameProject',
    width: 200,
    render: (text: string) => text,
  },
  {
    title: 'Mã sản phẩm',
    dataIndex: 'code',
    key: 'code',
    width: 150,
  },
  {
    title: 'Mã giao dịch',
    dataIndex: 'bookingTicketCode',
    key: 'bookingTicketCode',
    width: 150,
    render: (text: string, record: TTransitionHistory) => (
      <Typography.Link
        onClick={() => {
          window.open(
            record?.ticketType === 'YCDC'
              ? `${PROJECTS_MANAGEMENT}${OPERATE_SELL_PROGRAM}/deposit/:depositId/${record?.id}`
              : `${PROJECTS_MANAGEMENT}${OPERATE_SELL_PROGRAM}/:id/:bookingTicketId/${record?.id}`,
            '_blank',
            'noopener noreferrer',
          );
        }}
      >
        {text}
      </Typography.Link>
    ),
  },
  {
    title: 'Tình trạng sản phẩm',
    dataIndex: 'status',
    key: 'status',
    width: 180,
  },
  {
    title: 'Ngày tạo',
    dataIndex: 'createdDate',
    key: 'createdDate',
    width: 200,

    render: (value: string, record: ICustomers) => (
      <>
        <Text>{value ? dayjs(value).format(FORMAT_DATE_TIME) : ''}</Text>
        <br />
        <Text>{value ? (record?.createdBy ? record.createdBy : '-') : ''}</Text>
      </>
    ),
  },
  {
    title: '',
    key: 'expand',
    className: 'expand-column',
    width: 60,
    align: 'center',
  },
];
