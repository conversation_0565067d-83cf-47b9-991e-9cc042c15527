import { Checkbox, Col, DatePicker, Form, FormInstance, FormProps, Input, Radio, Row, Select, Typography } from 'antd';
import { DefaultOptionType } from 'antd/lib/cascader';
import { CheckboxChangeEvent } from 'antd/lib/checkbox';
import dayjs from 'dayjs';
import { useCallback, useEffect, useState } from 'react';
import ButtonOfPageDetail from '../../../../../components/button/buttonOfPageDetail';
import CurrencyInput from '../../../../../components/input/CurrencyInput';
import { OptionType } from '../../../../../components/select/selectEmployees';
import SelectAddress from '../../../../../components/selectAddress';
import {
  FORMAT_DATE,
  FORMAT_DATE_TIME,
  OPTIONS_GENDER,
  OPTIONS_IDENTITIES,
  OPTIONS_RELATIONSHIP,
  OPTIONS_STATUS_FILTER,
  OPTIONS_STATUS_KHTN_FILTER,
  REGEX_PHONE_VN,
} from '../../../../../constants/common';
import { PERMISSION_DEMAND_CUSTOMER } from '../../../../../constants/permissions/demand';
import { useCheckPermissions, useUpdateField } from '../../../../../hooks';
import { putUpdateCustomer } from '../../../../../service/customers';
import { handleErrors } from '../../../../../service/error/errorsService';
import { ICustomers, TDataDuplicate, TIdentities } from '../../../../../types/customers';
import { handleKeyDownEnterNumber } from '../../../../../utilities/regex';
import ModalCheckDuplicate from '../../../listPersonalCustomers/components/modalCheckDuplicate';
import BankInfo from '../bankInfo';
import Indentities from '../identities';
import SkeletonDetail from '../skeletonDetail';

const { Item } = Form;
const { Title, Text } = Typography;
const { TextArea } = Input;

interface CommonFormProps {
  data?: ICustomers;
  onFormInstanceChange?: (formInstance: FormInstance) => void;
}

const GeneralInformation: React.FC<CommonFormProps> = ({ data }) => {
  const [form] = Form.useForm();
  const cloneAddress = Form.useWatch(['info', 'cloneAddress'], form);
  const address = Form.useWatch(['address'], form);
  const rootAddress = Form.useWatch(['rootAddress'], form);

  const [valuesShareEmail, setValuesShareEmail] = useState<OptionType[]>();

  const [isModified, setIsModified] = useState(false);
  const [isOpenCheckDuplicate, setIsOpenCheckDuplicate] = useState(false);
  const [yearOnly, setYearOnly] = useState(false);

  const [dataSubmit, setDataSubmit] = useState<ICustomers>();
  const [dataDuplicate, setDataDuplicate] = useState<TDataDuplicate[]>();
  const [initialValues, setInitialValues] = useState<ICustomers>();

  const { customerChangeStatus } = useCheckPermissions(PERMISSION_DEMAND_CUSTOMER);

  const { mutateAsync, isPending } = useUpdateField({
    apiQuery: putUpdateCustomer,
    keyOfListQuery: ['get-customers-personal'],
    keyOfDetailQuery: ['get-detail-customer-personal', data?.id],
    checkDuplicate: true,
    isMessageError: false,
  });

  // const handleChangeListShare = (val: OptionType[]) => {
  //   setValuesShareEmail(val);
  //   setIsModified(true);
  // };

  const handleCloneAddress = useCallback(
    (e: CheckboxChangeEvent) => {
      if (e.target.checked) {
        form.setFieldsValue({
          rootAddress: form.getFieldValue('address'),
        });
      }
    },
    [form],
  );

  const onFinish: FormProps['onFinish'] = async (values: ICustomers) => {
    const checkChangePhone = form.isFieldTouched(['personalInfo', 'phone']);

    const newData = {
      id: data?.id,
      bankInfo: values?.bankInfo?.map(item => ({
        ...item,
        name: item?.bank?.label,
        branchCode: item?.bank?.branchCode,
        code: item?.bank?.code,
        bank: undefined,
      })),
      personalInfo: {
        ...values?.personalInfo,
        income:
          typeof values?.personalInfo?.income === 'string'
            ? parseInt(values?.personalInfo?.income)
            : values?.personalInfo?.income,
        identities: values?.personalInfo?.identities?.map(item => ({
          ...item,
          type: typeof item?.type !== 'string' ? item?.type?.value : item?.type,
          date: typeof item?.date !== 'string' ? item?.date?.format(FORMAT_DATE) : null,
        })),
      },
      info: {
        ...values?.info,
        birthdayYear:
          values?.info?.birthdayYear && typeof values?.info?.birthdayYear !== 'string'
            ? values?.info?.birthdayYear?.format('YYYY')
            : null,
        birthday:
          values?.info?.birthday && typeof values?.info?.birthday !== 'string'
            ? values?.info?.birthday?.format(FORMAT_DATE)
            : null,
        address: values?.address,
        rootAddress: values?.rootAddress,
      },
      share: { emails: valuesShareEmail?.map(item => item.value) || [] },
      takeNote: values?.takeNote,
      taxCode: values?.taxCode,
      type: 'individual',
      isActive: values?.isActive,
    };

    const continueUpdate = !(checkChangePhone && data?.personalInfo?.phone !== values?.personalInfo?.phone);
    const response = await mutateAsync({ ...newData, continueUpdate });
    const responseData = response?.data;

    if (responseData) {
      switch (responseData?.statusCode) {
        case '0':
          setIsModified(false);
          break;
        case 'DCUSE0001':
          setIsOpenCheckDuplicate(true);
          setDataDuplicate(response?.data?.data as TDataDuplicate[]);
          setDataSubmit(newData);
          break;
        default:
          handleErrors(responseData);
      }
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setIsModified(false);
  };

  const handleCancelCheckDuplicate = () => {
    setIsOpenCheckDuplicate(false);
  };

  const removeDataSubmit = () => {
    setDataSubmit(undefined);
  };

  const validateForm = () => {
    setIsModified(true);
  };

  useEffect(() => {
    if (cloneAddress) {
      form.setFieldsValue({
        rootAddress: form.getFieldValue('address'),
      });
    }
  }, [cloneAddress, form, address]);

  useEffect(() => {
    if (data) {
      const initialData = {
        ...data,
        bankInfo: Array.isArray(data?.bankInfo)
          ? data.bankInfo?.map(item => ({
              ...item,
              bank: { label: item?.name, value: item?.code, branchCode: item?.branchCode, code: item?.code },
            }))
          : [],
        info: {
          ...data.info,
          birthday: data?.info?.birthday ? dayjs(data.info.birthday, FORMAT_DATE) : null,
          birthdayYear: data?.info?.birthdayYear ? dayjs(data.info.birthdayYear, 'YYYY') : null,
          onlyYear: data?.info?.birthdayYear ? true : false,
        },
        personalInfo: {
          ...data.personalInfo,
          income: data?.personalInfo?.income ? data?.personalInfo?.income?.toString() : undefined,
          identities: data?.personalInfo?.identities?.map((item: TIdentities) => {
            const getIdentityByValue = (value: string): DefaultOptionType => {
              return OPTIONS_IDENTITIES
                ? OPTIONS_IDENTITIES.find(option => option.value === value) || { label: 'Unknown', value: '' }
                : {};
            };

            const newTypeObject: DefaultOptionType = getIdentityByValue(item.type as string);

            return {
              ...item,
              typeObject: newTypeObject,
              date: item?.date ? dayjs(item.date, FORMAT_DATE) : undefined,
            };
          }),
        },
        address: {
          ...data?.info?.address,
        },
        rootAddress: {
          ...data?.info?.rootAddress,
        },
      };
      setInitialValues(initialData as ICustomers);
      setYearOnly(!!data.info?.birthdayYear);
      setValuesShareEmail(data?.share?.emails?.map(item => ({ label: item, value: item })) || []);
    }
  }, [data, form]);

  useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  return (
    <div className="wrapper-detail-personal-customer">
      {!initialValues ? (
        <SkeletonDetail />
      ) : (
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          onValuesChange={validateForm}
          initialValues={initialValues}
          className="space-y-6"
        >
          <Row gutter={{ md: 24, lg: 40, xl: 80, xxl: 126 }}>
            <Col span={24}></Col>
            <Col xs={24} md={12}>
              <Title level={5}>Thông tin chung</Title>
              <Item label="Mã số khách hàng" name="code" required>
                <Input placeholder="Mã số khách hàng" disabled />
              </Item>

              <Row gutter={24}>
                <Col xs={24} sm={12}>
                  <Item
                    name={['personalInfo', 'name']}
                    label="Họ và tên"
                    rules={[{ required: true, message: 'Vui lòng nhập họ và tên' }]}
                  >
                    <Input
                      maxLength={60}
                      placeholder="Nhập họ và tên"
                      onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                        e.target.value = e.target.value.toUpperCase(); // Chuyển đổi trực tiếp
                      }}
                    />
                  </Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Item label="Tên ngắn" name={['personalInfo', 'shortName']}>
                    <Input maxLength={25} placeholder="Nhập tên ngắn" />
                  </Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item label="Trạng thái hoạt động" name="isActive">
                    <Select
                      placeholder="Chọn trạng thái hoạt động"
                      options={OPTIONS_STATUS_FILTER}
                      disabled={!customerChangeStatus ? true : false}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item label="Trạng thái KHTN" name="status">
                    <Select
                      placeholder="Chọn trạng thái KHTN"
                      allowClear
                      options={OPTIONS_STATUS_KHTN_FILTER}
                      disabled
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Item
                    name={['personalInfo', 'phone']}
                    label="Số điện thoại"
                    rules={[
                      {
                        required: true,
                        message: 'Vui lòng nhập số điện thoại',
                      },
                      {
                        pattern: REGEX_PHONE_VN,
                        message: 'Số điện thoại phải bắt đầu bằng số 0 và từ 9 đến 15 chữ số',
                      },
                    ]}
                  >
                    <Input maxLength={15} onKeyDown={handleKeyDownEnterNumber} placeholder="Nhập số điện thoại" />
                  </Item>
                </Col>
              </Row>

              <Item
                name={['info', 'gender']}
                label="Giới tính"
                className="item-gender"
                labelCol={{ span: 5 }}
                labelAlign="left"
                layout="horizontal"
                rules={[{ required: true, message: 'Vui lòng nhập giới tính' }]}
              >
                <Radio.Group options={OPTIONS_GENDER} />
              </Item>

              <Row gutter={24}>
                <Col xs={24} sm={12}>
                  <Item
                    name={['info', 'onlyYear']}
                    label="Ngày sinh"
                    layout="horizontal"
                    labelCol={{ span: 10 }}
                    labelAlign="left"
                    valuePropName="checked"
                  >
                    <Checkbox
                      checked={yearOnly}
                      onChange={e => setYearOnly(e.target.checked)}
                      style={{ marginLeft: '4px' }}
                    >
                      Chỉ năm sinh
                    </Checkbox>
                  </Item>
                </Col>
                <Col xs={24} sm={12} className="item-birthday">
                  {yearOnly ? (
                    <Item name={['info', 'birthdayYear']}>
                      <DatePicker picker="year" format="YYYY" placeholder="YYYY" />
                    </Item>
                  ) : (
                    <Item name={['info', 'birthday']}>
                      <DatePicker format={FORMAT_DATE} placeholder={FORMAT_DATE} />
                    </Item>
                  )}
                </Col>
              </Row>
              <Row gutter={24}>
                <Col xs={24} sm={12}>
                  <Item
                    label="Địa chỉ email"
                    name={['personalInfo', 'email']}
                    rules={[{ type: 'email', message: 'Địa chỉ email sai định dạng' }]}
                  >
                    <Input maxLength={25} placeholder="Nhập địa chỉ email" />
                  </Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Item label="Mã số thuế" name="taxCode">
                    <Input maxLength={15} placeholder="Nhập mã số thuế" />
                  </Item>
                </Col>
                <Indentities form={form} setIsModified={setIsModified} />
              </Row>

              <Title level={5}>Thông tin tài khoản</Title>
              <Row gutter={24}>
                <BankInfo />
                <Col xs={24} sm={12}>
                  <Item label="Ngành nghề" name={['personalInfo', 'job']}>
                    <Input maxLength={50} placeholder="Nhập ngành nghề" />
                  </Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Item label="Thu nhập / tháng (VNĐ)" name={['personalInfo', 'income']}>
                    <CurrencyInput placeholder="Nhập khoảng thu nhập" />
                  </Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Item label="Nguồn thu nhập" name={['personalInfo', 'incomeSource']}>
                    <Input placeholder="VD: công việc hành chính" maxLength={255} />
                  </Item>
                </Col>

                <Col xs={24} sm={12}>
                  <Item label="Tình trạng hôn nhân" name={['personalInfo', 'relationshipStatus']}>
                    <Select placeholder="Chọn tình trạng hôn nhân" options={OPTIONS_RELATIONSHIP} />
                  </Item>
                </Col>
              </Row>
              {/* <Item name={['share', 'email']} label="Chia sẻ thông tin với người khác">
                <MultiSelectLazy
                  enabled={!!data?.id}
                  queryKey={['getAllDataEmployees']}
                  apiQuery={getListEmployeeInternal}
                  keysLabel={['name', 'email']}
                  keysTag={'email'}
                  placeholder="Chọn nhân viên"
                  handleListSelect={handleChangeListShare}
                />
              </Item> */}

              <Title level={5}>Địa chỉ thường trú</Title>
              <Item label="Địa chỉ" name={'address'}>
                <SelectAddress
                  placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
                  parentName={'address'}
                  address={initialValues?.address}
                  handleAddressChange={validateForm}
                />
              </Item>
              <Item name={['address', 'address']}>
                <Input placeholder="Địa chỉ cụ thể" />
              </Item>

              <Title level={5}>Địa chỉ liên lạc</Title>
              <Item name={['info', 'cloneAddress']} valuePropName="checked">
                <Checkbox onChange={handleCloneAddress}>Sử dụng địa chỉ thường trú</Checkbox>
              </Item>
              <Item label="Địa chỉ" name="rootAddress">
                <SelectAddress
                  isDisable={cloneAddress}
                  placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
                  parentName={'rootAddress'}
                  address={rootAddress}
                  handleAddressChange={validateForm}
                />
              </Item>
              <Item name={['rootAddress', 'address']}>
                <Input placeholder="Địa chỉ cụ thể" disabled={cloneAddress} />
              </Item>
            </Col>
            <Col xs={24} md={12}>
              <Title level={5} style={{ marginBottom: '35px' }}>
                Ghi chú
              </Title>
              <Item name="takeNote">
                <TextArea rows={5} maxLength={500} placeholder="Nhập ghi chú nhanh" />
              </Item>
              <div className="info">
                <Row gutter={24}>
                  <Col lg={6} xs={8}>
                    <Text disabled>Ngày cập nhật: </Text>
                  </Col>
                  <Col lg={18} xs={16}>
                    <Text disabled>
                      {dayjs(data?.updatedDate).format(FORMAT_DATE_TIME)}&nbsp;&nbsp;
                      {`${data?.updatedByObj?.username || ''} - ${data?.updatedByObj?.fullName || ''}`}
                    </Text>
                  </Col>
                </Row>
                <Row gutter={24}>
                  <Col lg={6} xs={8}>
                    <Text disabled>Ngày tạo: </Text>
                  </Col>
                  <Col lg={18} xs={16}>
                    <Text disabled>
                      {dayjs(data?.createdDate).format(FORMAT_DATE_TIME)}&nbsp;&nbsp;
                      {`${data?.createdByObj?.username || ''} - ${data?.createdByObj?.fullName || ''}`}
                    </Text>
                  </Col>
                </Row>
              </div>
            </Col>
          </Row>
        </Form>
      )}
      {isModified && (
        <ButtonOfPageDetail handleSubmit={() => form.submit()} handleCancel={handleCancel} loadingSubmit={isPending} />
      )}
      <ModalCheckDuplicate
        isOpen={isOpenCheckDuplicate}
        handleCancelCheckDuplicate={handleCancelCheckDuplicate}
        dataDuplicate={dataDuplicate}
        dataSubmit={dataSubmit as ICustomers}
        removeDataSubmit={removeDataSubmit}
        type="update"
        setIsModified={setIsModified}
      />
    </div>
  );
};
export default GeneralInformation;
