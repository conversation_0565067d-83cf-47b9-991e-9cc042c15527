import { Checkbox, Col, Form, Row, Skeleton, Typography } from 'antd';

const { Item } = Form;
const { Title } = Typography;
const { Input } = Skeleton;

const SkeletonDetail = () => {
  return (
    <Form layout="vertical">
      <Row gutter={{ md: 24, lg: 40, xl: 80, xxl: 126 }}>
        <Col span={24}></Col>
        <Col xs={24} md={12}>
          <Title level={5}>Thông tin tài khoản</Title>
          <Item label="Mã số khách hàng" name="code" required>
            <Input active block />
          </Item>

          <Row gutter={24}>
            <Col xs={24} sm={12}>
              <Item
                name={['personalInfo', 'name']}
                label="Họ và tên"
                rules={[{ required: true, message: 'Họ và tên không được để trống!' }]}
              >
                <Input active block />
              </Item>
            </Col>
            <Col xs={24} sm={12}>
              <Item label="Tên ngắn" name={['personalInfo', 'shortName']}>
                <Input active block />
              </Item>
            </Col>
            <Col xs={24} sm={12}>
              <Item name={['personalInfo', 'phone']} label="Số điện thoại">
                <Input active block />
              </Item>
            </Col>
          </Row>

          <Item
            name={['info', 'gender']}
            label="Giới tính"
            className="item-gender"
            labelCol={{ span: 5 }}
            labelAlign="left"
            layout="horizontal"
          >
            <Input active block />
          </Item>

          <Row gutter={24}>
            <Col xs={24} sm={12}>
              <Item
                name={['info', 'onlyYear']}
                label="Ngày sinh"
                layout="horizontal"
                labelCol={{ span: 12 }}
                labelAlign="left"
                valuePropName="checked"
              >
                <Checkbox>Chỉ năm sinh</Checkbox>
              </Item>
            </Col>
            <Col xs={24} sm={12} className="item-birthday">
              <Input active block />
            </Col>
          </Row>
          <Row gutter={24}>
            <Col xs={24} sm={12}>
              <Item label="Địa chỉ email" name={['personalInfo', 'email']}>
                <Input active block />
              </Item>
            </Col>
            <Col xs={24} sm={12}>
              <Item label="Mã số thuế" name="taxCode">
                <Input active block />
              </Item>
            </Col>
            <Col xs={24} sm={12}>
              <Item label="Loại giấy tờ" name={['personalInfo', 'identities', 'type']}>
                <Input active block />
              </Item>
            </Col>
            <Col xs={24} sm={12}>
              <Item label="Số giấy tờ" name={['personalInfo', 'identities', 'value']}>
                <Input active block />
              </Item>
            </Col>

            <Col xs={24} sm={12}>
              <Item label="Ngày cấp giấy tờ" name={['personalInfo', 'identities', 'date']}>
                <Input active block />
              </Item>
            </Col>
            <Col xs={24} sm={12}>
              <Item label="Nơi cấp giấy tờ" name={['personalInfo', 'identities', 'place']}>
                <Input active block />
              </Item>
            </Col>
          </Row>

          <Title level={5}>Thông tin tài khoản</Title>
          <Row gutter={24}>
            <Col xs={24} sm={12}>
              <Item label="Ngân hàng" name={['bankInfo', 'name']}>
                <Input active block />
              </Item>
            </Col>
            <Col xs={24} sm={12}>
              <Item label="Số tài khoản" name={['bankInfo', 'accountNumber']}>
                <Input active block />
              </Item>
            </Col>

            <Col xs={24} sm={12}>
              <Item label="Ngành nghề" name={['personalInfo', 'job']}>
                <Input active block />
              </Item>
            </Col>
            <Col xs={24} sm={12}>
              <Item label="Thu nhập / tháng (VNĐ)" name={['personalInfo', 'income']}>
                <Input active block />
              </Item>
            </Col>
            <Col xs={24} sm={12}>
              <Item label="Nguồn thu nhập" name={['personalInfo', 'incomeSource']}>
                <Input active block />
              </Item>
            </Col>

            <Col xs={24} sm={12}>
              <Item label="Tình trạng hôn nhân" name={['personalInfo', 'relationshipStatus']}>
                <Input active block />
              </Item>
            </Col>
          </Row>
          <Item name={['share', 'email']} label="Chia sẻ thông tin với người khác">
            <Input active block />
          </Item>

          <Title level={5}>Địa chỉ thường trú</Title>
          <Item label="Địa chỉ" name={['address']}>
            <Input active block />
          </Item>
          <Item name={['address', 'address']}>
            <Input active block />
          </Item>

          <Title level={5}>Địa chỉ liên lạc</Title>
          <Item name={['info', 'useAddress']} valuePropName="checked">
            <Checkbox> Sử dụng địa chỉ thường trú </Checkbox>
          </Item>
          <Item label="Địa chỉ" name="rootAddress">
            <Input active block />
          </Item>
          <Item name={['rootAddress', 'address']}>
            <Input active block />
          </Item>
        </Col>
        <Col xs={24} md={12}>
          <Title level={5} style={{ marginBottom: '35px' }}>
            Ghi chú
          </Title>
          <Skeleton active paragraph={{ rows: 5 }} />
        </Col>
      </Row>
    </Form>
  );
};

export default SkeletonDetail;
