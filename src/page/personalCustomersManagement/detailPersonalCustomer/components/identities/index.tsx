import { CloseOutlined, PlusOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, <PERSON>, Col, DatePicker, Divider, Form, FormInstance, Input, Row, Select, SelectProps } from 'antd';
import { handleKeyDownEnterNumber } from '../../../../../utilities/regex';
import { useFetch } from '../../../../../hooks';
import { FORMAT_DATE, OPTIONS_IDENTITIES } from '../../../../../constants/common';
import { AddressType } from '../../../../../components/selectAddress';
import { getProvinces } from '../../../../../service/address';
import React from 'react';
import { DefaultOptionType } from 'antd/lib/cascader';
import { TIdentities } from '../../../../../types/commissionPolicy';
const { Item, List } = Form;

type IndentitiesProps = {
  form: FormInstance<unknown>;
  setIsModified: React.Dispatch<React.SetStateAction<boolean>>;
};

const Indentities = (props: IndentitiesProps) => {
  const { form, setIsModified } = props;
  const [provinces, setProvinces] = React.useState<SelectProps['options']>([]);
  const [selectedIden, setSelectedIden] = React.useState<TIdentities[]>(
    form?.getFieldValue(['personalInfo', 'identities']),
  );
  const [availableIden, setAvailableIden] = React.useState<SelectProps['options']>([]);

  const { data: dataProvinces } = useFetch<AddressType[]>({
    queryKeyArrWithFilter: ['get-province-issue-place'],
    api: getProvinces,
  });

  //Lấy danh sách tỉnh thành
  React.useEffect(() => {
    if (dataProvinces?.data?.data) {
      const provinces = dataProvinces?.data?.data.map(province => ({
        label: province.nameVN,
        value: province.code,
      }));
      setProvinces(provinces);
    }
  }, [dataProvinces?.data?.data, form]);

  //Danh sách loại giấy tờ còn lại
  React.useEffect(() => {
    // Filter out the used identity types
    const filteredOptions = OPTIONS_IDENTITIES
      ? OPTIONS_IDENTITIES.filter(option => !selectedIden?.map(o => o?.type).includes(option.value as string))
      : [];

    setAvailableIden(filteredOptions);
  }, [form, selectedIden]);

  const handleChangeIdenType = React.useCallback(
    (value: DefaultOptionType, key: number) => {
      // Cập nhật giá trị trong Form
      if (value) {
        form.setFieldValue(['personalInfo', 'identities', key, 'type'], value.value);

        // Lấy dữ liệu của danh sách hiện tại
        const updatedIdentities = form.getFieldValue(['personalInfo', 'identities']) || [];

        // Cập nhật object tại vị trí key
        const updatedIdentity = {
          ...updatedIdentities[key],
          type: value.value,
          typeObject: value,
        };

        // Tạo danh sách mới với giá trị đã cập nhật
        const newSelected = [...updatedIdentities];
        newSelected[key] = updatedIdentity;

        // Cập nhật state
        setSelectedIden(newSelected);

        // Lọc các options chưa được chọn
        const selectedTypes = newSelected.map(o => o?.type);
        const filteredOptions = OPTIONS_IDENTITIES
          ? OPTIONS_IDENTITIES.filter(option => !selectedTypes.includes(option.value))
          : [];

        setAvailableIden(filteredOptions);
      }
    },
    [form],
  );

  const handleClearIdenType = React.useCallback(
    (key: number) => {
      const clearIdenType: string = form.getFieldValue(['personalInfo', 'identities', key, 'type']);

      // Lấy dữ liệu của danh sách hiện tại
      const clearedInden = form.getFieldValue(['personalInfo', 'identities', key]) || {};

      //Set lại dữ liệu của indentities
      const newIndentities = form.getFieldValue(['personalInfo', 'identities']);
      if (newIndentities.length) newIndentities.splice(key, 1);
      form.setFieldValue(['personalInfo', 'identities'], newIndentities);

      const newSelected = [...selectedIden];
      const findIndex = newSelected?.findIndex(o => o.value === clearedInden.value);

      if (findIndex > -1) newSelected.splice(findIndex, 1);

      // Lọc các options chưa được chọn
      const filteredOptions = OPTIONS_IDENTITIES
        ? OPTIONS_IDENTITIES.filter(option => option?.value === clearIdenType)
        : [];

      setAvailableIden(availableIden ? [...availableIden, ...filteredOptions] : [...filteredOptions]);
      setIsModified(true);
    },
    [availableIden, form, selectedIden, setIsModified],
  );

  return (
    <Col span={24} style={{ marginBottom: '16px' }}>
      <p style={{ marginBottom: 8 }}>Thông tin giấy tờ</p>
      <Card size="small" style={{ backgroundColor: '#00000005' }}>
        <List name={['personalInfo', 'identities']}>
          {(fields, { add }) => (
            <>
              {fields.map(({ key, name, ...restField }) => {
                return (
                  <div key={key}>
                    <Row key={key} gutter={8} align={'middle'}>
                      <Col span={11}>
                        <Item {...restField} label="Loại giấy tờ" name={[name, 'typeObject']} style={{ flex: 1 }}>
                          <Select
                            placeholder="Chọn loại giấy tờ"
                            allowClear
                            options={availableIden}
                            labelInValue
                            onChange={value => handleChangeIdenType(value, key)}
                            onClear={() => {
                              handleClearIdenType(key);
                            }}
                            // showSearch
                            style={{ width: '100%' }}
                          />
                        </Item>
                      </Col>
                      <Col span={11}>
                        <Item {...restField} label="Số giấy tờ" name={[name, 'value']}>
                          <Input maxLength={60} placeholder="Nhập số giấy tờ" onKeyDown={handleKeyDownEnterNumber} />
                        </Item>
                      </Col>
                      <Col span={2}>
                        <CloseOutlined
                          onClick={() => {
                            handleClearIdenType(key);
                            // remove(name);
                          }}
                          style={{ marginTop: '14px' }}
                        />
                      </Col>
                      <Col span={11}>
                        <Item {...restField} label="Ngày cấp giấy tờ" name={[name, 'date']}>
                          <DatePicker format={FORMAT_DATE} style={{ width: '100%' }} />
                        </Item>
                      </Col>
                      <Col span={11}>
                        <Item {...restField} label="Nơi cấp giấy tờ" name={[name, 'place']}>
                          <Select
                            placeholder="Chọn nơi cấp"
                            showSearch
                            filterOption={(input, option) =>
                              (option?.label as string).toLowerCase().includes(input.toLowerCase())
                            }
                            options={provinces}
                          />
                        </Item>
                      </Col>
                    </Row>
                    <Divider style={{ margin: '0 0 12px 0' }} />
                  </div>
                );
              })}
              <Col span={22} style={{ padding: 0 }}>
                <Button
                  type="dashed"
                  onClick={() => add()}
                  style={{ padding: 0 }}
                  block
                  icon={<PlusOutlined />}
                  disabled={form.getFieldValue(['personalInfo', 'identities'])?.length === 3}
                >
                  Thêm loại giấy tờ
                </Button>
              </Col>
            </>
          )}
        </List>
      </Card>
    </Col>
  );
};

export default Indentities;
