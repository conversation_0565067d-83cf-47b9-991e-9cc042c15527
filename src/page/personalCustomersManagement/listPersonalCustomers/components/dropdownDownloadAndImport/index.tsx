import { DownOutlined } from '@ant-design/icons';
import { App, Button, Dropdown, Space, Typography, Upload } from 'antd';
import { RcFile } from 'antd/es/upload';
import { AxiosError } from 'axios';
import { useState } from 'react';
import { HISTORY_CUSTOMER } from '../../../../../configs/path';
import { useCreateField } from '../../../../../hooks';
import { importCustomer } from '../../../../../service/customers';
import { uploadMedia } from '../../../../../service/upload';
import { TResponsiveImport } from '../../../../../types/customers';

const PersonalDemandCustomerTemplate = '/assets/PersonalDemandCustomerTemplate.xlsx';
const BusinessDemandCustomerTemplate = `/assets/BusinessDemandCustomerTemplate.xlsx`;

const { Link } = Typography;

interface Props {
  isBusiness?: boolean;
}

const DropdownDownloadAndImport = (props: Props) => {
  const { isBusiness } = props;
  const { notification } = App.useApp();
  const [isLoadingImport, setIsLoadingImport] = useState(false);

  const { mutateAsync: importDemandCustomer } = useCreateField({
    apiQuery: importCustomer,
    isMessageSuccess: false,
  });

  const handleBeforeUpload = async (file: RcFile) => {
    const isAllowedType = file?.name.toLowerCase().endsWith('.xlsx');
    if (!isAllowedType) {
      notification.error({ message: 'File không đúng định dạng. Vui lòng sử dụng file .xlsx' });
      return Upload.LIST_IGNORE;
    }
    return true;
  };
  const items = [
    {
      key: 'download',
      label: (
        <Link
          download={isBusiness ? 'BusinessDemandCustomerTemplate.xlsx' : 'PersonalDemandCustomerTemplate.xlsx'}
          href={isBusiness ? BusinessDemandCustomerTemplate : PersonalDemandCustomerTemplate}
          target="_blank"
        >
          Tải file mẫu
        </Link>
      ),
    },
    {
      key: 'import',
      label: (
        <Upload
          beforeUpload={handleBeforeUpload}
          maxCount={1}
          showUploadList={false}
          customRequest={async ({ file, onSuccess, onError }) => {
            try {
              setIsLoadingImport(true);
              const response = await uploadMedia(file as RcFile, 'demand-customer');
              const { data } = response.data;
              const fileName = data?.Key.split('/')[1]?.replace('.xlsx', '');
              const importResponse = await importDemandCustomer({
                fileUrl: data.Location,
                fileName,
                type: isBusiness ? 'DEMAND_CUSTOMER_BUSINESS' : 'DEMAND_CUSTOMER_INVIDIUAL',
              });
              const status = (importResponse?.data?.data as TResponsiveImport)?.status;

              const link = (
                <Typography.Link href={HISTORY_CUSTOMER} target="_blank">
                  đây
                </Typography.Link>
              );

              if (status === 'PENDING') {
                notification.warning({
                  message: <>Upload đang được xử lý, xem tại {link}</>,
                });
              } else if (status === 'SUCCESS') {
                notification.success({
                  message: <>Hoàn thành upload khách hàng tiềm năng, xem tại {link}</>,
                });
              } else if (['PARTIAL_ERROR', 'ENTIRE_ERROR'].includes(status as string)) {
                notification.error({
                  message: <>Upload thất bại, xem tại {link}</>,
                });
              }

              onSuccess && onSuccess('ok');
            } catch (error: unknown) {
              onError && onError(error as AxiosError);
            } finally {
              setIsLoadingImport(false);
            }
          }}
          name="file-upload"
        >
          Nhập dữ liệu
        </Upload>
      ),
    },
  ];

  return (
    <div>
      <Dropdown menu={{ items }}>
        <Button loading={isLoadingImport}>
          <Space>
            Tải / nhập dữ liệu
            <DownOutlined />
          </Space>
        </Button>
      </Dropdown>
    </div>
  );
};

export default DropdownDownloadAndImport;
