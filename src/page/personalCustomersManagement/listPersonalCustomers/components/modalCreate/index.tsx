import { Button, Col, Form, Input, Modal, Radio, Row } from 'antd';
import React, { useEffect, useState } from 'react';
import { OPTIONS_GENDER, REGEX_PHONE_VN } from '../../../../../constants/common';
import { useCreateField } from '../../../../../hooks';
import { postCreateCustomer } from '../../../../../service/customers';
import { IModalCreateCustomer, TCreateCustomer, TDataDuplicate } from '../../../../../types/customers';
import { handleKeyDownEnterNumber } from '../../../../../utilities/regex';
import ModalCheckDuplicate from '../modalCheckDuplicate';
import './styles.scss';
import { handleErrors } from '../../../../../service/error/errorsService';

const { Item } = Form;

const ModalCreateCustomer = (props: IModalCreateCustomer) => {
  const { isOpen, handleCancel } = props;
  const [form] = Form.useForm();

  const [isOpenCheckDuplicate, setIsOpenCheckDuplicate] = useState(false);
  const [dataSubmit, setDataSubmit] = useState<TCreateCustomer>();
  const [dataDuplicate, setDataDuplicate] = useState<TDataDuplicate[]>();

  const { mutateAsync: createCustomer } = useCreateField<TCreateCustomer>({
    apiQuery: postCreateCustomer,
    keyOfListQuery: ['get-customers-personal'],
    checkDuplicate: true,
    isMessageError: false,
  });

  const handleSubmit = async (values: TCreateCustomer) => {
    const newData: TCreateCustomer = {
      ...values,
      type: 'individual',
      continueCreate: false,
    };

    if (dataSubmit?.phone === values.phone) {
      await createCustomer({ ...newData, continueCreate: true });
      removeDataSubmit();
      handleCancel();
    } else {
      const res = await createCustomer(newData);
      const responseData = res?.data;
      if (responseData) {
        switch (responseData.statusCode) {
          case '0':
            handleCancel();
            removeDataSubmit();
            break;
          case 'DCUSE0001':
            setIsOpenCheckDuplicate(true);
            setDataSubmit(newData as TCreateCustomer);
            setDataDuplicate(responseData.data as TDataDuplicate[]);
            break;
          default:
            handleErrors(responseData);
        }
      }
    }
  };
  const handleCancelCheckDuplicate = () => {
    setIsOpenCheckDuplicate(false);
  };

  const removeDataSubmit = () => {
    setDataSubmit(undefined);
  };

  useEffect(() => {
    if (form.isFieldsTouched(true) && isOpen) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [form, isOpen]);

  return (
    <>
      <Modal
        rootClassName="wrapper-modal-create-customer"
        title="Tạo mới khách hàng tiềm năng cá nhân"
        open={isOpen}
        maskClosable={false}
        centered
        onCancel={() => {
          handleCancel();
          removeDataSubmit();
        }}
        destroyOnClose
        zIndex={1000}
        afterClose={() => form.resetFields()}
        footer={
          <Button type="primary" htmlType="submit" onClick={() => form.submit()}>
            Tạo mới
          </Button>
        }
      >
        <Form form={form} layout="vertical" onFinish={handleSubmit}>
          <Row gutter={16} className="content-header-form">
            <Col span={12}>
              <Item
                name={'name'}
                label="Họ và tên"
                required={false}
                rules={[{ required: true, message: 'Vui lòng nhập họ và tên', whitespace: true }]}
              >
                <Input
                  maxLength={60}
                  placeholder="Nhập họ và tên"
                  onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                    e.target.value = e.target.value.toUpperCase();
                  }}
                />
              </Item>
            </Col>
            <Col span={12}>
              <Item
                name={'phone'}
                required={false}
                label="Số điện thoại"
                rules={[
                  { required: true, message: 'Vui lòng nhập số điện thoại' },
                  {
                    pattern: REGEX_PHONE_VN,
                    message: 'Số điện thoại phải bắt đầu bằng số 0 và từ 9 đến 15 chữ số',
                  },
                ]}
              >
                <Input maxLength={15} onKeyDown={handleKeyDownEnterNumber} placeholder="Nhập số điện thoại" />
              </Item>
            </Col>
            <Col span={12}>
              <Item
                name={'gender'}
                required={false}
                label="Giới tính"
                rules={[{ required: true, message: 'Vui lòng nhập giới tính' }]}
              >
                <Radio.Group options={OPTIONS_GENDER} />
              </Item>
            </Col>
          </Row>
        </Form>
      </Modal>
      <ModalCheckDuplicate
        isOpen={isOpenCheckDuplicate}
        handleCancelCheckDuplicate={handleCancelCheckDuplicate}
        handleCancel={handleCancel}
        dataDuplicate={dataDuplicate}
        dataSubmit={dataSubmit as TCreateCustomer}
        removeDataSubmit={removeDataSubmit}
        type="create"
      />
    </>
  );
};

export default ModalCreateCustomer;
