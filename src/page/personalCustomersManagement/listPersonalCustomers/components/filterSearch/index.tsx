import { Form, Select } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { useEffect, useMemo, useState } from 'react';
import { useLocation } from 'react-router-dom';
import DatePickerFilter from '../../../../../components/datePicker/DatePickerFilter';
import DropdownFilterSearch from '../../../../../components/dropdown/dropdownFilterSearch';
import MultiSelectLazy from '../../../../../components/select/mutilSelectLazy';
import { FORMAT_DATE_API, OPTIONS_STATUS_FILTER, OPTIONS_STATUS_KHTN_FILTER } from '../../../../../constants/common';
import { useEntitiesById } from '../../../../../hooks';
import useFilter from '../../../../../hooks/filter';
import { getEmployeeDropdownById, getListEmployeeAll } from '../../../../../service/customers';
import { TEmployeeAll } from '../../../../../types/customers';
import './styles.scss';

type TFilter = {
  isActive?: number | null;
  startCreatedDate?: string | Dayjs | null;
  endCreatedDate?: string | Dayjs | null;
  status?: string;
  createdBy?: string;
  share?: string;
};

function FilterSearch() {
  const [form] = Form.useForm();
  const [isOpenFilter, setIsOpenFilter] = useState(false);
  const [initialValues, setInitialValues] = useState<TFilter>();
  const [filter, setFilter] = useFilter();
  const { search } = useLocation();
  const params = useMemo(() => new URLSearchParams(search), [search]);

  const { entities: defaultEmployee } = useEntitiesById({
    apiQuery: getEmployeeDropdownById,
    queryKey: ['employee-dropdown'],
    params: { id: filter?.createdBy },
    labelField: 'name',
    valueField: 'id',
  });

  // const { entities: defaultEmployeeShare } = useEntitiesById({
  //   apiQuery: getEmployeeDropdownById,
  //   queryKey: ['employee-dropdown'],
  //   params: { id: filter?.share },
  //   labelField: 'name',
  //   valueField: 'id',
  // });

  useEffect(() => {
    const formatData = {
      status: params.get('status') || undefined,
      isActive: params.get('isActive') ? Number(params.get('isActive')) : undefined,
      startCreatedDate: params.get('startCreatedDate') ? dayjs(params.get('startCreatedDate')) : undefined,
      endCreatedDate: params.get('endCreatedDate') ? dayjs(params.get('endCreatedDate')) : undefined,
      createdBy: params.get('createdBy') || undefined,
      share: params.get('share') || undefined,
    };
    setInitialValues(formatData);
    form.setFieldsValue(formatData);
  }, [form, params]);

  const handleSubmitFilter = (values: TFilter) => {
    const newFilter: Record<string, unknown> = {
      status: values.status || null,
      isActive: values.isActive || null,
      startCreatedDate: values?.startCreatedDate ? dayjs(values?.startCreatedDate).format(FORMAT_DATE_API) : null,
      endCreatedDate: values?.endCreatedDate ? dayjs(values?.endCreatedDate).format(FORMAT_DATE_API) : null,
      createdBy: values?.createdBy ? values.createdBy : null,
      share: values?.share ? values.share : null,
    };
    setFilter({ ...filter, page: '1', ...newFilter });
    setIsOpenFilter(false);
  };

  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };
  const handleSelectEmployee = (values: TEmployeeAll[]) => {
    form.setFieldsValue({ createdBy: values.map(item => item.id).join(',') });
  };
  // const handleSelectEmployeeShare = (values: TEmployeeAll[]) => {
  //   form.setFieldsValue({ share: values.map(item => item.id).join(',') });
  // };

  const handleClearFilters = () => {
    setInitialValues(undefined);
    setTimeout(() => {
      setIsOpenFilter(false);
    }, 100);
  };
  return (
    <>
      <DropdownFilterSearch
        rootClassName="wrapper-filter-customers"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        showParams={true}
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        initialValues={initialValues}
        form={form}
        onClearFilters={handleClearFilters}
        extraFormItems={
          <>
            <Form.Item label="Người tạo" name="createdBy">
              <MultiSelectLazy
                enabled={isOpenFilter}
                apiQuery={getListEmployeeAll}
                queryKey={['employee-dropdown']}
                keysLabel={['name', 'email']}
                handleListSelect={handleSelectEmployee}
                defaultValues={defaultEmployee}
                placeholder="Chọn nhân viên"
                keysTag={'email'}
              />
            </Form.Item>
            {/* <Form.Item label="Người được chia sẻ" name="share">
              <MultiSelectLazy<TEmployeeAll>
                enabled={isOpenFilter}
                apiQuery={getListEmployeeAll}
                queryKey={['employee-dropdown']}
                keysLabel={['name', 'email']}
                handleListSelect={handleSelectEmployeeShare}
                defaultValues={defaultEmployeeShare}
                placeholder="Chọn nhân viên"
                keysTag={'email'}
              />
            </Form.Item> */}
            <Form.Item label="Trạng thái hoạt động" name="isActive">
              <Select placeholder="Chọn trạng thái hoạt động" allowClear options={OPTIONS_STATUS_FILTER} />
            </Form.Item>
            <Form.Item label="Trạng thái KHTN" name="status">
              <Select placeholder="Chọn trạng thái KHTN" allowClear options={OPTIONS_STATUS_KHTN_FILTER} />
            </Form.Item>
            <DatePickerFilter startDate="startCreatedDate" endDate="endCreatedDate" />
          </>
        }
      />
    </>
  );
}

export default FilterSearch;
