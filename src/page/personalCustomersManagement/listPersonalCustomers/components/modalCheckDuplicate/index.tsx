import { Button, Modal, Table, Typography } from 'antd';
import { IModalCheckDuplicate, TCreateCustomer } from '../../../../../types/customers';
import { ExclamationCircleFilled } from '@ant-design/icons';
import { columnsDuplicate } from '../../columns';
import { useCreateField, useUpdateField } from '../../../../../hooks';
import { postCreateCustomer, putUpdateCustomer } from '../../../../../service/customers';
import { useParams } from 'react-router-dom';

const ModalCheckDuplicate = (props: IModalCheckDuplicate) => {
  const {
    isOpen,
    handleCancelCheckDuplicate,
    dataDuplicate,
    handleCancel,
    dataSubmit,
    removeDataSubmit,
    type,
    setIsModified,
  } = props;
  const { id } = useParams();
  const { mutateAsync: createCustomer, isPending: isPendingCreate } = useCreate<PERSON>ield<TCreateCustomer>({
    apiQuery: postCreateCustomer,
    keyOfListQuery: ['get-customers-personal'],
  });

  const { mutateAsync: updateCustomer, isPending: isPendingUpdate } = useUpdateField({
    apiQuery: putUpdateCustomer,
    keyOfListQuery: ['get-customers-personal'],
    keyOfDetailQuery: ['get-detail-customer-personal', id],
  });

  const handleOk = async () => {
    if (type === 'update') {
      await updateCustomer({ ...dataSubmit, continueUpdate: true });
      setIsModified && setIsModified(false);
    } else {
      await createCustomer({ ...dataSubmit, continueCreate: true });
    }
    removeDataSubmit();
    handleCancelCheckDuplicate();
    handleCancel && handleCancel();
  };

  const getTitle = () => {
    if (type === 'create') {
      return 'Tạo mới khách hàng tiềm năng cá nhân';
    } else {
      return 'Chỉnh sửa khách hàng tiềm năng cá nhân';
    }
  };

  return (
    <Modal
      rootClassName="wrapper-modal-create-customer"
      title={getTitle()}
      open={isOpen}
      centered
      maskClosable={false}
      onCancel={handleCancelCheckDuplicate}
      width={800}
      zIndex={1100}
      footer={
        <Button type="primary" htmlType="submit" onClick={handleOk} loading={isPendingCreate || isPendingUpdate}>
          {type === 'update' ? 'Tiếp tục lưu thay đổi' : 'Tiếp tục tạo mới'}
        </Button>
      }
    >
      <div style={{ padding: '20px 0' }}>
        <ExclamationCircleFilled style={{ color: '#FAAD14', fontSize: 21 }} /> &nbsp;
        <Typography.Text>
          Số điện thoại đã có trên hệ thống, bạn có chắc chắn muốn tạo thêm thông tin không?
        </Typography.Text>
      </div>
      <Table
        scroll={{ y: 200 }}
        columns={columnsDuplicate}
        dataSource={dataDuplicate}
        pagination={false}
        rowKey={'id'}
      />
    </Modal>
  );
};

export default ModalCheckDuplicate;
