import { Flex, List, Table, TableProps, Typography } from 'antd';
import { useState } from 'react';
import BreadCrumbComponent from '../../components/breadCrumb';
import { useFetch } from '../../hooks';
import { getListOfPersonalCommission } from '../../service/personalCommission';
import { ICommissionItem, ICommissionResponse, IFilterPersonalCommission } from '../../types/personalCommission';
import FilterPersonalCommission from './FilterPersonalCommission';
import './styles.scss';

const { Text } = Typography;

const columns: TableProps<ICommissionItem>['columns'] = [
  { title: 'Dự án', dataIndex: ['project', 'name'], key: 'project', render: (value: string) => value || '-' },
  {
    title: 'Mã BĐS',
    dataIndex: ['propertyUnit', 'view1'],
    key: 'propertyUnitView1',
    render: (value: string) => value || '-',
  },
  {
    title: 'Tên khách hàng',
    dataIndex: ['customer', 'name'],
    key: 'customer',
    render: (value: string) => value || '-',
  },
  {
    title: '<PERSON><PERSON> tính hoa hồng',
    dataIndex: ['commission', 'period'],
    key: 'commissionPeriod',
    render: (value: string) => value || '-',
  },
  {
    title: 'Tỷ lệ chia',
    dataIndex: ['employees', 'revenueRate'],
    key: 'employeesRevenueRate',
    render: (value: number) => (value ? `${value.toFixed(2)}%` : '-'),
  },
  {
    title: 'Doanh thu tổng',
    dataIndex: 'commissionRevenue',
    key: 'commissionRevenue',
    render: (value: number) => value?.toLocaleString('vi-VN') || 0,
  },
  {
    title: 'Doanh thu chi cho đợt',
    dataIndex: 'commissionReceived',
    key: 'commissionReceived',
    render: (value: number) => value?.toLocaleString('vi-VN') || 0,
  },
  {
    title: 'Mã nhân viên',
    dataIndex: ['employees', 'code'],
    key: 'employeeCode',
    render: (value: number) => value || 0,
  },
  {
    title: 'Tỉ lệ hoa hồng',
    dataIndex: ['employees', 'commissions', 'rate'],
    key: 'employeeCommissionRate',
    render: (value: number) => (value ? `${value.toFixed(2)}%` : '-'),
  },
  {
    title: 'Tiền lương nhân sự',
    dataIndex: ['employees', 'commissions', 'revenue'],
    key: 'employeeCommissionRevenue',
    render: (value: number) => value?.toLocaleString('vi-VN') || 0,
  },
  {
    title: 'Thưởng thêm',
    dataIndex: ['employees', 'commissions', 'bonus'],
    key: 'employeeCommissionBonus',
    render: (value: number) => value?.toLocaleString('vi-VN') || 0,
  },
  {
    title: 'Tạm ứng hoa hồng',
    dataIndex: ['employees', 'commissions', 'advance'],
    key: 'employeeCommissionAdvance',
    render: (value: number) => value?.toLocaleString('vi-VN') || 0,
  },
  {
    title: 'Tổng thu nhập dự kiến',
    dataIndex: ['employees', 'commissions', 'totalExpectedIncome'],
    key: 'employeeCommissionTotalExpectedIncome',
    render: (value: number) => value?.toLocaleString('vi-VN') || 0,
  },
];

const ListOfPersonalCommission = () => {
  const [filterParams, setFilterParams] = useState<IFilterPersonalCommission>({});

  const { data, isLoading } = useFetch<ICommissionResponse>({
    api: getListOfPersonalCommission,
    queryKeyArr: ['get-list-of-personal-commission', filterParams],
    moreParams: filterParams,
    withFilter: false,
  });

  const dataSource = data?.data?.data;

  const summaryData = [
    {
      label: 'Tổng tạm ứng HH',
      value: dataSource?.totalAdvance || 0,
    },
    {
      label: 'Tổng doanh thu trong tháng',
      value: dataSource?.totalRevenue || 0,
    },
    {
      label: 'Tổng thu nhập dự kiến',
      value: dataSource?.totalIncome || 0,
    },
  ];

  return (
    <div className="wrapper-personal-commission">
      <BreadCrumbComponent />
      <Flex style={{ marginBottom: 16 }}>
        <FilterPersonalCommission setFilterParams={setFilterParams} />
      </Flex>
      <Table
        columns={columns}
        dataSource={dataSource?.items || []}
        pagination={false}
        loading={isLoading}
        scroll={{ x: 'max-content' }}
        footer={() => (
          <List
            itemLayout="vertical"
            dataSource={summaryData}
            renderItem={(item: { label: string; value: number }) => (
              <List.Item style={{ textAlign: 'right' }}>
                <Text>
                  {item.label}: <Text strong>{item.value.toLocaleString('vi-VN')} VNĐ</Text>
                </Text>
              </List.Item>
            )}
          />
        )}
      />
    </div>
  );
};

export default ListOfPersonalCommission;
