import { Typography } from 'antd';
import { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { FORMAT_DATE, FORMAT_DATE_TIME, OPTIONS_STATUS_FILTER } from '../../../constants/common';
import { TListOfDeliveryConfig } from '../../../types/deliveryConfig';

const { Text } = Typography;

export const Columns: ColumnsType<TListOfDeliveryConfig> = [
  {
    title: 'STT',
    dataIndex: 'stt',
    key: 'stt',
    width: 60,
    render: (_, __, index) => index + 1,
  },
  {
    title: 'Dự án',
    dataIndex: ['project', 'name'],
    key: 'projectName',
    width: 300,
    render: value => value || '-',
  },
  {
    title: 'Số lượng hạng mục',
    dataIndex: 'items',
    key: 'items',
    width: 150,
    render: value => (value !== undefined ? value : '-'),
  },
  {
    title: 'Thời hạn bàn giao dự kiến',
    dataIndex: 'timeDelivery',
    key: 'timeDelivery',
    width: 200,
    render: (_, { expectedStartDate, expectedEndDate }) =>
      `${expectedStartDate ? dayjs(expectedStartDate).format(FORMAT_DATE) : '-'} - ${expectedEndDate ? dayjs(expectedEndDate).format(FORMAT_DATE) : '-'}`,
  },
  {
    title: 'Trạng thái',
    dataIndex: 'status',
    key: 'status',
    width: 100,
    render: value => {
      const status = OPTIONS_STATUS_FILTER.find(item => item.value === value);
      if (!status) return '-';
      return <Text style={{ color: status.color }}>{status.label}</Text>;
    },
  },
  {
    title: 'Ngày tạo',
    dataIndex: 'createdDate',
    width: 150,
    key: 'createdDate',
    render: (value, { createdBy }) =>
      value ? (
        <>
          <Text>{value ? dayjs(value).format(FORMAT_DATE_TIME) : '-'}</Text>
          <br />
          <Text>{createdBy?.userName || '-'}</Text>
        </>
      ) : (
        '-'
      ),
  },
  {
    title: 'Ngày cập nhật',
    dataIndex: 'updatedDate',
    width: 150,
    key: 'updatedDate',
    render: (value, { updatedBy }) =>
      value ? (
        <>
          <Text>{value ? dayjs(value).format(FORMAT_DATE_TIME) : '-'}</Text>
          <br />
          <Text>{updatedBy?.userName || '-'}</Text>
        </>
      ) : (
        '-'
      ),
  },
];
