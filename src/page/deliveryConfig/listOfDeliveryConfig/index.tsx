import { MutationFunction } from '@tanstack/react-query';
import { App, But<PERSON> } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { useMemo, useState } from 'react';
import BreadCrumbComponent from '../../../components/breadCrumb';
import ConfirmDeleteModal from '../../../components/modal/specials/ConfirmDeleteModal';
import { modalConfirm } from '../../../components/modal/specials/ModalConfirm';
import TableComponent from '../../../components/table';
import { ActionsColumns } from '../../../components/table/components/ActionsColumns';
import { DELIVERY_CONFIGURATION } from '../../../configs/path';
import { PRIMARY_CONTRACT_PERMISSION } from '../../../constants/permissions/primaryContract';
import { useCheckPermissions, useFetch, useUpdateField } from '../../../hooks';
import {
  changeStatusDeliveryConfig,
  deleteDeliveryConfig,
  getAllDeliveryConfig,
} from '../../../service/deliveryConfig';
import { TListOfDeliveryConfig } from '../../../types/deliveryConfig';
import CreateDeliveryConfig from '../createDeliveryConfig';
import { useStoreDeliveryConfig } from '../storeDeliveryConfig';
import { Columns } from './Columns';
import FilterDeliveryConfig from './FilterDeliveryConfig';

const ListOfDeliveryConfig = () => {
  const { modal } = App.useApp();
  const [isOpenModalDelete, setIsOpenModalDelete] = useState<boolean>(false);
  const [currentRecord, setCurrentRecord] = useState<TListOfDeliveryConfig>();
  const { setOpenModalCreate } = useStoreDeliveryConfig();

  const {
    handoverChangeStatus,
    handoverGetId,
    handoverCreate,
    handoverDelete: isDelete,
  } = useCheckPermissions(PRIMARY_CONTRACT_PERMISSION);

  const { data, isLoading } = useFetch<TListOfDeliveryConfig[]>({
    api: getAllDeliveryConfig,
    queryKeyArrWithFilter: ['list-delivery-config'],
  });
  const dataSourceDeliveryConfig = data?.data?.data?.rows;

  const { mutateAsync, isPending } = useUpdateField({
    keyOfListQuery: ['list-delivery-config'],
    apiQuery: changeStatusDeliveryConfig,
    isMessageError: false,
  });

  const columnsActions: ColumnsType<TListOfDeliveryConfig> = useMemo(() => {
    return [
      ...Columns,
      {
        title: 'Hành động',
        dataIndex: 'action',
        key: 'action',
        width: '100px',
        align: 'center',
        render: (_: unknown, record: TListOfDeliveryConfig) => {
          const textModalConfirmActive = record?.status === 1 ? 'Vô hiệu hoá' : 'Kích hoạt';
          const handleActivePolicy = () => {
            return modalConfirm({
              modal: modal,
              loading: isPending,
              title: `${textModalConfirmActive}  thiết lập bàn giao`,
              content: `Bạn có muốn ${textModalConfirmActive} thiết lập bàn giao này không?`,
              handleConfirm: async () => {
                await mutateAsync({ id: record?.id });
              },
            });
          };
          const handleDeleteRole = () => {
            setIsOpenModalDelete(true);
            setCurrentRecord(record);
          };

          const openViewDetail = () => {
            window.open(`${DELIVERY_CONFIGURATION}/${record?.id}`, '_blank', 'noopener,noreferrer');
          };

          return (
            <ActionsColumns
              handleViewDetail={handoverGetId ? openViewDetail : undefined}
              textModalConfirmActive={textModalConfirmActive}
              handleActive={handoverChangeStatus ? handleActivePolicy : undefined}
              handleDelete={isDelete ? handleDeleteRole : undefined}
            />
          );
        },
      },
    ];
  }, [handoverChangeStatus, handoverGetId, isDelete, isPending, modal, mutateAsync]);

  const handleOpenModalCreate = () => {
    setOpenModalCreate(true);
  };
  const handleCancelModalCreate = () => {
    setOpenModalCreate(false);
  };

  return (
    <>
      <BreadCrumbComponent />
      <div className="header-filter" style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
        <FilterDeliveryConfig />
        {handoverCreate && (
          <Button type="primary" onClick={handleOpenModalCreate}>
            Tạo mới
          </Button>
        )}
      </div>
      <TableComponent
        className="table-time-config-default-list"
        columns={columnsActions}
        queryKeyArr={['list-delivery-config']}
        dataSource={dataSourceDeliveryConfig}
        loading={isLoading}
        rowKey={'id'}
      />
      <ConfirmDeleteModal
        label="Bộ thiết lập Bàn giao"
        open={isOpenModalDelete}
        apiQuery={deleteDeliveryConfig as MutationFunction<unknown, unknown>}
        keyOfListQuery={['list-delivery-config']}
        onCancel={() => setIsOpenModalDelete(false)}
        idDetail={currentRecord?.id}
        title="Xóa bộ thiết lập Bàn giao"
        description="Vui lòng nhập lý do muốn thiết lập bàn giao này"
        fieldNameReason="reasonDelete"
      />
      <CreateDeliveryConfig onCancel={handleCancelModalCreate} />
    </>
  );
};

export default ListOfDeliveryConfig;
