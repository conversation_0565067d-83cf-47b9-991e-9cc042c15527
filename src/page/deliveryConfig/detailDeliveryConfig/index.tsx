import { Form, Spin, Tabs } from 'antd';
import { TabsProps } from 'antd/lib';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { createSearchParams, useParams, useSearchParams } from 'react-router-dom';
import { v4 as uuidv4 } from 'uuid';
import BreadCrumbComponent from '../../../components/breadCrumb';
import ButtonOfPageDetail from '../../../components/button/buttonOfPageDetail';
import { FORMAT_DATE_API } from '../../../constants/common';
import { PRIMARY_CONTRACT_PERMISSION } from '../../../constants/permissions/primaryContract';
import { useCheckPermissions, useFetch, useUpdateField } from '../../../hooks';
import { getByIdDeliveryConfig, updateDeliveryConfig } from '../../../service/deliveryConfig';
import { DeliveryConfig, TDeliveryConfigGen } from '../../../types/deliveryConfig';
import GeneralInfoDeliveryConfig from '../components/GeneralInfoDeliveryConfig';
import NotificationDeliveryConfig from '../components/NotificationDeliveryConfig';
import { useStoreDeliveryConfig } from '../storeDeliveryConfig';
import { formatItemsToTimeFrames, formatTimeFrameToSubmit, validateForms } from '../utilities';

const DetailDeliveryConfig = () => {
  const { id } = useParams();
  const [urlParams, setUrlParams] = useSearchParams();
  const filter = Object.fromEntries([...urlParams]);
  const [formGen] = Form.useForm();
  const [formNoti] = Form.useForm();
  const [tab, setTab] = useState(filter?.tab || 'general-info');
  const { setInitialValueGen, setInitialValueNoti, setIsModified, setDataHandovers, isModified, dataHandovers } =
    useStoreDeliveryConfig();
  const { handoverUpdate } = useCheckPermissions(PRIMARY_CONTRACT_PERMISSION);

  const { data, isLoading } = useFetch<DeliveryConfig>({
    queryKeyArr: ['detail-delivery-config', id],
    api: () => getByIdDeliveryConfig(id),
    enabled: !!id,
  });
  const dataSource = data?.data?.data;

  const { mutateAsync, isPending } = useUpdateField({
    keyOfDetailQuery: ['detail-delivery-config', id],
    apiQuery: updateDeliveryConfig,
    isMessageError: false,
  });

  useEffect(() => {
    if (!dataSource) return;
    const timeFrames = formatItemsToTimeFrames(dataSource);

    const formatDataSourceGen: TDeliveryConfigGen = {
      accountingConfirm: dataSource?.accountingConfirm === 1 ? true : false,
      project: {
        id: dataSource?.project?.id,
        name: dataSource?.project?.name,
      },
      expectedDateRange: [dayjs(dataSource?.expectedStartDate), dayjs(dataSource?.expectedEndDate)], // Required fields for TDeliveryConfigGen
      status: dataSource?.status === 1 ? true : false,
      items: dataSource?.items || [],
      orgCharts: dataSource?.orgCharts || [],
      paymentPercent: dataSource?.paymentPercent?.toString() || '0',
      hotline: dataSource?.hotline || '',
      timeFrames: timeFrames || [],
    };

    setInitialValueGen(formatDataSourceGen);
    formGen.setFieldsValue(formatDataSourceGen);

    const notificationData = {
      emailTemplate: dataSource?.emailTemplate,
      smsTemplate: dataSource?.smsTemplate,
      smsBrandName: dataSource?.smsBrandName,
      emailTitle: dataSource?.emailTitle,
      emailFrom: dataSource?.emailFrom,
      emailCC: dataSource?.emailCC,
      emailBCC: dataSource?.emailBCC,
    };

    setInitialValueNoti(notificationData);
    formNoti.setFieldsValue(notificationData);
    setDataHandovers(
      dataSource?.items?.map(item => ({
        ...item,
        id: item?.id ? item.id : uuidv4(),
      })) || [],
    );
  }, [dataSource, formGen, formNoti, setDataHandovers, setInitialValueGen, setInitialValueNoti]);

  const items: TabsProps['items'] = [
    { label: 'Thông tin chung', key: 'general-info', children: <GeneralInfoDeliveryConfig form={formGen} /> },
    { label: 'Thông báo', key: 'notification', children: <NotificationDeliveryConfig form={formNoti} /> },
  ];
  const handleSubmit = async () => {
    try {
      const formResult = await validateForms(formGen, formNoti);
      if (!formResult) return;

      const { valuesGen, valuesNoti } = formResult;

      const formatExpectedDate = {
        expectedStartDate: dayjs(valuesGen?.expectedDateRange?.[0]).format(FORMAT_DATE_API),
        expectedEndDate: dayjs(valuesGen?.expectedDateRange?.[1]).format(FORMAT_DATE_API),
        expectedDateRange: undefined,
      };
      const formatTimeFrames = formatTimeFrameToSubmit(valuesGen?.timeFrames);

      const values = {
        ...valuesGen,
        ...valuesNoti,
        ...formatExpectedDate,
        id,
        items: dataHandovers,
        status: valuesGen?.status ? 1 : 2,
        accountingConfirm: valuesGen?.accountingConfirm ? 1 : 2,
        paymentPercent: valuesGen?.paymentPercent && parseFloat(valuesGen?.paymentPercent),
        ...formatTimeFrames,
        timeFrames: undefined,
      };
      const res = await mutateAsync(values);
      if (res?.data?.statusCode === '0') {
        setIsModified(false);
      }
    } catch (err) {
      console.error('Validation failed:', err);
    }
  };
  const handleCancel = () => {
    formGen.resetFields();
    formNoti.resetFields();
    setIsModified(false);
    setDataHandovers([]);
  };

  useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  const handleChangeTab = (activeKey: string) => {
    const params = createSearchParams({ tab: activeKey });
    setTab(activeKey);
    setUrlParams(params);
  };

  return (
    <Spin spinning={isLoading}>
      <BreadCrumbComponent titleBread={dataSource?.project?.name} />
      <Tabs items={items} activeKey={tab} onChange={handleChangeTab} />
      {isModified && handoverUpdate && (
        <ButtonOfPageDetail handleSubmit={handleSubmit} handleCancel={handleCancel} loadingSubmit={isPending} />
      )}
    </Spin>
  );
};

export default DetailDeliveryConfig;
