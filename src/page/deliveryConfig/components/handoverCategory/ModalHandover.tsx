import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Col, Form, Input, Modal, Row, Select } from 'antd';
import { OPTIONS_HANDOVER } from '../../../../constants/common';
import { THandover } from '../../../../types/deliveryConfig';
import { useStoreDeliveryConfig } from '../../storeDeliveryConfig';
import { v4 as uuIdv4 } from 'uuid';
import { useEffect } from 'react';

interface IModalHandover {
  open: boolean;
  onCancel: () => void;
  defaultValue?: THandover;
}

const ModalHandover = (props: IModalHandover) => {
  const { open, onCancel, defaultValue } = props;
  const [form] = Form.useForm();
  const { dataHandovers, setDataHandovers, setIsModified } = useStoreDeliveryConfig();

  useEffect(() => {
    form.setFieldsValue({ items: defaultValue });
  }, [defaultValue, form]);

  const handleFinish = (values: { items?: THandover }) => {
    if (!values?.items) return;
    const newList = values.items?.list?.filter(item => item.title && item.type && item.description);

    if (defaultValue?.id && values?.items?.name) {
      const updatedHandovers =
        dataHandovers?.map(item =>
          item.id === defaultValue.id && values.items
            ? { ...values.items, name: values.items?.name, id: defaultValue?.id, list: newList }
            : item,
        ) || [];
      setDataHandovers(updatedHandovers);
    } else {
      setDataHandovers([...(dataHandovers || []), { ...values.items, id: uuIdv4(), list: newList }]);
    }
    setIsModified(true);
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      className="modal-handover"
      open={open}
      title="Thêm hạng mục"
      width={700}
      onCancel={onCancel}
      afterClose={() => form.resetFields()}
      maskClosable={false}
      footer={
        <div>
          <Button type="primary" onClick={form.submit}>
            Lưu
          </Button>
        </div>
      }
    >
      <Form form={form} layout="vertical" onFinish={handleFinish}>
        <Form.Item
          label="Tên hạng mục"
          name={['items', 'name']}
          rules={[{ required: true, message: 'Vui lòng nhập tên hạng mục', whitespace: true }]}
        >
          <Input placeholder="Nhập tên hạng mục" />
        </Form.Item>
        <Form.Item label="Chi tiết hạng mục" style={{ margin: 0 }} layout="horizontal" />

        <Form.List name={['items', 'list']} initialValue={[{}]}>
          {(fields, { add, remove }) => (
            <>
              <Button type="dashed" onClick={() => add()} icon={<PlusOutlined />} style={{ marginBottom: 16 }} block>
                Thêm hạng mục
              </Button>
              {fields.map(({ key, name, ...restField }) => {
                return (
                  <Row gutter={12} key={key} justify="space-between" className="row-handover">
                    <Form.Item noStyle shouldUpdate>
                      {() => {
                        const row = form.getFieldValue(['items', 'list', name]) || {};
                        const { title, type, description } = row;

                        const hasAnyValue = title || type || description;
                        const hasMissingValue = hasAnyValue && (!title || !type || !description);

                        const validationError = hasMissingValue ? 'Vui lòng nhập đủ 3 giá trị' : '';

                        return (
                          <>
                            <Col span={8}>
                              <Form.Item
                                {...restField}
                                name={[name, 'title']}
                                validateStatus={hasMissingValue ? 'error' : ''}
                                help={hasMissingValue ? validationError : ''}
                              >
                                <Input placeholder="Nhập nội dung" maxLength={50} />
                              </Form.Item>
                            </Col>
                            <Col span={5}>
                              <Form.Item
                                {...restField}
                                name={[name, 'type']}
                                validateStatus={hasMissingValue ? 'error' : ''}
                              >
                                <Select allowClear placeholder="Chọn loại" options={OPTIONS_HANDOVER} />
                              </Form.Item>
                            </Col>
                            <Col span={10}>
                              <Form.Item
                                {...restField}
                                name={[name, 'description']}
                                validateStatus={hasMissingValue ? 'error' : ''}
                              >
                                <Input placeholder="Nhập mô tả" maxLength={50} />
                              </Form.Item>
                            </Col>
                            <Col span={1} className="delete-handover">
                              <DeleteOutlined onClick={() => remove(name)} style={{ color: 'red' }} />
                            </Col>
                          </>
                        );
                      }}
                    </Form.Item>
                  </Row>
                );
              })}
            </>
          )}
        </Form.List>
      </Form>
    </Modal>
  );
};

export default ModalHandover;
