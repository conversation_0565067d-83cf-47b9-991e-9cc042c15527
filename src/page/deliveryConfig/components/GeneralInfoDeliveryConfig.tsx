import { Col, DatePicker, Form, FormInstance, Input, Row, Switch } from 'antd';
import { useMemo } from 'react';
import { useParams } from 'react-router-dom';
import PercentInput from '../../../components/input/PercentInput';
import MultiSelectLazy from '../../../components/select/mutilSelectLazy';
import SingleSelectLazy from '../../../components/select/singleSelectLazy';
import { FORMAT_DATE } from '../../../constants/common';
import { getAllProjectsOfCommission } from '../../../service/commission';
import { getOrgChartDropdown } from '../../../service/lead';
import { projectAllForDeliveryConfig } from '../../../types/deliveryConfig';
import { TListDropdown } from '../../../types/salesPolicy';
import { handleKeyDownEnterNumber } from '../../../utilities/regex';
import { useStoreDeliveryConfig } from '../storeDeliveryConfig';
import TimeFrame from './TimeFrame';
import HandoverCategory from './handoverCategory';

const { Item } = Form;
const { RangePicker } = DatePicker;

interface IGeneralInfoDeliveryConfig {
  form: FormInstance;
}

const GeneralInfoDeliveryConfig = (props: IGeneralInfoDeliveryConfig) => {
  const { id } = useParams();
  const { form } = props;
  const { openModalCreate, initialValueGen, setIsModified } = useStoreDeliveryConfig();

  const defaultValueOrgCharts = useMemo(
    () => initialValueGen?.orgCharts?.map(item => ({ ...item, value: item?.id, label: item?.name })),
    [initialValueGen?.orgCharts],
  );

  const handleSelectProject = (value: projectAllForDeliveryConfig) => {
    form.setFieldsValue({ project: { name: value?.name, id: value?.id } });
    setIsModified(true);
  };

  const handleSelectListPos = (values: TListDropdown[]) => {
    form.setFieldsValue({
      orgCharts: values?.map(item => ({
        id: item?.id,
        name: item?.name,
        code: item?.code,
      })),
    });
    setIsModified(true);
  };
  const validateForm = () => {
    setIsModified(true);
  };

  return (
    <Form
      form={form}
      layout="vertical"
      name="formGeneralInfo"
      onValuesChange={validateForm}
      initialValues={initialValueGen}
    >
      <Row gutter={[16, 16]}>
        <Col xl={12} lg={24}>
          <Item name="project" label="Dự án" rules={[{ required: true, message: 'Vui lòng chọn dự án' }]}>
            <SingleSelectLazy
              disabled={!!id}
              apiQuery={getAllProjectsOfCommission}
              queryKey={['list-projects']}
              enabled={openModalCreate || !!id}
              placeholder="Chọn dự án"
              keysLabel={['name']}
              handleSelect={handleSelectProject}
              defaultValues={
                initialValueGen?.project && {
                  value: initialValueGen?.project?.id,
                  label: initialValueGen?.project?.name,
                }
              }
            />
          </Item>
          <Item
            name="expectedDateRange"
            label="Thời gian bàn giao (dự kiến)"
            rules={[{ required: true, message: 'Vui lòng chọn thời gian bàn giao (dự kiến)' }]}
          >
            <RangePicker format={FORMAT_DATE} />
          </Item>
          <Item
            name="status"
            layout="horizontal"
            labelCol={{ span: 10 }}
            labelAlign="left"
            label="Trạng thái"
            valuePropName="checked"
          >
            <Switch />
          </Item>

          <h3>Thông tin bàn giao</h3>
          <Item
            name="orgCharts"
            label="Đơn vị thực hiện"
            rules={[{ required: true, message: 'Vui lòng chọn đơn vị thực hiện' }]}
          >
            <MultiSelectLazy
              apiQuery={getOrgChartDropdown}
              queryKey={['orgChart-exchanges']}
              enabled={openModalCreate || !!id}
              keysTag={'name'}
              keysLabel={'name'}
              placeholder="Chọn đơn vị thực hiện"
              handleListSelect={handleSelectListPos}
              defaultValues={defaultValueOrgCharts}
            />
          </Item>
          <Item
            name="paymentPercent"
            label="Phần trăm thanh toán yêu cầu"
            rules={[{ required: true, message: 'Vui lòng nhập phần trăm thanh toán yêu cầu', whitespace: true }]}
          >
            <PercentInput suffix="%" placeholder="Nhập phần trăm thanh toán" max={100} />
          </Item>
          <Item
            name="hotline"
            label="Hotline"
            rules={[{ required: true, message: 'Vui lòng nhập hotline bàn giao', whitespace: true }]}
          >
            <Input placeholder="Nhập hotline bàn giao" onKeyDown={handleKeyDownEnterNumber} maxLength={13} />
          </Item>
          <Item
            name="accountingConfirm"
            layout="horizontal"
            labelCol={{ span: 10 }}
            labelAlign="left"
            label="Kế toán xác nhận bàn giao"
            valuePropName="checked"
          >
            <Switch />
          </Item>
          <TimeFrame />
          <HandoverCategory />
        </Col>
      </Row>
    </Form>
  );
};

export default GeneralInfoDeliveryConfig;
