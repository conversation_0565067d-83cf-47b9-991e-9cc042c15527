import { Col, Form, Input, Row } from 'antd';
import { FormInstance } from 'antd/lib';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import './styles.scss';
import { useEffect, useRef, useState } from 'react';
import { validateEmail } from '../../../utilities/shareFunc';
import { MAX_LENGTH } from '../../../constants/common';
import { useStoreDeliveryConfig } from '../storeDeliveryConfig';

const { Item } = Form;

const toolbarOptions = [
  [{ font: [] }],
  [{ header: ['1', '2', '3', '4', '5', false] }],
  [{ align: [] }],
  ['bold', 'italic', 'underline', 'strike'],
  [{ list: 'ordered' }, { list: 'bullet' }, { indent: '-1' }, { indent: '+1' }],
  [{ color: [] }, { background: [] }],
  [{ script: 'sub' }, { script: 'super' }],
  ['blockquote', 'code-block'],
  ['link', 'image', 'video', 'formula'],
  [{ direction: 'rtl' }],
  ['clean'],
];

interface INotificationDeliveryConfig {
  form: FormInstance;
}

const NotificationDeliveryConfig = (props: INotificationDeliveryConfig) => {
  const { form } = props;
  const [emailContent, setEmailContent] = useState('');
  const quillRef = useRef<ReactQuill>(null);
  const { initialValueNoti, setIsModified } = useStoreDeliveryConfig();

  const handleQuillChange = (value: string) => {
    const editor = quillRef.current?.getEditor();
    const plainText = editor?.getText() || '';

    if (plainText.trim().length <= MAX_LENGTH) {
      setEmailContent(value);
    } else {
      // Prevent inserting more text
      const delta = editor?.getContents();
      if (delta && editor) {
        editor.setContents(delta.slice(0, MAX_LENGTH));
      }
    }
  };

  useEffect(() => {
    const editor = quillRef.current?.getEditor();
    if (!editor) return;

    editor.on('text-change', () => {
      const plainText = editor.getText() || '';
      if (plainText.trim().length > MAX_LENGTH) {
        editor.deleteText(MAX_LENGTH, plainText.length);
      }
    });
  }, []);

  const validateForm = () => {
    setIsModified(true);
  };

  return (
    <Form
      form={form}
      layout="vertical"
      name="formNotification"
      initialValues={initialValueNoti}
      onValuesChange={validateForm}
    >
      <Row gutter={[16, 16]}>
        <Col xl={12} lg={24}>
          <Item
            name="emailTitle"
            label="Tiêu đề email"
            rules={[{ required: true, message: 'Vui lòng nhập tiêu đề email', whitespace: true }]}
          >
            <Input placeholder="Nhập tiêu đề email" maxLength={200} />
          </Item>

          <Item
            name="emailFrom"
            label="Email gửi đi"
            rules={[
              { required: true, message: 'Vui lòng nhập email gửi đi', whitespace: true },

              {
                validator: (_, value) => validateEmail(value),
              },
            ]}
          >
            <Input placeholder="Nhập email gửi đi" maxLength={50} />
          </Item>

          <Item
            name="emailCC"
            label="Email CC"
            rules={[
              {
                validator: (_, value) => validateEmail(value),
              },
            ]}
          >
            <Input placeholder="Email CC nhiều email cách nhau dấu;" maxLength={50} />
          </Item>

          <Item
            name="emailBCC"
            label="Email BCC"
            rules={[
              {
                validator: (_, value) => validateEmail(value),
              },
            ]}
          >
            <Input placeholder="Email BCC nhiều email cách nhau dấu;" maxLength={50} />
          </Item>

          <Item
            name="emailTemplate"
            label="Email thông báo"
            required
            rules={[
              {
                required: true,
                validator: (_, value) => {
                  if (!value || value === '<p><br></p>') {
                    return Promise.reject('Vui lòng nhập nội dung email');
                  }
                  return Promise.resolve();
                },
                whitespace: true,
              },
            ]}
          >
            <ReactQuill
              theme="snow"
              modules={{
                toolbar: toolbarOptions,
              }}
              placeholder="Nhập nội dung email"
              onChange={handleQuillChange}
              value={emailContent}
              ref={quillRef}
            />
          </Item>

          <Item
            name="smsBrandName"
            label="SMS Brandname"
            rules={[{ required: true, message: 'Vui lòng nhập SMS Brandname', whitespace: true }]}
          >
            <Input placeholder="Nhập SMS Brandname" maxLength={50} />
          </Item>

          <Item
            name="smsTemplate"
            label="SMS thông báo"
            rules={[{ required: true, message: 'Vui lòng nhập SMS thông báo', whitespace: true }]}
          >
            <Input.TextArea rows={3} maxLength={500} placeholder="Nhập SMS thông báo" />
          </Item>
        </Col>
      </Row>
    </Form>
  );
};

export default NotificationDeliveryConfig;
