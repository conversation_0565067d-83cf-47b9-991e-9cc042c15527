import { create } from 'zustand';
import { TDeliveryConfigGen, TDeliveryConfigNoti, THandover } from '../../types/deliveryConfig';

interface IStoreDeliveryConfig {
  openModalCreate: boolean;
  dataHandovers?: THandover[];
  initialValueGen?: TDeliveryConfigGen;
  initialValueNoti?: TDeliveryConfigNoti;
  isModified?: boolean;
  setDataHandovers: (data: THandover[]) => void;
  setOpenModalCreate: (openModalCreate: boolean) => void;
  setInitialValueGen: (initialValueGen: TDeliveryConfigGen) => void;
  setInitialValueNoti: (initialValueNoti: TDeliveryConfigNoti) => void;
  setIsModified: (isModified: boolean) => void;
}

export const useStoreDeliveryConfig = create<IStoreDeliveryConfig>(set => ({
  openModalCreate: false,
  dataHandovers: undefined,
  isModified: false,
  initialValue: undefined,
  setDataHandovers: action => set({ dataHandovers: action }),
  setOpenModalCreate: action => set({ openModalCreate: action }),
  setInitialValueGen: action => set({ initialValueGen: action }),
  setInitialValueNoti: action => set({ initialValueNoti: action }),
  setIsModified: action => set({ isModified: action }),
}));
