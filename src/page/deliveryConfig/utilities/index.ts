import dayjs from 'dayjs';
import { FORMAT_TIME } from '../../../constants/common';
import { DeliveryConfig, TTimeFrame } from '../../../types/deliveryConfig';
import { OutputTimeFrames } from '../../../types/common/common';
import { FormInstance, notification } from 'antd';

export function formatItemsToTimeFrames(item: DeliveryConfig) {
  return Object.entries(item)
    .filter(([key]) => key.includes('Frame') && key.endsWith('Frame'))
    .flatMap(([key, value]) => {
      if (!Array.isArray(value)) return [];

      const day = key.replace('Frame', '');
      return (value as TTimeFrame[]).map(({ startTime, endTime, amount }) => {
        return {
          day,
          amount: amount,
          startTime,
          endTime,
          timeRange: [
            startTime ? dayjs(startTime, FORMAT_TIME) : undefined,
            endTime ? dayjs(endTime, FORMAT_TIME) : undefined,
          ],
        };
      });
    });
}

export function formatTimeFrameToSubmit(timeFrame: TTimeFrame[]) {
  if (!timeFrame) return null;
  return timeFrame.reduce((result: OutputTimeFrames, item) => {
    const key = `${item.day}Frame`;
    result[key] = [
      ...(result[key] || []), // Nếu key đã tồn tại, giữ lại các giá trị cũ
      {
        amount: Number(item.amount),
        startTime: item.startTime,
        endTime: item.endTime,
      },
    ];
    return result;
  }, {} as OutputTimeFrames);
}

export const validateForms = async (formGen: FormInstance, formNoti: FormInstance) => {
  const genPromise = formGen.validateFields().catch(() => {
    return { error: true, form: 'general' };
  });

  const notiPromise = formNoti.validateFields().catch(() => {
    return { error: true, form: 'notification' };
  });

  const [genResult, notiResult] = await Promise.all([genPromise, notiPromise]);
  const valuesGen = formGen.getFieldsValue(true);
  const valuesNoti = formNoti.getFieldsValue(true);

  const hasGenError = genResult && genResult.error;
  const hasNotiError = notiResult && notiResult.error;

  if (hasGenError || Object.keys(valuesGen).length === 0) {
    notification.error({ message: 'Vui lòng kiểm tra lại biểu mẫu "Thông tin chung".' });
    return false;
  }

  if (hasNotiError || Object.keys(valuesNoti).length === 0) {
    notification.error({ message: 'Vui lòng kiểm tra lại biểu mẫu "Thông báo".' });
    return false;
  }

  return { valuesGen, valuesNoti };
};
