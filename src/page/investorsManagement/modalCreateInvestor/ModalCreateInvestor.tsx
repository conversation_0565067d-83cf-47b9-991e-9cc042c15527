import { Form, Modal } from 'antd';
import { useCreateField } from '../../../hooks';
import { createInvestor } from '../../../service/investor';
import { DetailInvestor } from '../../../types/investor/investor';
import { useCallback, useState } from 'react';
import ModalComponent from '../../../components/modal';
import FormUpdateCreateInvestor from '../formUpdateCreateInvestor';
import { validateAddressObject } from '../../../components/validation';

interface ModalCreateInvestorProps {
  visible: boolean;
  onClose: () => void;
}

const ModalCreateInvestor = ({ visible, onClose }: ModalCreateInvestorProps) => {
  const [formCreate] = Form.useForm();
  const [resetUpload, setResetUpload] = useState(false);

  const create = useCreateField<DetailInvestor>({
    keyOfListQuery: ['investor-list'],
    apiQuery: createInvestor,
    label: 'chủ đầu tư',
    isMessageError: false,
  });

  const resetFormAndUpload = useCallback(async () => {
    await formCreate.resetFields();
    setResetUpload(true);
  }, [formCreate]);

  const handleCancel = useCallback(async () => {
    if (formCreate.isFieldsTouched()) {
      Modal.confirm({
        title: 'Xác nhận hủy',
        content: 'Dữ liệu chưa được lưu, bạn có chắc chắn muốn thoát khỏi trang không?',
        cancelText: 'Quay lại',
        onOk: async () => {
          await resetFormAndUpload();
          onClose();
        },
        okButtonProps: { type: 'default' },
        cancelButtonProps: { type: 'primary' },
      });
    } else {
      await resetFormAndUpload();
      onClose();
    }
  }, [formCreate, onClose, resetFormAndUpload]);

  const handleCreate = useCallback(
    async (values: DetailInvestor) => {
      const resp = await create.mutateAsync({
        ...values,
        addressObject: values?.addressObject ? validateAddressObject(values.addressObject) : {},
      });
      if (resp?.data?.statusCode === '0' && resp?.data?.success) {
        await formCreate.setFieldValue('logo', null);
        await resetFormAndUpload();
        onClose();
      }
    },
    [create, onClose, resetFormAndUpload, formCreate],
  );

  return (
    <ModalComponent
      className="modal-investor"
      title="Tạo mới chủ đầu tư"
      open={visible}
      onCancel={handleCancel}
      footer={null}
      destroyOnClose
    >
      <FormUpdateCreateInvestor
        mode="create"
        onSubmit={handleCreate}
        formCreate={formCreate}
        resetUpload={resetUpload}
        onResetUpload={() => setResetUpload(false)}
      />
    </ModalComponent>
  );
};

export default ModalCreateInvestor;
