import { Button, Col, Form, Input, Modal, Row, TableColumnsType } from 'antd';
import BreadCrumbComponent from '../../../components/breadCrumb';
import InputSearch from '../../../components/input/InputSearch';
import './styles.scss';
import { useNavigate } from 'react-router-dom';
import TableComponent from '../../../components/table';
import { useDeleteField, useFetch } from '../../../hooks';
import { Investor } from '../../../types/investor/investor';
import { getListInvestor, softDeleteInvestor } from '../../../service/investor';
// import ImportOutlined from '../../../assets/icon/import';
import { useMemo, useState } from 'react';
import { ActionsColumns } from '../../../components/table/components/ActionsColumns';
import { INVESTORS_MANAGEMENT } from '../../../configs/path';
import ModalCreateInvestor from '../modalCreateInvestor/ModalCreateInvestor';
import { ListOfInvestorProvider } from './context/ListOfInvestorContext';

import { InfoCircleFilled } from '@ant-design/icons';

const ListOfInvestor = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const [isModalCreateInvestor, setModalCreateInvestor] = useState(false);

  const { data: investors, isLoading } = useFetch<Investor[]>({
    queryKeyArrWithFilter: ['investor-list'],
    api: getListInvestor,
  });
  const investorList = investors?.data?.data?.rows || [];

  const { mutateAsync: deleteInvestor } = useDeleteField({
    apiQuery: softDeleteInvestor,
    keyOfListQuery: ['investor-list'],
    messageSuccess: 'Xóa chủ đầu tư thành công',
    isMessageError: false,
  });

  const columnActions: TableColumnsType<Investor> = useMemo(() => {
    const columns: TableColumnsType<Investor> = [
      {
        title: 'Mã chủ đầu tư',
        dataIndex: 'investorCode',
        key: 'investorCode',
        width: 270,
        render: value => {
          return <p>{value}</p>;
        },
      },
      {
        title: 'Tên chủ đầu tư',
        key: 'name',
        dataIndex: 'name',
        width: 165,
        render: value => {
          return <p>{value}</p>;
        },
      },
      { width: 521 },
    ];

    return [
      ...columns,
      {
        title: 'Hành động',
        dataIndex: 'action',
        key: 'action',
        align: 'center',
        width: 140,
        render: (_, record: Investor) => {
          const handleDeleteInvestor = () => {
            Modal.confirm({
              title: 'Vui lòng nhập lý do muốn xóa chủ đầu tư',
              icon: <InfoCircleFilled style={{ color: '#1890ff' }} />,
              okText: 'Xác nhận',
              okType: 'default',
              cancelText: 'Hủy',
              cancelButtonProps: {
                type: 'primary',
              },
              content: (
                <Form form={form} layout="vertical">
                  <Form.Item name="reason" rules={[{ required: true, message: 'Vui lòng nhập lý do xóa chủ đầu tư' }]}>
                    <Input.TextArea
                      maxLength={255}
                      placeholder="Nhập lý do xóa chủ đầu tư"
                      style={{
                        marginTop: 10,
                        borderRadius: 0,
                      }}
                    />
                  </Form.Item>
                </Form>
              ),
              onOk: async () => {
                const values = await form.validateFields();
                await deleteInvestor({ id: record?.id, softDeleteReason: values.reason });
                await form.resetFields();
              },
              onCancel: async () => {
                await form.resetFields();
              },
            });
          };

          return (
            <ActionsColumns
              handleViewDetail={() => {
                navigate(`${INVESTORS_MANAGEMENT}/${record?.id}`);
              }}
              handleDelete={handleDeleteInvestor}
            />
          );
        },
      },
    ];
  }, [deleteInvestor, form, navigate]);

  return (
    <ListOfInvestorProvider>
      <BreadCrumbComponent />
      <div className="list-investor">
        <div className="header-content">
          <InputSearch className="input-search" placeholder="Tìm kiếm" keySearch="search" />
          <Row gutter={[16, 8]}>
            {/* <Col>
              <Button type="default" icon={<ImportOutlined />}>
                Nhập biểu mẫu Excel
              </Button>
            </Col> */}

            <Col>
              <Button type="primary" onClick={() => setModalCreateInvestor(true)}>
                Tạo mới
              </Button>
            </Col>
          </Row>
        </div>
      </div>
      <TableComponent
        rowKey={'id'}
        className="table-investor"
        columns={columnActions}
        dataSource={investorList}
        queryKeyArr={['investor-list']}
        loading={isLoading}
      />

      <ModalCreateInvestor
        visible={isModalCreateInvestor}
        onClose={() => {
          setModalCreateInvestor(false);
        }}
      />
    </ListOfInvestorProvider>
  );
};

export default ListOfInvestor;
