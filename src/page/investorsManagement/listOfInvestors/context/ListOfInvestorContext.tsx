import React, { createContext, useReducer, ReactNode, Dispatch } from 'react';
import { ListOfInvestorAction, reducer } from './reducer';
import { initialListOfInvestorState, ListOfInvestorState } from './state';

interface ListOfInvestorContextProps {
  state: ListOfInvestorState;
  dispatch: Dispatch<ListOfInvestorAction>;
}

export const ListOfInvestorContext = createContext<ListOfInvestorContextProps | undefined>(undefined);

export const ListOfInvestorProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(reducer, initialListOfInvestorState);

  return <ListOfInvestorContext.Provider value={{ state, dispatch }}>{children}</ListOfInvestorContext.Provider>;
};
