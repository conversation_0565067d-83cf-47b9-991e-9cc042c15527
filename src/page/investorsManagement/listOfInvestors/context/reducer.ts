import { ListOfInvestorState } from './state';

//định nghĩa các action có thể thực hiện được đang để test, sửa lại theo yêu cầu
// payload là dữ liệu truyền vào để update state, đang để string để test
export type ListOfInvestorAction = { type: 'increment'; payload: string } | { type: 'decrement' };

export const reducer = (state: ListOfInvestorState, action: ListOfInvestorAction): ListOfInvestorState => {
  switch (action.type) {
    case 'increment':
      return state;
    default:
      return state;
  }
};
