import { Button, Col, Form, FormInstance, Input, Modal, Row } from 'antd';
import './styles.scss';
import { useCallback, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { DetailInvestor } from '../../../types/investor/investor';
import SelectAddress from '../../../components/selectAddress';
import { INVESTORS_MANAGEMENT } from '../../../configs/path';
import { checkFormatPhoneNumber, checkLengthEqualTen, returnNumericValue } from '../../../components/validation';
import FormUploadImage from '../../../components/upload/FormUploadImage';
const { TextArea } = Input;

interface CommonFormProps {
  mode?: string;
  initialData?: DetailInvestor;
  onSubmit: (data: DetailInvestor) => void;
  formCreate?: FormInstance;
  resetUpload?: boolean;
  onResetUpload?: () => void;
}

const FormUpdateCreateInvestor: React.FC<CommonFormProps> = ({
  mode,
  initialData,
  onSubmit,
  formCreate,
  resetUpload,
  onResetUpload,
}) => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [isFormChanged, setIsFormChanged] = useState<boolean>(false);
  const currentInvestor = initialData;
  const addressObj = currentInvestor?.addressObject as undefined;

  const handleValuesChange = () => {
    if (!isFormChanged) {
      setIsFormChanged(true); // Cập nhật trạng thái form đã bị chỉnh sửa
    }
  };

  useEffect(() => {
    if (mode === 'create') {
      onResetUpload?.(); // Reset trạng thái sau khi upload được reset
    }
  }, [resetUpload, onResetUpload, mode]);

  useEffect(() => {
    if (mode === 'update' && currentInvestor) {
      form.setFieldsValue(currentInvestor);
    }
  }, [currentInvestor, form, mode]);

  const handleCancel = useCallback(() => {
    if (isFormChanged) {
      Modal.confirm({
        title: 'Xác nhận hủy',
        content: 'Dữ liệu chưa được lưu, bạn có chắc chắn muốn thoát khỏi trang không?',
        cancelText: 'Quay lại',
        okText: 'Đồng ý',
        onOk: () => {
          navigate(INVESTORS_MANAGEMENT); // Điều hướng về trang quản lý
        },
        okButtonProps: {
          type: 'default',
        },
        cancelButtonProps: {
          type: 'primary',
        },
      });
    } else {
      navigate(INVESTORS_MANAGEMENT); // Không có thay đổi, điều hướng trực tiếp
    }
  }, [isFormChanged, navigate]);

  const handleFinish = (values: DetailInvestor) => {
    onSubmit(values);
    setIsFormChanged(false); // Đặt lại trạng thái form về chưa thay đổi
  };

  return (
    <div className="form-update-create-investor">
      <Form
        onValuesChange={handleValuesChange} // Xử lý khi form thay đổi
        form={formCreate || form}
        onFinish={handleFinish} // Gọi khi nhấn nút submit
        initialValues={initialData} // Dữ liệu ban đầu cho chế độ update
        labelAlign="left"
        labelCol={{
          xs: { span: 24 },
          sm: { span: 24 },
          md: { span: 8 },
          lg: { span: 8 },
          xl: { span: 5 },
        }}
        wrapperCol={{
          xs: { span: 24 },
          sm: { span: 24 },
          md: { span: 16 },
          lg: { span: 16 },
          xl: { span: 19 },
        }}
      >
        <Row gutter={48}>
          <Col span={12}>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item
                  label="Mã chủ đầu tư"
                  name="investorCode"
                  rules={[
                    { required: true, message: 'Vui lòng nhập mã chủ đầu tư' },
                    {
                      validator: (_, value) => {
                        if (!value || /^[a-zA-Z0-9]+$/.test(value)) {
                          return Promise.resolve();
                        }
                        return Promise.reject(
                          new Error('Mã chủ đầu tư chỉ được chứa các ký tự chữ và số, không được chứa ký tự đặc biệt'),
                        );
                      },
                    },
                  ]}
                >
                  <Input placeholder="Nhập mã chủ đầu tư" maxLength={50} disabled={mode === 'update' && true} />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item
                  label="Tên chủ đầu tư"
                  name="name"
                  rules={[{ required: true, message: 'Vui lòng nhập tên chủ đầu tư' }]}
                >
                  <Input placeholder="Nhập tên chủ đầu tư" maxLength={255} />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item
                  label="Số điện thoại"
                  name="phone"
                  rules={[
                    { required: true, message: 'Vui lòng nhập số điện thoại' },
                    {
                      validator: checkLengthEqualTen,
                      message: 'Số điện thoại bao gồm 10 ký tự số',
                    },
                    {
                      validator: checkFormatPhoneNumber,
                      message: 'Số điện thoại sai định dạng',
                    },
                  ]}
                >
                  <Input placeholder="Nhập số điện thoại" onKeyDown={returnNumericValue} maxLength={10} />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item className="input-address" label="Địa chỉ" name="addressObject">
                  <SelectAddress parentName="addressObject" address={addressObj} />
                </Form.Item>
              </Col>
              <Col span={24} className="address">
                <Form.Item name="address" label=" ">
                  <Input placeholder="Nhập thông tin địa chỉ chi tiết" maxLength={255} />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item
                  label="Website"
                  name="website"
                  rules={[{ required: true, message: 'Vui lòng nhập địa chỉ website' }]}
                >
                  <Input placeholder="Nhập địa chỉ website" maxLength={255} />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item label="Mô tả" name="description">
                  <TextArea placeholder="Nhập nội dung" rows={4} maxLength={255} />
                </Form.Item>
              </Col>
            </Row>
          </Col>
          <Col span={12}>
            <FormUploadImage
              fileSize={0.5}
              fieldName={'logo'}
              label="Ảnh đại diện"
              path="investor"
              reset={resetUpload}
              defaultImage={currentInvestor?.logo}
              isValidate
              labelCol={{ span: 24 }}
            />
          </Col>
          <Col span={24}>
            <div className={mode === 'update' ? 'update-footer' : 'create-footer'}>
              {mode === 'update' && (
                <div>
                  <Button
                    onClick={handleCancel}
                    style={{ marginRight: 12 }} // Khoảng cách giữa các nút
                    type="primary"
                  >
                    Hủy
                  </Button>
                  <Button type="default" htmlType="submit">
                    Lưu thay đổi
                  </Button>
                </div>
              )}
              {mode === 'create' && (
                <div className="button-create">
                  <Button
                    type="primary"
                    onClick={() => {
                      formCreate?.submit();
                    }}
                  >
                    Lưu
                  </Button>
                </div>
              )}
            </div>
          </Col>
        </Row>
      </Form>
    </div>
  );
};

export default FormUpdateCreateInvestor;
