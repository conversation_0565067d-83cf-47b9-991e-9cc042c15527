import { memo, useCallback } from 'react';
import { Col, Row, Spin, Typography } from 'antd';
import { DetailInvestorProvider } from './context/DetailInvestorContext';
import { useParams } from 'react-router-dom';
import './styles.scss';
import { useFetch, useUpdateField } from '../../../hooks';
import { getDetailInvestor, updateInvestor } from '../../../service/investor';
import BreadCrumbComponent from '../../../components/breadCrumb';
import FormUpdateCreateInvestor from '../formUpdateCreateInvestor';
import { validateAddressObject } from '../../../components/validation';
import { DetailInvestor } from '../../../types/investor/investor';
const { Title } = Typography;

function DetailsUnits() {
  const id = useParams();
  const idInvestor = id?.id;

  const { data: investor, isLoading } = useFetch<DetailInvestor>({
    queryKeyArr: ['detail-investor'],
    api: () => idInvestor && getDetailInvestor(idInvestor || ''),
    withFilter: false,
    enabled: !!id,
    cacheTime: 10,
  });

  const update = useUpdateField({
    keyOfListQuery: ['investor-list'],
    keyOfDetailQuery: ['detail-investor'],
    label: 'Chủ đầu tư',
    apiQuery: updateInvestor,
    messageSuccess: 'Chỉnh sửa thông tin thành công!',
  });

  const handleUpdate = useCallback(
    async (data: DetailInvestor) => {
      await update.mutateAsync({
        id: idInvestor, // Ensure `idInvestor` is correctly passed to the component
        ...data,
        addressObject: data?.addressObject ? validateAddressObject(data.addressObject) : {},
      });
    },
    [update, idInvestor],
  );

  return (
    <div>
      <DetailInvestorProvider>
        <div className="layout-detail">
          <BreadCrumbComponent titleBread={investor?.data?.data?.name} />
          <Spin spinning={isLoading || update.status === 'pending'}>
            <div className="investor-detail">
              <Row gutter={[12, 12]}>
                <Col span={24}>
                  <Title style={{ marginBottom: '20px' }} level={5}>
                    Thông tin chủ đầu tư
                  </Title>
                </Col>
              </Row>
              <FormUpdateCreateInvestor mode="update" initialData={investor?.data?.data} onSubmit={handleUpdate} />
            </div>
          </Spin>
        </div>
      </DetailInvestorProvider>
    </div>
  );
}

export default memo(DetailsUnits);
