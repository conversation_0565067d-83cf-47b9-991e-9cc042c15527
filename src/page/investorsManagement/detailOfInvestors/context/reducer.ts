import { DetailInvestorAction, DetailInvestorState } from './state';

export const reducer = (state: DetailInvestorState, action: DetailInvestorAction): DetailInvestorState => {
  switch (action.type) {
    case 'SAVE_CONFIGURATION_EMAIL':
      return {
        ...state,
        configurationEmail: [...state.configurationEmail, action.configurationEmail],
      };
    case 'SET_FORM_MAP':
      return {
        ...state,
        formMap: action.formMap,
      };
    default:
      return state;
  }
};
