import React, { createContext, useReducer, ReactNode, Dispatch } from 'react';
import { reducer } from './reducer';
import { initialDetailInvestorState, DetailInvestorState, DetailInvestorAction } from './state';

interface DetailInvestorContextProps {
  state: DetailInvestorState;
  dispatch: Dispatch<DetailInvestorAction>;
}

export const DetailInvestorContext = createContext<DetailInvestorContextProps | undefined>(undefined);

export const DetailInvestorProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(reducer, initialDetailInvestorState);

  return <DetailInvestorContext.Provider value={{ state, dispatch }}>{children}</DetailInvestorContext.Provider>;
};

export const UseDetailInvestorContext = (): DetailInvestorContextProps => {
  const context = React.useContext(DetailInvestorContext);
  if (context === undefined) {
    throw new Error('useDetailsUnitsContext must be used within a DetailInvestorProvider');
  }
  return context;
};
