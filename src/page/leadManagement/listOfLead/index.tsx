import { Button, Flex } from 'antd';
import { useEffect, useState } from 'react';
import BreadCrumbComponent from '../../../components/breadCrumb';
import TableComponent from '../../../components/table';
import { PERMISSION_LEAD } from '../../../constants/permissions/lead';
import { useAuth } from '../../../context/AuthContext';
import { useFetch } from '../../../hooks';
import { getListOfLeadAll } from '../../../service/lead';
import { ILead } from '../../../types/lead';
import { columnsListOfLead } from '../columns';
import CreateOfLead from '../createOfLead';
import { useLeadStore } from '../store';
import FilterSearch from './components/FilterSearch';

const ListOfLead = () => {
  const {
    data: dataLead,
    isLoading,
    isPlaceholderData,
    isFetching,
  } = useFetch<ILead[]>({
    queryKeyArrWithFilter: ['list-lead-repo'],
    api: getListOfLeadAll,
  });

  const [openCreate, setOpenCreate] = useState(false);
  const { setType, setDefaultDataConfigs, setInitialValues } = useLeadStore(state => state);
  const { hasAuthority } = useAuth();
  const isPermissionCreate = hasAuthority(PERMISSION_LEAD.configCreate);

  useEffect(() => {
    setDefaultDataConfigs(undefined);
    setInitialValues({} as ILead);
  }, [setDefaultDataConfigs, setInitialValues]);

  const handleOpenModalCreate = () => {
    setOpenCreate(true);
    setType('create');
  };

  const handleCancelModalCreate = () => {
    setOpenCreate(false);
  };

  return (
    <div className="wrapper-list-Lead">
      <BreadCrumbComponent />
      <div className="header-content">
        <FilterSearch />

        <Flex gap={10}>
          {isPermissionCreate && (
            <Button type="primary" onClick={handleOpenModalCreate}>
              Thêm mới
            </Button>
          )}
        </Flex>
      </div>
      <div className="table-list-Lead">
        <TableComponent
          queryKeyArr={['list-lead-repo']}
          columns={columnsListOfLead}
          loading={isLoading || isPlaceholderData || isFetching}
          dataSource={dataLead?.data?.data?.rows ?? []}
          rowKey="id"
        />
      </div>
      <CreateOfLead open={openCreate} onCancel={handleCancelModalCreate} />
    </div>
  );
};

export default ListOfLead;
