import { Col, DatePicker, Form, Row } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { useEffect, useMemo, useState } from 'react';
import { useLocation } from 'react-router-dom';
import DropdownFilterSearch from '../../../../../components/dropdown/dropdownFilterSearch';
import MultiSelectLazy from '../../../../../components/select/mutilSelectLazy';
import { FORMAT_DATE, FORMAT_DATE_API } from '../../../../../constants/common';
import useFilter from '../../../../../hooks/filter';
import { getListEmployeeInternal } from '../../../../../service/employee';
import './styles.scss';
import { useEntitiesById } from '../../../../../hooks';
import { TEmployeeAll } from '../../../../../types/customers';
import { getEmployeeDropdownById } from '../../../../../service/customers';

type TFilter = {
  createdBy?: string;
  createFrom?: string | Dayjs | null;
  createTo?: string | Dayjs | null;
};

function FilterSearch() {
  const { search } = useLocation();
  const [form] = Form.useForm();
  const params = useMemo(() => new URLSearchParams(search), [search]);
  const [filter, setFilter] = useFilter();
  const [initialValues, setInitialValues] = useState<TFilter>();
  const [isOpenFilter, setIsOpenFilter] = useState(false);

  const { entities: defaultEmployee } = useEntitiesById({
    apiQuery: getEmployeeDropdownById,
    queryKey: ['employee-dropdown'],
    params: { id: filter?.createdBy },
    labelField: 'name',
    valueField: 'id',
  });

  useEffect(() => {
    if (params) {
      setInitialValues({
        createdBy: params.get('createdBy') || undefined,
        createFrom: params.get('createFrom') ? dayjs(params.get('createFrom')) : undefined,
        createTo: params.get('createTo') ? dayjs(params.get('createTo')) : undefined,
      });
    }
  }, [params]);

  const handleSubmitFilter = (values: TFilter) => {
    const newFilter: Record<string, unknown> = {
      createdBy: values?.createdBy || null,
      createFrom: values?.createFrom ? dayjs(values?.createFrom).format(FORMAT_DATE_API) : null,
      createTo: values?.createTo ? dayjs(values?.createTo).format(FORMAT_DATE_API) : null,
    };
    setFilter({ ...filter, page: '1', ...newFilter });
    setIsOpenFilter(false);
  };
  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };

  const handleSelectEmployee = (values: TEmployeeAll[]) => {
    form.setFieldsValue({ createdBy: values.map(item => item.id).join(',') });
  };

  const handleClearFilters = () => {
    setInitialValues(undefined);
    setTimeout(() => {
      setIsOpenFilter(false);
    }, 100);
  };

  return (
    <>
      <DropdownFilterSearch
        rootClassName="wrapper-filter-lead"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        showParams={true}
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        initialValues={initialValues}
        form={form}
        onClearFilters={handleClearFilters}
        extraFormItems={
          <>
            <Form.Item label="Người tạo" name="createdBy">
              <MultiSelectLazy
                enabled={isOpenFilter}
                queryKey={['getAllDataEmployees']}
                apiQuery={getListEmployeeInternal}
                keysLabel={['name', 'email']}
                keysTag={'email'}
                placeholder="Chọn nhân viên"
                handleListSelect={handleSelectEmployee}
                defaultValues={defaultEmployee}
              />
            </Form.Item>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="Ngày tạo" name="createFrom">
                  <DatePicker
                    format={FORMAT_DATE}
                    disabledDate={current => {
                      const createTo = form.getFieldValue('createTo');
                      return (
                        (current && current > dayjs().startOf('day')) ||
                        (createTo && current > dayjs(createTo).startOf('day')) // Disable ngày sau "Đến ngày"
                      );
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Đến ngày" name="createTo">
                  <DatePicker
                    format={FORMAT_DATE}
                    disabledDate={current => {
                      const createFrom = form.getFieldValue('createFrom');
                      return (
                        (current && current > dayjs().startOf('day')) ||
                        (createFrom && current < dayjs(createFrom).startOf('day')) // Disable ngày trước "Ngày tạo"
                      );
                    }}
                  />
                </Form.Item>
              </Col>
            </Row>
          </>
        }
      />
    </>
  );
}
export default FilterSearch;
