import dayjs, { Dayjs } from 'dayjs';
import { IOrgChart, IWorkingTime, TNotification, TStaffIds } from '../../../../types/lead';

// chuyển đổi dữ liệu notification từ string[] sang object
export const convertNotification = (data: string[]) => {
  return data?.reduce(
    (acc, item) => {
      if (typeof item === 'string') {
        acc[item.toLowerCase()] = {
          title: '',
          content: '',
          active: true,
        };
      }
      return acc;
    },
    {} as { [key: string]: TNotification },
  );
};

//Định dạng dữ liệu workingTime trước khi gửi lên server
export const formatWorkingTime = (item: Dayjs | IWorkingTime) => ({
  startTime: item && Array.isArray(item) ? item[0]?.toISOString() : (item as IWorkingTime)?.startTime,
  endTime: item && Array.isArray(item) && item.length > 1 ? item[1]?.toISOString() : (item as IWorkingTime)?.endTime,
});

// Định dạng dữ liệu orgCharts trước khi gửi lên server
export const formatOrgCharts = (orgCharts: IOrgChart[], deliverType: number) =>
  orgCharts?.map(item => ({
    ...item,
    staffIds:
      deliverType === 2
        ? item.staffIds
            .map((item: TStaffIds | string) => (typeof item === 'string' ? item : item.selected ? item.id : null))
            .filter(id => id !== null)
        : [],
  })) || [];

// Định dạng dữ liệu exploitTime trước khi gửi lên server
export const formatExploitTime = (exploitTime?: [Dayjs, Dayjs] | { from: string; to: string }) => {
  if (Array.isArray(exploitTime)) {
    return {
      from: exploitTime[0]?.toISOString() || null,
      to: exploitTime[1]?.toISOString() || null,
    };
  }
  return exploitTime || { from: null, to: null };
};

// Định dạng dữ liệu workingTime trước khi hiển thị lên component
export const formatWorkingTimeForComponent = (times: IWorkingTime[] = []): Dayjs[] =>
  times?.map(item => {
    if (item && JSON.stringify(item) !== '{}') {
      return [item.startTime ? dayjs(item.startTime) : null, item.endTime ? dayjs(item.endTime) : null];
    }
    return null;
  }) as unknown as Dayjs[];

export const formatNotificationForComponent = (notification: { [key: string]: TNotification } = {}) =>
  Object.keys(notification).filter(key => notification[key]?.active);

export const formatExploitTimeForComponent = (exploitTime: { from: string; to: string }) => {
  if (!exploitTime) return undefined;
  return [dayjs(exploitTime.from), dayjs(exploitTime.to)];
};
