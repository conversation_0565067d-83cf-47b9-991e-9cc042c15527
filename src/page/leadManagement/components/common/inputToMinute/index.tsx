import { Form, Input, InputProps, Select } from 'antd';
import React, { useEffect, useState } from 'react';

const { Option } = Select;

const validateFloatInput = (e: React.KeyboardEvent<HTMLInputElement>) => {
  const key = e.key;
  const input = e.currentTarget.value;

  // Chỉ cho phép nhập số, dấu chấm và phím điều khiển
  if (
    !/^\d$/.test(key) && // Không phải số
    key !== '.' && // Không phải dấu chấm
    key !== 'Backspace' && // Không phải xóa
    key !== 'ArrowLeft' && // Điều hướng trái
    key !== 'ArrowRight' && // Điều hướng phải
    key !== 'Delete' && // Xóa phía trước
    key !== 'Tab' // Phím Tab
  ) {
    e.preventDefault();
  }
  // Chặn nhập dấu chấm không hợp lệ
  if (key === '.' && (input.includes('.') || input === '')) {
    e.preventDefault();
  }
};

// chuyển đổi giá trị giữa các đơn vị thời gian
const conversionRates: { [key: string]: number } = {
  'Ngày-Giờ': 24,
  'Ngày-Phút': 24 * 60,
  'Giờ-Ngày': 1 / 24,
  'Giờ-Phút': 60,
  'Phút-Ngày': 1 / (24 * 60),
  'Phút-Giờ': 1 / 60,
  'Phút-Phút': 1,
};

const unitToMinute: Record<string, number> = {
  Ngày: 24 * 60,
  Giờ: 60,
  Phút: 1,
};

interface InputMinuteProps extends Omit<InputProps, 'onChange'> {
  onChange?: (value: string) => void;
  valueAssignDuration?: string;
  parentName?: string;
}

const InputMinute = (props: InputMinuteProps) => {
  const {
    onKeyDown = validateFloatInput,
    maxLength = 50,
    placeholder = 'Nhập thời gian',
    onChange,
    valueAssignDuration,
    parentName,
    ...rest
  } = props;
  const form = Form.useFormInstance();
  const [unit, setUnit] = useState<'Ngày' | 'Giờ' | 'Phút'>('Phút'); // Đơn vị hiện tại
  const [displayValue, setDisplayValue] = useState<string>(); // Giá trị hiển thị

  useEffect(() => {
    setDisplayValue(valueAssignDuration || ''); // Cập nhật khi prop `value` thay đổi
  }, [form, parentName, valueAssignDuration]);

  const updateConvertedValue = (input: string, currentUnit: 'Ngày' | 'Giờ' | 'Phút') => {
    if (parentName) {
      const numeric = parseFloat(input);
      const minuteValue = isNaN(numeric) ? undefined : numeric * unitToMinute[currentUnit];
      form.setFieldValue([parentName, 'assignDurationInMinute'], minuteValue);
    }
  };
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const input = e.target.value;
    if (/^\d*(\.\d*)?$/.test(input)) {
      setDisplayValue(input); // Chuyển đổi và lưu trữ giá trị
      updateConvertedValue(input, unit);
      onChange && onChange(input || '');
    }
  };

  const handleUnitChange = (newUnit: 'Ngày' | 'Giờ' | 'Phút') => {
    const conversionKey = `${unit}-${newUnit}`;
    const conversionRate = conversionRates[conversionKey] || 1;
    const newValue = displayValue ? parseFloat(displayValue.toString()) * conversionRate : undefined;

    setDisplayValue(newValue != null ? (newUnit === 'Phút' ? newValue.toFixed(0) : newValue.toString()) : undefined);
    setUnit(newUnit);
  };

  return (
    <Input
      {...rest}
      value={displayValue}
      onChange={handleInputChange}
      onKeyDown={onKeyDown}
      maxLength={maxLength}
      placeholder={placeholder}
      addonAfter={
        <span className="after-addon">
          <Select value={unit} onChange={handleUnitChange}>
            <Option value="Ngày">Ngày</Option>
            <Option value="Giờ">Giờ</Option>
            <Option value="Phút">Phút</Option>
          </Select>
        </span>
      }
    />
  );
};

export default InputMinute;
