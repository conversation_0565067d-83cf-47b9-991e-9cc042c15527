import { EditOutlined } from '@ant-design/icons';
import {
  Button,
  Checkbox,
  Col,
  Dropdown,
  Flex,
  Form,
  Input,
  List,
  Select,
  Table,
  TableColumnsType,
  Tag,
  Tooltip,
} from 'antd';
import { CheckboxChangeEvent } from 'antd/es/checkbox';
import { CheckboxProps } from 'antd/lib';
import { useEffect, useState } from 'react';
import SingleSelectLazy from '../../../../components/select/singleSelectLazy';
import { OPTIONS_TYPE_LEAD } from '../../../../constants/common';
import { useFetch } from '../../../../hooks';
import { getByIdOrgChartDropdown, getOrgChartDropdown } from '../../../../service/lead';
import { IOrgChart, TStaffIds, TUnits } from '../../../../types/lead';
import { useLeadStore } from '../../store';
import './styles.scss';

const { Item } = Form;

type Props = {
  children?: React.ReactNode;
  parentName: string;
  defaultValueOrgCharts?: IOrgChart[];
  resetKey?: number;
};

const UnitParticipating = (props: Props) => {
  const { children, parentName, defaultValueOrgCharts } = props;
  const form = Form.useFormInstance();
  const { type, setIsModified, setIsModifiedConfig } = useLeadStore(state => state);
  const deliverType = Form.useWatch([parentName, 'deliverType'], form);
  const orgCharts = form.getFieldValue([parentName, 'orgCharts']);
  const [openModalMember, setOpenModalMember] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [listStaffIds, setListStaffIds] = useState<TStaffIds[]>([]);

  const defaultStaffIds = defaultValueOrgCharts?.[0]?.staffIds as unknown as string[];
  const idOrgChart = defaultValueOrgCharts?.[0]?.id;

  const { data: dataOrgChartDropdown } = useFetch<TUnits>({
    api: () => getByIdOrgChartDropdown({ id: idOrgChart }),
    queryKeyArr: ['orgCharts', idOrgChart],
    enabled: !!idOrgChart,
  });
  const filteredStaff = listStaffIds?.filter(emp => emp?.name?.toLowerCase()?.includes(searchText?.toLowerCase()));

  useEffect(() => {
    const units = dataOrgChartDropdown?.data?.data;
    if (!Array.isArray(units) || !units.length) {
      setListStaffIds([]);
      return;
    }
    const [firstUnit] = units;

    const formatStaffIds =
      firstUnit?.staffIds?.map((staff: TStaffIds) => ({
        ...staff,
        name: `${staff.email} - ${staff.name}`,
        selected: defaultStaffIds?.includes(staff.id),
      })) ?? [];

    setListStaffIds(formatStaffIds);
  }, [dataOrgChartDropdown?.data?.data, defaultStaffIds, deliverType, type]);

  const handleCheckboxChange = (id: string) => (e: CheckboxChangeEvent) => {
    setListStaffIds(listStaffIds?.map(emp => (emp.id === id ? { ...emp, selected: e.target.checked } : emp)));
  };

  const onCheckAllChange: CheckboxProps['onChange'] = e => {
    const isChecked = e.target.checked;
    const updatedEmployees = listStaffIds?.map(emp => ({
      ...emp,
      selected: isChecked,
    }));
    setListStaffIds(updatedEmployees);
  };

  const handleSubmitOrgCharts = () => {
    form.setFieldValue([parentName, 'orgCharts', 0, 'staffIds'], listStaffIds);
    setOpenModalMember(false);
    setIsModified(true);
    parentName === 'configs' && setIsModifiedConfig(true);
  };

  const columns: TableColumnsType = [
    {
      key: 'name',
      title: 'Đơn vị',
      dataIndex: 'name',
      width: '20%',
    },
    {
      key: 'member',
      title: 'Nhân viên được phân bổ',
      dataIndex: 'staffIds',
      render: () => {
        const displayedStaff = listStaffIds?.filter(el => el.selected).slice(0, 3);
        const remainingCount = listStaffIds?.filter(el => el.selected).length - 3;
        return (
          <div className="staffIds">
            <Dropdown
              menu={{ items: listStaffIds?.map(staff => ({ key: staff.id, label: staff.name })) }}
              dropdownRender={() => (
                <div className="dropdown-member" style={{ width: '300px' }}>
                  <div className="header">
                    <Checkbox
                      indeterminate={
                        listStaffIds?.some(emp => emp.selected) && !listStaffIds?.every(emp => emp.selected)
                      }
                      onChange={onCheckAllChange}
                      checked={listStaffIds?.every(emp => emp.selected)}
                    >
                      <span>{listStaffIds?.filter(item => item?.selected)?.length} items</span>
                    </Checkbox>
                    <Input
                      placeholder="Tìm kiếm nhân viên"
                      value={searchText}
                      onChange={e => setSearchText(e.target.value)}
                      className="mb-4"
                    />
                  </div>
                  <div className="body">
                    <List
                      dataSource={filteredStaff}
                      renderItem={employee => (
                        <List.Item>
                          <Checkbox checked={employee.selected} onChange={handleCheckboxChange(employee.id)}>
                            {employee.name}
                          </Checkbox>
                        </List.Item>
                      )}
                    />
                  </div>
                  <div className="footer">
                    <Button type="default" onClick={handleSubmitOrgCharts}>
                      Thêm nhân viên
                    </Button>
                  </div>
                </div>
              )}
              trigger={['click']}
              open={openModalMember}
              onOpenChange={() => setOpenModalMember(true)}
            >
              <>
                <Button
                  className="modify-member"
                  type="dashed"
                  icon={<EditOutlined />}
                  onClick={() => setOpenModalMember(true)}
                >
                  Tuỳ chỉnh thành viên phân bổ
                </Button>
                <Item
                  name="staffValidation"
                  rules={[
                    () => ({
                      validator: () => {
                        const hasUnselectedStaff = orgCharts?.some((unit: IOrgChart) =>
                          unit.staffIds?.some(
                            staff =>
                              typeof staff === 'string' ||
                              (staff && typeof staff === 'object' && Boolean(staff.selected)),
                          ),
                        );
                        if (!hasUnselectedStaff) {
                          return Promise.reject(new Error('Vui lòng chọn nhân viên được phân bổ'));
                        }
                        return Promise.resolve();
                      },
                    }),
                  ]}
                  hidden
                  dependencies={['orgCharts']}
                >
                  <Input />
                </Item>
                <Form.Item shouldUpdate noStyle>
                  {() => {
                    const error = form.getFieldError('staffValidation');
                    return error.length > 0 ? <div style={{ color: 'red', marginTop: 4 }}>{error[0]}</div> : null;
                  }}
                </Form.Item>
              </>
            </Dropdown>
            <Flex gap={8} wrap="wrap">
              {displayedStaff?.map(member => (
                <div key={member?.id} className="flex items-center gap-2">
                  <Tag
                    closable
                    onClose={e => {
                      e.preventDefault();
                      const updatedStaffs = listStaffIds.map(emp =>
                        emp.id === member.id ? { ...emp, selected: false } : emp,
                      );
                      form.setFieldValue([parentName, 'orgCharts', 0, 'staffIds'], updatedStaffs);
                      setListStaffIds(updatedStaffs);
                      setIsModified(true);
                      parentName === 'configs' && setIsModifiedConfig(true);
                    }}
                  >
                    {member.name}
                  </Tag>
                </div>
              ))}
              {remainingCount > 0 && (
                <Tooltip
                  title={listStaffIds
                    .slice(5)
                    .map(member => member.name)
                    .join('\n')}
                >
                  <Tag>+{remainingCount} more...</Tag>
                </Tooltip>
              )}
            </Flex>
          </div>
        );
      },
    },
  ];

  const handleSelectUnit = (values: TUnits) => {
    const newValues = values
      ? {
          id: values?.id,
          name: values?.name,
          staffIds: values?.staffIds?.map((item: TStaffIds) => ({
            ...item,
            id: item?.id,
            name: `${item?.email} - ${item?.name}`,
            selected: values?.id === defaultValueOrgCharts?.[0]?.id ? defaultStaffIds?.includes(item?.id) : true,
          })),
        }
      : undefined;
    form.setFieldsValue({
      [parentName]: {
        orgCharts: newValues ? [newValues] : undefined,
      },
    });
    setIsModified(true);
    parentName === 'configs' && setIsModifiedConfig(true);
    setListStaffIds(newValues ? newValues?.staffIds : []);
    setOpenModalMember(false);

    form.validateFields([[parentName, 'orgCharts']]);
  };

  return (
    <div className="wrapper-unit-participating">
      <Col md={12} xs={24}>
        <Item
          label="Hình thức phân bổ"
          name={[parentName, 'deliverType']}
          required
          rules={[{ required: true, message: 'Vui lòng chọn hình thức phân bổ' }]}
        >
          <Select options={OPTIONS_TYPE_LEAD} placeholder="Vui lòng chọn hình thức phân bổ" />
        </Item>
        {children}
        <Item
          label="Đơn vị tham gia"
          name={[parentName, 'orgCharts']}
          labelCol={{ span: 24 }}
          wrapperCol={{ span: 24 }}
          rules={[{ required: true, message: 'Vui lòng chọn đơn vị tham gia' }]}
        >
          <SingleSelectLazy
            enabled
            apiQuery={getOrgChartDropdown}
            queryKey={['orgCharts']}
            keysLabel={['code', 'name']}
            handleSelect={handleSelectUnit}
            defaultValues={{ label: orgCharts?.[0]?.name, value: orgCharts?.[0]?.id }}
            placeholder="Chọn đơn vị tham gia"
          />
        </Item>
      </Col>
      {deliverType === 2 && orgCharts && (
        <Table pagination={false} columns={columns} dataSource={orgCharts} rowKey="id" />
      )}
    </div>
  );
};

export default UnitParticipating;
