import { ClockCircleOutlined } from '@ant-design/icons';
import { Col, DatePicker, Form, Input, Row, Select, Switch } from 'antd';
import { useEffect } from 'react';
import { OBJECT_ALL_WEEK, OPTIONS_SEND_NOTIFICATIONS, OPTIONS_WORKING_TIME } from '../../../../constants/common';
import { handleKeyDownEnterNumber } from '../../../../utilities/regex';
import InputMinute from '../common/inputToMinute';

const { Item, List } = Form;
type Props = {
  parentName: string;
};

const TimeLead = (props: Props) => {
  const { parentName } = props;
  const form = Form.useFormInstance();
  const isWorkingTime = Form.useWatch([parentName, 'isWorkingTime'], form);
  const assignDuration = Form.useWatch([parentName, 'assignDuration'], form);
  const workingTime = form.getFieldValue([parentName, 'workingTime']);

  useEffect(() => {
    if (isWorkingTime && (workingTime?.length === 0 || !workingTime)) {
      form.setFieldsValue({ [parentName]: { workingTime: OBJECT_ALL_WEEK.map(() => [null, null]) } });
    }
    form.setFieldValue([parentName, 'assignDurationInMinute'], form.getFieldValue([parentName, 'assignDuration']));
  }, [form, isWorkingTime, workingTime, parentName]);

  return (
    <Col md={12} xs={24}>
      <Item
        name={[parentName, 'assignDuration']}
        className="assign-duration"
        label="Thời gian thu hồi"
        labelCol={{ span: 24 }}
        wrapperCol={{ span: 24 }}
        rules={[
          { required: true, message: 'Vui lòng nhập thời gian thu hồi' },
          {
            validator: async () => {
              const minuteValue = form.getFieldValue([parentName, 'assignDurationInMinute']);
              if (minuteValue < 1) {
                return Promise.reject(new Error('Thời gian phải lớn hơn 1 phút'));
              } else return Promise.resolve();
            },
          },
        ]}
      >
        <InputMinute valueAssignDuration={assignDuration} parentName={parentName} />
      </Item>

      <Item name={[parentName, 'deassignLimit']} label="Giới hạn gỡ phân bổ">
        <Input onKeyDown={handleKeyDownEnterNumber} maxLength={25} placeholder="Nhập số lần giới hạn" suffix="lần" />
      </Item>
      <Item
        label="Thời gian phân bổ"
        name={[parentName, 'isWorkingTime']}
        rules={[{ required: true, message: 'Vui lòng nhập thời gian phân bổ' }]}
      >
        <Select placeholder="Chọn thời gian phân bổ" options={OPTIONS_WORKING_TIME} />
      </Item>
      <Row>
        <Col xxl={4} xl={7} lg={8} xs={0}></Col>
        {isWorkingTime && (
          <Col xxl={20} xl={17} lg={16} xs={24}>
            <List name={[parentName, 'workingTime']}>
              {fields => (
                <div className="working-time">
                  {fields.map(({ key, name }) => (
                    <Item
                      key={key}
                      name={name}
                      label={OBJECT_ALL_WEEK[name].label}
                      labelCol={{ span: 24 }}
                      wrapperCol={{ span: 24 }}
                      rules={[
                        () => ({
                          validator(_, value) {
                            const [start, end] = value || [];
                            if (start && end && start.isSame(end, 'second')) {
                              return Promise.reject(new Error('Thời gian bắt đầu và kết thúc không thể giống nhau'));
                            }
                            return Promise.resolve();
                          },
                        }),
                      ]}
                    >
                      <DatePicker.RangePicker
                        placeholder={['Giờ bắt đầu', 'Giờ kết thúc']}
                        suffixIcon={<ClockCircleOutlined />}
                        format="HH:mm"
                        picker="time"
                      />
                    </Item>
                  ))}
                </div>
              )}
            </List>
          </Col>
        )}
      </Row>
      <Item label="Thông báo" name={[parentName, 'notification']}>
        <Select
          placeholder="Chọn phương thức thông báo đến nhân viên"
          options={OPTIONS_SEND_NOTIFICATIONS}
          mode="multiple"
        />
      </Item>
      <Item label="Hiện số điện thoại" name={[parentName, 'visiblePhone']} valuePropName="checked">
        <Switch />
      </Item>
    </Col>
  );
};

export default TimeLead;
