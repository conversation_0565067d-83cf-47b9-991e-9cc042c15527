import { Button, Form, Tabs } from 'antd';
import { TabsProps } from 'antd/lib';
import { SetStateAction, useEffect, useState } from 'react';
import ModalComponent from '../../../../components/modal';
import { IConfig, ILead, IWorkingTime } from '../../../../types/lead';
import { useLeadStore } from '../../store';
import GeneralLead from './GeneralLead';
import SurveyLead from './SurveyLead';
import './styles.scss';
import { putLeadConfig } from '../../../../service/lead';
import { useUpdateField } from '../../../../hooks';
import { useParams } from 'react-router-dom';
import {
  convertNotification,
  formatExploitTime,
  formatExploitTimeForComponent,
  formatOrgCharts,
  formatWorkingTime,
  formatWorkingTimeForComponent,
} from '../common/shareFunc';
import { v4 as uuid } from 'uuid';
import { useAuth } from '../../../../context/AuthContext';
import { PERMISSION_LEAD } from '../../../../constants/permissions/lead';

type Props = {
  open: boolean;
  currentConfig?: ILead;
  onCancel: () => void;
  defaultDataConfig?: IConfig;
};

const ModalConfigLead = (props: Props) => {
  const { id } = useParams();
  const { open, onCancel, defaultDataConfig } = props;
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('1');
  const { hasAuthority } = useAuth();
  const isPermissionUpdate = hasAuthority(PERMISSION_LEAD.configUpdate);

  const { addDataConfig, type, typeConfig, setTypeConfig, isModifiedConfig, setIsModifiedConfig } = useLeadStore(
    state => state,
  );
  const handleTabChange = (key: SetStateAction<string>) => {
    setActiveTab(key);
  };

  const { mutateAsync: updateConfig, isPending } = useUpdateField<{ config: IConfig; id?: string }>({
    apiQuery: putLeadConfig,
    keyOfDetailQuery: ['get-lead-repo', id || ''],
    label: '',
  });

  useEffect(() => {
    if (defaultDataConfig && open) {
      const formatValue =
        type === 'update'
          ? defaultDataConfig
          : {
              ...defaultDataConfig,
              exploitTime: formatExploitTimeForComponent(
                defaultDataConfig?.exploitTime as { from: string; to: string },
              ),
              workingTime: formatWorkingTimeForComponent(defaultDataConfig?.workingTime as IWorkingTime[]),
            };
      form.setFieldsValue({
        configs: formatValue,
      });
    }
  }, [defaultDataConfig, form, open, type]);

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: 'Thông tin cấu hình',
      children: <GeneralLead defaultValue={defaultDataConfig} />,
    },
    {
      key: '2',
      label: 'Cấu hình khảo sát',
      children: <SurveyLead />,
    },
  ];

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      const assignDurationInMinute = form.getFieldValue(['configs', 'assignDurationInMinute']);
      const newData = {
        ...values.configs,
        name: values.configs?.name?.trim(),
        code: defaultDataConfig?.code?.trim(),
        deassignLimit: values.configs.deassignLimit ? Number(values.configs.deassignLimit) : undefined,
        assignDuration: assignDurationInMinute ? Number(assignDurationInMinute) : undefined,
        notification: convertNotification(values.configs?.notification as string[]),
        orgChartIds: [values.configs?.orgCharts?.[0]?.id],
        orgCharts: formatOrgCharts(values.configs.orgCharts, values?.configs?.deliverType),
        projectId: values.configs?.project?.id,
        exploitTime: formatExploitTime(values.configs?.exploitTime) as { from: string; to: string },
        workingTime: values.configs?.workingTime?.map(formatWorkingTime) || null,
      };

      if (type === 'update') {
        const res = await updateConfig({ config: newData, id: id });
        if (res?.data?.statusCode === '0') {
          setIsModifiedConfig(false);
          setActiveTab('1');
          onCancel && onCancel();
          form.resetFields();
          setTypeConfig();
        }
      } else {
        addDataConfig(
          typeConfig !== 'create'
            ? { ...newData, active: defaultDataConfig?.active ?? 1 }
            : { ...newData, active: 1, _id: uuid() },
          defaultDataConfig?._id,
        );
        setActiveTab('1');
        onCancel && onCancel();
        form.resetFields();
        setTypeConfig();
      }
    } catch (errorInfo) {
      console.log('errorInfo :', errorInfo);
    }
  };

  return (
    <ModalComponent
      rootClassName={`wrapper-create-of-lead detail-${isModifiedConfig ? 'modified' : 'not-modified'}`}
      open={open}
      title={type === 'update' ? 'Cập nhật cấu hình lead' : 'Tạo mới cấu hình lead'}
      onCancel={() => {
        onCancel();
        setActiveTab('1');
        form.resetFields();
        setTypeConfig();
        setIsModifiedConfig(false);
      }}
      footer={[
        (typeConfig === 'create' || (type === 'update' && isPermissionUpdate && isModifiedConfig)) && (
          <Button key="submit" type="primary" onClick={handleSubmit} loading={isPending}>
            Lưu
          </Button>
        ),
      ]}
    >
      <Form
        form={form}
        className="general-lead"
        layout="horizontal"
        labelCol={{ xxl: 4, xl: 7, lg: 8, xs: 24 }}
        wrapperCol={{ xxl: 20, xl: 17, lg: 16, xs: 24 }}
        onValuesChange={() => setIsModifiedConfig(true)}
      >
        <Tabs defaultActiveKey="1" items={items} activeKey={activeTab} onChange={handleTabChange} />
      </Form>
    </ModalComponent>
  );
};

export default ModalConfigLead;
