import { Col, DatePicker, Form, Input, Row } from 'antd';
import React from 'react';
import SingleSelectLazy from '../../../../components/select/singleSelectLazy';
import { FORMAT_DATE_TIME } from '../../../../constants/common';
import { getListProject } from '../../../../service/project';
import { IConfig } from '../../../../types/lead';
import { DetailProject } from '../../../../types/project/project';
import TimeLead from '../timeLead';
import UnitParticipating from '../unitParticipating';
import { useLeadStore } from '../../store';

const { Item } = Form;

const GeneralLead = ({ defaultValue }: { defaultValue?: IConfig }) => {
  const form = Form.useFormInstance();
  const project = Form.useWatch(['configs', 'project'], form);
  const { setIsModifiedConfig } = useLeadStore(state => state);

  const handleSelectProject = (values: DetailProject) => {
    form.setFieldValue(['configs', 'project'], { id: values?.id, name: values?.name });
    setIsModifiedConfig(true);
  };

  return (
    <>
      <Row gutter={16} className="general-info">
        <Col md={6} xs={24}>
          <Item label="Mã cấu hình" name={['configs', 'code']} labelCol={{ span: 24 }} wrapperCol={{ span: 24 }}>
            <Input disabled />
          </Item>
        </Col>
        <Col md={6} xs={24}>
          <Item
            label="Tên cấu hình"
            name={['configs', 'name']}
            labelCol={{ span: 24 }}
            wrapperCol={{ span: 24 }}
            required
            rules={[{ required: true, message: 'Vui lòng nhập tên cấu hình' }]}
          >
            <Input
              maxLength={60}
              placeholder="Nhập tên cấu hình"
              onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                e.target.value = e.target.value.toUpperCase(); // Chuyển đổi trực tiếp
              }}
            />
          </Item>
        </Col>
      </Row>
      <Row gutter={16} className="general-info">
        <UnitParticipating parentName="configs" defaultValueOrgCharts={defaultValue?.orgCharts}>
          <Item
            name={['configs', 'exploitTime']}
            label="Thời gian hiệu lực"
            labelCol={{ span: 24 }}
            wrapperCol={{ span: 24 }}
            rules={[{ required: true, message: 'Vui lòng nhập thời gian hiệu lực' }]}
          >
            <DatePicker.RangePicker
              format={FORMAT_DATE_TIME}
              showTime
              placeholder={['Ngày - Giờ bắt đầu ', 'Ngày - giờ kết thúc']}
              disabledDate={(current, { from }) => {
                return !!(from && current && current.isSame(from, 'second'));
              }}
            />
          </Item>
          <Item
            name={['configs', 'project']}
            className="assign-duration"
            label="Dự án áp dụng"
            labelCol={{ span: 24 }}
            wrapperCol={{ span: 24 }}
            rules={[{ required: true, message: 'Vui lòng chọn dự án áp dụng' }]}
          >
            <SingleSelectLazy
              enabled
              apiQuery={getListProject}
              queryKey={['project-dropdown']}
              keysLabel={'name'}
              handleSelect={handleSelectProject}
              defaultValues={{ label: project?.name, value: project?.id }}
              placeholder="Chọn dự án"
            />
          </Item>
        </UnitParticipating>
        <Col span={24}></Col>
        <TimeLead parentName="configs" />
      </Row>
    </>
  );
};

export default GeneralLead;
