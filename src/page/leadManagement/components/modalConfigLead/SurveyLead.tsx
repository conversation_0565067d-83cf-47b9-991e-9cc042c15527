import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Col, Form, Input, Row, Select } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { Table } from 'antd/lib';
import { ISurvey } from '../../../../types/lead';

const SurveyLead = () => {
  const form = Form.useFormInstance();

  const columns: ColumnsType<ISurvey> = [
    {
      title: 'Loại câu hỏi',
      dataIndex: 'type',
      key: 'type',
      width: '180px',
      align: 'start',
      render: (_, __, index) => (
        <>
          <Form.Item
            name={[index, 'type']}
            wrapperCol={{ span: 24 }}
            rules={[{ required: true, message: 'Vui lòng chọn loại câu hỏi' }]}
          >
            <Select
              style={{ width: '100%' }}
              placeholder="Loại câu hỏi"
              options={[
                { value: 'dropdown', label: 'Dropdown List' },
                { value: 'radio', label: 'Radio button List' },
                { value: 'checkboxes', label: 'Checkboxes' },
                { value: 'textbox', label: 'Textbox' },
                { value: 'multilineTextbox', label: 'Multiline Textbox' },
              ]}
            />
          </Form.Item>
        </>
      ),
    },
    {
      title: 'Câu hỏi',
      dataIndex: 'name',
      key: 'name',
      width: '30%',
      render: (_, __, index) => (
        <Form.Item
          name={[index, 'name']}
          wrapperCol={{ span: 24 }}
          rules={[{ required: true, message: 'Vui lòng nhập câu hỏi' }]}
        >
          <Input placeholder="Nhập câu hỏi" maxLength={50} />
        </Form.Item>
      ),
    },
    {
      title: 'Câu trả lời',
      dataIndex: 'value',
      key: 'value',
      render: (_, __, index) => {
        const type = form.getFieldValue(['configs', 'surveys', index, 'type']);
        if (type === 'textbox' || type === 'multilineTextbox') {
          return <p>Không yêu cầu câu trả lời</p>; // hoặc để trống cũng được
        }

        return (
          <Form.List
            name={[index, 'values']}
            rules={[
              {
                validator: async (_, answers) => {
                  if (!answers || answers.length === 0) {
                    return Promise.reject(new Error('Cần thêm ít nhất một câu trả lời'));
                  }
                },
              },
            ]}
          >
            {(fields, { add, remove }, { errors }) => {
              return (
                <>
                  {fields.map((field, idx) => (
                    <div key={field.key} className="survey-answer">
                      <Row gutter={16}>
                        <Col span={10}>
                          <Form.Item
                            wrapperCol={{ span: 24 }}
                            name={[field.name, 'code']}
                            rules={[
                              { required: true, message: 'Vui lòng nhập mã' },
                              {
                                validator: async (_, value) => {
                                  const values = form.getFieldValue(['configs', 'surveys', index, 'values']) || [];
                                  const duplicates = values
                                    .filter((item: { code: string }) => item?.code === value)
                                    .filter(Boolean);
                                  if (duplicates.length > 1) {
                                    return Promise.reject(new Error('Mã đã tồn tại'));
                                  }
                                  return Promise.resolve();
                                },
                              },
                            ]}
                          >
                            <Input placeholder="Nhập mã" maxLength={10} style={{ width: '100%' }} />
                          </Form.Item>
                        </Col>
                        <Col span={14}>
                          <Form.Item
                            wrapperCol={{ span: 24 }}
                            name={[field.name, 'name']}
                            rules={[{ required: true, message: 'Vui lòng nhập câu trả lời' }]}
                          >
                            <Input placeholder={`Câu trả lời ${idx + 1}`} maxLength={50} />
                          </Form.Item>
                        </Col>
                      </Row>
                      <Button type="link" onClick={() => remove(field.name)} style={{ color: 'red' }}>
                        Xóa
                      </Button>
                    </div>
                  ))}
                  <Button type="dashed" onClick={() => add()} icon={<PlusOutlined />}>
                    Thêm câu trả lời
                  </Button>
                  <Form.ErrorList errors={errors} />
                </>
              );
            }}
          </Form.List>
        );
      },
    },
  ];

  return (
    <Form.List name={['configs', 'surveys']}>
      {(fields, { add, remove }) => (
        <>
          <div className="survey-lead">
            <Button
              icon={<PlusOutlined />}
              className="add-answers"
              onClick={() => {
                add();
                form.validateFields();
              }}
            >
              Thêm câu hỏi mới
            </Button>
            <Table
              columns={[
                ...columns,
                {
                  title: 'Hành động',
                  key: 'actions',
                  render: (_, __, index) => (
                    <Button
                      type="link"
                      icon={<DeleteOutlined />}
                      onClick={() => remove(index)}
                      style={{ color: 'red' }}
                    >
                      Xóa
                    </Button>
                  ),
                },
              ]}
              dataSource={fields.map(field => ({
                key: field.key.toString(), // Đảm bảo key là string
                type: '', // Cung cấp giá trị mặc định nếu cần
                name: '',
                values: [],
              }))}
              pagination={false}
            />
          </div>
        </>
      )}
    </Form.List>
  );
};

export default SurveyLead;
