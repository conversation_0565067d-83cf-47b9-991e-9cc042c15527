import { create } from 'zustand';
import { IConfig, ILead } from '../../types/lead';

interface LeadStore {
  type?: 'create' | 'update';
  typeConfig?: 'create' | 'update';
  dataConfigs?: IConfig[];
  isModified?: boolean;
  initialValues?: ILead;
  isModifiedConfig?: boolean;
  setInitialValues: (value: ILead) => void;
  setIsModified: (value: boolean) => void;
  setType: (value: 'create' | 'update') => void;
  addDataConfig: (newData: IConfig, _id?: string) => void;
  removeDataConfig: (value: IConfig) => void;
  refetchDataConfig: () => void;
  setDefaultDataConfigs: (value?: IConfig[]) => void;
  setTypeConfig: (value?: 'create' | 'update') => void;
  setIsModifiedConfig: (value: boolean) => void;
}

export const useLeadStore = create<LeadStore>(set => ({
  type: undefined,
  dataConfigs: [],
  typeConfig: undefined,
  isModified: false,
  initialValues: undefined,
  isModifiedConfig: false,
  setInitialValues: value => set({ initialValues: value }),
  setIsModified: value => set({ isModified: value }),
  addDataConfig: (newData: IConfig, _id?: string) => {
    return set(state => {
      const dataConfigs = state?.dataConfigs || [];
      if (state?.typeConfig === 'create') {
        return {
          dataConfigs: [...dataConfigs, newData],
        };
      } else {
        return {
          dataConfigs: dataConfigs.map((config, index) => (String(index) === _id ? newData : config)),
        };
      }
    });
  },
  removeDataConfig(value) {
    set(state => ({
      dataConfigs: (state.dataConfigs || []).filter(item => item !== value), // Lọc dữ liệu cũ
    }));
  },
  refetchDataConfig: () => set({ dataConfigs: [] }),
  setType: value => set({ type: value }),
  setDefaultDataConfigs: (value?: IConfig[]) => set({ dataConfigs: value }),
  setTypeConfig: value => set({ typeConfig: value }),
  setIsModifiedConfig: value => set({ isModifiedConfig: value }),
}));
