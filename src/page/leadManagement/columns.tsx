import { TableColumnsType, Typography } from 'antd';
import dayjs from 'dayjs';
import { ActionsColumns } from '../../components/table/components/ActionsColumns';
import { LEAD_CONFIG } from '../../configs/path';
import { FORMAT_DATE_TIME } from '../../constants/common';
import { ILead } from '../../types/lead';

const { Text } = Typography;

export const columnsListOfLead: TableColumnsType = [
  {
    title: 'Mã kho cấu hình',
    dataIndex: 'code',
    key: 'code',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Tên kho cấu hình',
    dataIndex: 'name',
    key: 'name',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Số lượng cấu hình',
    dataIndex: 'configNumber',
    key: 'configNumber',
    align: 'center',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: '<PERSON><PERSON><PERSON> tạo',
    dataIndex: 'createdDate',
    key: 'createdDate',
    align: 'right',
    render: (value: string, record: ILead) => (
      <>
        <Text>{dayjs(value).format(FORMAT_DATE_TIME)}</Text>
        <br />
        <Text>{record?.createdByObj?.fullName ? record.createdByObj?.fullName : '-'}</Text>
      </>
    ),
  },
  {
    title: 'Ngày cập nhật',
    dataIndex: 'updatedDate',
    key: 'updatedDate',
    align: 'right',
    render: (value: string, record: ILead) => (
      <>
        <Text>{dayjs(value).format(FORMAT_DATE_TIME)}</Text> <br />
        <Text>{record?.updatedByObj?.fullName ? record.updatedByObj.fullName : '-'}</Text>
      </>
    ),
  },
  {
    dataIndex: 'action',
    key: 'action',
    width: '100px',
    align: 'center',
    render: (_: unknown, record: ILead) => {
      const openViewDetail = () => {
        window.open(`${LEAD_CONFIG}/${record?.id}`, '_blank', 'noopener,noreferrer');
      };

      return <ActionsColumns handleViewDetail={openViewDetail} />;
    },
  },
];

export const columnsListOfConfig: TableColumnsType = [
  {
    title: 'STT',
    key: 'stt',
    width: '82px',
    render: (_, __, index) => index + 1,
  },
  {
    title: 'Mã cấu hình',
    key: 'code',
    width: '20%',
    dataIndex: 'code',
    render: value => (value ? value : '-'),
  },
  {
    title: 'Tên cấu hình',
    key: 'name',
    width: '50%',
    dataIndex: 'name',
    render: value => (value ? value : '-'),
  },
  {
    title: 'Trạng thái',
    key: 'active',
    width: '142px',
    align: 'center',
    dataIndex: 'active',
    render: value => (value === 1 ? <Text type="success">Kích hoạt</Text> : <Text type="danger">Vô hiệu hoá</Text>),
  },
];
