import { EllipsisOutlined, LoadingOutlined } from '@ant-design/icons';
import { TableColumnsType, Typography } from 'antd';
import dayjs from 'dayjs';
import BreadCrumbComponent from '../../components/breadCrumb';
import TableComponent from '../../components/table';
import { FORMAT_DATE_TIME, OPTIONS_STATUS_IMPORT_COMMISSIONS } from '../../constants/common';
import './styles.scss';
import { useFetch } from '../../hooks';
import FilterSearchHistoryCommission from './FilterSearchHistoryCommission';
import { TListHistoryImportCommissions } from '../../types/commission';
import { getListHistoryImportCommissions } from '../../service/commission';

const { Text } = Typography;

const HistoryImportCommissions = () => {
  const { data, isFetching } = useFetch<TListHistoryImportCommissions[]>({
    api: getListHistoryImportCommissions,
    queryKeyArrWithFilter: ['list-history-import-commissions'],
  });
  const dataSources = data?.data?.data?.rows || [];

  const columns: TableColumnsType = [
    {
      title: 'Ngày tải lên',
      dataIndex: 'createdDate',
      key: 'createdDate',
      width: '20%',
      render: (value: string, record: TListHistoryImportCommissions) =>
        value || record?.processBy ? `${dayjs(value).format(FORMAT_DATE_TIME)} - ${record?.processBy?.email}` : '-',
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      width: '15%',
      align: 'center',
      render: (value: string) => {
        const itemStatus = OPTIONS_STATUS_IMPORT_COMMISSIONS.find(item => item.value === value);
        return value ? <Text style={{ color: itemStatus?.color }}>{itemStatus?.label}</Text> : '-';
      },
    },
    {
      title: 'Phí/ hoa hồng upload thành công',
      dataIndex: 'success',
      key: 'success',
      width: '25%',
      align: 'center',
      render: (value: number, record: TListHistoryImportCommissions) => {
        if (record?.status === 'PENDING' || record?.status === 'PROCESSING') {
          return <EllipsisOutlined />;
        }
        return typeof value === 'number' ? `${value}/${record?.fail + record?.success}` : '-';
      },
    },
    {
      title: 'Log file',
      dataIndex: 'failedFileUrl',
      key: 'failedFileUrl',
      width: '35%',
      render: (value: string, record: TListHistoryImportCommissions) => {
        if (record?.status === 'PENDING' || record?.status === 'PROCESSING') {
          return <LoadingOutlined style={{ color: '#1677FF', fontSize: 24 }} />;
        }
        return (
          <Typography.Link download={record?.failedFileName} href={value} target="_blank">
            {record?.failedFileName}
          </Typography.Link>
        );
      },
    },
  ];

  return (
    <div className="wrapper-list-history-import-commissions">
      <BreadCrumbComponent />
      <FilterSearchHistoryCommission />
      <TableComponent
        loading={isFetching}
        queryKeyArr={['list-history-import-commissions']}
        dataSource={dataSources}
        columns={columns}
        rowKey="id"
      />
    </div>
  );
};

export default HistoryImportCommissions;
