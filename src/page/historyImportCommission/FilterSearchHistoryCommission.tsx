import { Col, DatePicker, Form, Row, Select } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useMemo, useState } from 'react';
import { useLocation } from 'react-router-dom';
import DropdownFilterSearch from '../../components/dropdown/dropdownFilterSearch';
import { FORMAT_DATE, OPTIONS_STATUS_IMPORT_COMMISSIONS } from '../../constants/common';
import useFilter from '../../hooks/filter';
import { TFilterHistoryCustomer } from '../../types/customers';

const FilterSearchHistoryCommission = () => {
  const { search } = useLocation();
  const [form] = Form.useForm();
  const params = useMemo(() => new URLSearchParams(search), [search]);
  const [filter, setFilter] = useFilter();
  const [initialValues, setInitialValues] = useState<TFilterHistoryCustomer>();
  const [isOpenFilter, setIsOpenFilter] = useState(false);

  useEffect(() => {
    if (params) {
      const newInitialValues = {
        startCreatedDate: params.get('startCreatedDate') ? dayjs(params.get('startCreatedDate')) : undefined,
        endCreatedDate: params.get('endCreatedDate') ? dayjs(params.get('endCreatedDate')) : undefined,
        status: params.get('status') || undefined,
      };
      form.setFieldsValue(newInitialValues);
      setInitialValues(newInitialValues);
    }
  }, [form, params]);

  const handleSubmitFilter = (values: TFilterHistoryCustomer) => {
    const newFilter: Record<string, unknown> = {
      startCreatedDate: values?.startCreatedDate ? dayjs(values?.startCreatedDate).format(FORMAT_DATE) : null,
      endCreatedDate: values?.endCreatedDate ? dayjs(values?.endCreatedDate).format(FORMAT_DATE) : null,
      status: values?.status || null,
    };
    setFilter({ ...filter, page: '1', ...newFilter });
    setIsOpenFilter(false);
  };

  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };

  const handleClearFilters = () => {
    setInitialValues(undefined);
    isOpenFilter && setIsOpenFilter(false);
  };

  return (
    <>
      <DropdownFilterSearch
        rootClassName="wrapper-filter"
        onClearFilters={handleClearFilters}
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        showParams={true}
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        form={form}
        initialValues={initialValues}
        extraFormItems={
          <>
            <Form.Item label="Trạng thái" name="status">
              <Select placeholder="Chọn trạng thái" allowClear options={OPTIONS_STATUS_IMPORT_COMMISSIONS} />
            </Form.Item>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="Từ ngày" name="startCreatedDate">
                  <DatePicker
                    format={FORMAT_DATE}
                    disabledDate={current => {
                      const endCreatedDate = form.getFieldValue('endCreatedDate');
                      return (
                        (current && current > dayjs().startOf('day')) ||
                        (endCreatedDate && current > dayjs(endCreatedDate).startOf('day')) // Disable ngày sau "Đến ngày"
                      );
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Đến ngày" name="endCreatedDate">
                  <DatePicker
                    format={FORMAT_DATE}
                    disabledDate={current => {
                      const startCreatedDate = form.getFieldValue('startCreatedDate');
                      return (
                        (current && current > dayjs().startOf('day')) ||
                        (startCreatedDate && current < dayjs(startCreatedDate).startOf('day')) // Disable ngày trước "Ngày tạo"
                      );
                    }}
                  />
                </Form.Item>
              </Col>
            </Row>
          </>
        }
      />
    </>
  );
};

export default FilterSearchHistoryCommission;
