import React, { useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button, Result } from 'antd';
import './index.scss';
import { ResultStatusType } from 'antd/es/result';

type ErrorContent = React.ReactNode;

interface Props {
  status?: ResultStatusType;
}

function ErrorsPage(props: Props) {
  const { status } = props;
  const navigate = useNavigate();

  const getContentErrors = useMemo((): ErrorContent => {
    switch (status) {
      case '403':
        return <p>Xin lỗi, bạn không được phép truy cập trang này.</p>;
      case '404':
        return <p>Xin lỗi, trang bạn truy cập không tồn tại.</p>;
      default:
        return <p>Xin lỗi, có lỗi xảy ra, vui lòng thao tác lại.</p>;
    }
  }, [status]);

  return (
    <Result
      status={status}
      title={status}
      subTitle={getContentErrors}
      extra={
        <Button onClick={() => navigate('/')} type="primary">
          Quay về trang chủ
        </Button>
      }
    />
  );
}

export default ErrorsPage;
