import { Col, Form, Input, Row, Select, Table, Typography } from 'antd';
import BreadCrumbComponent from '../../components/breadCrumb';
import { useCreateField, useFetch } from '../../hooks';
import React, { Key } from 'react';
import { ARRAY_FIELD_CREATE_TRAINING_USER, columnCreate, columnsTrainingUser } from './component/columns';
import { TableRowSelection } from 'antd/lib/table/interface';
import ButtonOfPageDetail from '../../components/button/buttonOfPageDetail';
import { createTrainingUser, getEvents, getUsers } from '../../service/traingingUserCreate';
import { TCustomer, TEvent } from '../../types/trainingUserCreate';
import ListCreateTrainingUser from './component/ListCreateTrainingUser';

const { Item } = Form;
const { Title } = Typography;

const TrainingUserCreate = () => {
  const [form] = Form.useForm();

  const [eventSelected, setEventSelected] = React.useState<TEvent>();
  const [searchText, setSearchText] = React.useState('');
  const [currentSelectedRows, setCurrentSelectedRows] = React.useState<TCustomer[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = React.useState<Key[]>([]);

  const [dataSourceTraningUserCreate, setDataSourceTrainingUserCreate] = React.useState<TCustomer[]>([]);

  const { data: dataEvent } = useFetch<TEvent[]>({
    queryKeyArrWithFilter: ['get-event'],
    api: getEvents,
  });

  const { data: dataUserList } = useFetch<TCustomer[]>({
    queryKeyArrWithFilter: ['get-user'],
    api: getUsers,
  });

  const { mutateAsync: createTrainingUserList } = useCreateField<TCustomer[]>({
    apiQuery: createTrainingUser,
    keyOfListQuery: ['get-training-user', eventSelected?.id],
    checkDuplicate: true,
    isMessageError: false,
  });

  const userList = dataUserList?.data?.data;

  const listEvent = dataEvent?.data?.data;

  const filteredData = React.useMemo(() => {
    const normalize = (str: string = '') => str.toLowerCase().replace(/\s+/g, ' ').trim(); // chuẩn hóa: viết thường + loại bỏ khoảng trắng thừa

    const normalizedSearch = normalize(searchText);
    return userList
      ? userList.filter(item => {
          const code = normalize(item?.code);
          const name = normalize(item?.name);

          return code.includes(normalizedSearch) || name.includes(normalizedSearch);
        })
      : [];
  }, [userList, searchText]);

  const onSelectChange = (selectedRowKeys: Key[], selectedRows: TCustomer[]) => {
    setCurrentSelectedRows(selectedRows);
    setSelectedRowKeys(selectedRowKeys);

    if (selectedRows.length === 0) {
      // If all rows are unselected, remove them from `dataSourceTraningUserCreate`
      const filteredDataSource = dataSourceTraningUserCreate.filter(
        row => selectedRowKeys.includes(row.id as string), // Only keep selected ones
      );
      setDataSourceTrainingUserCreate(filteredDataSource);
      form?.setFieldValue('customers', filteredDataSource);
    } else {
      // Ensure unique selection when adding new rows
      const mergedUniqueRows = [
        ...selectedRows.filter(row => !dataSourceTraningUserCreate.some(selected => selected.id === row.id)),
        ...dataSourceTraningUserCreate,
      ]; // chỉ thêm các thành phần mới
      setDataSourceTrainingUserCreate(mergedUniqueRows);
      form?.setFieldValue('customers', mergedUniqueRows);
    }
  };

  const rowSelection: TableRowSelection<TCustomer> = {
    selectedRowKeys,
    onChange: (selectedRowKeys: Key[], selectedRows: TCustomer[]) => onSelectChange(selectedRowKeys, selectedRows),
    onSelectAll: (selected, selectedRows) => {
      if (selected) {
        // Handle Select All Logic
        const mergedUniqueRows = [...dataSourceTraningUserCreate, ...selectedRows].filter(
          (row, index, array) => array.findIndex(r => r.id === row.id) === index,
        );
        setCurrentSelectedRows(selectedRows);
        setSelectedRowKeys(selectedRows.map(row => row.key));
        setDataSourceTrainingUserCreate(mergedUniqueRows);
        form?.setFieldValue('customers', mergedUniqueRows);
      } else {
        // Handle Deselect All Logic
        const filteredDataSource = dataSourceTraningUserCreate.filter(
          item => !currentSelectedRows.some(row => row.id === item.id),
        );
        setSelectedRowKeys([]);
        setCurrentSelectedRows([]);
        setDataSourceTrainingUserCreate([]);
        form?.setFieldValue('customers', filteredDataSource);
      }
    },
  };

  const handleChangeSelectEvent = React.useCallback(
    (value: string) => {
      const event = listEvent?.find(o => o?.id === value);
      setEventSelected(event);
    },
    [listEvent],
  );

  const handleSubmit = async () => {
    await form.validateFields();
    const values = form.getFieldsValue(true);
    const customers = values?.customers;
    const newData = customers?.map((o: TCustomer) => ({ ...o, idEvent: eventSelected?.id }) as unknown as TCustomer);
    const res = await createTrainingUserList(newData as TCustomer[]);
    if (Number(res?.data?.statusCode) === 0) {
      console.log('');
    }
  };

  return (
    <div className="wrapper-detail-personal-proposal">
      <BreadCrumbComponent />
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        // initialValues={{ custormers: currentSelectedRows }}
        className="space-y-6"
      >
        <Row gutter={{ md: 24, lg: 40, xl: 80, xxl: 126 }}>
          <Col span={24}></Col>
          <Col xs={24} md={12}>
            <Title level={5}>Tên sự kiện</Title>
            <Row gutter={{ md: 24, lg: 40 }}>
              <Col xs={24}>
                <Item
                  label="Tên sự kiện"
                  name="idEvent"
                  required
                  rules={[{ required: true, message: 'Vui lòng chọn sự kiện' }]}
                >
                  <Select
                    placeholder={'Chọn sự kiện'}
                    onChange={handleChangeSelectEvent}
                    options={listEvent?.map(o => ({ ...o, value: o?.id, label: o?.name }))}
                  />
                </Item>
              </Col>
            </Row>
            <Title level={5}>Thông tin khách mời</Title>
            <Row gutter={{ md: 24, lg: 40 }}>
              <Col xs={24}>
                <Input.Search
                  placeholder="Tìm kiếm thông tin khách mời"
                  allowClear
                  onSearch={value => setSearchText(value)}
                />
              </Col>
              {/* </Row> */}
              <Col xs={24} md={24} style={{ marginTop: '8px' }}>
                <Table
                  rowSelection={rowSelection}
                  columns={columnsTrainingUser}
                  dataSource={filteredData}
                  rowKey={'id'}
                  key={'id'}
                />
              </Col>
            </Row>
          </Col>
          <Col xs={24} md={12}>
            <Title level={5}>Tạo mới khách mời</Title>
            <Row gutter={{ md: 24, lg: 40 }}>
              <Col xs={24} md={24}>
                <ListCreateTrainingUser<TCustomer>
                  labelAdd={'Thêm khách hàng'}
                  nameField="customers"
                  columns={columnCreate}
                  dataSource={dataSourceTraningUserCreate?.map(o => ({ ...o, event: eventSelected }))}
                  arrayFieldTable={ARRAY_FIELD_CREATE_TRAINING_USER}
                  setDataSource={setDataSourceTrainingUserCreate}
                />
              </Col>
            </Row>
          </Col>
        </Row>
      </Form>
      {true && (
        <ButtonOfPageDetail
          handleSubmit={() => form?.submit()}
          handleCancel={() => {
            form?.setFieldsValue({});
            setSelectedRowKeys([]);
            setCurrentSelectedRows([]);
          }}
          // loadingSubmit={isPending}
        />
      )}
    </div>
  );
};
export default TrainingUserCreate;
