import { create } from 'zustand';
import { ITrainingUser } from '../../types/trainingUserCreate';

interface IActionModal {
  isOpen: boolean;
  type?: 'create' | 'clone';
  id?: string;
}

interface ITrainingUserCreateStore {
  disabled: boolean;
  initialValue: ITrainingUser;
  isModified: boolean;
  actionModal: IActionModal;
  setDisabled: (value: boolean) => void;
  setInitialValue: (value?: ITrainingUser) => void;
  setIsModified: (value: boolean) => void;
  setActionModal: (value: IActionModal) => void;
}

export const useTrainingUserCreateStore = create<ITrainingUserCreateStore>(set => ({
  disabled: false,
  initialValue: {} as ITrainingUser,
  isModified: false,
  actionModal: { isOpen: false, type: undefined, id: undefined },
  setDisabled: value => set({ disabled: value }),
  setInitialValue: value => set({ initialValue: value }),
  setIsModified: value => set({ isModified: value }),
  setActionModal: (value: IActionModal) => set({ actionModal: value }),
}));
