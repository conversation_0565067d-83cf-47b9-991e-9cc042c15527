import { TableColumnsType } from 'antd';
import { ColumnTypes, EditColumns } from './editTable';
import { TCustomer } from '../../../types/trainingUserCreate';

export const columnsTrainingUser: TableColumnsType = [
  {
    title: 'Tên khách hàng',
    dataIndex: 'name',
    key: 'name',
    width: 140,
    render: (value: string[]) => (value ? value : '-'),
  },
  {
    title: 'Số điện thoại',
    dataIndex: 'phoneNumber',
    key: 'phoneNumber',
    width: 150,

    render: (value: string[]) => (value ? value : '-'),
  },
  {
    title: 'Email',
    dataIndex: 'email',
    key: 'email',
    width: 150,

    render: (value: string[]) => (value ? value : '-'),
  },
];

export const ARRAY_FIELD_CREATE_TRAINING_USER = ['name', 'phone', 'email'];

export const columnCreate: (ColumnTypes<TCustomer>[number] & EditColumns)[] = [
  {
    title: 'T<PERSON><PERSON> kh<PERSON>ch hàng',
    dataIndex: 'name',
    key: 'name',
    editable: true,
    align: 'center',
    width: '15%',
    inputType: 'text',
    inputProps: { maxLength: 60, placeholder: 'Nhập giá trị từ' },
  },
  {
    title: 'Số điện thoại',
    dataIndex: 'phoneNumber',
    key: 'phoneNumber',
    editable: true,
    align: 'center',
    width: '15%',
    inputType: 'text',
    inputProps: { maxLength: 15, placeholder: 'Nhập giá trị đến' },
  },
  {
    title: 'Email',
    dataIndex: 'email',
    key: 'email',
    editable: true,
    align: 'center',
    width: '15%',
    inputType: 'text',
    inputProps: { placeholder: 'Nhập email' },
    rules: (record: unknown) => [
      {
        required: !(record as TCustomer)?.unRequiredEmailRegister,
        message: 'Vui lòng nhập Email',
      },
    ],
  },
];
