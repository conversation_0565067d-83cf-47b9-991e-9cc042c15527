import { Form } from 'antd';
import React, { useEffect } from 'react';
import { v4 as uuid } from 'uuid';
import { ColumnTypes, Editable, EditableRecord, EditColumns } from './editTable';
import { EditableCell } from './editTable/EditableCell';
import { EditableRow } from './editTable/EditableRow';
import { useTrainingUserCreateStore } from '../store';

interface ListCreateTrainingUserProps<T> {
  labelAdd: string;
  nameField: string;
  columns: (ColumnTypes<T>[number] & EditColumns)[];
  dataSource: T[];
  arrayFieldTable: string[];
  setDataSource: React.Dispatch<React.SetStateAction<T[]>>;
}

const ListCreateTrainingUser = <T extends EditableRecord>({
  labelAdd,
  nameField,
  columns,
  arrayFieldTable,
  dataSource,
  setDataSource,
}: ListCreateTrainingUserProps<T>) => {
  const form = Form.useFormInstance();
  const { initialValue } = useTrainingUserCreateStore();
  const defaultListCreateTrainingUser = initialValue?.customers;

  useEffect(() => {
    if (defaultListCreateTrainingUser) {
      const fieldBonus = defaultListCreateTrainingUser;
      if (Array.isArray(fieldBonus) && fieldBonus?.length > 0) {
        setDataSource(fieldBonus as T[]);
      }
    }
  }, [defaultListCreateTrainingUser, nameField, setDataSource]);

  const handleDelete = (key: React.Key) => {
    const newData = dataSource.filter(item => item.id !== key);
    setDataSource(newData);
    form.setFieldsValue({ customers: newData });
  };

  const handleAdd = () => {
    const newData = {
      ...arrayFieldTable.reduce((acc, field) => ({ ...acc, [field]: '' }), {}),
      key: uuid(),
      id: uuid(),
      isNew: true,
    } as unknown as T;
    setDataSource([...dataSource, newData]);
  };

  const handleSave = (row: T) => {
    const newData = [...dataSource];
    const index = newData.findIndex((item: T) => row.id === item.id);
    const item = newData[index];
    newData.splice(index, 1, { ...item, ...row });
    setDataSource(newData);
    form.setFieldsValue({
      customers: newData,
    });
  };

  const components = {
    body: {
      row: EditableRow,
      cell: EditableCell,
    },
  };

  return (
    <>
      <Editable<T>
        columns={columns}
        dataSource={dataSource}
        handleAdd={handleAdd}
        handleDelete={handleDelete}
        handleSave={handleSave}
        components={components}
        labelAdd={labelAdd}
      />
    </>
  );
};

export default ListCreateTrainingUser;
