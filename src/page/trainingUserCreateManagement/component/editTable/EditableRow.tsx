import { Form } from 'antd';
import React from 'react';
import { EditableContext } from './EditableContext';

// EditableRow component remains unchanged
interface EditableRowProps {
  index?: string;
}

export const EditableRow: React.FC<EditableRowProps> = ({ index, ...props }) => {
  const [form] = Form.useForm();
  return (
    <Form form={form} component={false} key={index}>
      <EditableContext.Provider value={form}>
        <tr {...props} />
      </EditableContext.Provider>
    </Form>
  );
};
