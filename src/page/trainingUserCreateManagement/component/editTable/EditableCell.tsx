import { Form, Input, InputProps, Select } from 'antd';
import { Rule } from 'antd/es/form';
import { SelectProps } from 'antd/lib';
import React, { useContext, useEffect, useState } from 'react';
import { EditableContext } from './EditableContext';
import { useTrainingUserCreateStore } from '../../store';

interface EditableCellProps<T> {
  title: React.ReactNode;
  editable: boolean;
  dataIndex: keyof T;
  record: T;
  handleSave: (record: T) => void;
  inputType?: 'text' | 'select' | 'custom'; // New property to specify input type
  isNewRow: boolean;
  optionsSelect?: SelectProps['options'];
  rules: Rule[];
  renderEditComponent?: (props: {
    value: unknown;
    onChange: (value: unknown) => void;
    onPressEnter?: () => void;
    onBlur?: () => void;
    save: () => void;
    disabled?: boolean;
  }) => React.ReactNode;
  inputProps?: Omit<InputProps, 'onPressEnter' | 'onBlur'>;
  selectProps?: Omit<SelectProps, 'onChange' | 'onBlur' | 'options'>;
}

export const EditableCell = <T extends object>({
  editable,
  children,
  dataIndex,
  record,
  handleSave,
  inputType,
  optionsSelect,
  rules,
  renderEditComponent,
  inputProps,
  selectProps,
}: React.PropsWithChildren<EditableCellProps<T>>) => {
  const [editing, setEditing] = useState(false);
  const form = useContext(EditableContext)!;
  const { disabled, setIsModified } = useTrainingUserCreateStore();

  useEffect(() => {
    if (record && !record[dataIndex]) {
      setEditing(true);
    }
  }, [dataIndex, record]);

  const toggleEdit = () => {
    if (disabled) {
      return;
    }
    setEditing(!editing);
    form.setFieldsValue({ [dataIndex]: record[dataIndex] });
  };

  const save = async () => {
    try {
      const values = (await form.validateFields()) as Record<keyof T, unknown>;
      const currentValue = values[dataIndex];

      if (currentValue === undefined || currentValue === null) {
        return;
      }
      if (currentValue === '') {
        handleSave({ ...record, ...(values as Record<string, unknown>) });
        return;
      }
      toggleEdit();
      handleSave({ ...record, ...(values as Record<string, unknown>) });
      setIsModified(true);
    } catch (errInfo) {
      console.log('Save failed:', errInfo);
    }
  };

  let childNode = children;

  if (editable) {
    childNode = editing ? (
      <Form.Item style={{ margin: 0 }} name={dataIndex as string} rules={rules}>
        {inputType === 'custom' && renderEditComponent ? (
          renderEditComponent({
            value: record[dataIndex],
            onChange: val => form.setFieldsValue({ [dataIndex]: val }),
            onPressEnter: save,
            onBlur: save,
            save,
            disabled: disabled,
          })
        ) : inputType === 'select' ? (
          <Select
            onChange={() => setTimeout(save, 100)} // Save when a selection is made
            onBlur={save}
            options={optionsSelect}
            disabled={disabled}
            {...selectProps}
          />
        ) : (
          <Input {...inputProps} onPressEnter={save} onBlur={save} disabled={disabled} />
        )}
      </Form.Item>
    ) : (
      <div className="editable-cell-value-wrap" onClick={toggleEdit}>
        {children}
      </div>
    );
  }

  return <td>{childNode}</td>;
};
