import { App, But<PERSON>, Col, Flex, Form, Input, Radio, Row, Spin, Table, Tabs, TabsProps, Typography } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import dayjs from 'dayjs';
import { useCallback, useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import BreadCrumbComponent from '../../../components/breadCrumb';
import ButtonOfPageDetail from '../../../components/button/buttonOfPageDetail';
import SingleSelectLazy from '../../../components/select/singleSelectLazy';
import { PERMISSION_COMMISSION } from '../../../constants/permissions/commission';
import { useCheckPermissions, useFetch, useUpdateField } from '../../../hooks';
import useFilter from '../../../hooks/filter';
import {
  getDetailOfCommission,
  getDetailOfCommissionPublish,
  getExpenseDraft,
  getExpenseList,
  getSalesPolicyOfCommission,
  updateCommission,
  updateStatusCommission,
} from '../../../service/commission';
import { getOrgChartDropdown } from '../../../service/lead';
import { TAdjustmentVersion, TCommission, TExpense, TOriginalData } from '../../../types/commission';
import FormPeriod from '../../../components/select/FormPeriod';
import { transactionColumns, versionColumns } from './columnsDetail';
import FilterTransaction from './components/FilterTransaction';
import { GroupButton } from './components/GroupButtonAction';
import './styles.scss';

const { Title } = Typography;

// footer table expense
const transactionTableFooter = (dataSourceExpense: TExpense) => {
  const totalProperty = dataSourceExpense?.totalProperty || 0;
  const totalFee = dataSourceExpense?.listBonus?.toLocaleString() || 0;
  const totalFeePay = dataSourceExpense?.totalFee?.toLocaleString() || 0;

  return (
    <>
      <div className="item-footer">
        <span>Tổng số sản phẩm : {totalProperty}</span>
      </div>
      <div className="item-footer">
        <span>Tổng phí thưởng : {totalFee}</span>
      </div>
      <div className="item-footer">
        <span>Tổng phí phát trả : {totalFeePay}</span>
      </div>
    </>
  );
};

const DetailOfCommission = () => {
  const { id } = useParams();
  const { modal, notification } = App.useApp();
  const [form] = Form.useForm();
  const pos = Form.useWatch('pos', form);
  const salePolicy = Form.useWatch('salePolicy', form);

  const { getById, publishGetId, update: permissionUpdate } = useCheckPermissions(PERMISSION_COMMISSION);

  const [isModified, setIsModified] = useState(false);
  const [tab, setTab] = useState('expense');
  const [initialValue, setInitialValue] = useState<TCommission>({} as TCommission);
  const [dataExpenseList, setDataExpenseList] = useState<TExpense>();
  const [selectedAdjustment, setSelectedAdjustment] = useState<TAdjustmentVersion | null>();
  const [paramsDraft, setParamsDraft] = useState<Record<string, unknown>>({});
  const [checkUseDraft, setCheckUseDraft] = useState(false);
  const [filter] = useFilter();

  const { data: commission, isLoading } = useFetch<TCommission>({
    api: getById || (getById && publishGetId) ? getDetailOfCommission : getDetailOfCommissionPublish,
    queryKeyArr: ['detail-of-commission', id],
    moreParams: { id },
    withFilter: false,
    enabled: !!(getById || publishGetId),
  });
  const dataSource = commission?.data?.data;

  const { data: ExpenseList, isLoading: isLoadingExpense } = useFetch<TExpense>({
    api: getExpenseList,
    queryKeyArr: ['detail-of-ExpenseList', id, checkUseDraft ? {} : filter],
    moreParams: { commissionId: id, ...(checkUseDraft ? {} : filter) },
    withFilter: false,
  });
  const dataSourceExpense = ExpenseList?.data?.data;

  const update = useUpdateField({
    apiQuery: updateCommission,
    keyOfDetailQuery: ['detail-of-commission', id],
  });

  const updateCommissionStatus = useUpdateField({
    apiQuery: updateStatusCommission,
    keyOfDetailQuery: ['detail-of-commission', id],
    isMessageSuccess: false,
  });

  const {
    data: dataDraft,
    isLoading: isLoadingDraft,
    refetch: refetchDraft,
  } = useFetch<TExpense>({
    api: getExpenseDraft,
    queryKeyArr: ['detail-of-ExpenseDraft', id, filter, paramsDraft],
    moreParams: { commissionId: id, ...filter, ...paramsDraft },
    withFilter: false,
    enabled: !!checkUseDraft,
  });
  const dataSourceDraft = dataDraft?.data?.data;

  useEffect(() => {
    if (!dataSource) return;
    const newDataSource = {
      ...dataSource,
      periodObj: {
        periodFrom: dataSource?.periodFrom,
        periodTo: dataSource?.periodTo,
        periodName: dataSource?.periodName,
      },
    };
    setInitialValue(newDataSource);
    form.setFieldsValue(newDataSource);
  }, [dataSource, form]);

  useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  useEffect(() => {
    if (!checkUseDraft) {
      setDataExpenseList(dataSourceExpense);
    } else {
      setDataExpenseList(dataSourceDraft);
    }
  }, [dataSourceDraft, dataSourceExpense, checkUseDraft]);

  const RadioColumns: ColumnsType<TAdjustmentVersion> = [
    {
      title: '',
      dataIndex: 'radio',
      key: 'radio',
      align: 'center',
      width: 48,
      render: (_, record) => (
        <Radio
          checked={selectedAdjustment?.id === record.id}
          onClick={() => {
            if (selectedAdjustment?.id === record.id) {
              setSelectedAdjustment(null);
            } else {
              setSelectedAdjustment(record);
            }
          }}
        />
      ),
    },
    ...versionColumns,
  ];

  const items = [
    getById && {
      label: 'Dữ liệu gốc',
      key: 'expense',
      children: (
        <Table<TOriginalData>
          className="table-expense-list"
          columns={transactionColumns}
          dataSource={dataExpenseList?.expenseList}
          footer={() => (dataExpenseList ? transactionTableFooter(dataExpenseList) : null)}
          loading={isLoadingExpense || isLoadingDraft}
          rowKey={'code'}
          pagination={false}
          scroll={{ x: 2000 }}
        />
      ),
    },

    (getById || publishGetId) && {
      label: 'Dữ liệu điều chỉnh',
      key: 'adjustment',
      children: (
        <Table<TAdjustmentVersion>
          className="table-adjustment-list"
          pagination={false}
          footer={() => <></>}
          rowKey={'id'}
          columns={RadioColumns}
          dataSource={initialValue?.adjustmentVersions}
        />
      ),
    },
  ].filter(Boolean) as TabsProps['items'];

  const handleCancel = () => {
    form.setFieldsValue(initialValue);
    setIsModified(false);
  };

  const validateForm = () => {
    setIsModified(true);
  };

  const handleChangeTab = (key: string) => {
    setTab(key);
  };

  const handleSelectSalePolicy = (value: string) => {
    form.setFieldsValue({ salePolicy: value });
    setIsModified(true);
  };

  const handleSelectSalePos = (value: string) => {
    form.setFieldsValue({ pos: value });
    setIsModified(true);
  };

  const handleSubmit = async () => {
    const allValues = form.getFieldsValue(true);
    const periodObj = form.getFieldValue('periodObj');
    if (!allValues) return;
    const newData = {
      ...allValues,
      id,
      pos: {
        id: allValues.pos?.id,
        name: allValues.pos?.name,
        code: allValues.pos?.code,
      },
      salePolicy: {
        id: allValues.salePolicy?.id,
        name: allValues.salePolicy?.name,
      },
      type: 'FEE',

      year: allValues?.year,
      ...periodObj,
    };
    const res = await update.mutateAsync(newData);
    if (res?.data?.statusCode === '0') {
      setIsModified(false);
    }
  };

  const handleStatusUpdate = async (status: string, successMessage: string) => {
    const idVersion = tab === 'expense' ? dataSource?.adjustmentVersions[0]?.id : selectedAdjustment?.id;

    // Check if selectedAdjustment status and status parameter are both "WAITING"
    if (selectedAdjustment?.status === 'WAITING' && status === 'WAITING') {
      notification.info({
        message: 'Phiên bản đang được phê duyệt',
      });
      return;
    }

    const res = await updateCommissionStatus.mutateAsync({
      id,
      status,
      adjustmentVersionId: idVersion,
    });

    if (res?.data?.statusCode === '0') {
      notification.success({
        message: successMessage,
      });
      setSelectedAdjustment(null);
    }
  };

  const handleCharge = useCallback(async () => {
    try {
      const params = {
        salePolicyId: form.getFieldValue('salePolicy')?.id,
        year: dayjs(form.getFieldValue('year')).format('YYYY'),
        periodForm: form.getFieldValue('periodFrom'),
        periodTo: form.getFieldValue('periodTo'),
      };
      setParamsDraft(params);
      setCheckUseDraft(true);
      !!checkUseDraft && refetchDraft(); // chỉ set khi gọi từ draft
    } catch (error) {
      notification.error({
        message: 'Có lỗi xảy ra!',
      });
    }
  }, [form, notification, refetchDraft, checkUseDraft]);

  return (
    <div>
      <Spin spinning={isLoading || update.isPending}>
        <BreadCrumbComponent titleBread={dataSource?.code} />
        <Form
          form={form}
          layout="vertical"
          onValuesChange={validateForm}
          onFinish={handleSubmit}
          className="wrapper-detail-commission"
        >
          <Title level={5}>Thông tin chi tiết</Title>
          <Col sm={12} xs={24}>
            <Row gutter={24}>
              <Col sm={12} xs={24}>
                <Form.Item label="Mã đợt tính phí/ hoa hồng" required name="code">
                  <Input disabled placeholder="Chọn đơn vị bán hàng" />
                </Form.Item>
              </Col>
              <Col sm={12} xs={24}>
                <Form.Item label="Loại phí" required>
                  <Input disabled value={'Tính phí'} />
                </Form.Item>
              </Col>

              <Col sm={12} xs={24}>
                <Form.Item
                  label="Đơn vị"
                  name="pos"
                  required
                  rules={[{ required: true, message: 'Vui lòng chọn đơn vị' }]}
                >
                  <SingleSelectLazy
                    apiQuery={getOrgChartDropdown}
                    queryKey={['orgChart-exchanges']}
                    keysLabel={'name'}
                    placeholder=" Chọn đơn vị bán hàng"
                    handleSelect={handleSelectSalePos}
                    defaultValues={{ value: pos?.id, label: pos?.name }}
                  />
                </Form.Item>
              </Col>
              <Col sm={12} xs={24}>
                <Form.Item
                  label="Chính sách phí - hoa hồng"
                  required
                  name="salePolicy"
                  rules={[{ required: true, message: 'Vui lòng chọn chính sách phí - hoa hồng' }]}
                >
                  <SingleSelectLazy
                    apiQuery={getSalesPolicyOfCommission}
                    queryKey={['sales-policy']}
                    keysLabel={['code', 'name']}
                    placeholder="Chọn chính sách phí - hoa hồng"
                    defaultValues={{
                      value: salePolicy?.id,
                      label: salePolicy?.name,
                    }}
                    handleSelect={handleSelectSalePolicy}
                  />
                </Form.Item>
              </Col>
              <Col span={24}>
                <FormPeriod required label="Kỳ tính phí" fieldPos="pos" />
                {permissionUpdate && (
                  <Form.Item>
                    <Button type="primary" onClick={() => handleCharge()}>
                      Tính phí
                    </Button>
                  </Form.Item>
                )}
              </Col>
            </Row>
          </Col>
        </Form>
        <Title level={5}>Danh sách giao dịch</Title>
        <Flex justify="space-between">
          <FilterTransaction />
          <GroupButton
            modal={modal}
            notification={notification}
            versionAdjustment={selectedAdjustment}
            tab={tab}
            handleStatusUpdate={handleStatusUpdate}
          />
        </Flex>
        <Tabs className="tabs-transaction" type="card" onChange={handleChangeTab} items={items} />
        {isModified && permissionUpdate && (
          <ButtonOfPageDetail
            handleSubmit={() => form.submit()}
            handleCancel={handleCancel}
            loadingSubmit={update.isPending}
          />
        )}
      </Spin>
    </div>
  );
};

export default DetailOfCommission;
