import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Button, Space } from 'antd';
import { ButtonType } from 'antd/es/button';
import { NotificationInstance } from 'antd/es/notification/interface';
import { HookAPI } from 'antd/lib/modal/useModal';
import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import { modalConfirm } from '../../../../components/modal/specials/ModalConfirm';
import { PERMISSION_COMMISSION } from '../../../../constants/permissions/commission';
import { FetchResponse, TDataList, useCheckPermissions, useUpdateField } from '../../../../hooks';
import { downloadListTransaction, updateAnNounced } from '../../../../service/commission';
import { TAdjustmentVersion, TCommission } from '../../../../types/commission';
import { downloadArrayBufferFile } from '../../../../utilities/shareFunc';
import UploadFileVersionAdjustment from './UploadFileVersionAdjustment';

interface GroupButtonProps {
  handleStatusUpdate: (status: string, successMessage: string) => Promise<void>;
  modal: HookAPI;
  notification: NotificationInstance;
  tab: string;
  versionAdjustment?: TAdjustmentVersion | null;
}
interface ButtonConfig {
  key: string;
  label: string;
  permission?: boolean;
  onClick?: () => void;
  type?: ButtonType;
  component?: React.ReactNode;
  loading?: boolean;
}
export const GroupButton: React.FC<GroupButtonProps> = ({
  handleStatusUpdate,
  modal,
  notification,
  tab,
  versionAdjustment,
}) => {
  const { id } = useParams();
  const [isDownloadData, setIsDownloadData] = useState(false);

  const { publish, create, update, getById, approve } = useCheckPermissions(PERMISSION_COMMISSION);

  const queryClient = useQueryClient();
  const data = queryClient.getQueryData<FetchResponse<TDataList<TCommission>>>(['detail-of-commission', id]);
  const dataCommission = data?.data?.data;
  const v1Adjustment = dataCommission?.adjustmentVersions?.[0];
  const exportListTransaction = useMutation({
    mutationFn: () => downloadListTransaction({ commissionId: id }),
  });

  const mutateAnNounced = useUpdateField({
    apiQuery: updateAnNounced,
    keyOfDetailQuery: ['detail-of-commission', id],
    messageSuccess: 'Công bố kỳ tính phí thành công',
  });

  const handleDownloadOriginal = async () => {
    try {
      setIsDownloadData(true);
      const response = await exportListTransaction.mutateAsync();
      downloadArrayBufferFile({ data: response.data, fileName: `Commission_${dataCommission?.period}.xlsx` });
    } catch (error) {
      notification.error({ message: 'Xuất dữ liệu thất bại.' });
    } finally {
      setIsDownloadData(false);
    }
  };

  // Danh sách các nút với điều kiện hiển thị
  const buttonConfigs: ButtonConfig[] = [
    {
      key: 'approve',
      label: 'Duyệt',
      permission:
        (versionAdjustment?.status === 'WAITING' || (v1Adjustment?.status === 'WAITING' && tab === 'expense')) &&
        approve,
      onClick: () => {
        modalConfirm({
          title: 'Duyệt đợt tính phí',
          content: 'Bạn có muốn duyệt đợt tính phí không?',
          handleConfirm: () => {
            handleStatusUpdate('APPROVED', 'Duyệt thành công');
          },
          modal,
        });
      },
      type: 'default',
    },
    {
      key: 'sendApprove',
      label: 'Gửi duyệt',
      permission: !!(
        (versionAdjustment && versionAdjustment?.status !== 'APPROVED') ||
        (v1Adjustment && v1Adjustment?.status !== 'APPROVED' && tab === 'expense')
      ),
      onClick: () => {
        modalConfirm({
          title: 'Gửi duyệt đợt tính phí',
          content: 'Bạn có muốn gửi duyệt đợt tính phí không?',
          handleConfirm: () => {
            handleStatusUpdate('WAITING', 'Gửi duyệt thành công');
          },
          modal,
        });
      },
      type: 'primary',
    },
    {
      key: 'publish',
      label: 'Công bố',
      permission: publish,
      onClick: () => {
        modalConfirm({
          title: 'Xác nhận công bố',
          content: 'Bạn có muốn công bố đợt tính phí không?',
          handleConfirm: async () => {
            await mutateAnNounced.mutateAsync(id);
          },
          modal,
        });
      },
      type: 'default',
    },
    {
      key: 'reject',
      label: 'Từ chối ',
      permission:
        (versionAdjustment?.status === 'WAITING' || (v1Adjustment?.status === 'WAITING' && tab === 'expense')) &&
        approve,
      onClick: () => {
        modalConfirm({
          title: 'Từ chối đợt tính phí',
          content: 'Bạn có muốn từ chối đợt tính phí không?',
          handleConfirm: () => {
            handleStatusUpdate('REJECTED', 'Từ chối thành công');
          },
          modal,
        });
      },
      type: 'default',
    },
    {
      key: 'uploadTemplate',
      label: 'Tải nhập giao dịch',
      permission: true,
      component: <UploadFileVersionAdjustment />,
    },
    {
      key: 'downloadOriginal',
      label: 'Tải về dữ liệu gốc',
      permission: create || update || getById,
      onClick: handleDownloadOriginal,
      loading: isDownloadData,
      type: 'default',
    },
  ];

  return (
    <Space>
      {buttonConfigs.map(button => {
        if (!button.permission) return null;

        return (
          <React.Fragment key={button.key}>
            {button.component ? (
              button.component
            ) : (
              <Button className={button.key} loading={button?.loading} type={button.type} onClick={button.onClick}>
                {button.label}
              </Button>
            )}
          </React.Fragment>
        );
      })}
    </Space>
  );
};
