import { Button, notification, Typography, Upload } from 'antd';
import { RcFile } from 'antd/es/upload';
import { useParams } from 'react-router-dom';
import { FetchResponse, TDataList, useUpdateField } from '../../../../hooks';
import { updateCommission } from '../../../../service/commission';
import { uploadMedia } from '../../../../service/upload';
import { AxiosError } from 'axios';
import { TCommission } from '../../../../types/commission';
import { useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';
import { COMMISSION_HISTORY_IMPORT } from '../../../../configs/path';

const UploadFileVersionAdjustment = () => {
  const { id } = useParams();
  const queryClient = useQueryClient();

  const [isLoadingImport, setIsLoadingImport] = useState(false);

  const data = queryClient.getQueryData<FetchResponse<TDataList<TCommission>>>(['detail-of-commission', id]);
  const dataCommission = data?.data?.data;

  const mutateListTransaction = useUpdateField({
    apiQuery: updateCommission,
    keyOfDetailQuery: ['detail-of-commission', id],
    isMessageSuccess: false,
    isMessageError: false,
    isShowMessage: false,
  });

  const handleBeforeUpload = async (file: RcFile) => {
    const isAllowedType = file?.name.toLowerCase().endsWith('.xlsx');
    if (!isAllowedType) {
      notification.error({ message: 'File không đúng định dạng. Vui lòng sử dụng file .xlsx' });
      return Upload.LIST_IGNORE;
    }
    return true;
  };
  return (
    <Upload
      beforeUpload={handleBeforeUpload}
      showUploadList={false}
      customRequest={async ({ file, onSuccess, onError }) => {
        try {
          setIsLoadingImport(true);
          const response = await uploadMedia(file as RcFile, 'transaction-commission');
          const { data } = response.data;
          const fileName = data?.Key.replace('/', '_')?.replace('.xlsx', '');
          const res = await mutateListTransaction.mutateAsync({
            id,
            adjustmentVersions: [...(dataCommission?.adjustmentVersions || []), { fileUrl: data.Location, fileName }],
          });
          const status = res?.data?.statusCode;

          const link = (
            <Typography.Link href={COMMISSION_HISTORY_IMPORT} target="_blank">
              đây
            </Typography.Link>
          );
          if (status === '0') {
            notification.success({
              message: <>Hoàn thành upload giao dịch</>,
            });
          } else {
            notification.error({
              message: <>Upload thất bại, xem tại {link}</>,
            });
          }
          onSuccess && onSuccess('ok');
        } catch (error: unknown) {
          onError && onError(error as AxiosError);
        } finally {
          setIsLoadingImport(false);
        }
      }}
      name="file-upload"
    >
      <Button type="default" loading={isLoadingImport}>
        Tải nhập giao dịch
      </Button>
    </Upload>
  );
};

export default UploadFileVersionAdjustment;
