import { useQueries } from '@tanstack/react-query';
import { Form } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import DropdownFilterSearch from '../../../../components/dropdown/dropdownFilterSearch';
import MultiSelectLazy from '../../../../components/select/mutilSelectLazy';
import { getAllProjectsOfCommission, getByIdProjectsOfCommission } from '../../../../service/commission';
import useFilter from '../../../../hooks/filter';
import { URLSearchParamsInit } from 'react-router-dom';

type TFilterTransaction = {
  projectId?: string | string[];
  search?: string;
};

const FilterTransaction = () => {
  const [form] = Form.useForm();
  const [filter, setFilter] = useFilter({});
  const [initialValues, setInitialValues] = useState<TFilterTransaction>();
  const [isOpenFilter, setIsOpenFilter] = useState(false);
  const arrayProjectIds = useMemo(
    () => (typeof filter?.projectId === 'string' ? filter?.projectId?.split(',') || [] : filter?.projectId),
    [filter?.projectId],
  );

  const queryResults = useQueries({
    queries: (arrayProjectIds || []).map(id => ({
      queryKey: ['Project-commission', id],
      queryFn: () => getByIdProjectsOfCommission(id),
      enabled: !!id, // Chỉ chạy nếu có id
    })),
  });

  const projectSelected = queryResults
    .filter(query => query.data) // Chỉ lấy những query đã có dữ liệu
    .map(query => query?.data?.data?.data);

  const formatProjectsSelected = projectSelected.map(item => ({
    ...item,
    label: item?.name as string,
    value: item?.id as string,
  }));

  useEffect(() => {
    const initialValue = {
      projectId: arrayProjectIds || undefined,
    };
    setInitialValues(initialValue);
    form.setFieldsValue(initialValue);
  }, [form, arrayProjectIds]);

  const handleSubmitFilter = (values: TFilterTransaction) => {
    const projectIdToString =
      typeof values?.projectId === 'string' ? values?.projectId : values?.projectId?.join(',') || null;
    setFilter({ ...filter, projectId: projectIdToString } as URLSearchParamsInit);
    setIsOpenFilter(false);
  };

  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };

  const handleClearFilters = () => {
    setInitialValues(undefined);
    setFilter({ search: filter?.search ?? '' } as URLSearchParamsInit);
    setTimeout(() => {
      setIsOpenFilter(false);
    }, 100);
  };

  const handleSelectProject = (values: unknown) => {
    const newProjectFilter = (values as { value: string }[]).map(item => item.value);
    form.setFieldsValue({ projectId: newProjectFilter });
  };

  const handleSearchExpense = (value: string) => {
    const search = value ? value : '';
    setFilter({ ...filter, search: search } as URLSearchParamsInit);
  };

  return (
    <>
      <DropdownFilterSearch
        rootClassName="wrapper-filter"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        initialValues={initialValues}
        form={form}
        onClearFilters={handleClearFilters}
        onChangeSearch={handleSearchExpense}
        showParams={false}
        defaultValueSearch={filter?.search}
        extraFormItems={
          <>
            <Form.Item label="Chọn dự án" name="projectId">
              <MultiSelectLazy
                apiQuery={getAllProjectsOfCommission}
                queryKey={['list-projects-commission']}
                enabled={isOpenFilter}
                placeholder="Chọn dự án"
                keysLabel={['name']}
                keysTag={['name']}
                handleListSelect={handleSelectProject}
                defaultValues={formatProjectsSelected}
              />
            </Form.Item>
          </>
        }
      />
    </>
  );
};

export default FilterTransaction;
