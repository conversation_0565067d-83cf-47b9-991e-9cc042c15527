import { <PERSON><PERSON>, Col, Form, Modal, Row } from 'antd';
import SingleSelectLazy from '../../../components/select/singleSelectLazy';
import { COMMISSION } from '../../../configs/path';
import { useCreateField } from '../../../hooks';
import { createCommission, getSalesPolicyOfCommission } from '../../../service/commission';
import { getOrgChartDropdown } from '../../../service/lead';
import FormPeriod from '../../../components/select/FormPeriod';
import './styles.scss';
import { showConfirmCancelModal } from '../../../components/modal/specials/ConfirmCancelModal';
import { useBeforeUnload } from 'react-router-dom';

interface Props {
  handleCancel: () => void;
  open: boolean;
}

const CreateCommission = (props: Props) => {
  const { handleCancel, open } = props;
  const [form] = Form.useForm();

  const { mutateAsync, isPending } = useCreateField({
    apiQuery: createCommission,
    keyOfListQuery: ['list-of-commission'],
    isMessageError: false,
  });

  const handleSubmit = async () => {
    const allValues = form.getFieldsValue(true);
    const periodObj = form.getFieldValue('periodObj');
    const newData = {
      year: allValues?.year,
      pos: {
        id: allValues.pos?.id,
        name: allValues.pos?.name,
        code: allValues.pos?.code,
      },
      salePolicy: {
        id: allValues.salePolicy?.id,
        name: allValues.salePolicy?.name,
      },
      type: 'FEE',
      ...periodObj,
    };

    const res = await mutateAsync(newData);

    const idCommission = res?.data?.data ? (res.data.data as { id: string }).id : undefined;
    if (res?.data?.statusCode === '0') {
      handleCancel();
      form.resetFields();
      window.open(`${COMMISSION}/${idCommission}`, '_blank', 'noopener,noreferrer');
    }
  };

  const handleSelectSalePolicy = (value: string) => {
    form.setFieldsValue({ salePolicy: value });
  };

  const handleSelectSalePos = (value: string) => {
    form.setFieldsValue({ pos: value });
  };

  useBeforeUnload(event => {
    if (open && form.isFieldsTouched()) {
      event.preventDefault();
      return true;
    }
    return undefined;
  });

  return (
    <Modal
      className="wrapper-modal-create-commission"
      title="Tạo mới kỳ tính phí"
      open={open}
      footer={[
        <Button type="primary" loading={isPending} onClick={() => form.submit()}>
          Tạo mới
        </Button>,
      ]}
      onCancel={() => {
        if (!form.isFieldsTouched()) {
          handleCancel();
          return;
        }
        showConfirmCancelModal({
          onConfirm: handleCancel,
        });
      }}
      destroyOnClose
      maskClosable={false}
      afterClose={() => form.resetFields()}
      width={645}
    >
      <Form form={form} onFinish={handleSubmit} requiredMark={false} layout="vertical">
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item label="Đơn vị" name="pos" rules={[{ required: true, message: 'Vui lòng chọn đơn vị' }]}>
              <SingleSelectLazy
                apiQuery={getOrgChartDropdown}
                queryKey={['orgChart-exchanges']}
                keysLabel={'name'}
                enabled={open}
                placeholder="Chọn đơn vị"
                handleSelect={handleSelectSalePos}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Chính sách phí - hoa hồng"
              name="salePolicy"
              rules={[{ required: true, message: 'Vui lòng chọn chính sách phí - hoa hồng' }]}
            >
              <SingleSelectLazy
                apiQuery={getSalesPolicyOfCommission}
                queryKey={['sales-policy']}
                enabled={open}
                keysLabel={['code', 'name']}
                placeholder="Chọn chính sách phí - hoa hồng"
                handleSelect={handleSelectSalePolicy}
              />
            </Form.Item>
          </Col>

          <Col span={24}>
            <FormPeriod label="Kỳ tính phí" fieldPos="pos" />
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

export default CreateCommission;
