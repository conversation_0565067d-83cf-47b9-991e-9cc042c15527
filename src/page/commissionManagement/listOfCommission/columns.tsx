import { ColumnsType } from 'antd/es/table';
import { TListOfCommission } from '../../../types/commission';
import { Typography } from 'antd';
import dayjs from 'dayjs';
import { FORMAT_DATE } from '../../../constants/common';

const { Text } = Typography;

export const columns: ColumnsType<TListOfCommission> = [
  {
    title: 'Mã đợt phí',
    dataIndex: 'code',
    key: 'code',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Đơn vị bán hàng',
    dataIndex: ['pos', 'name'],
    key: 'pos',
    render: (value: string) => (value ? value : '-'),
  },

  {
    title: 'Kỳ phí',
    dataIndex: 'period',
    key: 'period',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Ngày tạo',
    dataIndex: 'createdDate',
    key: 'createdDate',
    render: (value: string, record: TListOfCommission) =>
      value ? (
        <>
          <Text>{dayjs(value).format(FORMAT_DATE)}</Text>
          <br />
          <Text>{record?.createdBy?.fullName ? record.createdBy.fullName : '-'}</Text>
        </>
      ) : (
        '-'
      ),
  },
  {
    title: 'Ngày cập nhật',
    dataIndex: 'modifiedDate',
    key: 'modifiedDate',
    render: (value: string, record: TListOfCommission) =>
      value ? (
        <>
          <Text>{dayjs(value).format(FORMAT_DATE)}</Text>
          <br />
          <Text>{record?.modifiedBy?.fullName ? record.modifiedBy.fullName : '-'}</Text>
        </>
      ) : (
        '-'
      ),
  },
];
