import { DatePicker, Form, Row } from 'antd';
import { useCallback, useEffect, useRef, useState } from 'react';
// import { useLocation } from 'react-router-dom';
import dayjs, { Dayjs } from 'dayjs';
import useFilter from '../../../../hooks/filter';
import { DEFAULT_PARAMS } from '../../../../constants/common';
import DropdownFilterSearch from '../../../../components/dropdown/dropdownFilterSearch';
import MultiSelectLazy from '../../../../components/select/mutilSelectLazy';
import {
  getListAccountExternal,
  getListAccountInternal,
  getListOrgchartExternal,
  getListOrgchartInternal,
} from '../../../../service/offer';
import { getListProjectHistory } from '../../../../service/uploadHistory';
import { useStoreOfferOrderMoneyAccountancy } from '../../storeOfferOrderMoney';
import './styles.scss';
import MultilSelectDropdownLazy from '../../../../components/select/multilSelectDropdownLazy';
import MultiSelectStatic from '../../../../components/select/mutilSelectStatic';
const { RangePicker } = DatePicker;

type TFilter = {
  createdFrom?: string | Dayjs | null;
  createdTo?: string | Dayjs | null;
  receiptDateFrom?: string | Dayjs | null;
  receiptDateTo?: string | Dayjs | null;
  types?: string[];
  orgchartIds?: string[];
  projectIds?: string[];
  createdByIds?: string[];
  statuses?: string[];
};

function FilterSearch() {
  // const { search } = useLocation();
  const [form] = Form.useForm();
  // const params = useMemo(() => new URLSearchParams(search), [search]);
  const [, setFilter] = useFilter();

  const [initialValues, setInitialValues] = useState<TFilter>();
  const [isOpenFilter, setIsOpenFilter] = useState<boolean>(false);
  const createRangePickerRef = useRef<React.ComponentRef<typeof DatePicker.RangePicker>>(null);
  const receiveRangePickerRef = useRef<React.ComponentRef<typeof DatePicker.RangePicker>>(null);
  const [createDates, setCreateDates] = useState<[Dayjs | null, Dayjs | null]>([null, null]);
  const [receiveDates, setReceiveDates] = useState<[Dayjs | null, Dayjs | null]>([null, null]);
  const { setTabFilter, tab, getCurrentFilter } = useStoreOfferOrderMoneyAccountancy();

  // useEffect(() => {
  //   if (params) {
  //     const initialValue = {
  //       createdFrom: params.get('createdFrom') ? dayjs(params.get('createdFrom')) : undefined,
  //       createdTo: params.get('createdTo') ? dayjs(params.get('createdTo')) : undefined,
  //       receiptDateFrom: params.get('receiptDateFrom') ? dayjs(params.get('receiptDateFrom')) : undefined,
  //       receiptDateTo: params.get('receiptDateTo') ? dayjs(params.get('receiptDateTo')) : undefined,
  //       statuses: params.get('statuses') ? params.get('statuses')?.split(',') : undefined,
  //       orgchartIds: params.get('orgchartIds') ? params.get('orgchartIds')?.split(',') : undefined,
  //       types: params.get('types') ? params.get('types')?.split(',') : undefined,
  //       projectIds: params.get('projectIds') ? params.get('projectIds')?.split(',') : undefined,
  //       createdByIds: params.get('createdByIds') ? params.get('createdByIds')?.split(',') : undefined,
  //     };
  //     setInitialValues(initialValue);
  //     form.setFieldsValue(initialValue);
  //   }
  // }, [params]);

  const handleSubmitFilter = (values: TFilter) => {
    const [receiptDateFrom, receiptDateTo] = receiveDates;
    const [createdFrom, createdTo] = createDates;

    const newFilter: Record<string, string> = {
      createdFrom: createdFrom ? dayjs(createdFrom)?.startOf('day').toISOString() : '',
      createdTo: createdTo ? dayjs(createdTo).endOf('day').toISOString() : '',
      receiptDateFrom: receiptDateFrom ? dayjs(receiptDateFrom)?.startOf('day').toISOString() : '',
      receiptDateTo: receiptDateTo ? dayjs(receiptDateTo).endOf('day').toISOString() : '',
      statuses: values?.statuses?.join(',') || '',
      types: values?.types?.join(',') || '',
      createdByIds: values?.createdByIds?.join(',') || '',
      orgchartIds: values?.orgchartIds?.join(',') || '',
      projectIds: values?.projectIds?.join(',') || '',
      tab: tab,
    };
    setTabFilter(tab, { ...getCurrentFilter(), ...newFilter });
    setFilter({ ...DEFAULT_PARAMS, tab: tab });
    setIsOpenFilter(false);
  };

  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };

  const handleClearFilters = () => {
    setInitialValues(undefined);
    setTabFilter(tab, { tab: tab, search: getCurrentFilter()?.search });
    setTimeout(() => {
      setIsOpenFilter(false);
    }, 100);
  };

  const handleSearch = (value: string) => {
    const search = value ? value : '';
    setFilter({ ...DEFAULT_PARAMS, tab: tab });
    setTabFilter(tab, { ...getCurrentFilter(), search });
  };

  const handleSelectStatuses = (values: unknown) => {
    const newStatusFilter = (values as { value: string }[]).map(item => item.value);
    form.setFieldsValue({ statuses: newStatusFilter });
  };

  const handleSelectTypeOffer = (values: unknown) => {
    const newTypeFilter = (values as { value: string }[]).map(item => item.value);
    form.setFieldsValue({ types: newTypeFilter });
  };

  const handleSelectOrgchart = (values: unknown) => {
    const newOrgChartFilter = (values as { value: string }[]).map(item => item.value);
    form.setFieldsValue({ orgchartIds: newOrgChartFilter });
  };

  const handleSelectProject = (values: unknown) => {
    const newProjectFilter = (values as { value: string }[]).map(item => item.value);
    form.setFieldsValue({ projectIds: newProjectFilter });
  };

  const handleSelectAccount = (values: unknown) => {
    const newAccountFilter = (values as { value: string }[]).map(item => item.value);
    form.setFieldsValue({ createdByIds: newAccountFilter });
  };

  const handleCreateCalendarChange = useCallback(
    (value: [Dayjs | null, Dayjs | null] | null) => {
      if (!value) {
        if (createDates[0] !== null || createDates[1] !== null) {
          setCreateDates([null, null]);
        }
        return;
      }

      const newStart = value[0];
      const newEnd = value[1];

      if (
        (newStart?.isSame(createDates[0], 'day') || (!newStart && !createDates[0])) &&
        (newEnd?.isSame(createDates[1], 'day') || (!newEnd && !createDates[1]))
      ) {
        return;
      }

      setCreateDates([newStart, newEnd]);
    },
    [createDates],
  );
  const handleCreateRangePickerChange = useCallback(
    (value: [Dayjs | null, Dayjs | null] | null) => {
      if (!value) {
        setCreateDates([null, null]);
        form.setFieldsValue({
          createdTo: undefined,
          createdFrom: undefined,
        });
        return;
      }

      const newStart = value[0];
      const newEnd = value[1];

      setCreateDates([newStart, newEnd]);
      // Cập nhật form mỗi khi chọn giá trị
      form.setFieldsValue({
        createdTo: newStart,
        createdFrom: newEnd,
      });
    },
    [form],
  );

  const handleReceiveCalendarChange = useCallback(
    (value: [Dayjs | null, Dayjs | null] | null) => {
      if (!value) {
        if (receiveDates[0] !== null || receiveDates[1] !== null) {
          setReceiveDates([null, null]);
        }
        return;
      }

      const newStart = value[0];
      const newEnd = value[1];

      if (
        (newStart?.isSame(receiveDates[0], 'day') || (!newStart && !receiveDates[0])) &&
        (newEnd?.isSame(receiveDates[1], 'day') || (!newEnd && !receiveDates[1]))
      ) {
        return;
      }

      setReceiveDates([newStart, newEnd]);
    },
    [receiveDates],
  );

  const handleReceiveRangePickerChange = useCallback(
    (value: [Dayjs | null, Dayjs | null] | null) => {
      if (!value) {
        setReceiveDates([null, null]);
        // Đảm bảo cập nhật giá trị trong form
        form.setFieldsValue({
          receiptDateTo: undefined,
          receiptDateFrom: undefined,
        });
        return;
      }

      const newStart = value[0];
      const newEnd = value[1];

      setReceiveDates([newStart, newEnd]);
      // Cập nhật form mỗi khi chọn giá trị
      form.setFieldsValue({
        receiptDateTo: newStart,
        receiptDateFrom: newEnd,
      });
    },
    [form],
  );

  const handleCreateBlur = useCallback(
    (e: FocusEvent, index: 0 | 1) => {
      const target = e.target as HTMLInputElement;
      const value = target.value;

      if (!value && createDates[index] !== null) {
        const newDates = [...createDates] as [Dayjs | null, Dayjs | null];
        newDates[index] = null;
        setCreateDates(newDates);
      }
    },
    [createDates],
  );

  const handleReceiveBlur = useCallback(
    (e: FocusEvent, index: 0 | 1) => {
      const target = e.target as HTMLInputElement;
      const value = target.value;

      if (!value && receiveDates[index] !== null) {
        const newDates = [...receiveDates] as [Dayjs | null, Dayjs | null];
        newDates[index] = null;
        setReceiveDates(newDates);
      }
    },
    [receiveDates],
  );

  useEffect(() => {
    const pickerNode = createRangePickerRef.current?.nativeElement as HTMLElement | null;
    const inputs = pickerNode?.querySelectorAll<HTMLInputElement>('input');

    if (inputs?.length === 2) {
      const input0 = inputs[0];
      const input1 = inputs[1];

      const blurHandler0 = (e: FocusEvent) => handleCreateBlur(e, 0);
      const blurHandler1 = (e: FocusEvent) => handleCreateBlur(e, 1);

      input0.addEventListener('blur', blurHandler0);
      input1.addEventListener('blur', blurHandler1);

      return () => {
        input0.removeEventListener('blur', blurHandler0);
        input1.removeEventListener('blur', blurHandler1);
      };
    }
  }, [handleCreateBlur]);

  useEffect(() => {
    const pickerNode = receiveRangePickerRef.current?.nativeElement as HTMLElement | null;
    const inputs = pickerNode?.querySelectorAll<HTMLInputElement>('input');

    if (inputs?.length === 2) {
      const input0 = inputs[0];
      const input1 = inputs[1];

      const blurHandler0 = (e: FocusEvent) => handleReceiveBlur(e, 0);
      const blurHandler1 = (e: FocusEvent) => handleReceiveBlur(e, 1);

      input0.addEventListener('blur', blurHandler0);
      input1.addEventListener('blur', blurHandler1);

      return () => {
        input0.removeEventListener('blur', blurHandler0);
        input1.removeEventListener('blur', blurHandler1);
      };
    }
  }, [handleReceiveBlur]);

  return (
    <>
      <DropdownFilterSearch
        rootClassName="wrapper-filter-offer-order"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        showParams={false}
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        initialValues={initialValues}
        form={form}
        onClearFilters={handleClearFilters}
        onChangeSearch={handleSearch}
        extraFormItems={
          <>
            <Form.Item label="Loại đề nghị" name="types">
              <MultiSelectStatic
                data={[
                  { label: 'Hợp đồng dịch vụ', value: 'PRIMARY_CONTRACT' },
                  { label: 'Yêu cầu đặt chỗ', value: 'YCDCH' },
                  { label: 'Yêu cầu đặt cọc', value: 'YCDC' },
                ]}
                handleListSelect={handleSelectTypeOffer}
                placeholder="Chọn loại đề nghị"
                keysTag={'label'}
              />
            </Form.Item>

            {tab === 'TRANSFERED' && (
              <Form.Item label="Trạng thái" name="statuses">
                <MultiSelectStatic
                  data={[
                    { label: 'Đã duyệt', value: 'TRANSFERED' },
                    { label: 'Bị từ chối', value: 'PROCESSING' },
                    { label: 'Đã hoàn', value: 'REFUNDED' },
                    { label: 'Đã hủy', value: 'CANCELED' },
                  ]}
                  handleListSelect={handleSelectStatuses}
                  placeholder="Chọn trạng thái"
                  keysTag={'label'}
                />
              </Form.Item>
            )}

            <Form.Item label="Người tạo" name="createdByIds">
              <MultilSelectDropdownLazy
                queryKey={['get-account']}
                selectedType="option1"
                apiQuery1={getListAccountInternal}
                apiQuery2={getListAccountExternal}
                textOption1="Nhân viên"
                textOption2="Người dùng ngoài cơ cấu"
                keysLabelOption1={['code', 'name']}
                keysTagOption1={['code', 'name']}
                keysLabelOption2={['code', 'name']}
                keysTagOption2={['code', 'name']}
                handleListSelect={handleSelectAccount}
                placeholder="Chọn người tạo"
              />
            </Form.Item>
            <Form.Item label="Đơn vị bán hàng" name="orgchartIds">
              <MultilSelectDropdownLazy
                queryKey={['get-orgchart']}
                selectedType="option1"
                textOption1="Đơn vị"
                textOption2="Đối tác hợp tác"
                apiQuery1={getListOrgchartInternal}
                apiQuery2={getListOrgchartExternal}
                keysLabelOption1={['code', 'nameVN']}
                keysTagOption1={['code', 'nameVN']}
                keysLabelOption2={['partnershipCode', 'partnershipName']}
                keysTagOption2={['partnershipCode', 'partnershipName']}
                handleListSelect={handleSelectOrgchart}
                placeholder="Chọn đơn vị bán hàng"
              />
            </Form.Item>
            <Form.Item label="Dự án" name="projectIds">
              <MultiSelectLazy
                enabled={isOpenFilter}
                queryKey={['get-project']}
                apiQuery={getListProjectHistory}
                keysLabel={['code', 'name']}
                keysTag={['name']}
                handleListSelect={handleSelectProject}
                placeholder="Chọn dự án"
              />
            </Form.Item>

            {tab === 'TRANSFERED' ? (
              <Form.Item label="Khoảng thời gian nhận tiền">
                <RangePicker
                  ref={receiveRangePickerRef}
                  value={receiveDates}
                  onCalendarChange={handleReceiveCalendarChange}
                  onChange={handleReceiveRangePickerChange}
                  allowClear
                  placeholder={['Từ ngày', 'Đến ngày']}
                  format="DD/MM/YYYY"
                />
              </Form.Item>
            ) : (
              <Form.Item label="Khoảng thời gian tạo">
                <RangePicker
                  ref={createRangePickerRef}
                  value={createDates}
                  onCalendarChange={handleCreateCalendarChange}
                  onChange={handleCreateRangePickerChange}
                  allowClear
                  placeholder={['Từ ngày', 'Đến ngày']}
                  format="DD/MM/YYYY"
                />
              </Form.Item>
            )}
            <Row gutter={16}></Row>
          </>
        }
      />
    </>
  );
}
export default FilterSearch;
