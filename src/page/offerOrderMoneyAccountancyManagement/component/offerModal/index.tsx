import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Modal, Input, Radio, Checkbox, Button, Form, Row, Col, Select, InputNumber, DatePicker } from 'antd';
import './styles.scss';
import { MutationFunction } from '@tanstack/react-query';
import ConfirmDeleteModal from '../../../../components/modal/specials/ConfirmDeleteModal';
import { approveOffer, rejectOffer } from '../../../../service/offer';
import { IOfferOrderMoneyAccountancy } from '../../../../types/offer';
import { useUpdateField } from '../../../../hooks';
import { useStoreOfferOrderMoneyAccountancy } from '../../storeOfferOrderMoney';
import { formatNumber } from '../../../../utilities/regex';
import dayjs from 'dayjs';

interface State {
  label: string;
  value: string;
}
interface PayBy {
  label: string;
  value: string;
}
interface FooterButton {
  label: string;
  onClick: () => void;
  type?: 'default' | 'primary' | 'dashed' | 'link' | 'text';
}
interface OfferModalComponentProps {
  visible: boolean;
  onOk: () => void;
  onCancel: () => void;
  title?: string;
  initialValues?: IOfferOrderMoneyAccountancy;
  onValuesChange: (values: IOfferOrderMoneyAccountancy) => void;
  footerButtons?: FooterButton[];
  states?: State[];
  payBys?: PayBy[];
}

const OfferModalComponent: React.FC<OfferModalComponentProps> = ({
  visible,
  onOk,
  onCancel,
  title = 'Điều chỉnh đề nghị thu tiền',
  initialValues = {},
  onValuesChange,
  payBys = [
    { label: 'Khách hàng', value: 'customer' },
    { label: 'Đơn vị bán hàng', value: 'pos' },
  ],
  states = [
    { label: 'Chuyển khoản', value: 'TRANSFER' },
    { label: 'Tiền mặt', value: 'CASH' },
    { label: 'Cà thẻ', value: 'DVBH' },
    { label: 'Đã thu CĐT', value: 'INVESTOR' },
    { label: 'Đã thu ĐVBH', value: 'POS' },
  ],
}) => {
  const [form] = Form.useForm();
  const state = Form.useWatch(['state'], form);
  const allowDuplicate = Form.useWatch(['allowDuplicate'], form);
  const payBy = Form.useWatch(['payBy'], form);
  const payer = Form.useWatch(['payer'], form);
  const receiptDate = Form.useWatch(['receiptDate'], form);
  const description = Form.useWatch(['description'], form);
  const bank = Form.useWatch(['bank'], form);
  const transferedMoney = Form.useWatch(['transferedMoney'], form);
  const [isOpenModalReject, setIsOpenModalReject] = useState<boolean>(false);
  const { tab } = useStoreOfferOrderMoneyAccountancy();

  const isApproveDisabled = useMemo(() => {
    const isTransferInvalid = state === 'TRANSFER' && !bank;
    const isTransferedMoneyInvalid = allowDuplicate && (transferedMoney === undefined || transferedMoney === null);
    return !payer || !receiptDate || !description || !state || !payBy || isTransferInvalid || isTransferedMoneyInvalid;
  }, [payer, receiptDate, description, state, payBy, bank, allowDuplicate, transferedMoney]);

  useEffect(() => {
    if (visible) {
      form.resetFields();
      const customer = initialValues?.propertyTicket?.customer;
      const corporate = initialValues?.pos;
      const isCorporate = customer?.type === 'business';
      const customerName = isCorporate ? corporate?.name || '' : customer?.personalInfo?.name || '';
      const customerCode = customer?.personalInfo?.code || corporate?.code || '';

      const defaultDescription =
        initialValues.description == null
          ? `Thu tiền từ khách hàng ${customerName} (${customerCode})`
          : initialValues.description;
      const defaultState = initialValues.state ?? 'TRANSFER';
      const defaultPayBy = 'customer';
      const defaultPayer = defaultPayBy === 'customer' ? customerName : corporate?.name || '';

      form.setFieldsValue({
        payBy: defaultPayBy,
        payer: defaultPayer,
        receiptDate: initialValues?.receiptDate ? dayjs(initialValues?.receiptDate) : null,
        description: defaultDescription,
        state: defaultState,
        receiptNum: initialValues?.receiptNum || undefined,
        transferedMoney: initialValues?.transferedMoney,
      });
    }
  }, [initialValues, form, visible]);

  useEffect(() => {
    if (!allowDuplicate) {
      form.setFieldsValue({ transferedMoney: initialValues?.transferedMoney });
    }
  }, [allowDuplicate, form, initialValues]);

  useEffect(() => {
    if (state !== 'TRANSFER') {
      form.setFieldsValue({ bank: undefined });
    }
  }, [state, form]);

  useEffect(() => {
    if (payBy) {
      const customer = initialValues?.propertyTicket?.customer;
      const corporate = initialValues?.pos;
      const isCorporate = customer?.type === 'business';
      const payerValue =
        payBy === 'customer'
          ? isCorporate
            ? corporate?.name || ''
            : customer?.personalInfo?.name || ''
          : corporate?.name || '';
      form.setFieldsValue({ payer: payerValue });
    }
  }, [payBy, form, initialValues]);

  const { mutateAsync: approve } = useUpdateField<IOfferOrderMoneyAccountancy>({
    keyOfListQuery: ['get-offer-order', tab],
    apiQuery: approveOffer,
    isMessageError: false,
    messageSuccess: 'Duyệt đề nghị thu tiền thành công!',
  });

  const approveOfferOder = async () => {
    const values = await form.validateFields();
    const payload = {
      id: initialValues?.id,
      reason: values?.reason,
      payer: values?.payer,
      receiptDate: values?.receiptDate,
      bankInfo: values.state === 'TRANSFER' ? values?.bank?.value || '' : '',
      bankName: values.state === 'TRANSFER' ? values?.bank?.label || '' : '',
      receiptNum: values?.receiptNum,
      description: values?.description,
      transactionFee: values?.transactionFee,
      state: values?.state,
      payBy: values?.payBy,
      allowDuplicate: values?.allowDuplicate,
      unCheckAmount: values?.unCheckAmount,
      transferedMoney: values?.transferedMoney,
    };
    const resp = await approve(payload);
    if (resp?.data.statusCode === '0') {
      await destroyAll();
    }
  };

  const destroyAll = () => {
    form.resetFields();
    onCancel();
  };

  const handleCancel = useCallback(async () => {
    if (form.isFieldsTouched()) {
      Modal.confirm({
        title: 'Xác nhận hủy',
        content: 'Dữ liệu chưa được lưu, bạn có chắc chắn muốn thoát khỏi trang không?',
        cancelText: 'Quay lại',
        onOk: async () => {
          destroyAll();
        },
        okButtonProps: { type: 'default' },
        cancelButtonProps: { type: 'primary' },
      });
    } else {
      await destroyAll();
    }
  }, [form]);

  return (
    <>
      <Modal
        width={600}
        title={<div className="modal-title">{`${title} ${initialValues?.code || ''}`}</div>}
        open={visible}
        onOk={() => {
          form.validateFields().then(() => {
            onOk();
          });
        }}
        onCancel={handleCancel}
        footer={
          <div className="modal-footer">
            <Button
              type="default"
              onClick={() => {
                setIsOpenModalReject(true);
              }}
            >
              Từ chối
            </Button>
            <Button type="primary" onClick={approveOfferOder} disabled={isApproveDisabled}>
              Duyệt
            </Button>
          </div>
        }
        destroyOnClose
        className="custom-modal"
      >
        <Form
          form={form}
          layout="vertical"
          onValuesChange={(_, allValues) => onValuesChange(allValues)}
          className="form-offer-order"
          initialValues={{ payBy: 'customer' }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                label="Thanh toán bởi"
                name="payBy"
                rules={[{ required: true, message: 'Vui lòng chọn thanh toán' }]}
              >
                <Radio.Group>
                  {payBys.map(pay => (
                    <Radio key={pay.value} value={pay.value}>
                      {pay.label}
                    </Radio>
                  ))}
                </Radio.Group>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Người nộp tiền"
                name="payer"
                rules={[{ required: true, message: 'Vui lòng nhập người nộp tiền', whitespace: true }]}
              >
                <Input placeholder="Nhập người nộp tiền" maxLength={255} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Ngày giờ thu tiền"
                name="receiptDate"
                rules={[{ required: true, message: 'Vui lòng nhập ngày giờ thu tiền' }]}
              >
                <DatePicker showTime format="DD-MM-YYYY HH:mm:ss" />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                label="Nhập ghi chú"
                name="description"
                rules={[{ required: true, message: 'Vui lòng nhập ghi chú', whitespace: true }]}
              >
                <Input placeholder="Nhập ghi chú" maxLength={255} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Số chứng từ" name="receiptNum" rules={[{ whitespace: true }]}>
                <Input placeholder="Nhập số chứng từ" maxLength={255} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Phí giao dịch" name="transactionFee">
                <InputNumber
                  placeholder="Nhập phí giao dịch"
                  style={{ width: '100%' }}
                  formatter={formatNumber}
                  maxLength={15}
                  min={0}
                  precision={0}
                  onKeyPress={e => {
                    const char = e.key;
                    if (!/[0-9]/.test(char)) {
                      e.preventDefault();
                    }
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="Lý do thanh toán" name="reason" rules={[{ whitespace: true }]}>
                <Input placeholder="Nhập lý do thanh toán" maxLength={255} />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                label="Phương thức giao dịch"
                name="state"
                rules={[{ required: true, message: 'Vui lòng chọn phương thức giao dịch' }]}
              >
                <Radio.Group>
                  {states.map(state => (
                    <Radio key={state.value} value={state.value}>
                      {state.label}
                    </Radio>
                  ))}
                </Radio.Group>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                name="bank"
                rules={state === 'TRANSFER' ? [{ required: true, message: 'Vui lòng chọn ngân hàng' }] : []}
              >
                <Select
                  placeholder="Chọn ngân hàng"
                  allowClear
                  filterOption={(input, option) =>
                    typeof option?.label === 'string' ? option.label.toLowerCase().includes(input.toLowerCase()) : false
                  }
                  showSearch
                  options={initialValues?.propertyTicket?.project?.banks?.map(item => ({
                    value: item?.accountNumber,
                    label: `${item?.name} - ${item?.accountNumber}`,
                  }))}
                  onChange={(_, option) => {
                    form.setFieldsValue({
                      bank: option,
                    });
                  }}
                  disabled={state !== 'TRANSFER'}
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item name="allowDuplicate" valuePropName="checked">
                <Checkbox>Thay đổi số tiền thu</Checkbox>
              </Form.Item>
            </Col>
            {allowDuplicate && (
              <Col span={24}>
                <Form.Item
                  label="Số tiền thu"
                  name="transferedMoney"
                  rules={[
                    {
                      required: true,
                      message: 'Vui lòng nhập số tiền thu',
                    },
                  ]}
                >
                  <InputNumber
                    placeholder="Nhập số tiền thu"
                    style={{ width: '100%' }}
                    formatter={formatNumber}
                    maxLength={15}
                    precision={0}
                    min={1}
                    onKeyPress={e => {
                      const char = e.key;
                      if (!/[0-9]/.test(char)) {
                        e.preventDefault();
                      }
                    }}
                  />
                </Form.Item>
              </Col>
            )}
            <Col span={24}>
              <Form.Item name="unCheckAmount" valuePropName="checked">
                <Checkbox>Thu tiền tại sự kiện</Checkbox>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
      <ConfirmDeleteModal
        label="khách hàng"
        open={isOpenModalReject}
        apiQuery={rejectOffer as MutationFunction<unknown, unknown>}
        keyOfListQuery={['get-offer-order', tab]}
        onCancel={() => {
          setIsOpenModalReject(false);
        }}
        idDetail={initialValues?.id}
        title="Xác nhận từ chối đề nghị thu tiền"
        description="Vui lòng nhập lý do từ chối"
        isTitlePlaceholder
        labelConfirm="Từ chối"
        fieldNameReason="reason"
        isUpdate={true}
        isMessageSuccess="Từ chối đề nghị thu tiền thành công!"
        setHideModal={() => {
          setIsOpenModalReject(false);
          destroyAll();
        }}
        maxLength={255}
      />
    </>
  );
};

export default OfferModalComponent;
