import { Tabs } from 'antd';
import BreadCrumbComponent from '../../../components/breadCrumb';
import useFilter from '../../../hooks/filter';
import { useFetch } from '../../../hooks';
import { getListOffer } from '../../../service/offer';
import { useEffect } from 'react';
import { IOfferOrderMoneyAccountancy, TabOfferOrderMoneyAccountancy } from '../../../types/offer';
import { useStoreOfferOrderMoneyAccountancy } from '../storeOfferOrderMoney';
import { DEFAULT_PARAMS } from '../../../constants/common';
import OfferOrderMoneyAccountancy from './OfferOrderMoneyAccountantcy';

const OfferOrderMoneyAccountancyList: React.FC<any> = ({}) => {
  const [filter, setFilter] = useFilter();
  const { setTab, tab, setDataForTab, setLoading, tabsFilters, getCurrentFilter, setTabFilter } =
    useStoreOfferOrderMoneyAccountancy();

  const { data, isFetching } = useFetch<IOfferOrderMoneyAccountancy[]>({
    queryKeyArrWithFilter: ['get-offer-order', tab],
    api: getListOffer,
    moreParams: {
      isDone: tab === 'TRANSFERED' ? true : null,
      sort: '-updatedAt',
      status: tab === 'WAITING_TRANSFER' ? 'WAITING_TRANSFER' : null,
      ...(tab !== 'WAITING_TRANSFER' && {
        statuses: tab === 'TRANSFERED' ? filter?.statuses || 'TRANSFERED,PROCESSING,REFUND,CANCELED' : '',
      }),
    },
  });

  const offerOrders = data?.data?.data?.rows || [];

  useEffect(() => {
    setTab((filter.tab || 'WAITING_TRANSFER') as TabOfferOrderMoneyAccountancy);
  }, [filter.tab, setTab]);

  useEffect(() => {
    if (offerOrders) {
      setDataForTab(tab, offerOrders);
      setLoading(isFetching);
    }
  }, [data, isFetching, setDataForTab, setLoading, tab]);

  const handleChangeTab = (key: string) => {
    const tabValue = key as TabOfferOrderMoneyAccountancy;
    setTab(tabValue as TabOfferOrderMoneyAccountancy);
    const savedFilter = tabsFilters[tabValue] || DEFAULT_PARAMS;
    setTabFilter(tabValue, savedFilter);
    setFilter(getCurrentFilter() as Record<string, string>);
  };

  const items = [
    {
      label: 'Đề nghị thu tiền chờ duyệt',
      key: 'WAITING_TRANSFER',
      children: <OfferOrderMoneyAccountancy tabActive="WAITING_TRANSFER" />,
    },

    {
      label: 'Đề nghị thu tiền đã xử lý',
      key: 'TRANSFERED',
      children: <OfferOrderMoneyAccountancy tabActive="TRANSFERED" />,
    },
  ];
  return (
    <div className="offer_order-list">
      <BreadCrumbComponent titleBread={''} />
      <Tabs onChange={handleChangeTab} activeKey={tab} items={items} />
    </div>
  );
};

export default OfferOrderMoneyAccountancyList;
