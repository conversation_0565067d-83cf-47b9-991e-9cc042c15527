import { Button, Col, Row, TableColumnsType, Typography } from 'antd';
import TableComponent from '../../../components/table';
import { useStoreOfferOrderMoneyAccountancy } from '../storeOfferOrderMoney';
import FilterSearch from '../component/FilterSearch';
import OfferModalComponent from '../component/offerModal';
import { useState } from 'react';
import './styles.scss';
import { formatCurrency } from '../../../utilities/shareFunc';
import dayjs from 'dayjs';
import { FORMAT_DATE_TIME, OFFER_STATUS_COLOR, OFFER_STATUS_NAME } from '../../../constants/common';
import { Link, useNavigate } from 'react-router-dom';
import { IOfferOrderMoneyAccountancy, TabOfferOrderMoneyAccountancy } from '../../../types/offer';
import { OFFER_ORDER_MONEY_ACCOUNTANCY_MANAGEMENT } from '../../../configs/path';
import { ActionsColumns } from '../../../components/table/components/ActionsColumns';
import OfferCreateModal from '../component/createOfferModal';

const { Text } = Typography;
interface OfferOrderMoneyAccountancyProps {
  tabActive: TabOfferOrderMoneyAccountancy;
}

const OfferOrderMoneyAccountancy = (props: OfferOrderMoneyAccountancyProps) => {
  const { tabActive } = props;
  const navigate = useNavigate();
  const { dataOfferOrder, loading, setInitialValue, getCurrentFilter } = useStoreOfferOrderMoneyAccountancy();

  const [isOpenOfferModal, setOfferModal] = useState(false);
  const [initialOffer, setInitialOffer] = useState<IOfferOrderMoneyAccountancy>();
  const [isCreateOpenOfferModal, setCreateOfferModal] = useState<boolean>(false);

  const columns: TableColumnsType<IOfferOrderMoneyAccountancy> = [
    {
      title: 'Tên dự án',
      dataIndex: 'projectName',
      key: 'projectName',
      width: 132,
      render: (_: any, record: IOfferOrderMoneyAccountancy) => (
        <Text>{record?.propertyTicket?.project?.name || ''}</Text>
      ),
    },
    {
      title: 'Số SP',
      dataIndex: 'propertyUnit',
      key: 'propertyUnit',
      width: 80,
      render: (_: any, record: IOfferOrderMoneyAccountancy) => (
        <Text>{record?.propertyTicket?.propertyUnit?.code || ''}</Text>
      ),
    },
    {
      title: 'Mã đề nghị',
      dataIndex: 'code',
      key: 'code',
      width: 110,
      render: (value: string, record?: IOfferOrderMoneyAccountancy) => {
        const isCheckType = record?.type === 'PRIMARY_CONTRACT' || record?.type === 'KTTT';
        const path = `${OFFER_ORDER_MONEY_ACCOUNTANCY_MANAGEMENT}/${isCheckType ? 'manual' : 'auto'}/${record?.id}`;
        return (
          <Link to={path} onClick={() => record && setInitialValue(record)}>
            {value || ''}
          </Link>
        );
      },
    },
    {
      title: 'Tên khách hàng',
      dataIndex: 'customerName',
      key: 'customerName',
      width: 153,
      render: (_: any, record: IOfferOrderMoneyAccountancy) => (
        <Text>
          {record?.propertyTicket?.customer?.type === 'business'
            ? record?.propertyTicket?.customer?.company?.name
            : record?.propertyTicket?.customer?.personalInfo?.name || ''}
        </Text>
      ),
    },
    {
      title: 'Đơn vị bán hàng',
      dataIndex: 'pos',
      key: 'pos',
      width: 183,
      render: (_: any, record: IOfferOrderMoneyAccountancy) => <Text>{record?.pos?.name || ''}</Text>,
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 106,
      render: (_: any, record: IOfferOrderMoneyAccountancy) => (
        <Text>{dayjs(record?.createdAt).format(FORMAT_DATE_TIME) || ''}</Text>
      ),
    },

    {
      title: 'Mã chứng từ',
      dataIndex: 'receiptNum',
      key: 'receiptNum',
      width: 132,
      render: (_: any, record: IOfferOrderMoneyAccountancy) => <Text>{record?.receiptNum || ''}</Text>,
    },
    {
      title: 'Mã YC/HĐ',
      dataIndex: 'bookingTicketCode',
      key: 'bookingTicketCode',
      width: 145,
      render: (_: any, record: IOfferOrderMoneyAccountancy) => (
        <Text>
          {record?.propertyTicket?.ticketType === 'YCDCH'
            ? record?.propertyTicket?.bookingTicketCode
            : record?.propertyTicket?.escrowTicketCode || ''}
        </Text>
      ),
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      width: 110,
      align: 'center',
      render: (value: string) => (
        <Text style={{ color: OFFER_STATUS_COLOR[value] }}>{OFFER_STATUS_NAME[value] || ''}</Text>
      ),
    },

    {
      title: 'Số tiền',
      dataIndex: 'money',
      key: 'money',
      width: 124,
      align: 'center',
      render: (value: number) => <Text>{formatCurrency(value?.toString()) || ''}</Text>,
    },

    {
      title: '',
      dataIndex: 'action',
      key: 'action',
      align: 'center',
      width: 80,
      render: (_: any, record: IOfferOrderMoneyAccountancy) => {
        const handleEdit = () => {};
        const handleAdjust = () => {};
        const handlePrint = () => {};
        const isCheckType = record?.type === 'PRIMARY_CONTRACT' || record?.type === 'KTTT';
        return tabActive === 'WAITING_TRANSFER' ? (
          isCheckType ? (
            <Button
              type="link"
              onClick={() => navigate(`${OFFER_ORDER_MONEY_ACCOUNTANCY_MANAGEMENT}/manual/${record?.id}`)}
            >
              Xem
            </Button>
          ) : (
            <Button
              type="link"
              onClick={() => {
                setOfferModal(true);
                setInitialOffer(record);
              }}
            >
              Duyệt
            </Button>
          )
        ) : (
          <ActionsColumns handleEdit={handleEdit} handleAdjust={handleAdjust} handlePrint={handlePrint} />
        );
      },
    },
  ];

  return (
    <>
      <div className="header-content" style={{ marginBottom: 16 }}>
        <FilterSearch />
        {tabActive === 'WAITING_TRANSFER' && (
          <Row gutter={[16, 8]}>
            <Col>
              <Button type="primary" onClick={() => setCreateOfferModal(true)}>
                Tạo mới
              </Button>
            </Col>
          </Row>
        )}
      </div>
      <TableComponent
        className="table-offer-order"
        columns={columns}
        queryKeyArr={['get-offer-order', getCurrentFilter()]}
        dataSource={dataOfferOrder[tabActive]}
        loading={loading}
        rowKey={'id'}
      />
      <OfferModalComponent
        title="Đề nghị thu tiền"
        visible={isOpenOfferModal}
        onCancel={() => setOfferModal(false)}
        onOk={() => setOfferModal(false)}
        onValuesChange={() => {}}
        initialValues={initialOffer}
      />
      <OfferCreateModal visible={isCreateOpenOfferModal} onClose={() => setCreateOfferModal(false)} />
    </>
  );
};

export default OfferOrderMoneyAccountancy;
