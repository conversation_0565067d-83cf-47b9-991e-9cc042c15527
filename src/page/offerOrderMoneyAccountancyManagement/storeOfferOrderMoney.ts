import { DEFAULT_PARAMS } from '../../constants/common';
import { create } from 'zustand';
import { URLSearchParamsInit } from 'react-router-dom';
import { IOfferOrderMoneyAccountancy, TabOfferOrderMoneyAccountancy } from '../../types/offer';

type OfferOrderMoneyAccountancyData = {
  WAITING_TRANSFER: IOfferOrderMoneyAccountancy[];
  TRANSFERED: IOfferOrderMoneyAccountancy[];
};

interface OfferOrderMoneyAccountancyDataStore {
  tab: TabOfferOrderMoneyAccountancy;
  dataOfferOrder: OfferOrderMoneyAccountancyData;
  loading?: boolean;
  tabsFilters: Record<string, URLSearchParamsInit>;
  initialValue?: IOfferOrderMoneyAccountancy;
  isModified: boolean;
  setIsModified: (isModified: boolean) => void;
  setTab: (value: TabOfferOrderMoneyAccountancy) => void;
  setDataForTab: (tab: TabOfferOrderMoneyAccountancy, data: IOfferOrderMoneyAccountancy[]) => void;
  setTabFilter: (tab: string, filter: URLSearchParamsInit) => void;
  setLoading: (loading: boolean) => void;
  getCurrentFilter: () => URLSearchParamsInit;
  setInitialValue: (data: IOfferOrderMoneyAccountancy) => void;
}

export const useStoreOfferOrderMoneyAccountancy = create<OfferOrderMoneyAccountancyDataStore>((set, get) => ({
  tab: 'WAITING_TRANSFER',
  dataOfferOrder: {
    WAITING_TRANSFER: [],
    TRANSFERED: [],
  },
  tabsFilters: {
    WAITING_TRANSFER: DEFAULT_PARAMS,
    TRANSFERED: DEFAULT_PARAMS,
  },
  loading: false,
  initialValue: undefined,
  isModified: false,
  setTab: (value: TabOfferOrderMoneyAccountancy) => set({ tab: value }),
  setDataForTab: (tab: TabOfferOrderMoneyAccountancy, data: IOfferOrderMoneyAccountancy[]) =>
    set(state => ({ dataOfferOrder: { ...state.dataOfferOrder, [tab]: data } })),
  setLoading: (loading: boolean) => set({ loading }),
  setTabFilter: (tab, filter) =>
    set(state => ({
      tabsFilters: {
        ...state.tabsFilters,
        [tab]: filter,
      },
    })),
  getCurrentFilter: () => {
    const state = get();
    const tabFilter = state.tabsFilters[state.tab] || {};
    return typeof tabFilter === 'object' ? { ...tabFilter, tab: state.tab } : { tab: state.tab };
  },
  setInitialValue: (data: IOfferOrderMoneyAccountancy) => set({ initialValue: data }),
  setIsModified: (isModified: boolean) => set({ isModified }),
}));
