import { URLSearchParamsInit } from 'react-router-dom';
import { create } from 'zustand';
import { DEFAULT_PARAMS } from '../../constants/common';

interface ReportLeadStore {
  activeTab: 'done' | 'assign' | 'processing' | 'cancel';
  tabsFilters: Record<string, URLSearchParamsInit>;
  setActiveTab: (tab: 'done' | 'assign' | 'processing' | 'cancel') => void;
  setTabFilter: (tab: string, filter: URLSearchParamsInit) => void;
  getCurrentFilter: () => URLSearchParamsInit;
}

export const useReportLeadStore = create<ReportLeadStore>((set, get) => ({
  activeTab: 'assign',
  tabsFilters: {
    assign: DEFAULT_PARAMS,
    processing: DEFAULT_PARAMS,
    done: DEFAULT_PARAMS,
    cancel: DEFAULT_PARAMS,
  },
  setActiveTab: tab => set({ activeTab: tab }),
  setTabFilter: (tab, filter) =>
    set(state => ({
      tabsFilters: {
        ...state.tabsFilters,
        [tab]: filter,
      },
    })),
  getCurrentFilter: () => {
    const state = get();
    return state.tabsFilters[state.activeTab];
  },
}));
