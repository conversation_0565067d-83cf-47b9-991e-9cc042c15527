import { useMutation, useQueries } from '@tanstack/react-query';
import {
  App,
  Button,
  Checkbox,
  CheckboxProps,
  Dropdown,
  Flex,
  List,
  Radio,
  RadioChangeEvent,
  Space,
  Tag,
  Typography,
} from 'antd';
import { CheckboxGroupProps } from 'antd/es/checkbox';
import { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { useMemo, useState } from 'react';
import BreadCrumbComponent from '../../components/breadCrumb';
import TableComponent from '../../components/table';
import { DEFAULT_PARAMS, FORMAT_DATE_TIME, OPTIONS_EXPLOIT_LEAD } from '../../constants/common';
import { useFetch } from '../../hooks';
import useFilter from '../../hooks/filter';
import { getExportReportLead, getReportLead } from '../../service/lead';
import { TakeCare, TCheckItem, TExportReportLead, TReportLead } from '../../types/reportLead';
import FilterReportLead from './FilterReportLead';
import { useReportLeadStore } from './stroreReportLead';
import './styles.scss';
import { downloadArrayBufferFile } from '../../utilities/shareFunc';

const { Text } = Typography;

const columns: ColumnsType<TReportLead> = [
  { title: 'STT', key: 'stt', width: '10%', render: (_, __, index: number) => index + 1 },
  { title: 'Mã Lead', dataIndex: 'code', key: 'code', render: (value: string) => (value ? value : '-') },
  { title: 'Họ và Tên', dataIndex: 'name', key: 'name', render: (value: string) => (value ? value : '-') },
  {
    title: 'Nhân viên tư vấn',
    dataIndex: 'takeCare',
    key: 'takeCare',
    render: (value: TakeCare) => (value ? <Tag>{value?.name}</Tag> : '-'),
  },
  {
    title: 'Ngày cập nhật',
    dataIndex: 'updatedDate',
    key: 'updatedDate',
    render: (value: string, record: TReportLead) => (
      <>
        <Text>{dayjs(value).format(FORMAT_DATE_TIME)}</Text> <br />
        <Text>{record?.modifiedByObj?.fullName ? record.modifiedByObj.fullName : '-'}</Text>
      </>
    ),
  },
];

const ReportLead = () => {
  const [filter, setFilter] = useFilter();
  const { notification } = App.useApp();
  const [openDropdown, setOpenDropdown] = useState(false);
  const [listCheckItem, setListCheckItem] = useState<TCheckItem[]>(
    OPTIONS_EXPLOIT_LEAD.map(option => ({
      key: option.value,
      label: option.label,
      selected: false,
    })),
  );
  const status = listCheckItem
    .filter(item => item.selected)
    .map(item => item.key)
    .join(',');

  const { activeTab, setActiveTab, tabsFilters, setTabFilter, getCurrentFilter } = useReportLeadStore();

  const { data, isFetching } = useFetch<TReportLead[]>({
    api: getReportLead,
    queryKeyArrWithFilter: ['report-lead', activeTab],
    moreParams: { type: activeTab },
    enabled: !!activeTab,
  });
  const dataSource = data?.data?.data?.rows || [];

  const results = useQueries({
    queries: OPTIONS_EXPLOIT_LEAD.map(item => ({
      queryKey: [DEFAULT_PARAMS, 'report-lead', item.value],
      queryFn: () => getReportLead({ type: item.value, ...DEFAULT_PARAMS }),
    })),
  });

  const exportHistoryMutation = useMutation({
    mutationFn: (params: TExportReportLead) => getExportReportLead(params),
  });

  const countMap = useMemo(
    () =>
      ({
        assign: results[0].data?.data?.data?.total ?? 0,
        processing: results[1].data?.data?.data?.total ?? 0,
        done: results[2].data?.data?.data?.total ?? 0,
        cancel: results[3].data?.data?.data?.total ?? 0,
      }) as Record<string, number>,
    [results],
  );

  const options: CheckboxGroupProps<string>['options'] = useMemo(
    () =>
      OPTIONS_EXPLOIT_LEAD.map(option => ({
        label: `${option.label}(${countMap[option.value]})`,
        value: option.value,
      })),
    [countMap],
  );

  const handleCheckboxChange = (id: string) => {
    setListCheckItem(listCheckItem.map(item => (item.key === id ? { ...item, selected: !item.selected } : item)));
  };

  const onCheckAllChange: CheckboxProps['onChange'] = e => {
    setListCheckItem(listCheckItem.map(item => ({ ...item, selected: e.target.checked })));
  };

  const handleSubmitExport = async () => {
    try {
      const response = await exportHistoryMutation.mutateAsync({
        status,
        startDate: filter.startDate,
        endDate: filter.endDate,
      });
      setOpenDropdown(false);
      downloadArrayBufferFile({ data: response.data, fileName: `Explotation-lead-reports.xlsx` });
    } catch (error) {
      notification.error({ message: 'Xuất dữ liệu thất bại.' });
    }
  };

  const handleSubmitExportHistory = async () => {
    try {
      const response = await exportHistoryMutation.mutateAsync({
        status,
        history: true,
        startDate: filter.startDate,
        endDate: filter.endDate,
      });
      downloadArrayBufferFile({ data: response.data, fileName: `Explotation-lead-reports-history.xlsx` });
      setOpenDropdown(false);
    } catch (error) {
      notification.error({ message: 'Xuất dữ liệu thất bại.' });
    }
  };

  const handleChangeActiveTab = (e: RadioChangeEvent) => {
    const tabValue = e.target.value as 'assign' | 'processing' | 'done' | 'cancel';
    setActiveTab(tabValue);
    const savedFilter = tabsFilters[tabValue] || DEFAULT_PARAMS;
    setTabFilter(tabValue, savedFilter);
    setFilter(getCurrentFilter() as Record<string, string>);
  };

  return (
    <div className="wrapper-report-lead">
      <BreadCrumbComponent />
      <Flex justify="space-between">
        <Radio.Group optionType="button" options={options} value={activeTab} onChange={handleChangeActiveTab} />
        <Dropdown
          menu={{
            items: Object.values(OPTIONS_EXPLOIT_LEAD).map(option => ({ key: option.value, label: option.label })),
          }}
          dropdownRender={() => (
            <div className="dropdown-report-lead" style={{ width: '300px' }}>
              <div className="header">
                <p>Chọn trạng thái lead bạn muốn xuất dữ liệu</p>
              </div>
              <div className="body">
                <Checkbox
                  indeterminate={listCheckItem?.some(emp => emp.selected) && !listCheckItem?.every(emp => emp.selected)}
                  onChange={onCheckAllChange}
                  checked={listCheckItem?.every(emp => emp.selected)}
                >
                  <span>Tất cả</span>
                </Checkbox>
                <List
                  dataSource={listCheckItem}
                  renderItem={employee => (
                    <List.Item>
                      <Checkbox checked={employee.selected} onChange={() => handleCheckboxChange(employee.key)}>
                        {employee.label}
                      </Checkbox>
                    </List.Item>
                  )}
                />
              </div>
              <div className="footer">
                <Space>
                  <Button type="default" onClick={handleSubmitExport}>
                    Xuất excel
                  </Button>
                  <Button type="default" onClick={handleSubmitExportHistory}>
                    Xuất lịch sử
                  </Button>
                </Space>
              </div>
            </div>
          )}
          trigger={['click']}
          open={openDropdown}
          onOpenChange={() => setOpenDropdown(!openDropdown)}
        >
          <>
            <Button type="default" onClick={() => setOpenDropdown(true)}>
              Xuất dữ liệu
            </Button>
          </>
        </Dropdown>
      </Flex>
      <FilterReportLead />

      <TableComponent
        queryKeyArr={['report-lead', activeTab]}
        columns={columns}
        dataSource={dataSource}
        loading={isFetching}
      />
    </div>
  );
};

export default ReportLead;
