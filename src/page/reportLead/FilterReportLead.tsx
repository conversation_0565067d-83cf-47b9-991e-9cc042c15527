import { Col, DatePicker, Form, Row } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { useEffect, useMemo, useState } from 'react';
import { useLocation } from 'react-router-dom';
import useFilter from '../../hooks/filter';
import { DEFAULT_PARAMS, FORMAT_DATE, FORMAT_DATE_API } from '../../constants/common';
import DropdownFilterSearch from '../../components/dropdown/dropdownFilterSearch';
import { useReportLeadStore } from './stroreReportLead';

type FilterReportLead = {
  startDate?: string | Dayjs;
  endDate?: string | Dayjs;
};

const FilterReportLead = () => {
  const { search } = useLocation();
  const [form] = Form.useForm();
  const params = useMemo(() => new URLSearchParams(search), [search]);
  const [filter, setFilter] = useFilter();
  const [initialValues, setInitialValues] = useState<FilterReportLead>();
  const [isOpenFilter, setIsOpenFilter] = useState(false);
  const { activeTab, setTabFilter } = useReportLeadStore();

  useEffect(() => {
    if (params) {
      const initialValue = {
        startDate: params.get('startDate') ? dayjs(params.get('startDate')) : undefined,
        endDate: params.get('endDate') ? dayjs(params.get('endDate')) : undefined,
      };
      setInitialValues(initialValue);
      form.setFieldsValue(initialValue);
    }
  }, [form, params, setTabFilter]);

  const handleSubmitFilter = (values: FilterReportLead) => {
    const newFilter: Record<string, unknown> = {
      startDate: values?.startDate ? dayjs(values?.startDate).format(FORMAT_DATE_API) : null,
      endDate: values?.endDate ? dayjs(values?.endDate).format(FORMAT_DATE_API) : null,
    };
    setFilter({ ...filter, page: '1', ...newFilter });
    setTabFilter(activeTab, { ...filter, page: '1', ...newFilter });
    setIsOpenFilter(false);
  };

  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };

  const handleClearFilters = () => {
    setInitialValues(undefined);
    setFilter({ ...DEFAULT_PARAMS, search: params.get('search') || '' });
    setTabFilter(activeTab, { ...DEFAULT_PARAMS, search: params.get('search') || '' });
    setTimeout(() => {
      setIsOpenFilter(false);
    }, 100);
  };

  const handleChangeSearch = (e: unknown) => {
    const searchTerm = typeof e === 'string' ? e : '';
    setFilter({ ...filter, page: '1', search: searchTerm });
    setTabFilter(activeTab, { ...filter, page: '1', search: searchTerm });
  };

  return (
    <>
      <DropdownFilterSearch
        rootClassName="wrapper-filter"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        showParams={false}
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        initialValues={initialValues}
        defaultValueSearch={params.get('search') || ''}
        keyInputSearch={`search-${activeTab}`}
        form={form}
        onChangeSearch={handleChangeSearch}
        onClearFilters={handleClearFilters}
        extraFormItems={
          <>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="Từ ngày" name="startDate">
                  <DatePicker
                    format={FORMAT_DATE}
                    disabledDate={current => {
                      const endDate = form.getFieldValue('endDate');
                      return (
                        (current && current > dayjs().startOf('day')) ||
                        (endDate && current > dayjs(endDate).startOf('day')) // Disable ngày sau "Đến ngày"
                      );
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Đến ngày" name="endDate">
                  <DatePicker
                    format={FORMAT_DATE}
                    disabledDate={current => {
                      const startDate = form.getFieldValue('startDate');
                      return (
                        (current && current > dayjs().startOf('day')) ||
                        (startDate && current < dayjs(startDate).startOf('day')) // Disable ngày trước "Ngày tạo"
                      );
                    }}
                  />
                </Form.Item>
              </Col>
            </Row>
          </>
        }
      />
    </>
  );
};

export default FilterReportLead;
