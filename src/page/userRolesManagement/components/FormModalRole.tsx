import { Button, Col, Form, Input, notification, Row, Select, Space, Spin, Tabs } from 'antd';
import isArray from 'lodash/isArray';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { IManagementRole } from '..';
import ModalComponent from '../../../components/modal';
import { OPTIONS_POSITION, OPTIONS_STATES } from '../../../constants/common';
import { useCreateField, useFetch, useUpdateField } from '../../../hooks';
import { getDetailUserRoles, getListFeature, sendCreateRole, sendUpdateRole } from '../../../service/roles';
import { useModalRoles } from '../context/ListOfUnitsContext';
import { messageError } from '../messError';
import { groupByMsxName } from '../utils';
import ListRoles, { IFeature } from './ListRoles';
import './styles.scss';
import TableListRolesAssignment from './TablelistRolesAssignment';
import { normalizeString } from '../../../utilities/regex';

const { Item } = Form;

export type TPermissions = {
  action: { readOwn: boolean };
  featureName: string;
  msxName: string;
  number: number;
  featureId: string;
  msxId: string;
};

const FormModalRole = () => {
  const [form] = Form.useForm();
  const currentValues = Form.useWatch([], form);

  const [tab, setTab] = useState<string>('1');
  const [initialDataDetail, setInitialDataDetail] = useState<Partial<IManagementRole>>({});
  const [isFormValid, setIsFormValid] = useState(false);

  const { state, dispatch } = useModalRoles();

  const { data: dataFeature, isLoading } = useFetch<IFeature[]>({
    queryKeyArr: ['list-feature'],
    api: getListFeature,
    withFilter: false,
    enabled: !!state.open,
  });

  const createRole = useCreateField<IManagementRole>({
    keyOfListQuery: ['user-roles'],
    keyOfDetailQuery: ['user-roles', state.idDetail],
    apiQuery: sendCreateRole,
    label: 'Vai trò',
    checkDuplicate: true,
  });

  const updateRole = useUpdateField<IManagementRole>({
    keyOfListQuery: ['user-roles'],
    keyOfDetailQuery: ['user-roles', state.idDetail],
    apiQuery: sendUpdateRole,
    label: 'Vai trò',
  });

  const { data: dataDetailRole, isFetching: isFetchingData } = useFetch<IManagementRole>({
    queryKeyArr: ['user-roles', state.idDetail],
    api: () => state.idDetail && getDetailUserRoles(state.idDetail),
    withFilter: false,
    enabled: !!state.idDetail,
    cacheTime: 100,
  });
  const dataDetail = dataDetailRole?.data?.data;
  const { keyList } = groupByMsxName(dataFeature?.data?.data);

  const getTitleCURDModal = useCallback(() => {
    switch (state.typeModal) {
      case 'create':
        return `Thêm mới `;
      case 'detail':
        return 'Xem chi tiết';
      case 'clone':
        return `Tạo bản sao ${dataDetail?.name}`;
      default:
        return ``;
    }
  }, [dataDetail, state.typeModal]);

  useEffect(() => {
    if (state.typeModal !== 'create') {
      const fieldData = {
        name: state.typeModal === 'detail' ? dataDetail?.name : `COPY-${dataDetail?.name}`,
        code: state.typeModal === 'detail' ? dataDetail?.code : ``,
        active: dataDetail?.active,
        type: dataDetail?.type,
        checkPermissions: dataDetail?.permissions?.length === 0 ? false : true,
      };
      form.setFieldsValue(fieldData);
      setInitialDataDetail(fieldData);
    } else {
      form.resetFields();
      setInitialDataDetail({});
    }
  }, [dataDetail, form, state.typeModal]);

  const items = useMemo(
    () => [
      {
        key: '1',
        label: 'Danh sách quyền',
        children: <ListRoles dataFeature={dataFeature?.data?.data} viewData={dataDetail} isLoading={isLoading} />,
      },
      {
        key: '2',
        label: 'Danh sách người dùng được gán',
        children: <TableListRolesAssignment idRole={state.idDetail} />,
      },
    ],
    [dataDetail, dataFeature?.data?.data, isLoading, state.idDetail],
  );

  const cloneRole = () => {
    dispatch({ type: 'OPEN_MODAL', payload: { typeModal: 'clone', idDetail: state.idDetail } });
  };

  const handleCancel = () => {
    form.resetFields();
    setTab('1');
    dispatch({ type: 'CLOSE_MODAL' });
  };

  const onFinish = async (values: IManagementRole) => {
    const fieldValuesPermissions = form.getFieldsValue(keyList);

    const listPermissions = dataFeature?.data?.data?.filter((item: IFeature) => {
      const permissions = fieldValuesPermissions[item.msxName as keyof IManagementRole];
      return isArray(permissions) && permissions.includes(item.name);
    });

    const formatDataPermissions = listPermissions?.map(
      (item: IFeature): TPermissions => ({
        featureName: item.name,
        msxName: item.msxName,
        featureId: item.id,
        msxId: item.msxId,
        action: { readOwn: true },
        number: 0,
      }),
    );

    const newData: IManagementRole = {
      name: values?.name && normalizeString(values.name),
      code: values?.code && normalizeString(values.code),
      type: values?.type,
      active: values.active,
      permissions: formatDataPermissions || [],
      id: state.typeModal === 'detail' ? state.idDetail : undefined,
    };

    const res =
      state.typeModal === 'detail' ? await updateRole.mutateAsync(newData) : await createRole.mutateAsync(newData);
    const statusCode = res?.data?.statusCode;
    if (statusCode === '0') {
      form.resetFields();
      dispatch({ type: 'CLOSE_MODAL' });
    } else {
      notification.error({ message: messageError(statusCode) });
    }
  };

  useEffect(() => {
    form
      .validateFields({ validateOnly: true })
      .then(() => {
        if (state.typeModal === 'create') {
          setIsFormValid(true);
        }
      })
      .catch(() => setIsFormValid(false));
  }, [currentValues, form, initialDataDetail, state.typeModal]);

  const handleValuesChanges = (changeValue: unknown) => {
    setIsFormValid(changeValue ? true : false);
  };
  return (
    <ModalComponent
      className="modal-roles"
      title={getTitleCURDModal()}
      open={state.open}
      onCancel={handleCancel}
      destroyOnClose
      footer={
        <Space>
          {state.typeModal === 'detail' && (
            <Button type="default" onClick={cloneRole}>
              Tạo bản sao
            </Button>
          )}
          <Button
            type="primary"
            htmlType="submit"
            onClick={() => form.submit()}
            disabled={!isFormValid}
            loading={updateRole.status === 'pending' || createRole.status === 'pending'}
          >
            Lưu
          </Button>
        </Space>
      }
    >
      <Spin spinning={isFetchingData}>
        <Form
          className="form-roles"
          form={form}
          layout="vertical"
          colon={false}
          labelAlign="left"
          initialValues={{ active: true }}
          onFinish={onFinish}
          onValuesChange={handleValuesChanges}
        >
          <Row gutter={[24, 24]} className="content-header-form">
            <Col lg={6} xs={12}>
              <Item
                name="name"
                rules={[
                  { required: true, message: 'Vui lòng nhập tên vai trò!' },
                  { max: 50, message: 'Tên vai trò không quá 50 ký tự!' },
                ]}
                required
                label="Tên vai trò"
              >
                <Input placeholder="Nhập tên vai trò" />
              </Item>
            </Col>
            <Col lg={6} xs={12}>
              <Item
                name="code"
                label="Mã vai trò"
                rules={[
                  { required: true, message: 'Vui lòng nhập mã vai trò!' },
                  { max: 50, message: 'Mã vai trò không quá 50 ký tự!' },
                ]}
                required
              >
                <Input placeholder="Nhập mã vai trò" />
              </Item>
            </Col>
          </Row>
          <Row gutter={[24, 24]}>
            <Col lg={6} xs={12}>
              <Item name="active" required label="Trạng thái">
                <Select style={{ width: '100%' }} placeholder="Chọn trạng thái" options={OPTIONS_STATES} />
              </Item>
            </Col>
            <Col lg={6} xs={12}>
              <Item
                name="type"
                required
                rules={[{ required: true, message: 'Vui lòng chọn loại vai trò!' }]}
                label="Loại vai trò"
              >
                <Select style={{ width: '100%' }} placeholder="Loại vai trò" options={OPTIONS_POSITION} />
              </Item>
            </Col>
          </Row>

          <div className={`${state.typeModal === 'detail' ? 'detail' : 'create'}`}>
            {state.typeModal === 'detail' ? (
              <Tabs activeKey={tab} items={items} onTabClick={(key: string) => setTab(key)} />
            ) : (
              <ListRoles dataFeature={dataFeature?.data?.data} viewData={dataDetail} isLoading={isLoading} />
            )}
          </div>
          <Form.Item
            name="checkPermissions"
            rules={[
              {
                required: true,
                validator: (_, value) => {
                  if (!value) {
                    return Promise.reject(new Error('Vui lòng chọn quyền!'));
                  }
                  return Promise.resolve();
                },
              },
            ]}
            hidden
          />
        </Form>
      </Spin>
    </ModalComponent>
  );
};

export default FormModalRole;
