.modal-roles {
  .content-form-roles {
    .check-list-checkbox {
      position: relative;
      h3 {
        font-weight: 600;
      }
      .description {
        padding: 8px 0;
      }
    }
  }
  .switch-type .ant-form-item-control-input .ant-switch {
    float: right;
  }

  .check-list-checkbox {
    .ant-collapse {
      margin-top: 16px;
      .ant-collapse-item {
        margin-bottom: 8px;
        background: rgba(0, 0, 0, 0.06);
        border-radius: 2px;
        border: 1px solid #0000000f;
        border-bottom: none;
        &.ant-collapse-item-active {
          border-bottom: 1px solid #0000000f;
        }
        .ant-collapse-header {
          padding: 9px 16px;
          border-bottom: 1px solid #0000000f;
        }

        .ant-collapse-content-box {
          padding: 9px 24px;
          .sub-list-check {
            display: flex;
            flex-direction: column;
            row-gap: 16px;
            .item-form-check-all {
              border-bottom: 1px solid #0000000f;
            }
            .ant-checkbox-wrapper {
              flex-basis: calc(33.33% - 24px);
            }
            .ant-form-item {
              margin-bottom: 0;
            }
            .ant-checkbox-group {
              column-gap: 24px;
              row-gap: 16px;
              width: 100%;
            }
          }
        }
      }
    }
  }
  .table-roles-assignment {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-top: 8px;
    .ant-input-affix-wrapper {
      width: 384px;
    }
  }
}

@media screen and (max-width: 575px) {
  .switch-type .ant-form-item {
    .ant-form-item-control {
      flex: 0 0 50% !important;
    }
  }
}
