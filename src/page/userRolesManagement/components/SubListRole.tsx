import { Checkbox, CheckboxProps, Form } from 'antd';
import { CheckboxGroupProps } from 'antd/es/checkbox';
import { useEffect } from 'react';
import { IFeature } from './ListRoles';

const CheckboxGroup = Checkbox.Group;
const { Item } = Form;
interface props {
  data: IFeature[];
  keyName: string;
  listKey: string[];
}

const SubListRole = ({ data, keyName, listKey }: props) => {
  const form = Form.useFormInstance();
  const listCheck = Form.useWatch(keyName, form);
  const values = data.map((obj: IFeature) => obj.value);
  const indeterminate = listCheck?.length > 0 && listCheck?.length < data.length;
  const checkAll = data.length === listCheck?.length;
  const dataOptions = data.map(obj => ({ label: obj.value, value: obj.value }));
  const listKeyPermissionValues = form.getFieldsValue(listKey);

  const onCheckAllChange: CheckboxProps['onChange'] = e => {
    form.setFieldsValue({
      [keyName]: e.target.checked ? values : [],
    });
    form.setFieldValue('checkPermissions', e.target.checked ? values : []);
  };

  const handleCheckChange: CheckboxGroupProps['onChange'] = (e: string[]) => {
    form.setFieldValue(keyName, e);
  };

  useEffect(() => {
    const listKeyPer = listKey
      .map((key: string | number) => listKeyPermissionValues[key])
      .flat()
      .filter(Boolean);
    form.setFieldValue('checkPermissions', listKeyPer.length > 0 ? true : false);
  }, [form, listKey, listKeyPermissionValues]);

  return (
    <div className="sub-list-check">
      <Item name="checkAll" className="item-form-check-all">
        <Checkbox onChange={onCheckAllChange} checked={checkAll} indeterminate={indeterminate} className="check-all">
          Tất cả
        </Checkbox>
      </Item>
      <Item name={keyName}>
        <CheckboxGroup options={dataOptions} onChange={handleCheckChange} />
      </Item>
    </div>
  );
};

export default SubListRole;
