import { Collapse, Empty, Form, Spin } from 'antd';
import { CollapseProps } from 'antd/lib';
import React, { CSSProperties, useEffect } from 'react';
import { IManagementRole } from '..';
import { useModalRoles } from '../context/ListOfUnitsContext';
import { groupByMsxName } from '../utils';
import { TPermissions } from './FormModalRole';
import SubListRole from './SubListRole';

interface props {
  defaultCheckedList?: (string | number | boolean)[];
  dataFeature?: IFeature[];
  isLoading?: boolean;
  viewData?: IManagementRole | null;
}

export interface IFeature {
  _id: string;
  description: string;
  active: boolean;
  softDelete: boolean;
  modifiedBy: string | null;
  msxId: string;
  msxName: string;
  id: string;
  featureName: string;
  name: string;
  __v: number;
  value: string;
}

const panelStyle: React.CSSProperties = {
  marginBottom: 8,
  background: '#0000000F',
  borderRadius: 2,
};

const ListRoles = ({ dataFeature, isLoading, viewData }: props) => {
  const form = Form.useFormInstance();
  const { state } = useModalRoles();
  const { transformedArray, keyList } = groupByMsxName(dataFeature);

  useEffect(() => {
    if (state.idDetail) {
      if (state.idDetail && viewData?.permissions) {
        keyList.forEach(key =>
          form.setFieldValue(
            key,
            viewData.permissions
              .filter((permission: TPermissions) => permission.msxName === key)
              .map(permission => permission.featureName),
          ),
        );
      }
    }
  }, [form, keyList, state.idDetail, viewData]);

  const itemCollapse: (panelStyle: CSSProperties) => CollapseProps['items'] = panelStyle =>
    transformedArray.map((x, index) => ({
      key: index,
      label: keyList[index],
      style: panelStyle,
      children: <SubListRole data={x} keyName={keyList[index]} listKey={keyList} />,
    }));

  return (
    <div className="check-list-checkbox">
      <h3>Danh sách nhóm quyền</h3>
      <p className="description">
        Danh sách nhóm quyền Admin có thể sử dụng và phân quyền cho người dùng khác trên hệ thống.
      </p>
      {isLoading ? (
        <Spin style={{ width: '100%', paddingTop: '24px' }} />
      ) : dataFeature?.length === 0 ? (
        <div style={{ textAlign: 'center', padding: '24px' }}>
          <Empty description="Danh sách hiện tại đang trống" />
        </div>
      ) : (
        <>
          <Collapse bordered={false} items={itemCollapse(panelStyle)} style={{ background: '#ffff' }} />
          <Form.Item shouldUpdate noStyle>
            {({ getFieldError }) => {
              const error = getFieldError('checkPermissions');
              return error.length > 0 ? <div style={{ color: 'red' }}>{error[0]}</div> : null;
            }}
          </Form.Item>
        </>
      )}
    </div>
  );
};

export default ListRoles;
