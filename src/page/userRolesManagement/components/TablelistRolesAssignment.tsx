import { ColumnsType } from 'antd/es/table';
import { memo, useState } from 'react';
import InputSearch from '../../../components/input/InputSearch';
import TableComponent from '../../../components/table';
import { useFetch } from '../../../hooks';
import { sendGetAssignPersonsRole } from '../../../service/roles';

interface Props {
  idRole?: string | null;
}
export interface IListRolesAssignment {
  id: string;
  code: string;
  email: string;
  fullName: string;
  jobTitle: unknown;
}

const columnRolesAssignment: ColumnsType<IListRolesAssignment> = [
  {
    title: 'Nhân viên',
    dataIndex: 'fullName',
    render: (_, record) => (record?.fullName ? `${record?.fullName} - ${record?.email}` : null),
  },
  { title: 'Chức danh', dataIndex: 'jobTitles', render: value => (value ? value?.join('\n') : null) },
  { title: 'Vùng dữ liệu truy cập', dataIndex: 'dataArea', render: value => (value ? value?.join('\n') : null) },
];

function TableListRolesAssignment({ idRole }: Props) {
  const [search, setSearch] = useState<string>();
  const [pagination, setPagination] = useState<{ page: string; pageSize: string }>({ page: '1', pageSize: '10' });

  const { data, isLoading } = useFetch<IListRolesAssignment[]>({
    queryKeyArr: ['list-person-assignment-role', idRole, search, pagination],
    api: async () => {
      const response = await sendGetAssignPersonsRole({ roleId: idRole, search, ...pagination });
      return response;
    },
  });
  const handleOnChangeSearch = (value: unknown) => {
    setSearch(value as string);
  };
  const handleChangePagination = (page: string, pageSize: string) => {
    setPagination({ page, pageSize });
  };

  return (
    <div className="table-roles-assignment">
      <InputSearch showParams={false} onChange={handleOnChangeSearch} />
      <TableComponent
        queryKeyArr={['list-person-assignment-role', idRole, search ?? null, pagination]}
        loading={isLoading}
        columns={columnRolesAssignment}
        dataSource={(data?.data?.data?.rows ?? []) as IListRolesAssignment[]}
        rowKey={'id'}
        scroll={{ x: '1000px', scrollToFirstRowOnChange: true }}
        onChangePagination={handleChangePagination}
        isWithParamsUrl={false}
      />
    </div>
  );
}

export default memo(TableListRolesAssignment);
