import { ModalOfRolesState } from './state';

export type ModalOfRolesAction =
  | { type: 'OPEN_MODAL'; payload: { typeModal: 'create' | 'detail' | 'clone'; idDetail?: string | null } }
  | { type: 'CLOSE_MODAL' };

export const reducer = (state: ModalOfRolesState, action: ModalOfRolesAction): ModalOfRolesState => {
  switch (action.type) {
    case 'OPEN_MODAL':
      return {
        ...state,
        open: true,
        typeModal: action.payload.typeModal,
        idDetail: action.payload.idDetail ?? undefined,
      };
    case 'CLOSE_MODAL':
      return {
        ...state,
        open: false,
        typeModal: 'create',
        idDetail: undefined,
      };

    default:
      return state;
  }
};
