import React, { createContext, Dispatch, ReactNode, useContext, useReducer } from 'react';
import { ModalOfRolesAction, reducer } from './reducer';
import { initialModalOfRolesState, ModalOfRolesState } from './state';

export interface ModalRolesContextProps {
  state: ModalOfRolesState;
  dispatch: Dispatch<ModalOfRolesAction>;
}

export const ListOfRolesContext = createContext<ModalRolesContextProps | undefined>(undefined);

export const ListOfRolesProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(reducer, initialModalOfRolesState);

  return <ListOfRolesContext.Provider value={{ state, dispatch }}>{children}</ListOfRolesContext.Provider>;
};
// eslint-disable-next-line react-refresh/only-export-components
export const useModalRoles = (): ModalRolesContextProps => {
  const context = useContext(ListOfRolesContext);
  if (context === undefined) {
    throw new Error('useModal must be used within a ModalProvider');
  }
  return context;
};
