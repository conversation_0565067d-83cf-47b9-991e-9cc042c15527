import keys from 'lodash/keys';
import values from 'lodash/values';
import { IFeature } from './components/ListRoles';

export function groupByMsxName(list: IFeature[] | undefined) {
  if (!list || list.length === 0) {
    return { transformedArray: [], keyList: [] };
  } else {
    const grouped = list?.reduce<Record<string, IFeature[]>>((acc, curr) => {
      const updatedCurr = {
        ...curr,
        label: curr.name || curr.name,
        value: curr.name || curr.name,
      };
      if (!acc[updatedCurr.msxName]) {
        acc[updatedCurr.msxName] = [];
      }
      acc[curr.msxName].push(updatedCurr);
      return acc;
    }, {});
    const transformedArray = values(grouped);
    const keyList = keys(grouped);
    return { transformedArray, keyList };
  }
}
