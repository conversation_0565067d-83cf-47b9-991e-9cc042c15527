import { App, But<PERSON>, Flex, Select, TableColumnsType } from 'antd';
import { useMemo, useState } from 'react';
import BreadCrumbComponent from '../../components/breadCrumb';
import InputSearch from '../../components/input/InputSearch';
import ConfirmDeleteModal from '../../components/modal/specials/ConfirmDeleteModal';
import { modalConfirm } from '../../components/modal/specials/ModalConfirm';
import TableComponent from '../../components/table';
import { ActionsColumns } from '../../components/table/components/ActionsColumns';
import { OPTIONS_STATES } from '../../constants/common';
import { useFetch, useUpdateField } from '../../hooks';
import { getListUserRoles, sendSoftDeleteRole, sendUpdateRole } from '../../service/roles';
import { columns } from './columns';
import FormModalRole, { TPermissions } from './components/FormModalRole';
import { ListOfRolesProvider, useModalRoles } from './context/ListOfUnitsContext';
import './styles.scss';
import useFilter from '../../hooks/filter';
import { MutationFunction } from '@tanstack/react-query';

export interface IManagementRole {
  [key: string]: unknown;
  id?: string;
  name?: string;
  code?: string;
  createdDate?: string;
  createdBy?: string;
  modifiedBy?: string;
  description?: string;
  type?: string | null;
  modifiedDate?: string;
  active?: boolean;
  permissions: TPermissions[];
}

const ListRolesPage: React.FC = () => {
  const { dispatch } = useModalRoles();
  const { modal } = App.useApp();
  const [isOpenModalDelete, setIsOpenModalDelete] = useState<boolean>(false);
  const [currentRecord, setCurrentRecord] = useState<IManagementRole>();
  const [filter, setFilter] = useFilter();

  const openModal = () => {
    dispatch({ type: 'OPEN_MODAL', payload: { typeModal: 'create' } });
  };

  const { mutateAsync: updateActive } = useUpdateField<IManagementRole>({
    keyOfListQuery: ['user-roles'],
    apiQuery: sendUpdateRole,
    label: 'Trạng thái',
  });
  const { data, isPlaceholderData, isLoading, isFetching } = useFetch<IManagementRole[]>({
    queryKeyArrWithFilter: ['user-roles'],
    api: getListUserRoles,
  });

  const columnActions: TableColumnsType<IManagementRole> = useMemo(() => {
    return [
      ...columns,
      {
        title: 'Hành động',
        dataIndex: 'action',
        key: 'action',
        width: '100px',
        align: 'center',
        render: (_: unknown, record: IManagementRole) => {
          const textModalConfirmActive = record?.active ? 'Vô hiệu hoá' : 'Kích hoạt';
          const handleActiveRole = () => {
            return modalConfirm({
              modal: modal,
              title: `${textModalConfirmActive} vai trò`,
              content: `Bạn chắc chắn muốn ${textModalConfirmActive} vai trò này?`,
              handleConfirm: () => updateActive({ ...record, active: !record.active }),
            });
          };
          const handleDeleteRole = () => {
            setIsOpenModalDelete(true);
            setCurrentRecord(record);
          };
          const openViewDetail = () => {
            dispatch({ type: 'OPEN_MODAL', payload: { typeModal: 'detail', idDetail: record?.id } });
          };
          const cloneRole = () => {
            dispatch({ type: 'OPEN_MODAL', payload: { typeModal: 'clone', idDetail: record?.id } });
          };

          return (
            <ActionsColumns
              handleViewDetail={openViewDetail}
              handleCloneRole={cloneRole}
              textModalConfirmActive={textModalConfirmActive}
              handleActive={handleActiveRole}
              handleDelete={handleDeleteRole}
            />
          );
        },
      },
    ];
  }, [dispatch, modal, updateActive]);

  const filterStatus = (value: string) => {
    setFilter({ ...filter, page: '1', isActive: value !== undefined ? value.toString() : '' });
  };

  return (
    <div className="wrapper-list-roles">
      <BreadCrumbComponent />
      <div className="header-content">
        <Flex gap={24}>
          <InputSearch />
          <Select
            placeholder="Lọc theo trạng thái"
            className="search-status"
            onChange={filterStatus}
            allowClear
            options={OPTIONS_STATES}
          />
        </Flex>
        <Button type="primary" onClick={openModal}>
          Thêm mới
        </Button>
      </div>

      <div className="user-Roles">
        <TableComponent
          queryKeyArr={['user-roles']}
          columns={columnActions}
          loading={isLoading || isPlaceholderData || isFetching}
          dataSource={data?.data?.data?.rows ?? []}
          rowKey="id"
        />
      </div>
      <FormModalRole />
      <ConfirmDeleteModal
        label="vai trò"
        path="/user-roles"
        open={isOpenModalDelete}
        apiQuery={sendSoftDeleteRole as MutationFunction<unknown, unknown>}
        keyOfListQuery={['user-roles']}
        onCancel={() => setIsOpenModalDelete(false)}
        idDetail={currentRecord?.id}
        title="Xoá vai trò"
        description="Vui lòng nhập lý do muốn xoá vai trò này"
      />
    </div>
  );
};
function UserRolesManagement() {
  return (
    <ListOfRolesProvider>
      <ListRolesPage />
    </ListOfRolesProvider>
  );
}
export default UserRolesManagement;
