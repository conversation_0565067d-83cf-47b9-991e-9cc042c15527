import { TableColumnsType, Typography } from 'antd';
import dayjs from 'dayjs';
import { FORMAT_DATE } from '../../constants/common';
const { Text } = Typography;

export interface IListRolesAssignment {
  code: string;
  email: string;
  name: string;
  pos: {
    code: string;
    name: string;
  };
}

export const columns: TableColumnsType = [
  {
    title: 'Mã vai trò',
    key: 'code',
    dataIndex: 'code',
    width: '150px',
    render: value => (value ? value : '-'),
  },
  {
    title: 'Tên vai trò',
    key: 'name',
    dataIndex: 'name',
    width: '200px',
    render: value => (value ? value : '-'),
  },
  {
    title: 'Trạng thái',
    key: 'active',
    dataIndex: 'active',
    align: 'center',
    width: '150px',
    render: value => (value ? <Text type="success">Đ<PERSON> kích hoạt</Text> : <Text type="danger"><PERSON><PERSON><PERSON> k<PERSON>ch hoạ<PERSON></Text>),
  },

  {
    title: '<PERSON><PERSON><PERSON> cập nhật',
    key: 'modifiedDate',
    dataIndex: 'modifiedDate',
    width: '140px',
    render: value => (value ? dayjs(value).format(FORMAT_DATE) : '-'),
  },
  {
    title: 'Người cập nhật',
    key: 'modifiedBy',
    dataIndex: 'modifiedBy',
    width: '140px',
    render: value => (value ? value : '-'),
  },
  {
    title: 'Ngày Tạo',
    key: 'createdDate',
    dataIndex: 'createdDate',
    width: '140px',
    render: value => (value ? dayjs(value).format(FORMAT_DATE) : '-'),
  },
  {
    title: 'Người tạo',
    key: 'createdBy',
    dataIndex: 'createdBy',
    width: '140px',
    render: value => (value ? value : '-'),
  },
];
