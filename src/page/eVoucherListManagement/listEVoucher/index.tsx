import { Flex } from 'antd';
import BreadCrumbComponent from '../../../components/breadCrumb';
import TableComponent from '../../../components/table';
import { useFetch } from '../../../hooks';
import FilterSearch from './components/filterSearch';
import './styles.scss';
import { ILead } from '../../../types/leadSources';
import { columns } from './columns';
import { getListEVoucher } from '../../../service/eVoucherList';
import { useStoreEVoucherList } from '../store';

function ListEVoucher() {
  const { getCurrentFilter } = useStoreEVoucherList();
  const {
    data: listEVoucher,
    isLoading,
    isPlaceholderData,
    isFetching,
  } = useFetch<ILead[]>({
    queryKeyArrWithFilter: ['get-list-e-voucher', getCurrentFilter()],
    api: getListEVoucher,
    moreParams: {
      ...(getCurrentFilter() as Record<string, string | undefined>),
    },
  });

  return (
    <div className="wrapper-list-e-voucher">
      <BreadCrumbComponent />
      <div className="header-content">
        <Flex gap={17}>
          <FilterSearch />
        </Flex>
      </div>
      <div className="table-e-voucher">
        <TableComponent
          queryKeyArr={['get-list-e-voucher', getCurrentFilter()]}
          columns={columns}
          loading={isLoading || isPlaceholderData || isFetching}
          dataSource={listEVoucher?.data?.data?.rows ?? []}
          rowKey="id"
        />
      </div>
    </div>
  );
}

export default ListEVoucher;
