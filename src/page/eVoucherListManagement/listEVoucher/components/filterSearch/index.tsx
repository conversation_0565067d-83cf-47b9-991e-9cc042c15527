import { Form } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { useEffect, useMemo, useState } from 'react';
import { useLocation } from 'react-router-dom';
import DropdownFilterSearch from '../../../../../components/dropdown/dropdownFilterSearch';
import { DEFAULT_PARAMS, FORMAT_DATE_API } from '../../../../../constants/common';
import useFilter from '../../../../../hooks/filter';
import './styles.scss';
import { useStoreEVoucherList } from '../../../store';

type TFilter = {
  person?: string;
  isActive?: number | null;
  startDate?: string | Dayjs | null;
  endDate?: string | Dayjs | null;
};

function FilterSearch() {
  const [form] = Form.useForm();
  const [initialValues, setInitialValues] = useState<TFilter>();
  const [, setFilter] = useFilter();
  const { search } = useLocation();
  const params = useMemo(() => new URLSearchParams(search), [search]);

  const { setFilter: setFilterParam, getCurrentFilter } = useStoreEVoucherList();

  useEffect(() => {
    setInitialValues({
      startDate: params.get('startDate') ? dayjs(params.get('startDate')) : undefined,
      endDate: params.get('endDate') ? dayjs(params.get('endDate')) : undefined,
    });
  }, [params]);

  const handleSubmitFilter = (values: TFilter) => {
    const newFilter: Record<string, unknown> = {
      startDate: values?.startDate ? dayjs(values?.startDate).format(FORMAT_DATE_API) : null,
      endDate: values?.endDate ? dayjs(values?.endDate).format(FORMAT_DATE_API) : null,
    };
    setFilterParam({ ...getCurrentFilter(), ...newFilter } as Record<string, unknown>);
    setFilter({ ...DEFAULT_PARAMS });
  };

  const handleSearch = (value: string) => {
    const search = value ? value : '';
    setFilter({ ...DEFAULT_PARAMS });
    setFilterParam({ ...getCurrentFilter(), search } as Record<string, unknown>);
  };

  return (
    <>
      <DropdownFilterSearch
        rootClassName="wrapper-filter-customers"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        showParams={false}
        onChangeSearch={handleSearch}
        handleOpenChange={() => {}}
        isOpenFilter={false}
        initialValues={initialValues}
        form={form}
        onClearFilters={() => {}}
      />
    </>
  );
}

export default FilterSearch;
