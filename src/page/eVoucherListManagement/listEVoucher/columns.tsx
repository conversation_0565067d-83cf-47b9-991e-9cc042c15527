import { TableColumnsType, Typography } from 'antd';
import { E_VOUCHER } from '../../../configs/path';

export const columns: TableColumnsType = [
  {
    title: 'Tên chương trình',
    dataIndex: ['voucher', 'nameOfHandover'],
    width: '50%',
    key: 'nameOfHandover',
    render: (value: string, record) => {
      return (
        <Typography.Link
          onClick={() => {
            window.open(`${E_VOUCHER}/${record?.id}`, '_blank', 'noopener noreferrer');
          }}
        >
          {value}
        </Typography.Link>
      );
    },
  },
  {
    title: 'Tên voucher',
    dataIndex: ['voucher', 'title'],
    width: '50%',
    key: 'title',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'ĐVBH',
    dataIndex: ['exchange', 'name'],
    width: '45%',
    key: 'exChangeName',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Số điện thoại',
    dataIndex: 'phone',
    width: '45%',
    key: 'phone',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Tên nhân viên',
    dataIndex: 'name',
    width: '45%',
    key: 'name',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Số lượng đã phân phối',
    dataIndex: 'numberOfInit',
    width: '40%',
    key: 'numberOfInit',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Số lượng chờ ĐVKH xác nhận',
    dataIndex: 'numberOfApply',
    width: '40%',
    key: 'numberOfApply',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Số lượng chờ khai báo thông tin',
    dataIndex: 'numberOfDeclare',
    width: '40%',
    key: 'numberOfDeclare',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Số lượng có hiệu lực',
    dataIndex: 'numberOfEffective',
    width: '40%',
    key: 'numberOfEffective',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Số lượng đã sử dụng',
    dataIndex: 'numberOfUsed',
    width: '40%',
    key: 'numberOfUsed',
    render: (value: string) => (value ? value : '-'),
  },
];
