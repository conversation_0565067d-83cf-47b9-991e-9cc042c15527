import { useQueryClient } from '@tanstack/react-query';
import { Button, notification, Typography, Upload } from 'antd';
import { RcFile } from 'antd/es/upload';
import { AxiosError } from 'axios';
import { useState } from 'react';
import { useParams } from 'react-router-dom';
import { COMMISSION_HISTORY_IMPORT } from '../../../../configs/path';
import { uploadFileVersion } from '../../../../service/commission';

const UploadFileVersion = () => {
  const { id } = useParams();
  const queryClient = useQueryClient();

  const [isLoadingImport, setIsLoadingImport] = useState(false);

  const handleBeforeUpload = async (file: RcFile) => {
    const isAllowedType = file?.name.toLowerCase().endsWith('.xlsx');
    if (!isAllowedType) {
      notification.error({ message: 'File không đúng định dạng. Vui lòng sử dụng file .xlsx' });
      return Upload.LIST_IGNORE;
    }
    return true;
  };
  return (
    <Upload
      beforeUpload={handleBeforeUpload}
      showUploadList={false}
      customRequest={async ({ file, onSuccess, onError }) => {
        try {
          setIsLoadingImport(true);
          const response = await uploadFileVersion(file as RcFile, id as string);
          const link = (
            <Typography.Link href={COMMISSION_HISTORY_IMPORT} target="_blank">
              đây
            </Typography.Link>
          );
          if (response?.data?.statusCode === '0') {
            notification.success({ message: 'Hoàn thành upload giao dịch' });
            await queryClient.invalidateQueries({
              queryKey: ['get-by-id-commission-period', id],
            });
          } else {
            notification.error({
              message: <>Upload thất bại, xem tại {link}</>,
            });
          }

          onSuccess && onSuccess('ok');
        } catch (error: unknown) {
          onError && onError(error as AxiosError);
        } finally {
          setIsLoadingImport(false);
        }
      }}
      name="file-upload"
    >
      <Button type="default" loading={isLoadingImport}>
        Tải nhập giao dịch
      </Button>
    </Upload>
  );
};

export default UploadFileVersion;
