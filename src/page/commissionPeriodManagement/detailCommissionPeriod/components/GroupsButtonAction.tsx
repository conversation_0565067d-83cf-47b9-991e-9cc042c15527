import { useQueryClient } from '@tanstack/react-query';
import { Button, notification, Space } from 'antd';
import { ButtonType } from 'antd/es/button';
import { NotificationInstance } from 'antd/es/notification/interface';
import { HookAPI } from 'antd/lib/modal/useModal';
import React from 'react';
import { useParams } from 'react-router-dom';
import { modalConfirm } from '../../../../components/modal/specials/ModalConfirm';
import { FetchResponse, TDataList, useCheckPermissions, useUpdateField } from '../../../../hooks';
import { TAdjustmentVersion, TCommission } from '../../../../types/commission';
import { PERMISSION_COMMISSION } from '../../../../constants/permissions/commission';
import UploadFileVersion from './UploadFileVersion';
import { updateAnNounced } from '../../../../service/commission';
import { STATUS_ADJUSTMENT_VERSION } from '../../../../constants/common';

interface GroupButtonProps {
  handleStatusUpdate: (status: string, successMessage: string) => Promise<void>;
  modal: HookAPI;
  notification: NotificationInstance;
  tab: string;
  versionAdjustment?: TAdjustmentVersion | null;
  loadingButton?: boolean;
  setSelectedAdjustment: React.Dispatch<React.SetStateAction<TAdjustmentVersion | null>>;
}
interface ButtonConfig {
  key: string;
  label: string;
  permission?: boolean;
  onClick?: () => void;
  type?: ButtonType;
  component?: React.ReactNode;
  loading?: boolean;
}
export const GroupButtonAction: React.FC<GroupButtonProps> = ({
  modal,
  versionAdjustment,
  tab,
  handleStatusUpdate,
  loadingButton,
  setSelectedAdjustment,
}) => {
  const { id } = useParams();
  const queryClient = useQueryClient();
  const mutateAnNounced = useUpdateField({
    apiQuery: updateAnNounced,
    keyOfDetailQuery: ['get-by-id-commission-period', id],
    messageSuccess: 'Công bố kỳ tính phí thành công',
    isMessageError: false,
  });

  const data = queryClient.getQueryData<FetchResponse<TDataList<TCommission>>>(['get-by-id-commission-period', id]);
  const dataCommission = data?.data?.data;
  const v1Adjustment = dataCommission?.adjustmentVersions?.[0];
  const { publish, create, update, getById, approve } = useCheckPermissions(PERMISSION_COMMISSION);
  const isButtonApprove =
    (versionAdjustment?.status === STATUS_ADJUSTMENT_VERSION.WAITING ||
      (v1Adjustment?.status === STATUS_ADJUSTMENT_VERSION.WAITING && tab === 'original')) &&
    approve;

  const isButtonSendApprove =
    versionAdjustment?.status === STATUS_ADJUSTMENT_VERSION.NEW ||
    versionAdjustment?.status === STATUS_ADJUSTMENT_VERSION.REJECTED ||
    (tab === 'original' &&
      (v1Adjustment?.status === STATUS_ADJUSTMENT_VERSION.NEW ||
        v1Adjustment?.status === STATUS_ADJUSTMENT_VERSION.REJECTED));

  const isButtonPublish =
    ((v1Adjustment?.status === STATUS_ADJUSTMENT_VERSION.APPROVED && tab === 'original') ||
      versionAdjustment?.status === STATUS_ADJUSTMENT_VERSION.APPROVED) &&
    publish;

  const handleDownloadOriginal = async () => {
    const linkDownload = v1Adjustment?.fileUrl;
    const nameFile = `Commission_${dataCommission?.period}.xlsx`;
    if (!linkDownload) {
      notification.error({ message: 'Không tìm thấy đường dẫn tải xuống' });
      return;
    }
    const response = await fetch(linkDownload);
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${nameFile}`;
    link.click();
    window.URL.revokeObjectURL(url);
  };

  // Danh sách các nút với điều kiện hiển thị
  const buttonConfigs: ButtonConfig[] = [
    {
      key: 'approve',
      label: 'Duyệt',
      permission: isButtonApprove,
      loading: loadingButton,
      onClick: () => {
        modalConfirm({
          title: 'Duyệt đợt tính phí',
          content: 'Bạn có muốn duyệt đợt tính phí không?',
          handleConfirm: () => {
            handleStatusUpdate('APPROVED', 'Duyệt thành công');
          },
          modal,
        });
      },
      type: 'default',
    },
    {
      key: 'sendApprove',
      label: 'Gửi duyệt',
      permission: isButtonSendApprove,
      loading: loadingButton,
      onClick: () => {
        modalConfirm({
          title: 'Gửi duyệt đợt tính phí',
          content: 'Bạn có muốn gửi duyệt đợt tính phí không?',
          handleConfirm: () => {
            handleStatusUpdate('WAITING', 'Gửi duyệt thành công');
          },
          modal,
        });
      },
      type: 'primary',
    },
    {
      key: 'publish',
      label: 'Công bố',
      permission: isButtonPublish,
      loading: mutateAnNounced.isPending,
      onClick: () => {
        modalConfirm({
          title: 'Xác nhận công bố',
          content: 'Bạn có muốn công bố đợt tính phí không?',
          handleConfirm: async () => {
            if (id) {
              const res = await mutateAnNounced.mutateAsync({
                id,
                status: versionAdjustment?.status || v1Adjustment?.status || '',
                adjustmentVersionId: versionAdjustment?.id || v1Adjustment?.id || '',
              });
              if (res?.data?.statusCode === '0') {
                setSelectedAdjustment(null);
              }
            }
          },
          modal,
        });
      },
      type: 'default',
    },
    {
      key: 'reject',
      label: 'Từ chối ',
      permission: isButtonApprove,
      loading: loadingButton,
      onClick: () => {
        modalConfirm({
          title: 'Từ chối đợt tính phí',
          content: 'Bạn có muốn từ chối đợt tính phí không?',
          handleConfirm: () => {
            handleStatusUpdate('REJECTED', 'Từ chối thành công');
          },
          modal,
        });
      },
      type: 'default',
    },
    {
      key: 'uploadTemplate',
      label: 'Tải nhập giao dịch',
      permission: update,
      component: <UploadFileVersion />,
    },
    {
      key: 'downloadOriginal',
      label: 'Tải về dữ liệu gốc',
      permission: create || update || getById,
      onClick: () => {
        handleDownloadOriginal();
      },
      type: 'default',
    },
  ];

  return (
    <Space>
      {buttonConfigs.map(button => {
        if (!button.permission) return null;

        return (
          <React.Fragment key={button.key}>
            {button.component ? (
              button.component
            ) : (
              <Button className={button.key} loading={button?.loading} type={button.type} onClick={button.onClick}>
                {button.label}
              </Button>
            )}
          </React.Fragment>
        );
      })}
    </Space>
  );
};
