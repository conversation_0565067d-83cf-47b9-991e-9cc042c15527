import { useQueryClient } from '@tanstack/react-query';
import { Button, notification, Space } from 'antd';
import { ButtonType } from 'antd/es/button';
import { NotificationInstance } from 'antd/es/notification/interface';
import { HookAPI } from 'antd/lib/modal/useModal';
import React from 'react';
import { useParams } from 'react-router-dom';
import { modalConfirm } from '../../../../components/modal/specials/ModalConfirm';
import { FetchResponse, TDataList, useCheckPermissions, useUpdateField } from '../../../../hooks';
import { TAdjustmentVersion, TCommission } from '../../../../types/commission';
import { PERMISSION_COMMISSION } from '../../../../constants/permissions/commission';
import UploadFileVersion from './UploadFileVersion';
import { updateAnNounced } from '../../../../service/commission';

interface GroupButtonProps {
  handleStatusUpdate: (status: string, successMessage: string) => Promise<void>;
  modal: HookAPI;
  notification: NotificationInstance;
  tab: string;
  versionAdjustment?: TAdjustmentVersion | null;
}
interface ButtonConfig {
  key: string;
  label: string;
  permission?: boolean;
  onClick?: () => void;
  type?: ButtonType;
  component?: React.ReactNode;
  loading?: boolean;
}
export const GroupButtonAction: React.FC<GroupButtonProps> = ({
  modal,
  versionAdjustment,
  tab,
  handleStatusUpdate,
}) => {
  const { id } = useParams();
  const queryClient = useQueryClient();
  const mutateAnNounced = useUpdateField({
    apiQuery: updateAnNounced,
    keyOfDetailQuery: ['get-by-id-commission-period', id],
    messageSuccess: 'Công bố kỳ tính phí thành công',
  });

  const data = queryClient.getQueryData<FetchResponse<TDataList<TCommission>>>(['get-by-id-commission-period', id]);
  const dataCommission = data?.data?.data;
  const rootData = dataCommission?.adjustmentVersions?.find(item => item?.version === 'V.1');
  const v1Adjustment = dataCommission?.adjustmentVersions?.[0];
  const { publish, create, update, getById, approve } = useCheckPermissions(PERMISSION_COMMISSION);

  // Danh sách các nút với điều kiện hiển thị
  const buttonConfigs: ButtonConfig[] = [
    {
      key: 'approve',
      label: 'Duyệt',
      permission:
        (versionAdjustment?.status === 'WAITING' || (v1Adjustment?.status === 'WAITING' && tab === 'root')) && approve,
      onClick: () => {
        modalConfirm({
          title: 'Duyệt đợt tính phí',
          content: 'Bạn có muốn duyệt đợt tính phí không?',
          handleConfirm: () => {
            handleStatusUpdate('APPROVED', 'Duyệt thành công');
          },
          modal,
        });
      },
      type: 'default',
    },
    {
      key: 'sendApprove',
      label: 'Gửi duyệt',
      permission: !!(
        (versionAdjustment && versionAdjustment?.status !== 'APPROVED') ||
        (v1Adjustment && v1Adjustment?.status !== 'APPROVED' && tab === 'root')
      ),
      onClick: () => {
        modalConfirm({
          title: 'Gửi duyệt đợt tính phí',
          content: 'Bạn có muốn gửi duyệt đợt tính phí không?',
          handleConfirm: () => {
            handleStatusUpdate('WAITING', 'Gửi duyệt thành công');
          },
          modal,
        });
      },
      type: 'primary',
    },
    {
      key: 'publish',
      label: 'Công bố',
      permission: publish,
      onClick: () => {
        modalConfirm({
          title: 'Xác nhận công bố',
          content: 'Bạn có muốn công bố đợt tính phí không?',
          handleConfirm: async () => {
            await mutateAnNounced.mutateAsync(id);
          },
          modal,
        });
      },
      type: 'default',
    },
    {
      key: 'reject',
      label: 'Từ chối ',
      permission:
        (versionAdjustment?.status === 'WAITING' || (v1Adjustment?.status === 'WAITING' && tab === 'root')) && approve,
      onClick: () => {
        modalConfirm({
          title: 'Từ chối đợt tính phí',
          content: 'Bạn có muốn từ chối đợt tính phí không?',
          handleConfirm: () => {
            handleStatusUpdate('REJECTED', 'Từ chối thành công');
          },
          modal,
        });
      },
      type: 'default',
    },
    {
      key: 'uploadTemplate',
      label: 'Tải nhập giao dịch',
      permission: update,
      component: <UploadFileVersion />,
    },
    {
      key: 'downloadOriginal',
      label: 'Tải về dữ liệu gốc',
      permission: create || update || getById,
      onClick: () => {
        if (rootData?.fileUrl) {
          window.open(rootData.fileUrl, '_blank');
        } else {
          notification.error({ message: 'Không tìm thấy đường dẫn file S3' });
        }
      },
      type: 'default',
    },
  ];

  return (
    <Space>
      {buttonConfigs.map(button => {
        if (!button.permission) return null;

        return (
          <React.Fragment key={button.key}>
            {button.component ? (
              button.component
            ) : (
              <Button className={button.key} loading={button?.loading} type={button.type} onClick={button.onClick}>
                {button.label}
              </Button>
            )}
          </React.Fragment>
        );
      })}
    </Space>
  );
};
