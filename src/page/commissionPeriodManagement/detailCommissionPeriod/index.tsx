import { App, Button, Col, Flex, Form, Input, Radio, Row, Spin, Table, Tabs, Typography } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useParams, useSearchParams } from 'react-router-dom';
import BreadCrumbComponent from '../../../components/breadCrumb';
import ButtonOfPageDetail from '../../../components/button/buttonOfPageDetail';
import FormPeriod from '../../../components/select/FormPeriod';
import SingleSelectLazy from '../../../components/select/singleSelectLazy';
import { PERMISSION_COMMISSION } from '../../../constants/permissions/commission';
import { useCheckPermissions, useFetch, useUpdateField } from '../../../hooks';
import { updateStatusCommission } from '../../../service/commission';
import {
  calculateCommissionPeriod,
  getAllOfCommissionIndicatorPos,
  getAllOfCommissionPolicyPos,
  getByIdCommissionPeriod,
  getSalesPolicyOfCommissionPeriod,
  putCommissionPeriod,
} from '../../../service/commissionPeriod';
import { getOrgChartDropdown } from '../../../service/lead';
import {
  CommissionTransaction,
  TAdjustmentVersion,
  TCommission,
  TDataSubmitCommissionPeriod,
  TSalePolicy,
} from '../../../types/commission';
import { ICommissionPolicy } from '../../../types/commissionPolicy';
import { Units } from '../../../types/units/units';
import FilterTransaction from '../../commissionManagement/detailOfCommission/components/FilterTransaction';
import { transactionColumns, versionColumns } from './columns';
import { GroupButtonAction } from './components/GroupsButtonAction';
import './styles.scss';
import dayjs from 'dayjs';
import { FORMAT_DATE_TIME } from '../../../constants/common';

const { Title, Text } = Typography;

const formatDataSubmit = (data: TCommission): TDataSubmitCommissionPeriod => {
  return {
    ...data?.periodObj,
    commissionCode: data?.code,
    pos: data?.pos,
    salePolicyCode: data?.salePolicy?.code,
    year: String(data.year),
    indicatorCode: data?.indicator.code,
    commissionPolicyPersonalCode: data?.commissionPolicyPersonal?.code,
    commissionPolicyManagerCode: data?.commissionPolicyManager?.code,
  };
};

const DetailCommissionPeriod = () => {
  const { id } = useParams();
  const [searchParams, setSearchParams] = useSearchParams();
  const params = Object.fromEntries([...searchParams]);
  const { modal, notification } = App.useApp();
  const { update } = useCheckPermissions(PERMISSION_COMMISSION);
  const [form] = Form.useForm();
  const pos = Form.useWatch('pos', form);
  const salePolicy = Form.useWatch('salePolicy', form);
  const commissionPolicyManager = Form.useWatch('commissionPolicyManager', form);
  const commissionPolicyPersonal = Form.useWatch('commissionPolicyPersonal', form);
  const indicator = Form.useWatch('indicator', form);
  const period = Form.useWatch('period', form);
  const year = Form.useWatch('year', form);

  const [isModified, setIsModified] = useState(false);
  const [tab, setTab] = useState(params?.tabData || 'original');
  const [initialValue, setInitialValue] = useState<TCommission>({} as TCommission);
  const [dataRoot, setDataRoot] = useState<CommissionTransaction[]>();
  const [selectedAdjustment, setSelectedAdjustment] = useState<TAdjustmentVersion | null>();

  const { data, isLoading: isLoadingRoot } = useFetch<TCommission>({
    api: () => id && getByIdCommissionPeriod(id),
    queryKeyArr: ['get-by-id-commission-period', id],
    withFilter: false,
    enabled: !!id,
  });
  const dataSource = data?.data?.data;

  const { mutateAsync, isPending } = useUpdateField({
    apiQuery: putCommissionPeriod,
    keyOfDetailQuery: ['get-by-id-commission-period', id],
    isMessageError: false,
  });

  const updateCommissionStatus = useUpdateField({
    apiQuery: updateStatusCommission,
    keyOfDetailQuery: ['get-by-id-commission-period', id],
    isMessageSuccess: false,
  });

  useEffect(() => {
    const initialValue = {
      ...dataSource,
      periodObj: {
        periodFrom: dataSource?.periodFrom,
        periodTo: dataSource?.periodTo,
        periodName: dataSource?.periodName,
      },
      transactions: dataSource?.transactions?.map(item => ({
        ...item,
        employees: item?.employees?.[0],
        children: item?.employees?.[0]?.managers?.map(item => ({
          employees: { ...item, code: item?.code, name: item?.name },
        })),
      })),
    };
    form.setFieldsValue(initialValue);

    setInitialValue(initialValue as TCommission);
  }, [dataSource, form]);

  const disabled = useMemo(() => {
    return !!(pos && period && year);
  }, [pos, period, year]);

  const handleSubmit = async () => {
    await form.validateFields();
    const allValues = form.getFieldsValue(true);
    const payload = formatDataSubmit(allValues);

    const res = await mutateAsync({ ...payload, id });
    if (res?.data?.statusCode === '0') {
      setIsModified(false);
    }
  };

  const handleCancel = () => {
    form.setFieldsValue(initialValue);
    setIsModified(false);
  };

  const handleChangeTab = (key: string) => {
    setTab(key);
    setSearchParams({ ...params, tabData: key });
  };

  const handleStatusUpdate = async (status: string, successMessage: string) => {
    const idVersion = tab === 'original' ? dataSource?.adjustmentVersions[0]?.id : selectedAdjustment?.id;

    if (selectedAdjustment?.status === 'WAITING' && status === 'WAITING') {
      notification.info({
        message: 'Phiên bản đang được phê duyệt',
      });
      return;
    }

    const res = await updateCommissionStatus.mutateAsync({
      id,
      status,
      adjustmentVersionId: idVersion,
    });

    if (res?.data?.statusCode === '0') {
      notification.success({
        message: successMessage,
      });
      setSelectedAdjustment(null);
    }
  };

  const handleCharge = useCallback(async () => {
    const allValues = form.getFieldsValue(true);

    await form.validateFields();
    const payload = formatDataSubmit(allValues);
    const res = await calculateCommissionPeriod(payload);
    setIsModified(true);
    setDataRoot(JSON.stringify(res?.data?.data?.data) === '{}' ? [] : res?.data?.data?.data);
  }, [form]);

  const expandedRowKeys = dataSource?.transactions?.map(item => item?.id);

  const RadioColumns: ColumnsType<TAdjustmentVersion> = [
    {
      title: '',
      dataIndex: 'radio',
      key: 'radio',
      align: 'center',
      width: 48,
      render: (_, record) => (
        <Radio
          checked={selectedAdjustment?.id === record.id}
          onClick={() => {
            if (selectedAdjustment?.id === record.id) {
              setSelectedAdjustment(null);
            } else {
              setSelectedAdjustment(record);
            }
          }}
        />
      ),
    },
    ...versionColumns,
  ];

  const items = [
    {
      label: 'Dữ liệu gốc',
      key: 'original',
      children: (
        <Table
          className="table-root-list"
          columns={transactionColumns as unknown as ColumnsType}
          dataSource={dataRoot ? dataRoot || [] : initialValue?.transactions}
          loading={isLoadingRoot}
          rowClassName={record => (record.children ? 'parent-row' : '')}
          rowKey={'id'}
          pagination={false}
          footer={() => <></>}
          scroll={{ x: 'max-content' }}
          expandable={{
            expandIcon: () => null,
            expandedRowKeys, // Luôn mở rộng các dòng cha
          }}
        />
      ),
    },

    {
      label: 'Dữ liệu điều chỉnh',
      key: 'adjustment',
      children: (
        <Table
          className="table-adjustment-list"
          pagination={false}
          footer={() => <></>}
          rowKey={'id'}
          columns={RadioColumns}
          dataSource={initialValue?.adjustmentVersions}
        />
      ),
    },
  ];

  const handleSelectSalePos = (value: Units) => {
    form.setFieldsValue({
      pos: value
        ? {
            id: value?.id,
            name: value?.name,
            code: value?.code,
          }
        : undefined,
    });
    handleClearFormFields();
    setIsModified(true);
  };

  const handleSelectSalePolicy = (value: TSalePolicy) => {
    form.setFieldsValue({ salePolicy: value });
    setIsModified(true);
  };

  const handleSelectCommissionPolicyPosManager = (value: ICommissionPolicy) => {
    form.setFieldsValue({ commissionPolicyManager: value });
    setIsModified(true);
  };

  const handleSelectCommissionPolicyPosPersonal = (value: ICommissionPolicy) => {
    form.setFieldsValue({ commissionPolicyPersonal: value });
    setIsModified(true);
  };

  const handleSelectCommissionIndicatorPos = (value: ICommissionPolicy) => {
    form.setFieldsValue({ indicator: value });
    setIsModified(true);
  };

  const validateForm = () => {
    setIsModified(true);
  };
  const handleClearFormFields = () => {
    form.setFieldsValue({
      salePolicy: undefined,
      indicator: undefined,
      commissionPolicyPersonal: undefined,
      commissionPolicyManager: undefined,
    });
    setIsModified(true);
  };

  return (
    <>
      <Spin spinning={isLoadingRoot}>
        <BreadCrumbComponent titleBread={dataSource?.code} />
        <Form
          form={form}
          layout="vertical"
          onValuesChange={validateForm}
          onFinish={handleSubmit}
          initialValues={initialValue}
          className="wrapper-detail-commission"
        >
          <Title level={5}>Thông tin chi tiết</Title>
          <Row gutter={{ md: 24, lg: 40, xl: 80, xxl: 126 }}>
            <Col sm={12} xs={24}>
              <Row gutter={24}>
                <Col sm={12} xs={24}>
                  <Form.Item label="Mã đợt tính hoa hồng" required name="code">
                    <Input disabled />
                  </Form.Item>
                </Col>
                <Col sm={12} xs={24}>
                  <Form.Item label="Loại" required>
                    <Input disabled value={'Tính hoa hồng'} />
                  </Form.Item>
                </Col>

                <Col span={24}>
                  <Form.Item
                    label="Đơn vị"
                    name="pos"
                    required
                    rules={[{ required: true, message: 'Vui lòng chọn đơn vị' }]}
                  >
                    <SingleSelectLazy
                      apiQuery={getOrgChartDropdown}
                      queryKey={['orgChart-exchanges']}
                      keysLabel={'name'}
                      placeholder="Chọn đơn vị"
                      handleSelect={handleSelectSalePos}
                      defaultValues={{ value: pos?.code, label: pos?.name }}
                    />
                  </Form.Item>
                </Col>
                <Col span={24}>
                  <FormPeriod
                    required
                    label="Kỳ tính hoa hồng"
                    fieldPos="pos"
                    clearFormValueDependency={handleClearFormFields}
                  />
                </Col>

                <Col sm={12} xs={24}>
                  <Form.Item
                    label="Chính sách phí - hoa hồng"
                    required
                    name="salePolicy"
                    rules={[{ required: true, message: 'Vui lòng chọn chính sách phí - hoa hồng' }]}
                  >
                    <SingleSelectLazy
                      apiQuery={getSalesPolicyOfCommissionPeriod}
                      queryKey={['sales-policy']}
                      keysLabel={['code', 'name']}
                      placeholder="Chọn chính sách phí - hoa hồng"
                      handleSelect={handleSelectSalePolicy}
                      disabled={!disabled}
                      enabled={disabled && !!pos?.name}
                      moreParams={{ name: pos?.name }}
                      defaultValues={{
                        value: salePolicy?.id,
                        label: salePolicy?.name,
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="Chỉ tiêu"
                    name="indicator"
                    rules={[{ required: true, message: 'Vui lòng chọn chỉ tiêu' }]}
                  >
                    <SingleSelectLazy
                      apiQuery={getAllOfCommissionIndicatorPos}
                      queryKey={['indicator']}
                      keysLabel={'period'}
                      placeholder="Chọn chỉ tiêu"
                      handleSelect={handleSelectCommissionIndicatorPos}
                      disabled={!disabled}
                      enabled={disabled && !!pos?.name && !!period && !!year}
                      moreParams={{
                        name: pos?.name,
                        periodFrom: '2025-01-26T00:00:00.000Z',
                        periodTo: '2025-02-25T00:00:00.000Z',
                        year: year,
                      }}
                      defaultValues={{
                        value: indicator?.id,
                        label: indicator?.name,
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="Bộ chỉ tiêu KPI - Cá nhân"
                    name="commissionPolicyPersonal"
                    rules={[{ required: true, message: 'Vui lòng chọn bộ chỉ tiêu KPI - Cá nhân' }]}
                  >
                    <SingleSelectLazy
                      apiQuery={getAllOfCommissionPolicyPos}
                      queryKey={['commission-policy-pos']}
                      keysLabel={['code', 'name']}
                      placeholder="Chọn bộ chỉ tiêu KPI - cá nhân"
                      moreParams={{ type: 'Cá nhân', name: pos?.name }}
                      handleSelect={handleSelectCommissionPolicyPosPersonal}
                      disabled={!disabled}
                      enabled={disabled && !!pos?.name}
                      defaultValues={{
                        value: commissionPolicyPersonal?.id,
                        label: commissionPolicyPersonal?.name,
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="Bộ chỉ tiêu KPI - Quản lý"
                    name="commissionPolicyManager"
                    rules={[{ required: true, message: 'Vui lòng chọn bộ chỉ tiêu KPI - Quản lý' }]}
                  >
                    <SingleSelectLazy
                      apiQuery={getAllOfCommissionPolicyPos}
                      queryKey={['commission-policy-pos', 'manager']}
                      keysLabel={['code', 'name']}
                      moreParams={{ type: 'Quản lý', name: pos?.name }}
                      placeholder="Chọn bộ chỉ tiêu KPI - Quản lý"
                      handleSelect={handleSelectCommissionPolicyPosManager}
                      disabled={!disabled}
                      enabled={disabled && !!pos?.name}
                      defaultValues={{
                        value: commissionPolicyManager?.id,
                        label: commissionPolicyManager?.name,
                      }}
                    />
                  </Form.Item>
                </Col>
                {update && (
                  <Col span={24}>
                    <Form.Item>
                      <Button
                        type="primary"
                        onClick={() => handleCharge()}
                        disabled={
                          !pos ||
                          !period ||
                          !year ||
                          !salePolicy ||
                          !indicator ||
                          !commissionPolicyPersonal ||
                          !commissionPolicyManager
                        }
                      >
                        Tính hoa hồng
                      </Button>
                    </Form.Item>
                  </Col>
                )}
              </Row>
            </Col>
            <Col sm={12} xs={24}>
              <Row gutter={24}>
                <Col lg={6} xs={8}>
                  <Text disabled>Ngày cập nhật: </Text>
                </Col>
                <Col lg={18} xs={16}>
                  <Text disabled>
                    {dayjs(dataSource?.createdDate).format(FORMAT_DATE_TIME)}&nbsp;&nbsp;
                    {`${dataSource?.createdBy?.userName} - ${dataSource?.createdBy?.fullName}`}
                  </Text>
                </Col>
              </Row>
              <Row gutter={24}>
                <Col lg={6} xs={8}>
                  <Text disabled>Ngày tạo: </Text>
                </Col>
                <Col lg={18} xs={16}>
                  <Text disabled>
                    {dayjs(dataSource?.modifiedDate).format(FORMAT_DATE_TIME)}&nbsp;&nbsp;
                    {`${dataSource?.modifiedBy?.userName} - ${dataSource?.modifiedBy?.fullName}`}
                  </Text>
                </Col>
              </Row>
            </Col>
          </Row>
        </Form>
        <Title level={5}>Danh sách giao dịch</Title>
        <Flex justify="space-between">
          <FilterTransaction />
          <GroupButtonAction
            modal={modal}
            notification={notification}
            versionAdjustment={selectedAdjustment}
            tab={tab}
            handleStatusUpdate={handleStatusUpdate}
          />
        </Flex>
        <Tabs className="tabs-transaction" type="card" activeKey={tab} onChange={handleChangeTab} items={items} />
        {isModified && update && (
          <ButtonOfPageDetail
            handleSubmit={() => form.submit()}
            handleCancel={handleCancel}
            loadingSubmit={isPending}
          />
        )}
      </Spin>
    </>
  );
};

export default DetailCommissionPeriod;
