import { Button, Flex, TableColumnsType } from 'antd';
import { useMemo, useState } from 'react';
import BreadCrumbComponent from '../../../components/breadCrumb';
import TableComponent from '../../../components/table';
import { ActionsColumns } from '../../../components/table/components/ActionsColumns';
import { COMMISSION_PERIOD_LIST } from '../../../configs/path';
import { PERMISSION_COMMISSION } from '../../../constants/permissions/commission';
import { useCheckPermissions, useFetch } from '../../../hooks';
import { getListOfCommissionPeriod } from '../../../service/commissionPeriod';
import { TListOfCommission } from '../../../types/commission';
import CreateCommissionPeriod from '../createCommissionPeriod';
import { columns } from './columns';
import FilterCommissionPeriod from './FilterCommissionPeriod';
import ConfirmDeleteModal from '../../../components/modal/specials/ConfirmDeleteModal';
import { MutationFunction } from '@tanstack/react-query';
import { softDeleteCommission } from '../../../service/commission';

const ListOfCommissionPeriod = () => {
  const [isOpenModalDelete, setIsOpenModalDelete] = useState<boolean>(false);
  const [currentRecord, setCurrentRecord] = useState<TListOfCommission>();
  const [isOpenModalCreate, setIsOpenModalCreate] = useState(false);

  const {
    getById: isCheckGetById,
    publishGetId: isCheckPublishGetId,
    delete: isCheckDelete,
    create: isCheckCreate,
  } = useCheckPermissions(PERMISSION_COMMISSION);

  const { data, isFetching } = useFetch<TListOfCommission[]>({
    queryKeyArrWithFilter: ['list-of-commission-period'],
    api: getListOfCommissionPeriod,
  });
  const dataCommission = data?.data?.data?.rows;

  const actionsColumns: TableColumnsType<TListOfCommission> = useMemo(() => {
    return [
      ...columns,
      {
        title: 'Hành động',
        dataIndex: 'action',
        key: 'action',
        width: '100px',
        align: 'center',
        fixed: 'right',
        render: (_, record: TListOfCommission) => {
          const isDelete = !record?.isPublish && record?.adjustmentVersions?.every(item => item?.status === null);

          const openViewDetail = (): void => {
            window.open(`${COMMISSION_PERIOD_LIST}/${record?.id}`, '_blank', 'noopener,noreferrer');
          };

          const handleDeleteCommission = (): void => {
            setIsOpenModalDelete(true);
            setCurrentRecord(record);
          };

          return (
            <ActionsColumns
              handleViewDetail={isCheckGetById || isCheckPublishGetId ? openViewDetail : undefined}
              handleDelete={isCheckDelete && isDelete ? handleDeleteCommission : undefined}
            />
          );
        },
      },
    ];
  }, [isCheckDelete, isCheckGetById, isCheckPublishGetId]);

  const handleCancelModalCreate = () => {
    setIsOpenModalCreate(false);
  };

  return (
    <div className="wrapper-list-of-commission">
      <BreadCrumbComponent />
      <Flex justify="space-between" style={{ marginBottom: 16 }}>
        <FilterCommissionPeriod />
        {isCheckCreate && (
          <Button type="primary" onClick={() => setIsOpenModalCreate(true)}>
            Thêm mới
          </Button>
        )}
      </Flex>
      <TableComponent
        queryKeyArr={['list-of-commission-period']}
        loading={isFetching}
        columns={actionsColumns}
        dataSource={dataCommission}
        rowKey={'id'}
      />
      <ConfirmDeleteModal
        label="Đợt tính hoa hồng"
        open={isOpenModalDelete}
        apiQuery={softDeleteCommission as MutationFunction<unknown, unknown>}
        keyOfListQuery={['list-of-commission-period']}
        onCancel={() => setIsOpenModalDelete(false)}
        idDetail={currentRecord?.id}
        title="Xóa đợt tính hoa hồng"
        description="Vui lòng nhập lý do muốn xoá đợt tính hoa hồng này"
      />
      <CreateCommissionPeriod handleCancel={handleCancelModalCreate} open={isOpenModalCreate} />
    </div>
  );
};

export default ListOfCommissionPeriod;
