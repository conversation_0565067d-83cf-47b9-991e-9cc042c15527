import { ColumnsType } from 'antd/es/table';
import { TListOfCommission } from '../../../types/commission';
import { Typography } from 'antd';
import dayjs from 'dayjs';
import { FORMAT_DATE } from '../../../constants/common';

const { Text } = Typography;

export const columns: ColumnsType<TListOfCommission> = [
  {
    title: 'Mã đợt tính hoa hồng',
    dataIndex: 'code',
    key: 'code',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Đơn vị ',
    dataIndex: ['pos', 'name'],
    key: 'pos',
    render: (value: string) => (value ? value : '-'),
  },

  {
    title: 'Kỳ tính hoa hồng',
    dataIndex: 'period',
    key: 'period',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: '<PERSON><PERSON><PERSON> tạo',
    dataIndex: 'createdDate',
    key: 'createdDate',
    render: (value: string, record: TListOfCommission) => (
      <>
        <Text>{value ? dayjs(value).format(FORMAT_DATE) : '-'}</Text>
        <br />
        <Text>{record?.createdBy?.fullName ? record.createdBy.fullName : '-'}</Text>
      </>
    ),
  },
  {
    title: 'Ngày cập nhật',
    dataIndex: 'modifiedDate',
    key: 'modifiedDate',
    render: (value: string, record: TListOfCommission) => (
      <>
        <Text>{value ? dayjs(value).format(FORMAT_DATE) : '-'}</Text>
        <br />
        <Text>{record?.modifiedBy?.fullName ? record.modifiedBy.fullName : '-'}</Text>
      </>
    ),
  },
];
