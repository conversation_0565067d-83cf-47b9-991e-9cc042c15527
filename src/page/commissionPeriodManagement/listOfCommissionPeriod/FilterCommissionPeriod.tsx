import { Col, DatePicker, Form, Row } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useMemo, useState } from 'react';
import { useLocation } from 'react-router-dom';
import DropdownFilterPeriod from '../../../components/dropdown/dropdownFilterPeriod';
import DropdownFilterSearch from '../../../components/dropdown/dropdownFilterSearch';
import MultiSelectLazy from '../../../components/select/mutilSelectLazy';
import { FORMAT_DATE, FORMAT_DATE_API } from '../../../constants/common';
import { useEntitiesById } from '../../../hooks';
import useFilter from '../../../hooks/filter';
import { getEmployeeDropdownById, getListEmployeeAll } from '../../../service/customers';
import { getOrgChartDropdown, getOrgChartDropdownById } from '../../../service/lead';
import { TFilterCommission } from '../../../types/commission';
import { TEmployeeAll } from '../../../types/customers';
import { TListDropdown } from '../../../types/salesPolicy';

const FilterCommissionPeriod = () => {
  const { search } = useLocation();
  const [form] = Form.useForm();
  const params = useMemo(() => new URLSearchParams(search), [search]);
  const [filter, setFilter] = useFilter();
  const [initialValues, setInitialValues] = useState<TFilterCommission>();
  const [isOpenFilter, setIsOpenFilter] = useState(false);

  const { entities: defaultEmployee } = useEntitiesById({
    apiQuery: getEmployeeDropdownById,
    queryKey: ['employee-dropdown'],
    params: { id: filter?.createdBy },
    labelField: 'name',
    valueField: 'id',
  });

  const { entities: defaultOrgChart } = useEntitiesById({
    apiQuery: getOrgChartDropdownById,
    queryKey: ['orgChart-exchanges'],
    params: { id: filter?.orgchart },
    labelField: 'name',
    valueField: 'id',
  });

  useEffect(() => {
    if (params) {
      const initialValue = {
        startDate: params.get('startDate') ? dayjs(params.get('startDate')) : undefined,
        endDate: params.get('endDate') ? dayjs(params.get('endDate')) : undefined,
        period: params.get('period') || undefined,
        year: params.get('year') ? dayjs(params.get('year')).format('YYYY') : undefined,
        orgchart: params.get('orgchart') || undefined,
        createdBy: params.get('createdBy') || undefined,
      };
      setInitialValues(initialValue);
      form.setFieldsValue(initialValue);
    }
  }, [form, params]);

  const handleSubmitFilter = (values: TFilterCommission) => {
    const newFilter: Record<string, unknown> = {
      startDate: values?.startDate ? dayjs(values?.startDate).format(FORMAT_DATE_API) : null,
      endDate: values?.endDate ? dayjs(values?.endDate).format(FORMAT_DATE_API) : null,
      period: values?.period || null,
      year: values?.year ? values.year : null,
      orgchart: values?.orgchart ? values.orgchart : null,
      createdBy: values?.createdBy ? values.createdBy : null,
    };
    setFilter({ ...filter, page: '1', ...newFilter });
    setIsOpenFilter(false);
  };

  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };

  const handleClearFilters = () => {
    setInitialValues(undefined);
    setTimeout(() => {
      setIsOpenFilter(false);
    }, 100);
  };

  const handleSelectEmployee = (values: TEmployeeAll[]) => {
    form.setFieldsValue({ createdBy: values.map(item => item.id).join(',') });
  };

  const handleSelectListPos = (values: TListDropdown[]) => {
    form.setFieldsValue({
      orgchart: values?.map(value => value?.id).join(','),
    });
  };

  return (
    <>
      <DropdownFilterSearch
        rootClassName="wrapper-filter"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        showParams={true}
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        initialValues={initialValues}
        form={form}
        onClearFilters={handleClearFilters}
        extraFormItems={
          <>
            <Form.Item label="Đơn vị" name="orgchart">
              <MultiSelectLazy
                apiQuery={getOrgChartDropdown}
                queryKey={['orgChart-exchanges']}
                enabled={isOpenFilter}
                keysTag={'name'}
                keysLabel={'name'}
                placeholder="Chọn đơn vị"
                handleListSelect={handleSelectListPos}
                defaultValues={defaultOrgChart}
              />
            </Form.Item>
            <Form.Item label="Người tạo" name="createdBy">
              <MultiSelectLazy
                enabled={isOpenFilter}
                apiQuery={getListEmployeeAll}
                queryKey={['employee-dropdown']}
                keysLabel={['name', 'email']}
                handleListSelect={handleSelectEmployee}
                defaultValues={defaultEmployee}
                placeholder="Chọn nhân viên"
                keysTag={'email'}
              />
            </Form.Item>
            <DropdownFilterPeriod label="Kỳ tính hoa hồng" defaultValues={initialValues?.period as string} />
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="Từ ngày" name="startDate">
                  <DatePicker
                    format={FORMAT_DATE}
                    disabledDate={current => {
                      const endDate = form.getFieldValue('endDate');
                      return (
                        (current && current > dayjs().startOf('day')) ||
                        (endDate && current > dayjs(endDate).startOf('day')) // Disable ngày sau "Đến ngày"
                      );
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Đến ngày" name="endDate">
                  <DatePicker
                    format={FORMAT_DATE}
                    disabledDate={current => {
                      const startDate = form.getFieldValue('startDate');
                      return (
                        (current && current > dayjs().startOf('day')) ||
                        (startDate && current < dayjs(startDate).startOf('day')) // Disable ngày trước "Ngày tạo"
                      );
                    }}
                  />
                </Form.Item>
              </Col>
            </Row>
          </>
        }
      />
    </>
  );
};

export default FilterCommissionPeriod;
