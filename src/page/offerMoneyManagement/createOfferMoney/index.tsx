import { Form, Modal } from 'antd';
import { useCallback, useState } from 'react';
import ModalComponent from '../../../components/modal';
import OfferMoneyForm from '../components/formOfferMoney';
import { useCreateField } from '../../../hooks';
import { createPaymentTransaction } from '../../../service/transaction';

interface OfferMoneyModalProps {
  visible: boolean;
  onClose: () => void;
}

const OfferMoneyModal = ({ visible, onClose }: OfferMoneyModalProps) => {
  const [form] = Form.useForm();
  const [resetUpload, setResetUpload] = useState<boolean>(false);

  const { mutateAsync: create } = useCreateField({
    keyOfListQuery: ['get-list-payment-transaction'],
    apiQuery: createPaymentTransaction,
    isMessageError: false,
    messageSuccess: 'Tạo mới chính sách thành công!',
  });

  const resetFormPaymentTransaction = useCallback(async () => {
    await form.resetFields();
    setResetUpload(true);
  }, [form]);

  const handleCancel = useCallback(async () => {
    if (form.isFieldsTouched()) {
      Modal.confirm({
        title: 'Xác nhận hủy',
        content: 'Dữ liệu chưa được lưu, bạn có chắc chắn muốn thoát khỏi trang không?',
        cancelText: 'Quay lại',
        onOk: async () => {
          await resetFormPaymentTransaction();
          onClose();
        },
        okButtonProps: { type: 'default' },
        cancelButtonProps: { type: 'primary' },
      });
    } else {
      await resetFormPaymentTransaction();
      onClose();
    }
  }, [form, onClose, resetFormPaymentTransaction]);
  const handleCreate = useCallback(
    async (values: unknown) => {
      try {
        const resp = await create(values);
        if (resp?.data?.statusCode === '0' && resp?.data?.success) {
          await resetFormPaymentTransaction();
          onClose();
        }
      } catch (error) {
        console.error('Create error:', error);
      }
    },
    [create, onClose, resetFormPaymentTransaction],
  );

  return (
    <ModalComponent
      title="Tạo mới đề nghị thu tiền"
      open={visible}
      onCancel={handleCancel}
      footer={null}
      destroyOnClose
    >
      <OfferMoneyForm
        form={form}
        onFinish={handleCreate}
        resetUpload={resetUpload}
        setResetUpload={setResetUpload}
        loading={false}
      />
    </ModalComponent>
  );
};

export default OfferMoneyModal;
