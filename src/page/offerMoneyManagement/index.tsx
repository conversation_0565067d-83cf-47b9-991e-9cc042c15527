import { Button, TableColumnsType } from 'antd';
import BreadCrumbComponent from '../../components/breadCrumb';
import TableComponent from '../../components/table';
import dayjs from 'dayjs';
import OfferMoneyModal from './createOfferMoney';
import { useState } from 'react';
import { useFetch } from '../../hooks';
import { getListTransaction } from '../../service/transaction';
import { ITransaction } from '../../types/transaction';
import { formatCurrency } from '../../utilities/shareFunc';
import FilterOfferMoney from './components/filterSearch';
import { Link } from 'react-router-dom';
import { OFFER_COLLECT_MONEY_MANAGEMENT } from '../../configs/path';
import { STATUS_LABELS } from '../../constants/common';

const formatDate = (date: string) => {
  const formattedDate = dayjs(date).format('DD/MM/YYYY');
  const formattedTime = dayjs(date).format('HH:mm');
  return (
    <>
      <div>{formattedDate}</div>
      <div className="text-time">{formattedTime}</div>
    </>
  );
};

const OfferMoneyManagement = () => {
  const [isModalCreateOfferMoney, setModalCreateOfferMoney] = useState(false);

  const {
    data: transaction,
    isLoading,
    isPlaceholderData,
    isFetching,
  } = useFetch<ITransaction[]>({
    queryKeyArrWithFilter: ['get-list-payment-transaction'],
    api: getListTransaction,
  });
  const dataPaymentTransaction = transaction?.data?.data?.rows || [];

  const columns: TableColumnsType<ITransaction> = [
    { title: 'Tên dự án', dataIndex: ['propertyTicket', 'project', 'name'], key: 'projectName' },
    {
      title: 'Số SP',
      dataIndex: ['propertyTicket', 'propertyUnit', 'code'],
      key: 'propertyUnitCode',
    },
    {
      title: 'Mã đề nghị',
      dataIndex: 'code',
      key: 'code',
      render: (value: string, record?: ITransaction) => (
        <Link to={`${OFFER_COLLECT_MONEY_MANAGEMENT}/${record?.id}`}>{value || '-'}</Link>
      ),
    },
    {
      title: 'Tên khách hàng',
      dataIndex: ['propertyTicket', 'customer', 'personalInfo', 'name'],
      key: 'personalInfoName',
    },
    { title: 'Đơn vị bán hàng', dataIndex: ['pos', 'name'], key: 'posName' },
    { title: 'Ngày tạo', dataIndex: 'createdAt', key: 'createdAt', className: 'text-time-td', render: formatDate },
    { title: 'Mã chứng từ', dataIndex: 'receiptNum', key: 'receiptNum' },
    {
      title: 'Mã YC/HĐ',
      dataIndex: ['propertyTicket', 'bookingTicketCode'],
      key: 'bookingTicketCode',
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      width: '10%',
      render: (status: string) => <span>{STATUS_LABELS[status] || 'Không xác định'}</span>,
    },
    {
      title: 'Số tiền',
      dataIndex: 'money',
      key: 'money',
      render: (money: number) => {
        return <span>{formatCurrency(money?.toString())}</span>;
      },
    },
  ];

  return (
    <>
      <BreadCrumbComponent />
      <div className="header-content">
        <FilterOfferMoney />
        <Button type="primary" onClick={() => setModalCreateOfferMoney(true)}>
          Tạo mới
        </Button>
      </div>
      <TableComponent
        columns={columns}
        dataSource={dataPaymentTransaction}
        rowKey="id"
        queryKeyArr={['get-list-payment-transaction']}
        loading={isFetching || isPlaceholderData || isLoading}
      />
      <OfferMoneyModal
        visible={isModalCreateOfferMoney}
        onClose={() => {
          setModalCreateOfferMoney(false);
        }}
      />
    </>
  );
};

export default OfferMoneyManagement;
