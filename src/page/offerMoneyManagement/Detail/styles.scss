.customer-info-container {
  display: flex;
  justify-content: center;
  padding: 10px;
}

.customer-info {
  color: #333;
  width: 90%;
  max-width: 600px;
}

.info-row {
  display: grid;
  grid-template-columns: minmax(max-content, 1fr) auto;
  gap: 10px;
  margin-bottom: 12px;
  align-items: center;
}

.info-label {
  white-space: nowrap;
  padding-right: 10px;
  color: #000000e0;
}

.info-value {
  text-align: right;
  word-break: break-all;
  min-width: 150px;
  color: #000000e0;
}

/* Responsive cho màn hình nhỏ */
@media (max-width: 480px) {
  .info-row {
    grid-template-columns: 1fr;
    gap: 4px;
  }

  .info-value {
    text-align: left;
    min-width: unset;
    padding-left: 10px;
  }
}
