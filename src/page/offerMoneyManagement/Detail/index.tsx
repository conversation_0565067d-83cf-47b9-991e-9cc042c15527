import { <PERSON>, useParams } from 'react-router-dom';
import BreadCrumbComponent from '../../../components/breadCrumb';
import { useFetch } from '../../../hooks';
import { getDetailOffer } from '../../../service/offer';
import { DetailOfferMoneyAccountancy } from '../../../types/offer';
import { Button, Col, Row, Spin, TableColumnsType, Typography } from 'antd';
import { PaperClipOutlined } from '@ant-design/icons';
import './styles.scss';
import TableComponent from '../../../components/table';
import { formatCurrency } from '../../../utilities/shareFunc';
import dayjs from 'dayjs';
import { FORMAT_DATE, FORMAT_DATE_TIME, STATUS_COLORS, STATUS_LABELS } from '../../../constants/common';
import { OFFER_COLLECT_MONEY_MANAGEMENT } from '../../../configs/path';
import { formatNumber } from '../../../utilities/regex';

const { Title, Text } = Typography;
const DetailOfferMoney = () => {
  const { id } = useParams();

  const {
    data: offerOrderData,
    isLoading,
    isFetching,
  } = useFetch<DetailOfferMoneyAccountancy>({
    api: () => id && getDetailOffer({ id }),
    queryKeyArr: ['get-detail-offer', id],
    enabled: !!id,
    cacheTime: 10,
  });

  const offerOrder = offerOrderData?.data?.data;

  const columns: TableColumnsType<DetailOfferMoneyAccountancy> = [
    {
      title: 'Dự án',
      dataIndex: 'projectName',
      key: 'projectName',
      render: (_, record: DetailOfferMoneyAccountancy) => <Text>{record?.propertyTicket?.project?.name || '-'}</Text>,
    },
    {
      title: 'Mã YCĐCHO/ YCĐCO',
      dataIndex: 'bookingTicketCode',
      key: 'bookingTicketCode',
      render: (_, record: DetailOfferMoneyAccountancy) => <Text>{record?.propertyTicket?.bookingTicketCode}</Text>,
    },

    {
      title: 'Số SP',
      dataIndex: ['propertyTicket', 'propertyUnit', 'code'],
      key: 'propertyUnitCode',
    },
    {
      title: 'Mã đề nghị',
      dataIndex: 'code',
      key: 'code',
      width: 150,
      render: (value: string, record?: DetailOfferMoneyAccountancy) => (
        <Link to={`${OFFER_COLLECT_MONEY_MANAGEMENT}/${record?.id}`}>{value || '-'}</Link>
      ),
    },

    {
      title: 'Số tiền',
      dataIndex: 'money',
      key: 'money',
      width: 250,
      render: (value: number) => <Text>{formatCurrency(value.toString()) || '-'}</Text>,
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      width: '10%',
      render: (status: string) => (
        <span style={{ color: STATUS_COLORS[status] || 'black' }}>{STATUS_LABELS[status] || 'Không xác định'}</span>
      ),
    },
    {
      title: 'Ngày cập nhật',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 160,
      render: (value: string) => <Text>{dayjs(value).format(FORMAT_DATE) || '-'}</Text>,
    },
  ];
  const fullAddress = [
    offerOrder?.propertyTicket.customer.info?.address?.address,
    offerOrder?.propertyTicket.customer.info?.address?.ward?.name,
    offerOrder?.propertyTicket.customer.info?.address?.district?.name,
    offerOrder?.propertyTicket.customer.info?.address?.province?.name,
  ]
    .filter(Boolean)
    .join(', ');

  return (
    <Spin spinning={isLoading || isFetching} size="large">
      <BreadCrumbComponent titleBread={offerOrder?.code} />
      <Row
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          borderBottom: '1px solid rgba(0, 0, 0, 0.06)',
          padding: '20px 0px',
        }}
      >
        <Title level={5}>Phiếu thu {offerOrder?.code}</Title>
        <Button>Hủy phiếu</Button>
      </Row>
      <Title level={5} style={{ marginBottom: '16px', marginTop: '30px' }}>
        Thông tin chi tiết
      </Title>
      <div className="customer-info">
        <div className="info-row">
          <span className="info-label">Mã đề nghị:</span>
          <span className="info-value">{offerOrder?.code}</span>
        </div>
        <div className="info-row">
          <span className="info-label">Mã khách hàng:</span>
          <span className="info-value">{offerOrder?.propertyTicket?.customer?.code}</span>
        </div>
        <div className="info-row">
          <span className="info-label">Họ và tên:</span>
          <span className="info-value">{offerOrder?.propertyTicket?.customer?.personalInfo?.name}</span>
        </div>
        <div className="info-row">
          <span className="info-label">Số giấy tờ:</span>
          <span className="info-value">
            {(offerOrder?.propertyTicket?.customer?.identities?.length ?? 0 > 0)
              ? offerOrder?.propertyTicket?.customer?.identities?.[0]?.value
              : ''}
          </span>
        </div>
        <div className="info-row">
          <span className="info-label">Địa chỉ liên lạc:</span>
          <span className="info-value">{fullAddress}</span>
        </div>
        <div className="info-row">
          <span className="info-label">Số tiền:</span>
          <span className="info-value">{formatNumber(offerOrder?.money)}</span>
        </div>
        <div className="info-row">
          <span className="info-label">Hình thức thanh toán:</span>
          <span className="info-value">
            {offerOrder?.state === 'CASH' ? 'Cà thẻ' : offerOrder?.state === 'TRANSFER' ? 'Tiền mặt' : ''}
          </span>
        </div>
        <div className="info-row">
          <span className="info-label">Ngày tạo đề nghị thu tiền:</span>
          <span className="info-value">{dayjs(offerOrder?.createdAt).format(FORMAT_DATE_TIME)}</span>
        </div>
        <div className="info-row">
          <span className="info-label">Ngày nộp tiền:</span>
          <span className="info-value">{dayjs(offerOrder?.date).format(FORMAT_DATE_TIME)}</span>
        </div>
        <div className="info-row">
          <span className="info-label">Ghi chú:</span>
          <span className="info-value">{offerOrder?.description}</span>
        </div>
        <div className="info-row">
          <span className="info-label">Ghi chú phiếu YC:</span>
          <span className="info-value">{offerOrder?.propertyTicket?.note}</span>
        </div>
        <div className="info-row">
          <span className="info-label">Lí do thanh toán:</span>
          <span className="info-value">{offerOrder?.reason}</span>
        </div>
        <div className="info-row">
          <span className="info-label">Phí giao dịch:</span>
          <span className="info-value">{formatNumber(offerOrder?.transactionFee)}</span>
        </div>
        <div className="info-row">
          <span className="info-label" style={{ width: '100%' }}>
            Thông tin đính kèm:
          </span>
          <span className="info-value">
            {Array.isArray(offerOrder?.files) ? (
              <>
                {offerOrder?.files?.map((file, index) => (
                  <div key={index}>
                    {file.url ? (
                      <a
                        href={`${import.meta.env.VITE_S3_IMAGE_URL}/${file.url}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        style={{ color: '#1890ff' }}
                        download={file.name}
                      >
                        <PaperClipOutlined />
                        {file.name}
                      </a>
                    ) : (
                      <span style={{ color: '#1890ff' }}>{file.name}</span>
                    )}
                  </div>
                ))}
              </>
            ) : (
              ''
            )}
          </span>
        </div>
      </div>

      <Title level={5} style={{ fontSize: '14px', marginBottom: '16px', marginTop: '30px' }}>
        Thông tin chung
      </Title>

      <TableComponent
        className="table-offer-order"
        columns={columns}
        queryKeyArr={['get-offer-order']}
        dataSource={offerOrder?.relatedTransactions}
        rowKey={'id'}
        isPagination={false}
      />
      <Row
        style={{
          backgroundColor: 'rgba(230, 244, 255, 1)',
          padding: '12px 8px',
          borderBottom: '1px solid rgba(0, 0, 0, 0.06)',
          marginTop: '30px',
        }}
      >
        <Col span={8} style={{ textAlign: 'center' }}>
          <span>
            Số tiền: <span style={{ fontWeight: 600 }}>{formatNumber(offerOrder?.totalPendingAmount)}</span>
          </span>
        </Col>
        <Col span={8} style={{ textAlign: 'center' }}>
          <span>
            Đã thanh toán: <span style={{ fontWeight: 600 }}>{formatNumber(offerOrder?.totalPaidAmount)}</span>
          </span>
        </Col>
        <Col span={8} style={{ textAlign: 'right' }}>
          <span>
            Còn lại: <span style={{ fontWeight: 600 }}>{formatNumber(offerOrder?.totalRemainingAmount)}</span>
          </span>
        </Col>
      </Row>
    </Spin>
  );
};

export default DetailOfferMoney;
