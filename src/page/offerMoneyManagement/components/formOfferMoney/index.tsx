import React, { useEffect, useState } from 'react';
import {
  Form,
  Input,
  Button,
  Row,
  Col,
  Typography,
  UploadFile,
  FormInstance,
  Select,
  Radio,
  Card,
  InputNumber,
  RadioChangeEvent,
  Spin,
} from 'antd';
import './styles.scss';
import SingleSelectLazy from '../../../../components/select/singleSelectLazy';
import UploadFileDiscountPolicy from '../../../discountPolicyManagement/component/uploadFileDiscountPolicy';
import { getListBookingTicket } from '../../../../service/bookingTicket';
import { Bank, BookingTicket } from '../../../../types/bookingRequest';
import { formatNumber } from '../../../../utilities/regex';
import MyDatePicker from '../../../../components/datePicker';
import dayjs from 'dayjs';

const { Title } = Typography;
const { Option } = Select;

interface FormValues {
  type: string;
  propertyTicket: string;
  projectName?: string;
  code?: string;
  customerName?: string;
  documentNumber?: string;
  address?: string;
  state?: string;
  bankName?: string;
  bankNumber?: string;
  contentBank?: string;
  money: number;
  reason: string;
  date: dayjs.Dayjs;
  files?: ExtendedUploadFile[];
}
interface ExtendedUploadFile extends UploadFile {
  absoluteUrl?: string;
  key?: string;
}
interface OfferMoneyFormProps {
  form?: FormInstance;
  onFinish?: (values: unknown) => void;
  resetUpload?: boolean;
  setResetUpload?: (value: boolean) => void;
  loading: boolean;
}

const OfferMoneyForm: React.FC<OfferMoneyFormProps> = ({
  form: parentForm,
  onFinish,
  setResetUpload,
  resetUpload,
  loading,
}) => {
  const [internalForm] = Form.useForm();
  const form = parentForm || internalForm;
  const [fileList, setFileList] = useState<ExtendedUploadFile[]>([]);
  const [selectedDate, setSelectedDate] = useState<dayjs.Dayjs | null>(null);
  const [paymentMethod, setPaymentMethod] = useState<string>('TRANSFER');
  const [listBank, setListBank] = useState<Bank[]>([]);
  const [type, setType] = useState<string>('YCDCH');

  const handlePaymentMethodChange = (e: RadioChangeEvent) => {
    setPaymentMethod(e.target.value);
  };
  useEffect(() => {
    if (resetUpload) {
      setFileList([]);
      setResetUpload?.(false);
    }
    if (fileList.length > 0) {
      form.setFieldsValue({ files: fileList });
    }
  }, [resetUpload, setResetUpload, fileList, form]);
  const handleDateChange = (date: dayjs.Dayjs | null) => {
    setSelectedDate(date);
    form.setFieldsValue({ date: date });
  };
  const handleTypeChange = (value: string) => {
    form.resetFields();
    form.setFieldsValue({ type: value });
    setType(value);
  };
  const handleBankChange = (value: string) => {
    const selectedBank = listBank.find(bank => bank.code === value);
    form.setFieldsValue({
      bankNumber: selectedBank?.accountNumber || '',
    });
  };
  const handleSelectBookingTicket = (value: BookingTicket) => {
    if (!value) {
      form.resetFields();
      form.setFieldsValue({
        bankName: undefined,
        bankNumber: undefined,
      });
    } else {
      const customerType = value.customer?.type;
      let addressInfo;
      let namePersonalInfo;
      if (customerType === 'individual') {
        addressInfo = value.customer?.info?.rootAddress;
        namePersonalInfo = value.customer?.personalInfo?.name;
      } else {
        addressInfo = value.customer?.company?.address;
        namePersonalInfo = value.customer?.company?.name;
      }

      const streetAddress = addressInfo?.address || '';
      const wardName = addressInfo?.ward?.name || '';
      const districtName = addressInfo?.district?.name || '';
      const provinceName = addressInfo?.province?.name || '';

      const fullAddress = [streetAddress, wardName, districtName, provinceName].filter(Boolean).join(', ');

      const customerName = namePersonalInfo || '';
      const customerCode = value.customer?.code || '';
      const reason = `Thu tiền khách hàng ${customerName} (${customerCode})`;

      form.setFieldsValue({
        propertyTicket: value,
        customerName: customerName || '',
        documentNumber: value.customer?.personalInfo?.identities?.[0]?.value ?? '',
        code: value?.propertyUnit?.code || '',
        projectName: value.project?.name || '',
        address: fullAddress || '',
        money: value.amountRegistration || 0,
        reason: reason || '',
      });

      setListBank(value.project && Array.isArray(value.project.banks) ? value.project.banks : []);
    }
  };

  const handleFinish = (values: FormValues) => {
    const payload: unknown = {
      ...values,
      state: paymentMethod,
      files: fileList.map(file => ({
        uid: file.uid || '',
        name: file.name,
        url: file.key || '',
        absoluteUrl: file?.absoluteUrl || '',
        uploadName: file.name,
      })),
    };
    onFinish?.(payload);
  };
  return (
    <Spin spinning={loading}>
      <Form form={form} onFinish={handleFinish} layout="vertical">
        <Row gutter={64}>
          <Col span={12}>
            <Title level={5} style={{ marginBottom: 16 }}>
              Thông tin đề nghị thu tiền
            </Title>
            <Row gutter={24}>
              <Col span={14}>
                <Form.Item
                  label="Loại đề nghị"
                  name="type"
                  rules={[{ required: true, message: 'Vui lòng chọn loại đề nghị' }]}
                  initialValue="YCDCH"
                >
                  <Select placeholder="Chọn loại đề nghị" onChange={handleTypeChange}>
                    <Option value="a">Hợp đồng dịch vụ</Option>
                    <Option value="YCDCH">Yêu cầu đặt chỗ</Option>
                    <Option value="YCDC">Yêu cầu đặt cọc</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={10}>
                <Form.Item label="Mã đề nghị">
                  <Input placeholder="Mã đề nghị" disabled />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              label="Mã YCĐCHO/YCĐCO"
              name="propertyTicket"
              rules={[{ required: true, message: 'Vui lòng chọn mã YCĐCHO/YCĐCO' }]}
            >
              <SingleSelectLazy
                moreParams={{ type }}
                queryKey={['get-booking-tickets', type]}
                apiQuery={getListBookingTicket}
                placeholder="Chọn mã YCĐCHO/YCĐCO"
                keysLabel={'bookingTicketCode'}
                handleSelect={handleSelectBookingTicket}
              />
            </Form.Item>
            <Row gutter={24}>
              <Col span={14}>
                <Form.Item label="Dự án" name="projectName" required={true}>
                  <Input placeholder="Tên dự án" disabled />
                </Form.Item>
              </Col>
              <Col span={10}>
                <Form.Item label="Số sản phẩm" name="code">
                  <Input placeholder="Số sản phẩm" disabled />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={14}>
                <Form.Item label="Tên khách hàng" name="customerName">
                  <Input placeholder="Tên khách hàng" disabled />
                </Form.Item>
              </Col>
              <Col span={10}>
                <Form.Item label="Số giấy tờ" name="documentNumber">
                  <Input placeholder="Số giấy tờ" disabled />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item label="Địa chỉ liên lạc" name="address">
              <Input placeholder="Địa chỉ liên lạc" disabled />
            </Form.Item>
            <Form.Item label="Phương thức thanh toán" layout="horizontal" name="state" required={true}>
              <Radio.Group
                style={{ display: 'flex', justifyContent: 'space-evenly' }}
                name="radiogroup"
                defaultValue="TRANSFER"
                onChange={handlePaymentMethodChange}
                options={[
                  { value: 'TRANSFER', label: 'Chuyển khoản' },
                  { value: 'CASH', label: 'Tiền mặt' },
                ]}
              />
            </Form.Item>
            {paymentMethod === 'TRANSFER' && (
              <Form.Item>
                <Col span={24}>
                  <Card className="border-account-transactions">
                    <Row align="middle" gutter={24}>
                      <Col span={15}>
                        <Form.Item
                          name="bankName"
                          label="Ngân hàng"
                          rules={[{ required: true, message: 'Vui lòng chọn ngân hàng' }]}
                        >
                          <Select
                            placeholder="Chọn ngân hàng"
                            allowClear
                            filterOption={(input, option) =>
                              typeof option?.label === 'string'
                                ? option.label.toLowerCase().includes(input.toLowerCase())
                                : false
                            }
                            showSearch
                            options={listBank.map(item => ({
                              value: item.code,
                              label: item.name,
                            }))}
                            onChange={handleBankChange}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={9}>
                        <Form.Item
                          name="bankNumber"
                          label="Số tài khoản"
                          rules={[{ required: true, message: 'Vui lòng nhập số tài khoản' }]}
                          normalize={value => value?.trim()}
                        >
                          <Input placeholder="Nhập số tài khoản" maxLength={50} disabled />
                        </Form.Item>
                      </Col>
                      <Col span={24}>
                        <Form.Item
                          name="contentBank"
                          rules={[
                            {
                              validator: (_, value) => {
                                if (!value || value.trim() === '') {
                                  return Promise.reject(new Error('Vui lòng nhập nội dung chuyển khoản'));
                                }
                                return Promise.resolve();
                              },
                            },
                          ]}
                        >
                          <Input.TextArea
                            placeholder="Nhập nội dung chuyển khoản"
                            maxLength={255}
                            onBlur={e => {
                              form.setFieldsValue({ contentBank: e.target.value.trim() });
                            }}
                          />
                        </Form.Item>
                      </Col>
                    </Row>
                  </Card>
                </Col>
              </Form.Item>
            )}
          </Col>
          <Col span={12}>
            <Title level={5} style={{ marginBottom: 16 }}>
              Thông tin chi phí
            </Title>
            <Row gutter={24}>
              <Col span={20}>
                <Form.Item
                  label="Số tiền đề nghị thu"
                  name="money"
                  rules={[{ required: true, message: 'Vui lòng nhập số tiền đề nghị thu' }]}
                >
                  <InputNumber min={1} placeholder="Nhập số tiền đề nghị thu" formatter={formatNumber} maxLength={12} />
                </Form.Item>
              </Col>
              <Col span={20}>
                <Form.Item
                  label="Lý do thanh toán"
                  name="reason"
                  rules={[
                    {
                      validator: (_, value) => {
                        if (!value || value.trim() === '') {
                          return Promise.reject(new Error('Vui lòng nhập lý do thanh toán'));
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <Input.TextArea
                    placeholder="Nhập lý do thanh toán"
                    maxLength={255}
                    onBlur={e => {
                      form.setFieldsValue({ reason: e.target.value.trim() });
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={20}>
                <Form.Item
                  label="Ngày nộp tiền"
                  name="date"
                  initialValue={dayjs()}
                  rules={[{ required: true, message: 'Vui lòng chọn ngày nộp tiền' }]}
                >
                  <MyDatePicker placeholder="Chọn ngày nộp tiền" onDateChange={handleDateChange} value={selectedDate} />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item label="Tài liệu định kèm" name="files">
              <UploadFileDiscountPolicy fileList={fileList} setFileList={setFileList} uploadPath="transaction" />
            </Form.Item>
          </Col>
        </Row>
        <div className="create-footer">
          <div className="button-create">
            <Button type="primary" htmlType="submit" loading={loading} size="small">
              Lưu
            </Button>
          </div>
        </div>
      </Form>
    </Spin>
  );
};

export default OfferMoneyForm;
