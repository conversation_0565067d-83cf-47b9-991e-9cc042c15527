import { Button, Space, Typography } from 'antd';
import { ColumnsType } from 'antd/es/table';
import TableComponent from '../../components/table';
import './styles.scss';
import BreadCrumbComponent from '../../components/breadCrumb';
import { FORMAT_DATE, getPolicyStatus } from '../../constants/common';
import { useFetch } from '../../hooks';
import { IPaymentPolicy } from '../../types/paymentPolicy';
import { getListPaymentPolicy } from '../../service/paymentPolicy';
import dayjs from 'dayjs';
import FilterPaymentPolicy from './component/filterSearch';
import PaymentPolicyModal from './createPaymentPolicy';
import { useState } from 'react';
import { ActionsColumns } from '../../components/table/components/ActionsColumns';
import { useNavigate } from 'react-router-dom';
import { PAYMENT_POLICY } from '../../configs/path';

const { Text } = Typography;

const PaymentPolicy = () => {
  const navigate = useNavigate();
  const [isModalCreatePaymentPolicy, setPaymentPolicyCreate] = useState(false);
  const {
    data: paymentPolicy,
    isLoading,
    isPlaceholderData,
    isFetching,
  } = useFetch<IPaymentPolicy[]>({
    queryKeyArrWithFilter: ['get-payment-policy'],
    api: getListPaymentPolicy,
  });
  const dataPaymentPolicy = paymentPolicy?.data?.data?.rows || [];
  const columns: ColumnsType<IPaymentPolicy> = [
    {
      title: 'Mã tờ trình',
      dataIndex: 'eappNumber',
      key: 'eappNumber',
      render: (value: string) => <a>{value}</a>,
    },
    {
      title: 'Mã chính sách',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: 'Tên chính sách',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Dự án',
      dataIndex: ['project', 'name'],
      key: 'project',
    },
    {
      title: 'Thời gian áp dụng',
      dataIndex: 'date',
      key: 'date',
      render: (_: string, record: IPaymentPolicy) => (
        <>
          <Text>{dayjs(record?.startDate).format(FORMAT_DATE) || ''}</Text>
          {record?.expiredDate && (
            <>
              {' - '}
              <Text>{dayjs(record?.expiredDate).format(FORMAT_DATE) || ''}</Text>
            </>
          )}
        </>
      ),
    },
    {
      title: 'Trạng thái duyệt',
      dataIndex: '',
      key: '',
      align: 'center',
    },
    {
      title: 'Trạng thái chính sách',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      render: (value: string) => {
        const statusObject = getPolicyStatus(value);
        return <Text style={{ color: statusObject.value }}>{statusObject.label} </Text>;
      },
    },
    {
      title: '',
      dataIndex: 'action',
      key: 'action',
      align: 'center',
      width: 140,
      render: (_, record: IPaymentPolicy) => {
        const isApproved = record.status === 'APPROVED_ACTIVE' || record.status === 'APPROVED_INACTIVE';
        return (
          <ActionsColumns
            moreActions={[
              {
                label: 'Xem chi tiết',
                key: 'detail',
                onClick: () => navigate(`${PAYMENT_POLICY}/${record?.id}`),
              },

              isApproved
                ? {
                    label: record.status === 'APPROVED_ACTIVE' ? 'Vô hiệu hóa' : 'Kích hoạt',
                    key: 'toggleStatus',
                    onClick: () => {},
                  }
                : null,
              {
                label: 'Xóa',
                key: 'delete',
              },
            ].filter(action => action !== null)}
          />
        );
      },
    },
  ];

  return (
    <div className="list-payment-policy">
      <BreadCrumbComponent />
      <div className="header-filter-list-payment-policy">
        <FilterPaymentPolicy />
        <Space>
          <Button type="primary" onClick={() => setPaymentPolicyCreate(true)}>
            Tạo mới
          </Button>
        </Space>
      </div>
      <TableComponent
        columns={columns}
        queryKeyArr={['get-payment-policy']}
        rowKey="id"
        dataSource={dataPaymentPolicy}
        loading={isFetching || isPlaceholderData || isLoading}
      />
      <PaymentPolicyModal
        visible={isModalCreatePaymentPolicy}
        onClose={() => {
          setPaymentPolicyCreate(false);
        }}
      />
    </div>
  );
};

export default PaymentPolicy;
