.payment-policy-form {
  .payment-batch {
    margin-bottom: 50px;
    .add-payment-batch {
      margin-bottom: 22px;
    }
    .ant-typography {
      margin-bottom: 24px;
      > span {
        color: red;
      }
    }
    .payment-batch-row {
      display: flex;
      padding: 24px 0;
      align-items: flex-start;
      border-top: 1px solid #0000000f;
      width: 100%;
      gap: 16px;
      > input {
        flex: 0.8;
      }
    }
  }

  .custom-radio-checkbox .ant-checkbox-inner {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 1px solid #d9d9d9;
    background-color: white;
  }
  .custom-radio-checkbox .ant-checkbox-checked .ant-checkbox-inner {
    background-color: #1890ff;
    border-color: #1890ff;
  }
  .custom-radio-checkbox .ant-checkbox-checked .ant-checkbox-inner::after {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: white;
    border: none;
    transform: translate(-50%, -50%) scale(1);
    top: 50%;
    left: 50%;
    display: block;
  }
  .custom-radio-checkbox .ant-checkbox:hover .ant-checkbox-inner,
  .custom-radio-checkbox .ant-checkbox-input:focus + .ant-checkbox-inner {
    border-color: #40a9ff;
  }
  .custom-radio-checkbox .ant-checkbox-inner::after {
    display: none;
  }
  .transaction-success-checkbox {
    color: #34c759 !important;
  }
  .ant-input-number-group-addon {
    color: black !important;
  }
}
