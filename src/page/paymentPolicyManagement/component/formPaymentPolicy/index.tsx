import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  Form,
  Input,
  DatePicker,
  Switch,
  Button,
  Select,
  Row,
  Col,
  Typography,
  UploadFile,
  FormInstance,
  Modal,
  Checkbox,
  InputNumber,
  Spin,
} from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { Installment, IPaymentPolicy, Payments } from '../../../../types/paymentPolicy';
import { FORMAT_DATE, FORMAT_DATE_API } from '../../../../constants/common';
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import './styles.scss';
import SingleSelectLazy from '../../../../components/select/singleSelectLazy';
import { getListProject } from '../../../../service/project';
import UploadFileDiscountPolicy from '../../../discountPolicyManagement/component/uploadFileDiscountPolicy';
import { formatNumber, handleKeyDownEnterNumber } from '../../../../utilities/regex';

const { Title } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

interface ExtendedUploadFile extends UploadFile {
  absoluteUrl?: string;
  key?: string;
}

interface PaymentPolicyFormProps {
  form?: FormInstance;
  initialValues?: IPaymentPolicy;
  onFinish?: (values: IPaymentPolicy) => void;
  isUpdate?: boolean;
  resetUpload?: boolean;
  setResetUpload?: (value: boolean) => void;
  loading?: boolean;
}

const PaymentPolicyForm: React.FC<PaymentPolicyFormProps> = ({
  form: parentForm,
  initialValues,
  isUpdate = false,
  resetUpload,
  setResetUpload,
  onFinish,
  loading,
}) => {
  const [internalForm] = Form.useForm();
  const form = parentForm || internalForm;
  const [fileList, setFileList] = useState<ExtendedUploadFile[]>([]);
  const [fileListTemplate, setFileListTemplate] = useState<ExtendedUploadFile[]>([]);
  const [isDefaultApply, setIsDefaultApply] = useState<boolean>(initialValues?.defaultApply || false);
  const rangePickerRef = useRef<React.ComponentRef<typeof DatePicker.RangePicker>>(null);
  const [dates, setDates] = useState<[Dayjs | null, Dayjs | null]>([null, null]);

  const initialPayments = React.useMemo(() => {
    return (
      initialValues?.schedule?.installments?.map((installment, index) => ({
        id: index + 1,
        name: installment.name,
        type: installment.typeRealEstate === 'default' ? 'TT theo giá bán sản phẩm' : 'TT theo giá nhà và đất',
        time: installment.expiredDateType === 'exactDays' ? 'Mốc thời gian TT' : 'Khoảng thời gian TT',
        productCurrency: installment.type === 'percent' ? '%' : 'VND',
        houseCurrency: installment.type === 'percent' ? '%' : 'VND',
        landCurrency: installment.type2 === 'percent' ? '%' : 'VND',
        value: installment.typeRealEstate === 'default' ? installment.value?.toString() : '',
        productValue: installment.typeRealEstate === 'default' ? installment.value?.toString() : '',
        houseValue: installment.typeRealEstate !== 'default' ? installment.value?.toString() : '',
        landValue: installment.typeRealEstate !== 'default' ? installment.value2?.toString() : '',
        exactDays: installment.exactDays ? dayjs(installment.exactDays) : null,
        expiredDays: installment.expiredDays || null,
        descriptionProgress: installment.descriptionProgress,
        isToContract: installment.isToContract,
        transactionSuccessful: installment.transactionSuccessful,
      })) || [
        {
          id: 1,
          name: '',
          type: 'TT theo giá bán sản phẩm',
          time: 'Mốc thời gian TT',
          productCurrency: 'VND',
          houseCurrency: 'VND',
          landCurrency: 'VND',
          value: '',
          houseValue: '',
          landValue: '',
          exactDays: null,
          expiredDays: null,
          descriptionProgress: '',
          isToContract: false,
          transactionSuccessful: false,
        },
      ]
    );
  }, [initialValues]);

  const initialFormValues = React.useMemo(() => {
    return initialValues
      ? {
          ...initialValues,
          approvedDate: initialValues.approvedDate ? dayjs(initialValues.approvedDate).format(FORMAT_DATE) : '',
          applicationPeriod: [
            initialValues.startDate ? dayjs(initialValues.startDate) : null,
            initialValues.expiredDate ? dayjs(initialValues.expiredDate) : null,
          ],
          active: initialValues.active ?? true,
          typeDiscount: initialValues.typeDiscount || 'percent',
          payments: initialPayments,
        }
      : {
          active: true,
          defaultApply: false,
          payments: initialPayments,
        };
  }, [initialValues, initialPayments]);

  useEffect(() => {
    if (resetUpload) {
      setFileList([]);
      setFileListTemplate([]);
      setResetUpload?.(false);
    }
  }, [resetUpload, setResetUpload]);

  useEffect(() => {
    if (initialValues) {
      form.setFieldsValue(initialFormValues);
      if (initialValues.files) {
        setFileList(
          initialValues.files.map(file => ({
            uid: file._id || '',
            name: file.name || '',
            url: `${import.meta.env.VITE_S3_IMAGE_URL}/${file.url}` || '',
            status: 'done',
          })),
        );
      }
      if (initialValues.schedule?.templateFiles) {
        setFileListTemplate(
          initialValues.schedule.templateFiles.map(file => ({
            uid: file._id || '',
            name: file.name || '',
            url: `${import.meta.env.VITE_S3_IMAGE_URL}/${file.url}` || '',
            status: 'done',
          })),
        );
      }
    } else {
      form.resetFields();
      setFileList([]);
      setFileListTemplate([]);
    }
  }, [initialValues, form, initialFormValues]);

  const handleFinish = (values: IPaymentPolicy) => {
    const { applicationPeriod, active, payments, ...rest } = values;
    const [startDate, expiredDate] = applicationPeriod || [];
    const payload: IPaymentPolicy = {
      ...rest,
      active: active !== undefined ? active : true,
      schedule: {
        installments: (payments || []).map((payment: Payments) => {
          const baseInstallment = {
            name: payment.name,
            type:
              payment.type === 'TT theo giá bán sản phẩm'
                ? payment.productCurrency === '%'
                  ? 'percent'
                  : 'currency'
                : payment.houseCurrency === '%'
                  ? 'percent'
                  : 'currency',
            value: payment.type === 'TT theo giá bán sản phẩm' ? Number(payment.productValue) : null,
            typeRealEstate: payment.type === 'TT theo giá bán sản phẩm' ? 'default' : 'houseAndLand',
            expiredDateType: payment.time === 'Mốc thời gian TT' ? 'exactDays' : 'expiredDays',
            ...(payment.time === 'Mốc thời gian TT'
              ? { exactDays: payment.exactDays ? dayjs(payment.exactDays).format(FORMAT_DATE_API) : null }
              : { expiredDays: payment.expiredDays || null }),
            descriptionProgress: payment.descriptionProgress,
            isToContract: payment.isToContract,
            transactionSuccessful: payment.transactionSuccessful,
          };

          if (payment.type === 'TT theo giá nhà và đất') {
            return {
              ...baseInstallment,
              type2: payment.landCurrency === '%' ? 'percent' : 'currency',
              value: payment.houseValue ? Number(payment.houseValue) : null,
              value2: payment.landValue ? Number(payment.landValue) : null,
            };
          }
          return baseInstallment;
        }) as unknown as Installment[],
        templateFiles: fileListTemplate.map(file => ({
          name: file.name,
          url: file.key || '',
          absoluteUrl: file?.absoluteUrl || '',
          uploadName: file.name,
        })),
      },
      startDate: startDate ? dayjs(startDate).format(FORMAT_DATE_API) : undefined,
      expiredDate: expiredDate ? dayjs(expiredDate).format(FORMAT_DATE_API) : null,
      files: fileList.map(file => ({
        name: file.name,
        url: file.key || '',
        absoluteUrl: file?.absoluteUrl || '',
        uploadName: file.name,
      })),
    };
    onFinish?.(payload);
  };

  const handleSelectProject = (value: IPaymentPolicy) => {
    form.setFieldsValue({
      project: {
        id: value?.id,
        name: value?.name,
      },
    });
  };

  const handleCalendarChange = useCallback(
    (value: [Dayjs | null, Dayjs | null] | null) => {
      if (!value) {
        if (dates[0] !== null || dates[1] !== null) {
          setDates([null, null]);
          form.setFieldsValue({ applicationPeriod: [null, null] });
        }
        return;
      }

      const newStart = value[0];
      const newEnd = value[1];

      if (
        (newStart?.isSame(dates[0], 'day') || (!newStart && !dates[0])) &&
        (newEnd?.isSame(dates[1], 'day') || (!newEnd && !dates[1]))
      ) {
        return;
      }

      setDates([newStart, newEnd]);
      form.setFieldsValue({ applicationPeriod: [newStart, newEnd] });
    },
    [dates, form],
  );

  const handleChangeRangePicker = useCallback(
    (value: [Dayjs | null, Dayjs | null] | null) => {
      if (!value) {
        if (dates[0] !== null || dates[1] !== null) {
          setDates([null, null]);
          form.setFieldsValue({ applicationPeriod: [null, null] });
        }
        return;
      }

      const newStart = value[0];
      const newEnd = value[1];

      if (
        (newStart?.isSame(dates[0], 'day') || (!newStart && !dates[0])) &&
        (newEnd?.isSame(dates[1], 'day') || (!newEnd && !dates[1]))
      ) {
        return;
      }

      setDates([newStart, newEnd]);
      form.setFieldsValue({ applicationPeriod: [newStart, newEnd] });
    },
    [dates, form],
  );

  const handleBlur = useCallback(
    (e: React.FocusEvent<HTMLInputElement>, index: 0 | 1) => {
      const value = e.target.value;
      if (!value && dates[index] !== null) {
        const newDates = [...dates] as [Dayjs | null, Dayjs | null];
        newDates[index] = null;
        setDates(newDates);
        form.setFieldsValue({ applicationPeriod: newDates });
      }
    },
    [dates, form],
  );

  useEffect(() => {
    const formDates = form.getFieldValue('applicationPeriod');
    if (formDates && (!formDates[0]?.isSame(dates[0], 'day') || !formDates[1]?.isSame(dates[1], 'day'))) {
      setDates([formDates[0] || null, formDates[1] || null]);
    }
  }, [form, initialValues]);

  useEffect(() => {
    const pickerNode = rangePickerRef.current?.nativeElement as HTMLElement | null;
    const inputs = pickerNode?.querySelectorAll<HTMLInputElement>('input');
    if (inputs?.length === 2) {
      const input0 = inputs[0];
      const input1 = inputs[1];

      const blurHandler0 = (e: React.FocusEvent<HTMLInputElement>) => handleBlur(e, 0);
      const blurHandler1 = (e: React.FocusEvent<HTMLInputElement>) => handleBlur(e, 1);

      input0.addEventListener('blur', blurHandler0 as unknown as EventListener);
      input1.addEventListener('blur', blurHandler1 as unknown as EventListener);

      return () => {
        input0.removeEventListener('blur', blurHandler0 as unknown as EventListener);
        input1.removeEventListener('blur', blurHandler1 as unknown as EventListener);
      };
    }
  }, [handleBlur]);

  const showWarningModal = useCallback(() => {
    Modal.warning({
      title: 'Cảnh báo',
      content: 'Thao tác này sẽ xoá trạng thái mặc định trước đó và chọn chính sách mới này làm mặc định.',
    });
  }, []);

  const handleCheckboxChange = (
    fieldName: 'isToContract' | 'transactionSuccessful',
    index: number,
    checked: boolean,
  ) => {
    const payments = form.getFieldValue('payments') || [];
    if (checked) {
      // Nếu tích true cho một mục, đặt tất cả các mục khác về false cho fieldName
      const updatedPayments = payments.map((payment: Payments, i: number) => ({
        ...payment,
        [fieldName]: i === index ? true : false, // Chỉ mục được chọn là true, các mục khác là false
      }));
      form.setFieldsValue({ payments: updatedPayments });
    }
  };
  const PaymentInput: React.FC<{
    name: number;
    restField: { [key: string]: unknown };
    getFieldValue: (name: (string | number)[]) => unknown;
    currencyName: string;
  }> = ({ name, restField, getFieldValue, currencyName }) => {
    const [inputValue, setInputValue] = useState('');
    const currency = getFieldValue(['payments', name, currencyName]);
    const form = Form.useFormInstance();
    const paymentType = getFieldValue(['payments', name, 'type']);

    return (
      <Form.Item
        {...restField}
        name={[name, currencyName.replace('Currency', 'Value')]}
        rules={[
          {
            validator: (_, value) => {
              const isHouseAndLand = paymentType === 'TT theo giá nhà và đất';
              if (!isHouseAndLand) {
                // Nếu là TT theo giá bán sản phẩm, yêu cầu bắt buộc nhập
                if (!value) return Promise.reject('Vui lòng nhập giá trị');
                return Promise.resolve();
              }
              // Nếu là TT theo giá nhà và đất, kiểm tra ít nhất một ô có giá trị
              const houseValue = getFieldValue(['payments', name, 'houseValue']);
              const landValue = getFieldValue(['payments', name, 'landValue']);
              if (!houseValue && !landValue) {
                return Promise.reject('Vui lòng nhập giá trị');
              }
              return Promise.resolve();
            },
          },
        ]}
      >
        <InputNumber
          maxLength={currency === '%' ? 5 : 16}
          style={{ width: '100%' }}
          min={currency === '%' ? '1' : 1}
          max={currency === '%' ? '100' : undefined}
          step={currency === '%' ? 0.1 : 1000}
          value={inputValue}
          onChange={value => {
            if (value === null || value === undefined) {
              setInputValue('');
              form.setFieldsValue({
                payments: {
                  [name]: {
                    [currencyName.replace('Currency', 'Value')]: undefined, // Đặt thành undefined nếu trống
                  },
                },
              });
              return;
            }
            if (currency === '%') {
              if (Number(value) < 1 || Number(value) > 100) return;
              setInputValue(value.toString().replace(',', '.') || '');
            } else {
              setInputValue(value ? new Intl.NumberFormat('vi-VN').format(Number(value)) : '');
            }
          }}
          onKeyDown={handleKeyDownEnterNumber}
          formatter={formatNumber}
          placeholder={currency === '%' ? 'Tỉ lệ TT' : 'Số tiền TT'}
          addonAfter={
            <Form.Item {...restField} name={[name, currencyName]} noStyle>
              <Select
                onChange={() => {
                  setInputValue('');
                  form.setFieldsValue({
                    payments: {
                      [name]: {
                        [currencyName.replace('Currency', 'Value')]: null,
                      },
                    },
                  });
                }}
              >
                <Option value="VND">VNĐ</Option>
                <Option value="%">%</Option>
              </Select>
            </Form.Item>
          }
        />
      </Form.Item>
    );
  };

  return (
    <Spin spinning={loading}>
      <Form
        form={form}
        name="discountPolicyForm"
        onFinish={handleFinish}
        initialValues={initialFormValues}
        layout="vertical"
      >
        <Row gutter={64} className="payment-policy-form">
          <Col span={12}>
            <Title level={5} style={{ marginBottom: 16 }}>
              Thông tin chung
            </Title>
            <Row gutter={24}>
              <Col span={14}>
                <Form.Item label="Mã tờ trình" name="eappNumber">
                  <Input placeholder="Mã tờ trình EAPP" disabled />
                </Form.Item>
              </Col>
              <Col span={10}>
                <Form.Item label="Ngày được duyệt" name="approvedDate">
                  <Input placeholder="Ngày CS được duyệt" disabled />
                </Form.Item>
              </Col>
              <Col span={14}>
                <Form.Item
                  label="Tên chính sách"
                  name="name"
                  rules={[
                    {
                      required: true,
                      validator: (_, value) => {
                        if (!value || value.trim() === '') {
                          return Promise.reject(new Error('Vui lòng nhập tên chính sách'));
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <Input
                    placeholder="Nhập tên chính sách"
                    maxLength={255}
                    onBlur={e => {
                      form.setFieldsValue({ name: e.target.value.trim() });
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={10}>
                <Form.Item label="Mã chính sách" name="code">
                  <Input disabled placeholder="Mã chính sách" />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item
              label="Thời gian"
              name="applicationPeriod"
              required
              rules={[
                {
                  validator: async (_, value: [Dayjs | null, Dayjs | null] | null) => {
                    if (!value || (!value[0] && !value[1])) {
                      return Promise.reject(new Error('Vui lòng chọn thời gian'));
                    }
                    if (!value[0]) {
                      return Promise.reject(new Error('Vui lòng chọn ngày bắt đầu'));
                    }
                    return Promise.resolve();
                  },
                },
              ]}
            >
              <RangePicker
                ref={rangePickerRef}
                value={dates}
                onCalendarChange={handleCalendarChange}
                onChange={handleChangeRangePicker}
                placeholder={['Ngày bắt đầu', 'Ngày kết thúc']}
                format="DD/MM/YYYY"
                allowClear={true}
              />
            </Form.Item>

            <Form.Item
              label="Dự án áp dụng"
              name={['project', 'id']}
              rules={[{ required: true, message: 'Vui lòng chọn dự án áp dụng' }]}
            >
              <SingleSelectLazy
                apiQuery={getListProject}
                queryKey={['get-project']}
                placeholder="Chọn dự án áp dụng"
                keysLabel={['name']}
                handleSelect={handleSelectProject}
                defaultValues={
                  initialValues?.project
                    ? {
                        label: ` ${initialValues.project.name}` || '',
                        value: initialValues?.project?.id || '',
                      }
                    : undefined
                }
              />
            </Form.Item>
            <Form.Item layout="horizontal" label="Kích hoạt" name="active" valuePropName="checked">
              <Switch defaultChecked={true} style={{ marginLeft: 140 }} />
            </Form.Item>
            <Form.Item layout="horizontal" label="Chính sách mặc định" name="defaultApply" valuePropName="checked">
              <Switch
                style={{ marginLeft: 70 }}
                checked={isDefaultApply}
                onChange={checked => {
                  if (checked) {
                    showWarningModal(); // Hiển thị cảnh báo khi bật Switch
                  }
                  setIsDefaultApply(checked);
                }}
              />
            </Form.Item>
            <Form.Item label="Mẫu tiến độ thanh toán">
              <UploadFileDiscountPolicy
                fileList={fileListTemplate}
                setFileList={setFileListTemplate}
                uploadPath="primary-contract/policy/payment/template"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Title level={5} style={{ marginBottom: 16 }}>
              Tài liệu liên quan
            </Title>
            <Form.Item label="Tải tệp định kèm" name="files">
              <UploadFileDiscountPolicy
                fileList={fileList}
                setFileList={setFileList}
                uploadPath="primary-contract/policy/payment"
              />
            </Form.Item>
            <Form.Item label="Ghi chú" name="description">
              <Input.TextArea rows={4} placeholder="Nhập ghi chú" maxLength={250} />
            </Form.Item>
          </Col>
          <Col span={24} className="payment-batch">
            <Title level={5}>
              Đợt thanh toán <span>*</span>
            </Title>
            <Form.List name="payments">
              {(fields, { add, remove }) => (
                <>
                  <Button
                    className="add-payment-batch"
                    type="dashed"
                    onClick={() =>
                      add({
                        id: fields.length + 1,
                        name: '',
                        type: 'TT theo giá bán sản phẩm',
                        time: 'Mốc thời gian TT',
                        productCurrency: 'VND',
                        houseCurrency: 'VND',
                        landCurrency: 'VND',
                        value: '',
                        houseValue: '',
                        landValue: '',
                        exactDays: null,
                        expiredDays: null,
                        descriptionProgress: '',
                        isToContract: false,
                        transactionSuccessful: false,
                      })
                    }
                    icon={<PlusOutlined />}
                  >
                    Thêm đợt TT
                  </Button>
                  {fields.map(({ key, name, ...restField }) => (
                    <div className="payment-batch-row" key={key}>
                      <Form.Item
                        {...restField}
                        name={[name, 'name']}
                        rules={[
                          {
                            validator: (_, value) => {
                              if (!value || value.trim() === '') {
                                return Promise.reject(new Error('Vui lòng nhập tên đợt'));
                              }
                              return Promise.resolve();
                            },
                          },
                        ]}
                      >
                        <Input
                          placeholder="Tên đợt"
                          onBlur={e => {
                            const trimmedValue = e.target.value.trim();
                            form.setFieldsValue({
                              payments: form.getFieldValue('payments').map((payment: Payments, index: number) => {
                                if (index === name) {
                                  return { ...payment, name: trimmedValue };
                                }
                                return payment;
                              }),
                            });
                          }}
                          maxLength={50}
                        />
                      </Form.Item>
                      <div style={{ display: 'flex', flexDirection: 'column', flex: 1.2 }}>
                        <Form.Item
                          {...restField}
                          name={[name, 'type']}
                          rules={[{ required: true, message: 'Vui lòng chọn loại thanh toán' }]}
                        >
                          <Select>
                            <Option value="TT theo giá bán sản phẩm">TT theo giá bán sản phẩm</Option>
                            <Option value="TT theo giá nhà và đất">TT theo giá nhà và đất</Option>
                          </Select>
                        </Form.Item>
                        <Form.Item
                          noStyle
                          shouldUpdate={(prev, curr) => prev.payments[name]?.type !== curr.payments[name]?.type}
                        >
                          {({ getFieldValue }) =>
                            getFieldValue(['payments', name, 'type']) === 'TT theo giá bán sản phẩm' ? (
                              <PaymentInput
                                name={name}
                                restField={restField}
                                getFieldValue={getFieldValue}
                                currencyName="productCurrency"
                              />
                            ) : (
                              <>
                                <Form.Item label="Giá theo nhà">
                                  <PaymentInput
                                    name={name}
                                    restField={restField}
                                    getFieldValue={getFieldValue}
                                    currencyName="houseCurrency"
                                  />
                                </Form.Item>
                                <Form.Item label="Giá theo đất">
                                  <PaymentInput
                                    name={name}
                                    restField={restField}
                                    getFieldValue={getFieldValue}
                                    currencyName="landCurrency"
                                  />
                                </Form.Item>
                              </>
                            )
                          }
                        </Form.Item>
                      </div>
                      <div style={{ display: 'flex', flexDirection: 'column', flex: 1 }}>
                        <Form.Item
                          {...restField}
                          name={[name, 'time']}
                          rules={[{ required: true, message: 'Vui lòng chọn thời gian thanh toán' }]}
                        >
                          <Select>
                            <Option value="Mốc thời gian TT">Mốc thời gian TT</Option>
                            <Option value="Khoảng thời gian TT">Khoảng thời gian TT</Option>
                          </Select>
                        </Form.Item>
                        <Form.Item
                          noStyle
                          shouldUpdate={(prev, curr) => prev.payments[name]?.time !== curr.payments[name]?.time}
                        >
                          {({ getFieldValue }) =>
                            getFieldValue(['payments', name, 'time']) === 'Mốc thời gian TT' ? (
                              <Form.Item
                                {...restField}
                                name={[name, 'exactDays']}
                                rules={[{ required: true, message: 'Vui lòng chọn ngày thanh toán' }]}
                              >
                                <DatePicker
                                  placeholder="Chọn ngày TT"
                                  format="DD/MM/YYYY"
                                  disabledDate={current => current && current < dayjs().startOf('day')}
                                />
                              </Form.Item>
                            ) : (
                              <Form.Item
                                {...restField}
                                name={[name, 'expiredDays']}
                                rules={[{ required: true, message: 'Vui lòng nhập số ngày thanh toán' }]}
                              >
                                <InputNumber
                                  style={{ width: '100%' }}
                                  placeholder="Nhập số"
                                  min={1}
                                  maxLength={3}
                                  parser={value => (value ? Number(value.replace(/\D/g, '')) : '')}
                                  addonAfter={'Ngày'}
                                  onKeyDown={handleKeyDownEnterNumber}
                                />
                              </Form.Item>
                            )
                          }
                        </Form.Item>
                      </div>
                      <Form.Item {...restField} name={[name, 'descriptionProgress']}>
                        <Input placeholder="Mô tả tiến độ" maxLength={150} />
                      </Form.Item>
                      <div style={{ display: 'flex', gap: 16 }}>
                        <Form.Item {...restField} name={[name, 'isToContract']} valuePropName="checked" noStyle>
                          <Checkbox
                            className="custom-radio-checkbox"
                            onChange={e => handleCheckboxChange('isToContract', name, e.target.checked)}
                          >
                            Ra HDMB
                          </Checkbox>
                        </Form.Item>
                        <Form.Item
                          {...restField}
                          name={[name, 'transactionSuccessful']}
                          valuePropName="checked"
                          noStyle
                        >
                          <Checkbox
                            className="custom-radio-checkbox transaction-success-checkbox"
                            onChange={e => handleCheckboxChange('transactionSuccessful', name, e.target.checked)}
                          >
                            Giao dịch thành công
                          </Checkbox>
                        </Form.Item>
                      </div>
                      {fields.length > 1 && (
                        <Button type="link" danger onClick={() => remove(name)}>
                          <DeleteOutlined /> Xóa
                        </Button>
                      )}
                    </div>
                  ))}
                </>
              )}
            </Form.List>
          </Col>
        </Row>

        <div className="create-footer">
          <div className="button-create">
            {isUpdate && (
              <>
                <Button onClick={() => {}} style={{ marginRight: 12 }} disabled={loading} size="small">
                  Soạn tờ trình
                </Button>
                <Button type="primary" htmlType="submit" loading={loading} size="small">
                  Lưu
                </Button>
              </>
            )}
            {!isUpdate && (
              <Button type="primary" htmlType="submit" loading={loading} size="small">
                Lưu
              </Button>
            )}
          </div>
        </div>
      </Form>
    </Spin>
  );
};

export default PaymentPolicyForm;
