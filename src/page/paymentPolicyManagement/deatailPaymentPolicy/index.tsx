import React, { useState } from 'react';
import { Form } from 'antd';
import { useFetch, useUpdateField } from '../../../hooks';
import BreadCrumbComponent from '../../../components/breadCrumb';
import { DiscountPolicy } from '../../../types/discountPolicy';
import { useParams } from 'react-router-dom';
import PaymentPolicyForm from '../component/formPaymentPolicy';
import { IPaymentPolicy } from '../../../types/paymentPolicy';
import { getDetailPaymentPolicy, updatePaymentPolicy } from '../../../service/paymentPolicy';

interface PaymentPolicyDetailProps {
  initialData?: DiscountPolicy;
}

const PaymentPolicyDetail: React.FC<PaymentPolicyDetailProps> = ({ initialData }) => {
  const { id } = useParams<{ id: string }>();

  const [form] = Form.useForm();
  const [resetUpload, setResetUpload] = useState(false);

  const { data, isLoading } = useFetch<IPaymentPolicy>({
    queryKeyArr: ['get-detail-payment-policy'],
    api: () => id && getDetailPaymentPolicy(id || ''),
    enabled: !!id,
    cacheTime: 10,
  });
  const discountPolicy: IPaymentPolicy | undefined = data?.data?.data || (initialData as IPaymentPolicy);

  const { mutateAsync: update } = useUpdateField<IPaymentPolicy>({
    keyOfListQuery: ['get-payment-policy'],
    keyOfDetailQuery: ['get-payment-policy'],
    apiQuery: updatePaymentPolicy,
    isMessageError: false,
    messageSuccess: 'Chỉnh sửa chính sách thành công!',
  });

  const handleUpdate = async (values: IPaymentPolicy) => {
    await update({ ...values, id: id });
  };

  return (
    <div className="discount-policy-detail">
      <BreadCrumbComponent titleBread={data?.data?.data?.name} />
      <PaymentPolicyForm
        form={form}
        onFinish={handleUpdate}
        isUpdate={true}
        resetUpload={resetUpload}
        setResetUpload={setResetUpload}
        initialValues={discountPolicy}
        loading={isLoading}
      />
    </div>
  );
};

export default PaymentPolicyDetail;
