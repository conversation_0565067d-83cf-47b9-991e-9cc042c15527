import { Form, Modal } from 'antd';
import { useCallback, useState } from 'react';
import ModalComponent from '../../../components/modal';
import PaymentPolicyForm from '../component/formPaymentPolicy';
import { useCreateField } from '../../../hooks';
import { IPaymentPolicy } from '../../../types/paymentPolicy';
import { createPaymentPolicy } from '../../../service/paymentPolicy';

interface PaymentPolicyModalProps {
  visible: boolean;
  onClose: () => void;
}

const PaymentPolicyModal = ({ visible, onClose }: PaymentPolicyModalProps) => {
  const [form] = Form.useForm();
  const [resetUpload, setResetUpload] = useState<boolean>(false);

  const { mutateAsync: create } = useCreateField<IPaymentPolicy>({
    keyOfListQuery: ['get-payment-policy'],
    apiQuery: createPaymentPolicy,
    isMessageError: false,
    messageSuccess: '<PERSON><PERSON><PERSON> mới chính sách thành công!',
  });

  const resetFormPaymentPolicy = useCallback(async () => {
    await form.resetFields();
    setResetUpload(true);
  }, [form]);

  const handleCancel = useCallback(async () => {
    if (form.isFieldsTouched()) {
      Modal.confirm({
        title: 'Xác nhận hủy',
        content: 'Dữ liệu chưa được lưu, bạn có chắc chắn muốn thoát khỏi trang không?',
        cancelText: 'Quay lại',
        onOk: async () => {
          await resetFormPaymentPolicy();
          onClose();
        },
        okButtonProps: { type: 'default' },
        cancelButtonProps: { type: 'primary' },
      });
    } else {
      await resetFormPaymentPolicy();
      onClose();
    }
  }, [form, onClose, resetFormPaymentPolicy]);

  const handleCreate = useCallback(
    async (values: IPaymentPolicy) => {
      try {
        const resp = await create(values);
        if (resp?.data?.statusCode === '0' && resp?.data?.success) {
          await resetFormPaymentPolicy();
          onClose();
        }
      } catch (error) {
        console.error('Create error:', error);
      }
    },
    [create, onClose, resetFormPaymentPolicy],
  );

  return (
    <ModalComponent
      className="modal-discount-policy"
      title="Tạo mới chính sách thanh toán"
      open={visible}
      onCancel={handleCancel}
      footer={null}
      destroyOnClose
    >
      <PaymentPolicyForm
        form={form}
        onFinish={handleCreate}
        isUpdate={false}
        resetUpload={resetUpload}
        setResetUpload={setResetUpload}
        loading={false}
      />
    </ModalComponent>
  );
};

export default PaymentPolicyModal;
