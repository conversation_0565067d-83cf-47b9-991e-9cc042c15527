import { ExclamationCircleFilled } from '@ant-design/icons';
import { <PERSON><PERSON>, <PERSON>, Table, TableColumnsType, Typography } from 'antd';
import { useParams } from 'react-router-dom';
import { CUSTOMER_PERSONAL } from '../../../../configs/path';
import { useCreateField, useUpdateField } from '../../../../hooks';
import { postCreateCustomer } from '../../../../service/customers';
import { updateExploitStatusLeadCare } from '../../../../service/lead';
import { IModalCheckDuplicate, TDataDuplicate } from '../../../../types/customers';
import { ICustomerFormLead } from '../../../../types/leadAssigned';

const columns: TableColumnsType = [
  {
    title: 'Khách hàng',
    dataIndex: 'name',
    key: 'name',
    fixed: 'left',
    render: (value: string, record: TDataDuplicate) =>
      value ? (
        <a href={`${CUSTOMER_PERSONAL}/${record?.id}`} target="_blank" rel="noopener noreferrer">
          {value}
        </a>
      ) : (
        '-'
      ),
  },
  {
    title: 'Mã khách hàng',
    dataIndex: 'code',
    key: 'code',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Số giấy tờ',
    dataIndex: 'identities',
    key: 'identities',
    render: (value: string[], record: TDataDuplicate) =>
      value?.length > 0 ? value?.join(', ') : record?.taxCode ? record?.taxCode : '-',
  },
  {
    title: 'Loại khách hàng',
    dataIndex: 'type',
    key: 'type',
    render: (value: string) => (value === 'business' ? 'Doanh nghiệp' : 'Cá nhân'),
  },
  {
    title: 'Email',
    dataIndex: 'email',
    key: 'email',
    render: (value: string) => (value ? value : '-'),
  },
];

const ModalCheckDuplicateKHTN = (props: IModalCheckDuplicate) => {
  const { isOpen, handleCancelCheckDuplicate, dataDuplicate, handleCancel, dataSubmit, removeDataSubmit, type } = props;
  const { id } = useParams();

  const { mutateAsync: createCustomer, isPending: isPendingCreate } = useCreateField<ICustomerFormLead>({
    apiQuery: postCreateCustomer,
    checkDuplicate: true,
    isMessageSuccess: false,
  });

  const { mutateAsync: updateExploitStatus } = useUpdateField({
    apiQuery: updateExploitStatusLeadCare,
    keyOfDetailQuery: ['detail-lead-assigned', id],
    messageSuccess: 'Trạng thái lead được cập nhật thành công',
    messageError: 'Trạng thái lead được cập nhật lead thất bại',
  });

  const handleOk = async () => {
    const res = await createCustomer({ ...dataSubmit, continueCreate: true });
    const responseData = res?.data;
    if (!responseData) return;
    if (responseData.statusCode === '0') {
      await updateExploitStatus({ id, status: 'done' });
    }
    removeDataSubmit();
    handleCancelCheckDuplicate();
    handleCancel && handleCancel();
  };

  return (
    <Modal
      rootClassName="wrapper-modal-create-customer"
      title="Tạo mới khách hàng tiềm năng cá nhân"
      open={isOpen}
      centered
      maskClosable={false}
      onCancel={handleCancelCheckDuplicate}
      width={800}
      zIndex={1100}
      footer={
        <Button type="primary" htmlType="submit" onClick={handleOk} loading={isPendingCreate}>
          {type === 'update' ? 'Tiếp tục lưu thay đổi' : 'Tiếp tục tạo mới'}
        </Button>
      }
    >
      <div style={{ padding: '20px 0' }}>
        <ExclamationCircleFilled style={{ color: '#FAAD14', fontSize: 21 }} /> &nbsp;
        <Typography.Text>
          Số điện thoại đã có trên hệ thống, bạn có chắc chắn muốn tạo thêm thông tin không?
        </Typography.Text>
      </div>
      <Table scroll={{ y: 200 }} columns={columns} dataSource={dataDuplicate} pagination={false} rowKey={'id'} />
    </Modal>
  );
};

export default ModalCheckDuplicateKHTN;
