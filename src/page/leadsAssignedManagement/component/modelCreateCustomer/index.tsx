import { Button, Col, Form, Input, Modal, Radio, Row } from 'antd';
import React, { useEffect, useState } from 'react';
import { OPTIONS_GENDER, REGEX_PHONE_VN } from '../../../../constants/common';
import { useCreateField, useUpdateField } from '../../../../hooks';
import { postCreateCustomer } from '../../../../service/customers';
import { updateExploitStatusLeadCare } from '../../../../service/lead';
import { TDataDuplicate } from '../../../../types/customers';
import { ICustomerFormLead, LeadAssigned } from '../../../../types/leadAssigned';
import { handleKeyDownEnterNumber } from '../../../../utilities/regex';
import ModalCheckDuplicateKHTN from '../ModalCheckDuplicateKHTN';
import './styles.scss';

const { Item } = Form;

interface Props {
  isOpen: boolean;
  handleCancel: () => void;
  defaultValue?: LeadAssigned;
}
const ModalCreateCustomerFormLead = (props: Props) => {
  const { isOpen, handleCancel, defaultValue } = props;
  const [form] = Form.useForm();

  const [isOpenCheckDuplicate, setIsOpenCheckDuplicate] = useState(false);
  const [dataSubmit, setDataSubmit] = useState<ICustomerFormLead>();
  const [dataDuplicate, setDataDuplicate] = useState<TDataDuplicate[]>();

  const { mutateAsync: createCustomer } = useCreateField<ICustomerFormLead>({
    apiQuery: postCreateCustomer,
    checkDuplicate: true,
    isMessageSuccess: false,
  });
  const { mutateAsync: updateExploitStatus } = useUpdateField({
    apiQuery: updateExploitStatusLeadCare,
    keyOfDetailQuery: ['detail-lead-assigned', defaultValue?.id],
    messageSuccess: 'Trạng thái lead được cập nhật thành công',
    messageError: 'Trạng thái lead được cập nhật lead thất bại',
  });

  useEffect(() => {
    if (defaultValue) {
      form.setFieldsValue({ ...defaultValue, name: defaultValue.name.toUpperCase() });
    }
  }, [defaultValue, form]);

  const handleSubmit = async (values: ICustomerFormLead) => {
    const newData: ICustomerFormLead = {
      ...values,
      type: 'individual',
      leadCode: defaultValue?.code,
      continueCreate: false,
    };

    try {
      const isResubmission = dataSubmit?.phone === values.phone;
      const submitData = isResubmission ? { ...newData, continueCreate: true } : newData;

      const res = await createCustomer(submitData);
      const responseData = res?.data;

      if (!responseData || !defaultValue?.id) return;

      if (responseData.statusCode === 'DCUSE0001' && !isResubmission) {
        setIsOpenCheckDuplicate(true);
        setDataSubmit(newData);
        setDataDuplicate(responseData.data as TDataDuplicate[]);
        return;
      }

      if (responseData.statusCode === '0' && defaultValue?.id) {
        await updateExploitStatus({ id: defaultValue.id, status: 'done' });
        removeDataSubmit();
        handleCancel();
      }
    } catch (error) {
      console.error('Error creating customer:', error);
    }
  };

  const handleCancelCheckDuplicate = () => {
    setIsOpenCheckDuplicate(false);
  };

  const removeDataSubmit = () => {
    setDataSubmit(undefined);
  };

  useEffect(() => {
    if (form.isFieldsTouched(true) && isOpen) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [form, isOpen]);

  return (
    <>
      <Modal
        rootClassName="wrapper-modal-create-customer"
        title="Tạo mới khách hàng tiềm năng cá nhân"
        open={isOpen}
        maskClosable={false}
        centered
        onCancel={() => {
          handleCancel();
          removeDataSubmit();
        }}
        zIndex={1000}
        footer={
          <Button type="primary" htmlType="submit" onClick={() => form.submit()}>
            Tạo mới
          </Button>
        }
      >
        <Form form={form} layout="vertical" onFinish={handleSubmit}>
          <Row gutter={16} className="content-header-form">
            <Col span={12}>
              <Item
                name={'name'}
                label="Họ và tên"
                required={false}
                rules={[{ required: true, message: 'Vui lòng nhập họ và tên' }]}
              >
                <Input
                  maxLength={60}
                  placeholder="Nhập họ và tên"
                  onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                    e.target.value = e.target.value.toUpperCase();
                  }}
                />
              </Item>
            </Col>
            <Col span={12}>
              <Item
                name={'phone'}
                required={false}
                label="Số điện thoại"
                rules={[
                  { required: true, message: 'Vui lòng nhập số điện thoại' },
                  {
                    pattern: REGEX_PHONE_VN,
                    message: 'Số điện thoại phải bắt đầu bằng số 0 và từ 9 đến 15 chữ số',
                  },
                ]}
              >
                <Input maxLength={15} onKeyDown={handleKeyDownEnterNumber} placeholder="Nhập số điện thoại" />
              </Item>
            </Col>
            <Col span={12}>
              <Item
                name={'gender'}
                required={false}
                label="Giới tính"
                rules={[{ required: true, message: 'Vui lòng nhập giới tính' }]}
              >
                <Radio.Group options={OPTIONS_GENDER} />
              </Item>
            </Col>
          </Row>
        </Form>
      </Modal>
      <ModalCheckDuplicateKHTN
        isOpen={isOpenCheckDuplicate}
        handleCancelCheckDuplicate={handleCancelCheckDuplicate}
        handleCancel={handleCancel}
        dataDuplicate={dataDuplicate}
        dataSubmit={dataSubmit as ICustomerFormLead}
        removeDataSubmit={removeDataSubmit}
        type="create"
      />
    </>
  );
};

export default ModalCreateCustomerFormLead;
