import { Form, Timeline } from 'antd';
import dayjs from 'dayjs';
import { EXPLOIT_STATUS_MAP, FORMAT_DATE_TIME } from '../../../../constants/common';
import { TExploitHistoryItem } from '../../../../types/leadAssigned';

const TabHistoryLead = () => {
  const form = Form.useFormInstance();
  const historyLead = form.getFieldValue('exploitHistory');

  const items = historyLead
    ?.filter((item: TExploitHistoryItem) => item.status !== 'new') // Ẩn status "new"
    ?.map((item: TExploitHistoryItem) => {
      const isRenew = item.status === 'renew';
      const status = EXPLOIT_STATUS_MAP[item?.status as keyof typeof EXPLOIT_STATUS_MAP];

      return {
        color: isRenew ? '#F5222D' : status?.color,
        children: (
          <div className="timeline-item-lead">
            <div className="time">{dayjs(item?.updatedAt).format(FORMAT_DATE_TIME)}</div>
            <div className="status">{isRenew ? 'Từ chối' : status?.label}</div>
          </div>
        ),
      };
    })
    ?.reverse();

  return (
    <div>
      <Timeline items={items} />
    </div>
  );
};

export default TabHistoryLead;
