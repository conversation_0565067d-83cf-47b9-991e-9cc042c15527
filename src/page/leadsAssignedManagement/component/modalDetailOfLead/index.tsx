import { But<PERSON>, Form, Modal, Spin, Tabs } from 'antd';
import { memo, useEffect } from 'react';
import { PERMISSION_LEAD } from '../../../../constants/permissions/lead';
import { useAuth } from '../../../../context/AuthContext';
import { useFetch, useSubscribeQuery, useUpdateField } from '../../../../hooks';
import { getDetailOfLeadCare, updateExploitStatusLeadCare } from '../../../../service/lead';
import { LeadAssigned } from '../../../../types/leadAssigned';
import TabGeneralLead from './TabGeneralLead';
import TabHistoryLead from './TabHistoryLead';
import './styles.scss';
import { EXPLOIT_STATUS_MAP, FORMAT_DATE } from '../../../../constants/common';
import dayjs from 'dayjs';

type Props = {
  id: string;
  handleCancel: () => void;
  urlRedirect: string;
  queryKeyArr?: string[];
  keyOfDetailQuery?: string[];
};

const ModalDetailOfLead = (props: Props) => {
  const { id, handleCancel, urlRedirect, queryKeyArr, keyOfDetailQuery } = props;
  const [form] = Form.useForm();
  const { hasAuthority } = useAuth();
  const isPermissionDetail = hasAuthority(PERMISSION_LEAD.getId);

  const dataAccount = useSubscribeQuery<{ id: string }>(['info-account']);

  const { mutateAsync: updateExploitStatus } = useUpdateField({
    apiQuery: updateExploitStatusLeadCare,
    keyOfListQuery: queryKeyArr,
    keyOfDetailQuery,
    messageSuccess: 'Trạng thái lead được cập nhật thành công',
    messageError: 'Trạng thái lead được cập nhật thất bại',
  });
  const { data, isLoading } = useFetch<LeadAssigned>({
    api: getDetailOfLeadCare,
    queryKeyArr: ['detail-lead-assigned', id],
    moreParams: { id },
    enabled: !!id,
    withFilter: false,
  });
  const dataLeadDetail = data?.data?.data;
  const exploitStatus = dataLeadDetail?.exploitStatus;
  const status = EXPLOIT_STATUS_MAP[dataLeadDetail?.exploitStatus as keyof typeof EXPLOIT_STATUS_MAP];
  const isCheckTakeCare = dataAccount?.data?.data?.id === dataLeadDetail?.takeCare?.accountId;

  const items = [
    {
      key: '2',
      label: 'Thông tin chung',
      children: <TabGeneralLead />,
    },
    {
      key: '3',
      label: 'Lịch sử khai thác lead',
      children: <TabHistoryLead />,
    },
  ];

  useEffect(() => {
    if (dataLeadDetail && id) {
      const newData = {
        ...dataLeadDetail,
        exploitStatus: status?.label,
        createdDate: dataLeadDetail?.createdDate ? dayjs(dataLeadDetail?.createdDate).format(FORMAT_DATE) : undefined,
      };
      form.setFieldsValue(newData);
    }
  }, [dataLeadDetail, form, id, status?.label]);

  const handleSubmit = async () => {
    if (exploitStatus === 'assign' && isCheckTakeCare) {
      await updateExploitStatus({ id, status: 'processing' });
    }
    window.open(`${urlRedirect}/${id}`, '_blank', 'noopener,noreferrer');
    handleCancel();
  };
  return (
    <Modal
      rootClassName="modal-lead-detail"
      title={dataLeadDetail?.name}
      open={!!id}
      footer={
        isPermissionDetail && (
          <Button type="primary" onClick={handleSubmit}>
            Khai thác Lead
          </Button>
        )
      }
      destroyOnClose
      width={645}
      onCancel={() => {
        handleCancel();
        form.resetFields();
      }}
    >
      <Spin spinning={isLoading}>
        <Form form={form} layout="horizontal" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }} disabled={true}>
          <Tabs items={items} defaultActiveKey="1" />
        </Form>
      </Spin>
    </Modal>
  );
};

export default memo(ModalDetailOfLead);
