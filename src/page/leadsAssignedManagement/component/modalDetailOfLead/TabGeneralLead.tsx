import { Form, Input } from 'antd';
import TextArea from 'antd/es/input/TextArea';

const { Item } = Form;

const TabGeneralLead = () => {
  return (
    <>
      {/* <Item label="Mã KHTN" name={'demandCustomer'}>
        <Input disabled />
      </Item> */}
      <Item label="Mã Lead" name="code">
        <Input disabled />
      </Item>
      <Item label="Trạng thái" name="exploitStatus">
        <Input disabled />
      </Item>
      <Item label="Tên Lead" name="name">
        <Input />
      </Item>
      <Item label="Số điện thoại" name="phone">
        <Input />
      </Item>
      <Item label="Dự án" name={['project', 'name']}>
        <Input disabled title={Form.useWatch(['project', 'name']) || ''} />
      </Item>
      <Item label="Nguồn" name="source">
        <Input disabled title={Form.useWatch('source') || ''} />
      </Item>
      <Item label="Ngày tạo" name="createdDate">
        <Input disabled />
      </Item>
      {/* <Item label="<PERSON>ho cấu hình" name={['repo', 'name']}>
        <Input disabled />
      </Item>
      <Item label="Cấu hình phân bổ" name={['configData', 'name']}>
        <Input disabled />
      </Item> */}
      {/* <Item label="Email" name="email">
        <Input />
      </Item>
      <Item label="Link profile" name="profileUrl">
        <Input />
      </Item> */}
      <Item label="Ghi chú" name="note">
        <TextArea rows={4} />
      </Item>
    </>
  );
};

export default TabGeneralLead;
