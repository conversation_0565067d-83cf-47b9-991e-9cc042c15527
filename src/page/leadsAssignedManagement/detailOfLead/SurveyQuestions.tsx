import { Checkbox, Form, Radio, Select } from 'antd';
import { Input } from 'antd/lib';
import { ISurvey, IValuesSurvey } from '../../../types/lead';

interface Props {
  data?: ISurvey[];
  disabled?: boolean;
}
const SurveyQuestions = (props: Props) => {
  const { data, disabled } = props;

  return (
    <>
      {data?.map((item, index) => {
        const { type, name, values } = item;

        switch (type) {
          case 'radio': {
            return (
              <>
                <Form.Item hidden name={['surveys', index]} />
                <Form.Item
                  label={name}
                  key={index}
                  labelCol={{ span: 24 }}
                  wrapperCol={{ span: 24 }}
                  name={['surveys', index, 'values']}
                  getValueProps={value => {
                    if (value) {
                      const selectedValue = value.find((v: IValuesSurvey) => v.value)?.code;
                      return { value: selectedValue };
                    }
                    return {};
                  }}
                  getValueFromEvent={e => {
                    const selectedValue = e.target.value;
                    return values.map((value: IValuesSurvey) => ({
                      ...value,
                      value: value.code === selectedValue,
                    }));
                  }}
                >
                  <Radio.Group
                    disabled={disabled}
                    options={values.map(value => ({
                      value: value.code,
                      label: value.name,
                    }))}
                  />
                </Form.Item>
              </>
            );
          }

          case 'checkboxes':
            return (
              <>
                <Form.Item hidden name={['surveys', index]} />

                <Form.Item
                  label={name}
                  key={index}
                  labelCol={{ span: 24 }}
                  wrapperCol={{ span: 24 }}
                  name={['surveys', index, 'values']}
                  getValueProps={value => {
                    if (value) {
                      const selectedValues = value
                        .filter((v: IValuesSurvey) => v.value)
                        .map((v: IValuesSurvey) => v.code);
                      return { value: selectedValues };
                    }
                    return {};
                  }}
                  getValueFromEvent={checkedValues => {
                    return values.map((value: IValuesSurvey) => ({
                      ...value,
                      value: checkedValues.includes(value.code),
                    }));
                  }}
                >
                  <Checkbox.Group
                    disabled={disabled}
                    options={values.map(v => ({
                      label: v.name,
                      value: v.code,
                    }))}
                  />
                </Form.Item>
              </>
            );

          case 'dropdown':
            return (
              <>
                <Form.Item hidden name={['surveys', index]} />
                <Form.Item
                  label={name}
                  key={index}
                  labelCol={{ span: 24 }}
                  wrapperCol={{ span: 24 }}
                  name={['surveys', index, 'values']}
                  getValueProps={value => {
                    if (value) {
                      const selectedValues = value
                        .filter((v: IValuesSurvey) => v.value)
                        .map((v: IValuesSurvey) => v.code);
                      return { value: selectedValues };
                    }
                    return {};
                  }}
                  getValueFromEvent={selectedValues => {
                    return values.map((value: IValuesSurvey) => ({
                      ...value,
                      value: selectedValues.includes(value.code),
                    }));
                  }}
                >
                  <Select
                    disabled={disabled}
                    mode="multiple"
                    placeholder="Chọn câu trả lời"
                    options={values.map(v => ({
                      value: v.code,
                      label: v.name,
                    }))}
                  />
                </Form.Item>
              </>
            );

          case 'textbox':
            return (
              <>
                <Form.Item hidden name={['surveys', index]} />
                <Form.Item
                  label={name}
                  key={index}
                  labelCol={{ span: 24 }}
                  wrapperCol={{ span: 24 }}
                  name={['surveys', index, 'text']}
                >
                  <Input.TextArea disabled={disabled} maxLength={250} rows={3} placeholder="Nhập câu trả lời" />
                </Form.Item>
              </>
            );

          case 'multilineTextbox':
            return (
              <>
                <Form.Item hidden name={['surveys', index]} />
                <Form.Item
                  label={name}
                  key={index}
                  labelCol={{ span: 24 }}
                  wrapperCol={{ span: 24 }}
                  name={['surveys', index, 'multilineText']}
                >
                  <Input.TextArea disabled={disabled} rows={5} maxLength={500} placeholder="Nhập câu trả lời" />
                </Form.Item>
              </>
            );
          default:
            return null;
        }
      })}
    </>
  );
};

export default SurveyQuestions;
