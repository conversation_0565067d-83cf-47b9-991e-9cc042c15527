import { App, But<PERSON>, Col, Form, Input, Row, Space, Spin, Typography } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import BreadCrumbComponent from '../../../components/breadCrumb';
import { modalConfirm } from '../../../components/modal/specials/ModalConfirm';
import { EXPLOIT_STATUS_MAP, FORMAT_DATE_TIME, REGEX_PHONE_VN } from '../../../constants/common';
import { PERMISSION_LEAD } from '../../../constants/permissions/lead';
import { useAuth } from '../../../context/AuthContext';
import { useFetch, useSubscribeQuery, useUpdateField } from '../../../hooks';
import { getDetailOfLeadCare, updateDetailOfLeadCare, updateExploitStatusLeadCare } from '../../../service/lead';
import { LeadAssigned } from '../../../types/leadAssigned';
import { handleKeyDownEnterNumber } from '../../../utilities/regex';
import { validateEmail, validateProfileUrl } from '../../../utilities/shareFunc';
import ModalCreateCustomerFormLead from '../component/modelCreateCustomer';
import './styles.scss';
import SurveyQuestions from './SurveyQuestions';

const { TextArea } = Input;
const { Text, Title } = Typography;
const { Item } = Form;

const DetailOfLeadCare = () => {
  const { id } = useParams();
  const [form] = Form.useForm();
  const [isModified, setIsModified] = useState(false);
  const [initialData, setInitialData] = useState<LeadAssigned>();
  const [isOpenModalCreate, setIsOpenModalCreate] = useState(false);

  const { modal } = App.useApp();
  const { hasAuthority } = useAuth();
  const isPermissionDetail = hasAuthority(PERMISSION_LEAD.update);

  const { data, isLoading } = useFetch<LeadAssigned>({
    api: getDetailOfLeadCare,
    queryKeyArr: ['detail-lead-assigned', id],
    moreParams: { id },
    withFilter: false,
  });

  const dataAccount = useSubscribeQuery<{ id: string }>(['info-account']);

  const dataLeadDetail = data?.data?.data;
  const status = EXPLOIT_STATUS_MAP[dataLeadDetail?.exploitStatus as keyof typeof EXPLOIT_STATUS_MAP];
  const disabled = dataLeadDetail?.exploitStatus === 'done' || dataLeadDetail?.exploitStatus === 'cancel';
  const isCheckTakeCare = dataAccount?.data?.data?.id === dataLeadDetail?.takeCare?.accountId;
  const dataSurveys = data?.data?.data?.configData?.surveys;

  const { mutateAsync: updateLeadCare } = useUpdateField({
    apiQuery: updateDetailOfLeadCare,
    keyOfDetailQuery: ['detail-lead-assigned', id],
    isMessageError: false,
  });

  const { mutateAsync: updateExploitStatus } = useUpdateField({
    apiQuery: updateExploitStatusLeadCare,
    keyOfDetailQuery: ['detail-lead-assigned', id],
    messageSuccess: 'Trạng thái lead được cập nhật thành công',
    messageError: 'Trạng thái lead được cập nhật thất bại',
  });

  useEffect(() => {
    if (!dataLeadDetail) return;
    const fields = {
      ...dataLeadDetail,
      exploitStatus: status?.label,
      surveys: dataLeadDetail?.surveys?.length ? dataLeadDetail?.surveys : dataLeadDetail?.configData?.surveys,
    };
    form.setFieldsValue(fields);
    setInitialData(fields);
  }, [form, dataLeadDetail, status?.label]);

  useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  const validateForm = () => {
    setIsModified(true);
  };

  const handleCancel = () => {
    modal.confirm({
      title: 'Xác nhận hủy',
      content: 'Dữ liệu đang chưa được lưu, bạn có chắc muốn huỷ dữ liệu đang nhập không?',
      cancelText: 'Quay lại',
      onOk: () => {
        form.resetFields();
        setIsModified(false);
      },
      okButtonProps: { type: 'default' },
      cancelButtonProps: { type: 'primary' },
    });
  };

  const handleSubmit = async () => {
    const values = await form.validateFields();

    const newValue = {
      id,
      name: values?.name?.trim(),
      phone: values?.phone?.trim(),
      email: values?.email?.trim(),
      profileUrl: values?.profileUrl?.trim(),
      note: values?.note?.trim(),
      configData: values?.configData,
      surveys: values?.surveys,
    };
    const res = await updateLeadCare(newValue);
    if (res?.data?.statusCode === '0') {
      setIsModified(false);
    }
  };

  const handleConvert = async () => {
    setIsOpenModalCreate(true);
  };

  const handleReturn = async () => {
    return modalConfirm({
      modal: modal,
      title: 'Trả về Lead',
      content: 'Bạn có chắc chắn muốn trả về lead này?',
      handleConfirm: async () => {
        await updateExploitStatus({ id, status: 'cancel' });
      },
    });
  };

  const handleCancelModalCreate = () => {
    setIsOpenModalCreate(false);
  };

  return (
    <div className="wrapper-lead-care">
      <Spin spinning={isLoading}>
        <BreadCrumbComponent titleBread={dataLeadDetail?.name} />
        <Form
          form={form}
          layout="horizontal"
          initialValues={initialData}
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 18 }}
          onValuesChange={validateForm}
          onFinish={handleSubmit}
          disabled={!isPermissionDetail}
        >
          <Row justify="space-between" gutter={32}>
            <Col lg={12} xs={24}>
              <Title level={5} className="title-detail">
                Thông tin cá nhân
              </Title>
              <Item label="Mã KHTN" name={['demandCustomer', 'code']}>
                <Input disabled />
              </Item>
              <Item label="Nguồn Lead" name="source">
                <Input disabled />
              </Item>
              <Item label="Kho cấu hình" name={['repo', 'name']}>
                <Input disabled />
              </Item>
              <Item label="Cấu hình phân bổ" name={['configData', 'name']}>
                <Input disabled />
              </Item>
              <Item label="Mã Lead" name="code">
                <Input disabled />
              </Item>
              <Item
                label="Tên Lead"
                name="name"
                required
                rules={[{ required: true, message: 'Vui lòng nhập tên lead' }]}
              >
                <Input placeholder="Nhập tên lead" maxLength={60} disabled={disabled || !isCheckTakeCare} />
              </Item>
              <Item
                label="Số điện thoại"
                name="phone"
                required
                rules={[
                  { required: true, message: 'Vui lòng nhập số điện thoại' },
                  { pattern: REGEX_PHONE_VN, message: 'Số điện thoại phải bắt đầu bằng số 0 và từ 9 đến 15 chữ số' },
                ]}
              >
                <Input
                  placeholder="Nhập số điện thoại"
                  disabled={disabled || !isCheckTakeCare}
                  onKeyDown={handleKeyDownEnterNumber}
                  maxLength={15}
                />
              </Item>
              <Item
                label="Email"
                name="email"
                rules={[
                  {
                    validator: (_, value) => validateEmail(value),
                  },
                ]}
              >
                <Input placeholder="Nhập Email" maxLength={25} disabled={disabled || !isCheckTakeCare} />
              </Item>
              <Item
                label="Link profile"
                name="profileUrl"
                rules={[
                  {
                    validator: (_, value) => {
                      const result = validateProfileUrl(value);
                      return result.isValid ? Promise.resolve() : Promise.reject(result.message);
                    },
                  },
                ]}
              >
                <Input placeholder="Nhập link profile" maxLength={50} disabled={disabled || !isCheckTakeCare} />
              </Item>
              <Item label="Ghi chú" name="note">
                <TextArea placeholder="Nhập ghi chú" rows={4} maxLength={500} disabled={disabled || !isCheckTakeCare} />
              </Item>
              <Item label="Trạng thái" name="exploitStatus">
                <Input disabled style={{ color: status?.color }} />
              </Item>
              <div className="info">
                <Row gutter={24}>
                  <Col lg={6} xs={8}>
                    <Text disabled>Ngày cập nhật: </Text>
                  </Col>
                  <Col lg={18} xs={16}>
                    <Text disabled>
                      {dayjs(dataLeadDetail?.updatedDate).format(FORMAT_DATE_TIME)}&nbsp;&nbsp;
                      {`${dataLeadDetail?.modifiedByObj?.username || ''} - ${dataLeadDetail?.modifiedByObj?.fullName || ''}`}
                    </Text>
                  </Col>
                </Row>
                <Row gutter={24}>
                  <Col lg={6} xs={8}>
                    <Text disabled>Ngày tạo: </Text>
                  </Col>
                  <Col lg={18} xs={16}>
                    <Text disabled>
                      {dayjs(dataLeadDetail?.createdDate).format(FORMAT_DATE_TIME)}&nbsp;&nbsp;
                      {`${dataLeadDetail?.createdByObj?.username || ''} - ${dataLeadDetail?.createdByObj?.fullName || ''}`}
                    </Text>
                  </Col>
                </Row>
                <Row gutter={24}>
                  <Col lg={6} xs={8}>
                    <Text disabled>Ngày Nhận: </Text>
                  </Col>
                  <Col lg={18} xs={16}>
                    <Text disabled>{dayjs(dataLeadDetail?.assignedDate).format(FORMAT_DATE_TIME)}</Text>
                  </Col>
                </Row>
              </div>
            </Col>
            {dataSurveys && dataSurveys.length > 0 && (
              <Col lg={12} xs={24}>
                <Title level={5} className="title-detail">
                  Nhu cầu quan tâm
                </Title>
                <SurveyQuestions data={dataSurveys} disabled={disabled || !isCheckTakeCare} />
              </Col>
            )}
          </Row>
        </Form>
        <ModalCreateCustomerFormLead
          defaultValue={initialData}
          isOpen={isOpenModalCreate}
          handleCancel={handleCancelModalCreate}
        />
      </Spin>
      {isPermissionDetail && !disabled && (
        <Space className="btn-group-action" size="small">
          {isModified && (
            <>
              <Button onClick={handleCancel}>Hủy</Button>
              <Button type="primary" onClick={handleSubmit}>
                Lưu thay đổi
              </Button>
            </>
          )}
          {isCheckTakeCare && (
            <>
              <Button type="primary" className="btn-convert" onClick={handleConvert}>
                Chuyển đổi
              </Button>
              <Button type="primary" danger onClick={handleReturn}>
                Trả về
              </Button>
            </>
          )}
        </Space>
      )}
    </div>
  );
};

export default DetailOfLeadCare;
