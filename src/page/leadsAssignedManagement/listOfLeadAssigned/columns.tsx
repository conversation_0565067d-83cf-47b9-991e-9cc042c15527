import { Tag, Typography } from 'antd';
import { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { EXPLOIT_STATUS_MAP, FORMAT_DATE_TIME } from '../../../constants/common';
import { LeadAssigned } from '../../../types/leadAssigned';
import { TRepo, TRepoConfig } from '../../../types/leadCommon';
import { formatEncryptPhone } from '../../../utilities/regex';

const { Text } = Typography;

export const columns: ColumnsType<LeadAssigned> = [
  {
    title: 'Mã Lead',
    dataIndex: 'code',
    key: 'code',
    width: 180,
    render: (value: string, record: LeadAssigned) =>
      value ? (
        <div className="cell-name">
          {record.isHot ? <Tag color="red">Hot</Tag> : null}
          {value}
        </div>
      ) : (
        '-'
      ),
  },
  {
    title: 'Nguồn lead',
    dataIndex: 'source',
    key: 'source',
    align: 'center',
    width: 200,
    render: value => (value ? value : '-'),
  },
  {
    title: '<PERSON>ho cấu hình',
    dataIndex: 'repo',
    key: 'repo',
    width: 200,
    render: (value: TRepo) => (value?.name ? value?.name : '-'),
  },
  {
    title: 'Cấu hình phân bổ',
    dataIndex: ['repo', 'config'],
    key: 'configs',
    width: 200,
    render: (value: TRepoConfig) => (value?.name ? value?.name : '-'),
  },
  {
    title: 'Họ và tên',
    dataIndex: 'name',
    key: 'name',
    width: 200,
    render: value => (value ? value : '-'),
  },
  {
    title: 'Số điện thoại',
    dataIndex: 'phone',
    key: 'phone',
    width: 150,
    render: (value, record: LeadAssigned) => {
      if (value) {
        return <> {record?.repo?.config?.visiblePhone ? formatEncryptPhone(value) : value}</>;
      } else {
        return '-';
      }
    },
  },
  {
    title: 'Nhân viên chăm sóc',
    dataIndex: ['takeCare', 'name'],
    key: 'takeCare',
    width: 200,
    render: value => (value ? <Tag>{value}</Tag> : '-'),
  },
  {
    title: 'Trạng thái',
    dataIndex: 'exploitStatus',
    key: 'exploitStatus',
    align: 'center',
    width: 180,
    render: (value: string) => {
      if (value) {
        const status = EXPLOIT_STATUS_MAP[value as keyof typeof EXPLOIT_STATUS_MAP];
        return <Text style={{ color: `${status?.color}` }}>{status?.label}</Text>;
      }
      return '-';
    },
  },
  {
    title: 'Ngày tạo',
    dataIndex: 'createdDate',
    key: 'createdDate',
    width: 180,
    render: (value: string, record: LeadAssigned) => (
      <>
        <Text>{value ? dayjs(value).format(FORMAT_DATE_TIME) : '-'}</Text> <br />
        <Text>{record?.createdByObj?.fullName ? record.createdByObj?.fullName : '-'}</Text>
      </>
    ),
  },
  {
    title: 'Ngày cập nhật',
    dataIndex: 'updatedDate',
    key: 'updatedDate',
    width: 180,
    render: (value: string, record: LeadAssigned) => (
      <>
        <Text>{value ? dayjs(value).format(FORMAT_DATE_TIME) : '-'}</Text> <br />
        <Text>{record?.modifiedByObj?.fullName ? record.modifiedByObj?.fullName : '-'}</Text>
      </>
    ),
  },
  {
    title: 'Ngày nhận',
    dataIndex: 'assignedDate',
    key: 'assignedDate',
    width: 180,
    render: (value: string) => (
      <>
        <Text>{value ? dayjs(value).format(FORMAT_DATE_TIME) : '-'}</Text>
      </>
    ),
  },
];
