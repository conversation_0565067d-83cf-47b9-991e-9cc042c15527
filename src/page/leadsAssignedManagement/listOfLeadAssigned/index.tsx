import { ColumnsType } from 'antd/es/table';
import { useState } from 'react';
import BreadCrumbComponent from '../../../components/breadCrumb';
import TableComponent from '../../../components/table';
import { ActionsColumns } from '../../../components/table/components/ActionsColumns';
import { PERMISSION_LEAD } from '../../../constants/permissions/lead';
import { useAuth } from '../../../context/AuthContext';
import { useFetch } from '../../../hooks';
import { getListOfLeadAssigned } from '../../../service/lead';
import { LeadAssigned } from '../../../types/leadAssigned';
import ModalDetailOfLead from '../component/modalDetailOfLead';
import FilterLeadAssigned from './FilterLeadAssigned';
import { columns } from './columns';
import { LEAD_ASSIGNED } from '../../../configs/path';

const LeadAssignedList = () => {
  const { hasAuthority } = useAuth();
  const isPermissionDetail = hasAuthority(PERMISSION_LEAD.getId);
  const [idDetail, setIdDetail] = useState('');
  const { data, isFetching } = useFetch<LeadAssigned[]>({
    api: getListOfLeadAssigned,
    queryKeyArrWithFilter: ['list-lead-assigned'],
  });

  const assignedLeads = data?.data?.data?.rows || [];

  const columnAction: ColumnsType<LeadAssigned> = [
    ...columns,
    ...(isPermissionDetail
      ? [
          {
            dataIndex: 'action',
            key: 'action',
            width: '100px',
            align: 'center' as const,
            render: (_: unknown, record: LeadAssigned) => {
              const openViewDetail = () => {
                setIdDetail(record?.id);
              };

              return (
                <ActionsColumns
                  handleViewDetail={record?.exploitStatus !== 'manual_deliver' ? openViewDetail : undefined}
                />
              );
            },
          },
        ]
      : []),
  ];

  const handleCloseModalDetail = () => {
    setIdDetail('');
  };

  return (
    <div className="list-lead-assigned">
      <BreadCrumbComponent />
      <div className="header-filter-lead-assigned" style={{ marginBottom: 16 }}>
        <FilterLeadAssigned />
      </div>
      <TableComponent
        columns={columnAction}
        queryKeyArr={['list-lead-assigned']}
        rowKey={'id'}
        dataSource={assignedLeads}
        loading={isFetching}
      />
      <ModalDetailOfLead
        id={idDetail}
        handleCancel={handleCloseModalDetail}
        urlRedirect={LEAD_ASSIGNED}
        queryKeyArr={['list-lead-assigned']}
      />
    </div>
  );
};

export default LeadAssignedList;
