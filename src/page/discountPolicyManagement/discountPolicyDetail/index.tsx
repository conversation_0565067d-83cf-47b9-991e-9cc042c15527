import React, { useState } from 'react';
import { Form } from 'antd';
import { useFetch, useUpdateField } from '../../../hooks';
import BreadCrumbComponent from '../../../components/breadCrumb';
import { DiscountPolicy } from '../../../types/discountPolicy';
import { getDetailDiscountPolicy, updateDiscountPolicy } from '../../../service/discountPolicy';
import { useParams } from 'react-router-dom';
import './styles.scss';
import DiscountPolicyForm from '../component/discountPolicyForm';

interface DiscountPolicyDetailProps {
  initialData?: DiscountPolicy;
}

const DiscountPolicyDetail: React.FC<DiscountPolicyDetailProps> = ({ initialData }) => {
  const { id } = useParams<{ id: string }>();
  const [form] = Form.useForm();
  const [resetUpload, setResetUpload] = useState(false);

  const { data, isFetching, refetch } = useFetch<DiscountPolicy>({
    queryKeyArr: ['get-detail-discount-policy'],
    api: () => id && getDetailDiscountPolicy(id || ''),
    enabled: !!id,
    cacheTime: 10,
  });
  const discountPolicy: DiscountPolicy | undefined = data?.data?.data || (initialData as DiscountPolicy);

  const { mutateAsync: update } = useUpdateField<DiscountPolicy>({
    keyOfListQuery: ['get-discount-policy'],
    keyOfDetailQuery: ['get-discount-policy'],
    apiQuery: updateDiscountPolicy,
    isMessageError: false,
    messageSuccess: 'Chỉnh sửa chính sách thành công',
  });

  const handleUpdate = async (values: DiscountPolicy) => {
    await update({ ...values, id: id });
  };

  return (
    <div className="discount-policy-detail">
      <BreadCrumbComponent titleBread={data?.data?.data?.name} />
      <DiscountPolicyForm
        form={form}
        onFinish={handleUpdate}
        isUpdate={true}
        resetUpload={resetUpload}
        setResetUpload={setResetUpload}
        initialValues={discountPolicy}
        isRefresh={refetch}
        loading={isFetching}
      />
    </div>
  );
};

export default DiscountPolicyDetail;
