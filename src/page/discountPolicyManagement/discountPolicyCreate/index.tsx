import { Form, Modal } from 'antd';
import { useCreateField } from '../../../hooks';
import { useCallback, useState } from 'react';
import ModalComponent from '../../../components/modal';
import { DiscountPolicy } from '../../../types/discountPolicy';
import { createDiscountPolicy } from '../../../service/discountPolicy';
import DiscountPolicyForm from '../component/discountPolicyForm';

interface DiscountPolicyModalProps {
  visible: boolean;
  onClose: () => void;
}

const DiscountPolicyModal = ({ visible, onClose }: DiscountPolicyModalProps) => {
  const [form] = Form.useForm();
  const [resetUpload, setResetUpload] = useState<boolean>(false);

  const { mutateAsync: create } = useCreateField<DiscountPolicy>({
    keyOfListQuery: ['get-discount-policy'],
    apiQuery: createDiscountPolicy,
    isMessageError: false,
    messageSuccess: 'Tạo mới chính sách thành công!',
  });

  const resetFormDiscountPolicy = useCallback(async () => {
    await form.resetFields();
    setResetUpload(true);
  }, [form]);

  const handleCancel = useCallback(async () => {
    if (form.isFieldsTouched()) {
      Modal.confirm({
        title: 'Xác nhận hủy',
        content: 'Dữ liệu chưa được lưu, bạn có chắc chắn muốn thoát khỏi trang không?',
        cancelText: 'Quay lại',
        onOk: async () => {
          await resetFormDiscountPolicy();
          onClose();
        },
        okButtonProps: { type: 'default' },
        cancelButtonProps: { type: 'primary' },
      });
    } else {
      await resetFormDiscountPolicy();
      onClose();
    }
  }, [form, onClose, resetFormDiscountPolicy]);

  const handleCreate = useCallback(
    async (values: DiscountPolicy) => {
      try {
        const resp = await create(values);
        if (resp?.data?.statusCode === '0' && resp?.data?.success) {
          await resetFormDiscountPolicy();
          onClose();
        }
      } catch (error) {
        console.error('Create error:', error);
      }
    },
    [create, onClose, resetFormDiscountPolicy],
  );

  return (
    <ModalComponent
      className="modal-discount-policy"
      title="Tạo mới chính sách chiết khấu"
      open={visible}
      onCancel={handleCancel}
      footer={null}
      destroyOnClose
    >
      <DiscountPolicyForm
        form={form}
        onFinish={handleCreate}
        isUpdate={false}
        resetUpload={resetUpload}
        setResetUpload={setResetUpload}
        loading={false}
      />
    </ModalComponent>
  );
};

export default DiscountPolicyModal;
