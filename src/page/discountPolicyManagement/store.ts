// src/stores/formReportStore.ts
import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

interface ProjectState {
  projectId?: string;
  setProjectId: (id: string) => void;
  clearFormData: () => void;
}

export const useProjectStore = create<ProjectState>()(
  persist(
    set => ({
      projectId: undefined,
      setProjectId: (id: string) => set({ projectId: id }),
      clearFormData: () => set({ projectId: undefined }),
    }),
    {
      name: 'project-store',
      storage: createJSONStorage(() => sessionStorage),
    },
  ),
);
