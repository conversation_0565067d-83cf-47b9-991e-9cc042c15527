import { DatePicker, Form, Row } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { FORMAT_DATE_API } from '../../../../../constants/common';
import useFilter from '../../../../../hooks/filter';
import DropdownFilterSearch from '../../../../../components/dropdown/dropdownFilterSearch';
import MultiSelectLazy from '../../../../../components/select/mutilSelectLazy';
import { getListStatusDiscountPolicy, getListStatusTicketPolicy } from '../../../../../service/discountPolicy';
import { getListProjectHistory } from '../../../../../service/uploadHistory';

type TFilter = {
  startDate?: string | Dayjs | null;
  endDate?: string | Dayjs | null;
  statuses?: string[];
  projectIds?: string[];
  updatedByIds?: string[];
  ticketStatuses?: string[];
};
const { RangePicker } = DatePicker;

function FilterSearch() {
  const { search } = useLocation();
  const [form] = Form.useForm();
  const params = useMemo(() => new URLSearchParams(search), [search]);
  const [filter, setFilter] = useFilter();
  const rangePickerRef = useRef<React.ComponentRef<typeof DatePicker.RangePicker>>(null);
  const [dates, setDates] = useState<[Dayjs | null, Dayjs | null]>([null, null]);

  const [initialValues, setInitialValues] = useState<TFilter>();
  const [isOpenFilter, setIsOpenFilter] = useState<boolean>(false);

  useEffect(() => {
    if (params) {
      setInitialValues({
        startDate: params.get('startDate') ? dayjs(params.get('startDate')) : '',
        endDate: params.get('endDate') ? dayjs(params.get('endDate')) : '',
        statuses: params.get('statuses') ? params.get('statuses')?.split(',') : [],
        projectIds: params.get('projectIds') ? params.get('projectIds')?.split(',') : [],
        ticketStatuses: params.get('ticketStatuses') ? params.get('ticketStatuses')?.split(',') : [],
        updatedByIds: params.get('updatedByIds') ? params.get('updatedByIds')?.split(',') : [],
      });
    }
  }, [params]);

  const handleSubmitFilter = (values: TFilter) => {
    const [startDate, endDate] = dates;
    const newImportFilter: Record<string, string> = {
      startDate: startDate ? startDate.format(FORMAT_DATE_API) : '',
      endDate: endDate ? endDate.format(FORMAT_DATE_API) : '',
      statuses: values?.statuses?.join(',') || '',
      projectIds: values?.projectIds?.join(',') || '',
      ticketStatuses: values?.ticketStatuses?.join(',') || '',
      page: '1',
    };
    setFilter({ ...filter, page: '1', ...newImportFilter });
    setIsOpenFilter(false);
  };
  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };
  const handleSelectStatusTicket = (values: unknown) => {
    const newStatusTicketFilter = (values as { value: string }[]).map(item => item.value);
    form.setFieldsValue({ ticketStatuses: newStatusTicketFilter });
  };
  const handleSelectStatus = (values: unknown) => {
    const newStatusFilter = (values as { value: string }[]).map(item => item.value);
    form.setFieldsValue({ statuses: newStatusFilter });
  };

  const handleSelectProject = (values: unknown) => {
    const newProjectFilter = (values as { value: string }[]).map(item => item.value);
    form.setFieldsValue({ projectIds: newProjectFilter });
  };

  const handleSelectUploader = (values: unknown) => {
    const newUploaderFilter = (values as { value: string }[]).map(item => item.value);
    form.setFieldsValue({ updatedByIds: newUploaderFilter });
  };

  const handleCalendarChange = useCallback(
    (value: [Dayjs | null, Dayjs | null] | null) => {
      // Khai báo hàm xử lý thay đổi ngày trên lịch, memoized bằng useCallback
      if (!value) {
        // Kiểm tra nếu không có giá trị (người dùng xóa ngày)
        if (dates[0] !== null || dates[1] !== null) {
          // Nếu dates hiện tại có ít nhất 1 giá trị
          setDates([null, null]); // Xóa cả hai ngày trong state
        }
        return; // Thoát hàm sau khi xử lý trường hợp null
      }

      const newStart = value[0]; // Gán ngày bắt đầu mới từ value
      const newEnd = value[1]; // Gán ngày kết thúc mới từ value

      if (
        (newStart?.isSame(dates[0], 'day') || (!newStart && !dates[0])) && // So sánh ngày bắt đầu mới với hiện tại hoặc cả hai null
        (newEnd?.isSame(dates[1], 'day') || (!newEnd && !dates[1])) // So sánh ngày kết thúc mới với hiện tại hoặc cả hai null
      ) {
        return; // Thoát nếu không có thay đổi, tránh cập nhật thừa
      }

      setDates([newStart, newEnd]); // Cập nhật state dates với giá trị mới
    },
    [dates],
  );

  const handleChangeRangePicker = useCallback(
    (value: [Dayjs | null, Dayjs | null] | null) => {
      // Khai báo hàm xử lý onChange của RangePicker
      if (!value) {
        // Kiểm tra nếu không có giá trị
        if (dates[0] !== null || dates[1] !== null) {
          // Nếu dates có giá trị
          setDates([null, null]); // Xóa dates về [null, null]
        }
        return; // Thoát hàm
      }

      const newStart = value[0]; // Lấy ngày bắt đầu mới
      const newEnd = value[1]; // Lấy ngày kết thúc mới

      if (
        (newStart?.isSame(dates[0], 'day') || (!newStart && !dates[0])) && // Kiểm tra ngày bắt đầu có thay đổi không
        (newEnd?.isSame(dates[1], 'day') || (!newEnd && !dates[1])) // Kiểm tra ngày kết thúc có thay đổi không
      ) {
        return; // Thoát nếu không thay đổi
      }

      setDates([newStart, newEnd]); // Cập nhật dates với giá trị mới
    },
    [dates],
  );

  const handleBlur = useCallback(
    (e: FocusEvent, index: 0 | 1) => {
      // Khai báo hàm xử lý blur của input
      const target = e.target as HTMLInputElement; // Lấy element gây ra sự kiện, ép kiểu thành input
      const value = target.value; // Lấy giá trị của input

      if (!value && dates[index] !== null) {
        // Nếu input rỗng và ngày tại index không null
        const newDates = [...dates] as [Dayjs | null, Dayjs | null]; // Tạo bản sao của dates
        newDates[index] = null; // Xóa ngày tại index

        setDates(newDates); // Cập nhật state dates
      }
    },
    [dates],
  );

  useEffect(() => {
    // Khai báo effect để thêm sự kiện blur
    const pickerNode = rangePickerRef.current?.nativeElement as HTMLElement | null; // Lấy DOM node của RangePicker
    const inputs = pickerNode?.querySelectorAll<HTMLInputElement>('input'); // Lấy tất cả input trong RangePicker

    if (inputs?.length === 2) {
      // Kiểm tra có đúng 2 input không
      const input0 = inputs[0]; // Lấy input ngày bắt đầu
      const input1 = inputs[1]; // Lấy input ngày kết thúc

      const blurHandler0 = (e: FocusEvent) => handleBlur(e, 0); // Tạo hàm xử lý blur cho input 0
      const blurHandler1 = (e: FocusEvent) => handleBlur(e, 1); // Tạo hàm xử lý blur cho input 1

      input0.addEventListener('blur', blurHandler0); // Thêm sự kiện blur cho input 0
      input1.addEventListener('blur', blurHandler1); // Thêm sự kiện blur cho input 1

      return () => {
        // Trả về cleanup function
        input0.removeEventListener('blur', blurHandler0); // Xóa sự kiện blur của input 0
        input1.removeEventListener('blur', blurHandler1); // Xóa sự kiện blur của input 1
      };
    }
  }, [handleBlur]); // Dependency array, effect chạy lại khi handleBlur thay đổi

  return (
    <>
      <DropdownFilterSearch
        rootClassName="wrapper-filter-discount-policy"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm theo mã, tên chính sách"
        showParams={true}
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        initialValues={initialValues}
        form={form}
        onClearFilters={() => {
          setDates([null, null]);
          handleSelectUploader([]);
          handleSelectProject([]);
          handleSelectStatus([]);
          const clearedValues = {
            startDate: '',
            endDate: '',
            statuses: [],
            projectIds: [],
            ticketStatuses: [],
          };

          setInitialValues(clearedValues); // Cập nhật initialValues
          form.setFieldsValue(clearedValues);
        }}
        extraFormItems={
          <>
            <Form.Item label="Dự án" name="projectIds">
              <MultiSelectLazy
                enabled={isOpenFilter}
                queryKey={['get-project']}
                apiQuery={getListProjectHistory}
                keysLabel={['name', 'code']}
                keysTag={['name', 'code']}
                handleListSelect={handleSelectProject}
                placeholder="Chọn dự án"
              />
            </Form.Item>
            <Form.Item label="Thời gian hiệu lực">
              <RangePicker
                ref={rangePickerRef}
                value={dates}
                onCalendarChange={handleCalendarChange}
                onChange={handleChangeRangePicker}
                allowClear
                placeholder={['Từ ngày', 'Đến ngày']}
                format="DD/MM/YYYY"
              />
            </Form.Item>
            <Form.Item label="Trạng thái duyệt" name="ticketStatuses">
              <MultiSelectLazy
                enabled={isOpenFilter}
                queryKey={['get-status-ticket']}
                apiQuery={getListStatusTicketPolicy}
                keysLabel={'name'}
                keysTag={'name'}
                handleListSelect={handleSelectStatusTicket}
                placeholder="Chọn trạng thái"
              />
            </Form.Item>
            <Form.Item label="Trạng thái chính sách" name="statuses">
              <MultiSelectLazy
                enabled={isOpenFilter}
                queryKey={['get-status']}
                apiQuery={getListStatusDiscountPolicy}
                keysLabel={'name'}
                keysTag={'name'}
                handleListSelect={handleSelectStatus}
                placeholder="Chọn trạng thái"
              />
            </Form.Item>

            <Row gutter={16}></Row>
          </>
        }
      />
    </>
  );
}
export default FilterSearch;
