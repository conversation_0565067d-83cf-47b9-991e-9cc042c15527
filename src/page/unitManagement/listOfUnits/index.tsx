import { Col, Row, TableColumnsType } from 'antd';
import InputSearch from '../../../components/input/InputSearch';
import TableComponent from '../../../components/table';
import { useFetch } from '../../../hooks';
import './styles.scss';
import BreadCrumbComponent from '../../../components/breadCrumb';
import { getListUnits } from '../../../service/units';
import { UNIT_MANAGEMENT } from '../../../configs/path';
import { useNavigate } from 'react-router-dom';
import { ListOfUnitsProvider } from './context/ListOfUnitsContext';
import { LEVEL_DVBH } from '../../../constants/common';
import { LevelDVBH, Units } from '../../../types/units/units';

const ListOfUnits = () => {
  const navigate = useNavigate();

  const { data, isLoading, isPlaceholderData } = useFetch<Units[]>({
    queryKeyArrWithFilter: ['get-units-management'],
    api: getListUnits,
  });

  const listUnit = data?.data?.data?.rows || [];

  const columns: TableColumnsType<Units> = [
    {
      title: 'Tên ĐVBH',
      dataIndex: 'nameVN',
      key: 'nameVN',
      width: '60%',
      render: (value, record: Units) => {
        return (
          <Row>
            <Col md={12} xs={24}>
              <p>{value}</p>
            </Col>
            {record.taxCode && (
              <Col md={12} xs={24}>
                <p className="color-MST">MST: {record.taxCode}</p>
              </Col>
            )}
          </Row>
        );
      },
    },
    {
      title: 'Loại ĐVBH',
      key: 'level',
      dataIndex: 'level',
      ellipsis: true,
      align: 'left',
      width: 100,
      render: (value: keyof LevelDVBH) => {
        return (
          <p>
            {value}. {LEVEL_DVBH[value]}
          </p>
        );
      },
    },
    {
      title: 'Mã ĐVBH',
      dataIndex: 'code',
      key: 'code',
      ellipsis: true,
      align: 'left',
      width: 150,
    },
    {
      title: 'Hành động',
      dataIndex: 'action',
      key: 'action',
      align: 'center',
      width: 100,
      render: (_, record) => <a onClick={() => navigate(`${UNIT_MANAGEMENT}/${record.id}`)}>Xem chi tiết</a>,
    },
  ];

  return (
    <ListOfUnitsProvider>
      <div>
        <BreadCrumbComponent />
        <div className="header-content">
          <InputSearch keySearch="search" />
        </div>
        <TableComponent
          queryKeyArr={['get-units-management']}
          columns={columns}
          dataSource={listUnit}
          loading={isLoading || isPlaceholderData}
        />
      </div>
    </ListOfUnitsProvider>
  );
};

export default ListOfUnits;
