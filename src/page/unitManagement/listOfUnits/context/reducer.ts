import { ListOfUnitsState } from './state';

//định nghĩa các action có thể thực hiện được đang để test, sửa lại theo yêu cầu
// payload là dữ liệu truyền vào để update state, đang để string để test
export type ListOfUnitsAction = { type: 'increment'; payload: string } | { type: 'decrement' };

export const reducer = (state: ListOfUnitsState, action: ListOfUnitsAction): ListOfUnitsState => {
  switch (action.type) {
    case 'increment':
      return state;
    default:
      return state;
  }
};
