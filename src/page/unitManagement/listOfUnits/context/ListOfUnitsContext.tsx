import React, { createContext, useReducer, ReactNode } from 'react';
import { reducer } from './reducer';
import { initialListOfUnitsState, ListOfUnitsState } from './state';

interface ListOfUnitsContextProps {
  state: ListOfUnitsState;
}

export const ListOfUnitsContext = createContext<ListOfUnitsContextProps | undefined>(undefined);

export const ListOfUnitsProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state] = useReducer(reducer, initialListOfUnitsState);

  return <ListOfUnitsContext.Provider value={{ state }}>{children}</ListOfUnitsContext.Provider>;
};
