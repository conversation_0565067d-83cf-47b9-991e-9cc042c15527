import { DetailsUnitsAction, DetailsUnitsState } from './state';

export const reducer = (state: DetailsUnitsState, action: DetailsUnitsAction): DetailsUnitsState => {
  switch (action.type) {
    case 'SAVE_CONFIGURATION_EMAIL':
      return {
        ...state,
        configurationEmail: [...state.configurationEmail, action.configurationEmail],
      };
    case 'SET_FORM_MAP':
      return {
        ...state,
        formMap: action.formMap,
      };
    default:
      return state;
  }
};
