import { FormInstance } from 'antd';

export interface DetailsUnitsState {
  tabsActive: string;
  formMap: { [key: string]: FormInstance | null };
  configurationEmail: ConfigurationEmail[];
}

export interface ConfigurationEmail {
  id: string;
  name: string;
  title: string;
  type: string;
  template: string;
  mailForm: string;
}

export const initialDetailsUnitsState: DetailsUnitsState = {
  tabsActive: '1',
  formMap: {
    '1': null,
    '2': null,
    '6': null,
  },
  configurationEmail: [],
};

export type DetailsUnitsAction =
  | { type: 'SAVE_CONFIGURATION_EMAIL'; configurationEmail: ConfigurationEmail }
  | { type: 'EDIT_CONFIGURATION_EMAIL'; configurationEmail: ConfigurationEmail }
  | { type: 'DELETE_CONFIGURATION_EMAIL'; configurationEmail: ConfigurationEmail }
  | { type: 'SET_FORM_MAP'; formMap: { [key: string]: FormInstance | null } };
