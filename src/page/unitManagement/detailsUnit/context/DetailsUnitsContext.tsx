import React, { createContext, useReducer, ReactNode, Dispatch } from 'react';
import { reducer } from './reducer';
import { initialDetailsUnitsState, DetailsUnitsState, DetailsUnitsAction } from './state';

interface DetailsUnitsContextProps {
  state: DetailsUnitsState;
  dispatch: Dispatch<DetailsUnitsAction>;
}

export const DetailsUnitsContext = createContext<DetailsUnitsContextProps | undefined>(undefined);

export const DetailsUnitsProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(reducer, initialDetailsUnitsState);

  return <DetailsUnitsContext.Provider value={{ state, dispatch }}>{children}</DetailsUnitsContext.Provider>;
};

export const UseDetailsUnitsContext = (): DetailsUnitsContextProps => {
  const context = React.useContext(DetailsUnitsContext);
  if (context === undefined) {
    throw new Error('useDetailsUnitsContext must be used within a DetailsUnitsProvider');
  }
  return context;
};
