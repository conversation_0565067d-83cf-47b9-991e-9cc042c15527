import { DetailsUnitsProvider } from './context/DetailsUnitsContext';
import { memo, useEffect, useReducer, useState } from 'react';
import { Form, Modal, Spin, Tabs } from 'antd';
import type { FormInstance, TabsProps } from 'antd';
import AddFieldTab from './components/addFieldTab';
import DetailsTab from './components/detailsTab';
import LegalInfoTab from './components/legalInfoTab';
import AffiliatedUnitsTab from './components/affiliatedUnitsTab';
import PartnerManagementTab from './components/partnerManagementTab';
import EmployeeManagementTab from './components/employeeManagementTab';
import { getDetailUnits, sendUpdateUnits } from '../../../service/units';
import { useFetch, useUpdateField } from '../../../hooks';
import { Units } from '../../../types/units/units';
import { useNavigate, useParams } from 'react-router-dom';
import PageLayoutDetail from '../../../layout/pageLayoutDetail/PageLayoutDetail';
import { UNIT_MANAGEMENT } from '../../../configs/path';
import { initialDetailsUnitsState } from './context/state';
import { reducer } from './context/reducer';

function DetailsUnits() {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const { id } = useParams();
  const [state, dispatch] = useReducer(reducer, initialDetailsUnitsState);
  const [isSaved, setIsSaved] = useState(false);

  const [formDetailsTab, setFormDetailsTab] = useState<FormInstance | null>(null);
  const [formLegalInfoTab, setFormLegalInfoTab] = useState<FormInstance | null>(null);
  const [formAddFieldTab, setFormAddFieldTab] = useState<FormInstance | null>(null);

  const [isFormChanged, setIsFormChanged] = useState(false);

  const handleFormChange = (changed: boolean) => {
    setIsFormChanged(changed);
  };

  const [activeTab, setActiveTab] = useState<string>('1');
  useEffect(() => {
    if (id) {
      setActiveTab('1');
    }
  }, [id]);

  const { data: detailUnitsData, isLoading } = useFetch<Units>({
    api: () => id && getDetailUnits(id),
    queryKeyArr: id ? ['units', id] : [],
    withFilter: false,
    enabled: !!id,
    cacheTime: 10,
  });
  const detailUnits = detailUnitsData?.data?.data;

  const update = useUpdateField({
    keyOfListQuery: ['get-units-management'],
    keyOfDetailQuery: ['units', id],
    label: 'Đơn vị hợp tác',
    apiQuery: sendUpdateUnits,
    messageSuccess: 'Chỉnh sửa thông tin thành công!',
  });

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: 'Thông tin chi tiết',
      children: <DetailsTab data={detailUnits} onFormInstanceChange={setFormDetailsTab} />,
    },
    {
      key: '2',
      label: 'Thông tin pháp lý',
      children: (
        <LegalInfoTab onFormChange={handleFormChange} data={detailUnits} onFormInstanceChange={setFormLegalInfoTab} />
      ),
    },
    {
      key: '3',
      label: 'Đơn vị trực thuộc',
      children: <AffiliatedUnitsTab />,
    },

    {
      key: '4',
      label: 'Danh sách đối tác',
      children: <PartnerManagementTab form={form} unitsData={detailUnits} />,
    },
    {
      key: '5',
      label: 'Quản lí nhân viên',
      children: <EmployeeManagementTab form={form} unitsData={detailUnits} />,
    },
    {
      key: '6',
      label: 'Thêm trường thông tin khác',
      children: (
        <AddFieldTab
          unitsData={detailUnits}
          onFormChange={handleFormChange}
          onFormInstanceChange={setFormAddFieldTab}
        />
      ),
    },
  ];
  const onChange = (key: string) => {
    setActiveTab(key);
  };
  const handleCancel = () => {
    if (!isSaved && isFormChanged) {
      Modal.confirm({
        title: 'Xác nhận hủy',
        content: 'Dữ liệu đang chưa được lưu, bạn có chắc muốn huỷ dữ liệu đang nhập không?',
        cancelText: 'Quay lại',
        onOk: () => {
          navigate(UNIT_MANAGEMENT);
        },
        okButtonProps: {
          type: 'default',
        },
        cancelButtonProps: {
          type: 'primary',
        },
      });
    } else {
      navigate(UNIT_MANAGEMENT);
    }
  };

  const handleSave = async () => {
    const activeForm = state.formMap[activeTab];

    if (activeForm) {
      const formValues = await activeForm.validateFields();
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      let payload: any = {
        id: id,
        type: 'update',
        logo: formValues?.logo,
      };
      if (activeTab !== '1') {
        payload = {
          ...payload,
          ...formValues,
        };
      }

      const res = await update.mutateAsync(payload);

      if (res?.data.statusCode === '0') {
        setIsSaved(true);
        setIsFormChanged(false);
      }
    }
  };

  useEffect(() => {
    const formMap = {
      '1': formDetailsTab,
      '2': formLegalInfoTab,
      '6': formAddFieldTab,
    };

    dispatch({
      type: 'SET_FORM_MAP',
      formMap,
    });
  }, [formDetailsTab, formLegalInfoTab, formAddFieldTab]);

  return (
    <div>
      <DetailsUnitsProvider>
        <PageLayoutDetail
          titleBreadCrumb={detailUnits?.nameVN}
          handleCancel={handleCancel}
          handleSubmit={handleSave}
          loadingSubmit={update.status === 'pending'}
          isEditableSubmit={['3', '4', '5'].includes(activeTab)}
        >
          <Spin spinning={isLoading || update.status === 'pending'}>
            <div className="unit-management_detail">
              <Tabs activeKey={activeTab} items={items} onChange={onChange} />
            </div>
          </Spin>
        </PageLayoutDetail>
      </DetailsUnitsProvider>
    </div>
  );
}

export default memo(DetailsUnits);
