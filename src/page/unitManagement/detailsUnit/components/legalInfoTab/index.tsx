import { Col, Form, FormInstance, Input, Row, Select, Typography } from 'antd';
import { useEffect, useState } from 'react';
import { Units } from '../../../../../types/units/units';
import './index.scss';
import dayjs from 'dayjs';
import MyDatePicker from '../../../../../components/datePicker';
import SelectAddress, { AddressType } from '../../../../../components/selectAddress';
import { useFetch } from '../../../../../hooks';
import { getBanks } from '../../../../../service/bank';
import { getProvinces } from '../../../../../service/address';
import { returnNumericValue } from '../../../../../components/validation';

const { Title } = Typography;
interface Bank {
  bankCode: string;
  bankName: string;
}
interface CommonFormProps {
  data?: Units;
  onFormInstanceChange?: (formInstance: FormInstance) => void;
  onFormChange?: (isFormChanged: boolean) => void;
}

const LegalInfoTab: React.FC<CommonFormProps> = ({ data, onFormInstanceChange, onFormChange }) => {
  const [form] = Form.useForm();
  const [selectedDate, setSelectedDate] = useState<dayjs.Dayjs | null>(null);
  const [bankList, setBankList] = useState<Bank[]>([]);
  const [visibleBankCount, setVisibleBankCount] = useState(20);
  const [isBankSelected, setIsBankSelected] = useState(false);

  const handleDateChange = (date: dayjs.Dayjs | null) => {
    setSelectedDate(date);
    form.setFieldsValue({ representIssuedDate: date });
  };

  const handleChangeAddress = () => {
    if (onFormChange) {
      onFormChange(true);
    }
  };
  const { data: dataBanks, isLoading } = useFetch<Bank[]>({
    queryKeyArrWithFilter: ['get-list-banks'],
    api: getBanks,
  });

  const { data: dataIdentity } = useFetch<AddressType[]>({
    queryKeyArrWithFilter: ['get-list-identity'],
    api: getProvinces,
  });
  const listIdentity = dataIdentity?.data?.data;

  useEffect(() => {
    setBankList(dataBanks?.data?.data || []);
  }, [dataBanks]);

  useEffect(() => {
    if (data) {
      form.setFieldsValue({
        ...data,
        representIssuedDate: data.representIssuedDate ? dayjs(data.representIssuedDate) : null,
      });
      const hasBankData = !!data.bank?.bankName;
      const hasBankInfo = !!data.bankInfo;
      setIsBankSelected(hasBankData || hasBankInfo);
    }
  }, [data, form]);

  useEffect(() => {
    if (onFormInstanceChange) {
      onFormInstanceChange(form);
    }
  }, [form, onFormInstanceChange]);

  const handleFormChange = () => onFormChange?.(true);

  const handleBankChange = (value: { value: string; label: string } | null) => {
    setIsBankSelected(!!value);
    form.setFieldsValue({
      bank: value ? { bankName: value.label } : null,
      bankInfo: value ? undefined : null,
    });
  };

  const handleScroll = (e: React.UIEvent<HTMLElement>) => {
    const target = e.target as HTMLElement;
    if (target.scrollTop + target.clientHeight === target.scrollHeight && visibleBankCount < bankList.length) {
      setVisibleBankCount(prev => prev + 20);
    }
  };

  return (
    <Form layout="vertical" form={form} onValuesChange={handleFormChange}>
      <Row gutter={48} className="container-legal-information">
        <Col span={12}>
          <Title level={5} className="title-legal-information">
            Thông tin pháp lý công ty
          </Title>
          <Row className="company-legal-information" gutter={24}>
            <Col span={12}>
              <Form.Item label="Tên công ty" name="branchName">
                <Input placeholder="Nhập thông tin tên công ty" maxLength={255} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Fax" name="contactFax">
                <Input placeholder="Nhập thông tin fax" maxLength={10} onKeyDown={returnNumericValue} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Giấy CNĐKDN số" name="certNumber">
                <Input placeholder="Nhập thông tin số CNĐKDN" maxLength={20} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Số điện thoại" name="contactPhone">
                <Input placeholder="Nhập thông tin số điện thoại" maxLength={15} onKeyDown={returnNumericValue} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Ngân hàng" name={['bank', 'bankName']}>
                <Select
                  placeholder="Chọn thông tin ngân hàng"
                  allowClear
                  filterOption={(input, option) =>
                    typeof option?.label === 'string' ? option.label.toLowerCase().includes(input.toLowerCase()) : false
                  }
                  showSearch
                  onChange={handleBankChange}
                  loading={isLoading}
                  onPopupScroll={handleScroll}
                  options={bankList.slice(0, visibleBankCount).map(item => ({
                    value: item.bankCode,
                    label: item.bankName,
                  }))}
                  labelInValue
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Số tài khoản"
                name="bankInfo"
                rules={[
                  {
                    required: isBankSelected,
                    message: 'Vui lòng nhập thông tin này',
                  },
                ]}
              >
                <Input placeholder="Nhập thông tin số tài khoản" maxLength={50} disabled={!isBankSelected} />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item className="input-address" label="Địa chỉ" name="addressObject">
                <SelectAddress
                  parentName="addressObject"
                  address={form.getFieldValue('addressObject')}
                  handleAddressChange={handleChangeAddress}
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item name="contactAddress">
                <Input placeholder="Nhập thông tin địa chỉ chi tiết" maxLength={255} />
              </Form.Item>
            </Col>
          </Row>
          <Title level={5} className="title-representative-information">
            Thông tin người đại diện
          </Title>
          <Row className="company-representative-information" gutter={24}>
            <Col span={12}>
              <Form.Item label="Đại diện bởi" name="representBy">
                <Input placeholder="Nhập thông tin tên người đại diện" maxLength={50} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Mã số thuế cá nhân" name="representTaxNumber">
                <Input placeholder="Nhập thông tin mã số thuế cá nhân" maxLength={20} onKeyDown={returnNumericValue} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Số điện thoại" name="representPhone">
                <Input placeholder="Nhập thông tin số điện thoại" maxLength={15} onKeyDown={returnNumericValue} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Email" name="representEmail" rules={[{ type: 'email', message: 'Email không hợp lệ' }]}>
                <Input placeholder="Nhập thông tin địa chỉ email" maxLength={50} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Số CMND / CCCD" name="representIDValue">
                <Input placeholder="Nhập thông tin CMND/CCCD" maxLength={12} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Ngày cấp CMND / CCCD" name="representIssuedDate">
                <MyDatePicker
                  onDateChange={handleDateChange}
                  value={selectedDate}
                  placeholder="Nhập thông tin ngày cấp CMND/CCCD"
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="Nơi cấp CMND / CCCD" name="representIssuedPlace">
                <Select
                  filterOption={(input, option) =>
                    typeof option?.label === 'string' ? option.label.toLowerCase().includes(input.toLowerCase()) : false
                  }
                  allowClear
                  options={listIdentity?.map(item => {
                    return {
                      value: item.code,
                      label: item.nameVN,
                    };
                  })}
                  labelInValue
                  showSearch
                  placeholder="Chọn nơi cấp CMND/CCCD"
                  onChange={value => {
                    form.setFieldsValue({
                      representIssuedPlace: value?.label,
                    });
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item className="input-address" label="Địa chỉ" name="representAddressObject">
                <SelectAddress
                  parentName="representAddressObject"
                  address={form.getFieldValue('representAddressObject')}
                  handleAddressChange={handleChangeAddress}
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item name="representContactAddress">
                <Input placeholder="Nhập thông tin địa chỉ chi tiết" maxLength={255} />
              </Form.Item>
            </Col>
          </Row>
        </Col>
      </Row>
    </Form>
  );
};

export default LegalInfoTab;
