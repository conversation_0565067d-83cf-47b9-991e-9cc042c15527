export interface OptionsPlaceOfIDentityCode {
  label: string;
  value: string;
}

export const OPTIONS_PLACEOFIDENTITYCODE: OptionsPlaceOfIDentityCode[] = [
  {
    label: 'Cục <PERSON>ảnh sát quản lý hành chính về trật tự xã hội',
    value: '<PERSON><PERSON>c <PERSON>ảnh sát quản lý hành chính về trật tự xã hội',
  },
  { label: '<PERSON><PERSON><PERSON>ồ sơ Cảnh sát', value: '<PERSON><PERSON><PERSON>ồ sơ Cảnh sát' },
  {
    label: 'Cục Cảnh sát ĐKQL cư trú và DLQG về dân cư',
    value: 'Cục Cảnh sát ĐKQL cư trú và DLQG về dân cư',
  },
  {
    label: 'Cục Quản lý xuất nhập cảnh',
    value: 'Cục Quản lý xuất nhập cảnh',
  },
  {
    label: 'CA. An Giang',
    value: 'CA. An Giang',
  },
  {
    label: 'CA. Bà Rịa Vũng Tàu',
    value: 'CA. Bà Rịa Vũng Tàu',
  },
  {
    label: 'CA. Bắc Giang',
    value: 'CA. Bắc Giang',
  },
  {
    label: 'CA. Bắc Kạn',
    value: 'CA. Bắc Kạn',
  },
  {
    label: 'CA. Bạc Liêu',
    value: 'CA. Bạc Liêu',
  },
  {
    label: 'CA. Bắc Ninh',
    value: 'CA. Bắc Ninh',
  },
  {
    label: 'CA. Bến Tre',
    value: 'CA. Bến Tre',
  },
  {
    label: 'CA. Bình Định',
    value: 'CA. Bình Định',
  },
  {
    label: 'CA. Bình Dương',
    value: 'CA. Bình Dương',
  },
  {
    label: 'CA. Bình Phước',
    value: 'CA. Bình Phước',
  },
  {
    label: 'CA. Bình Thuận',
    value: 'CA. Bình Thuận',
  },
  {
    label: 'CA. Cà Mau',
    value: 'CA. Cà Mau',
  },
  {
    label: 'CA. Cần Thơ',
    value: 'CA. Cần Thơ',
  },
  {
    label: 'CA. Cao Bằng',
    value: 'CA. Cao Bằng',
  },
  {
    label: 'CA. Đà Nẵng',
    value: 'CA. Đà Nẵng',
  },
  {
    label: 'CA. Đắk Lắk',
    value: 'CA. Đắk Lắk',
  },
  {
    label: 'CA. Đắk Nông',
    value: 'CA. Đắk Nông',
  },
  {
    label: 'CA. Điện Biên',
    value: 'CA. Điện Biên',
  },
  {
    label: 'CA. Đồng Nai',
    value: 'CA. Đồng Nai',
  },
  {
    label: 'CA. Đồng Tháp',
    value: 'CA. Đồng Tháp',
  },
  {
    label: 'CA. Gia Lai',
    value: 'CA. Gia Lai',
  },
  {
    label: 'CA. Hà Giang',
    value: 'CA. Hà Giang',
  },
  {
    label: 'CA. Hà Nam',
    value: 'CA. Hà Nam',
  },
  {
    label: 'CA. Hà Nội',
    value: 'CA. Hà Nội',
  },
  {
    label: 'CA. Hà Tĩnh',
    value: 'CA. Hà Tĩnh',
  },
  {
    label: 'CA. Hải Dương',
    value: 'CA. Hải Dương',
  },
  {
    label: 'CA. Hải Phòng',
    value: 'CA. Hải Phòng',
  },
  {
    label: 'CA. Hậu Giang',
    value: 'CA. Hậu Giang',
  },
  {
    label: 'CA. Hoà Bình',
    value: 'CA. Hoà Bình',
  },
  {
    label: 'CA. Hưng Yên',
    value: 'CA. Hưng Yên',
  },
  {
    label: 'CA. Khánh Hoà',
    value: 'CA. Khánh Hoà',
  },
  {
    label: 'CA. Kiên Giang',
    value: 'CA. Kiên Giang',
  },
  {
    label: 'CA. Lai Châu',
    value: 'CA. Lai Châu',
  },
  {
    label: 'CA. Lâm Đồng',
    value: 'CA. Lâm Đồng',
  },
  {
    label: 'CA. Lạng Sơn',
    value: 'CA. Lạng Sơn',
  },
  {
    label: 'CA. Lào Cai',
    value: 'CA. Lào Cai',
  },
  {
    label: 'CA. Long An',
    value: 'CA. Long An',
  },
  {
    label: 'CA. Nam Định',
    value: 'CA. Nam Định',
  },
  {
    label: 'CA. Kon Tum',
    value: 'CA. Kon Tum',
  },
  {
    label: 'CA. Nghệ An',
    value: 'CA. Nghệ An',
  },
  {
    label: 'CA. Ninh Bình',
    value: 'CA. Ninh Bình',
  },
  {
    label: 'CA. Ninh Thuận',
    value: 'CA. Ninh Thuận',
  },
  {
    label: 'CA. Phú Thọ',
    value: 'CA. Phú Thọ',
  },
  {
    label: 'CA. Phú Yên',
    value: 'CA. Phú Yên',
  },
  {
    label: 'CA. Quảng Bình',
    value: 'CA. Quảng Bình',
  },
  {
    label: 'CA. Quảng Nam',
    value: 'CA. Quảng Nam',
  },
  {
    label: 'CA. Quảng Ngãi',
    value: 'CA. Quảng Ngãi',
  },
  {
    label: 'CA. Quảng Ninh',
    value: 'CA. Quảng Ninh',
  },
  {
    label: 'CA. Quảng Trị',
    value: 'CA. Quảng Trị',
  },
  {
    label: 'CA. Sóc Trăng',
    value: 'CA. Sóc Trăng',
  },
  {
    label: 'CA. Sơn La',
    value: 'CA. Sơn La',
  },
  {
    label: 'CA. Tây Ninh',
    value: 'CA. Tây Ninh',
  },
  {
    label: 'CA. Thái Bình',
    value: 'CA. Thái Bình',
  },
  {
    label: 'CA. Thái Nguyên',
    value: 'CA. Thái Nguyên',
  },
  {
    label: 'CA. Thanh Hoá',
    value: 'CA. Thanh Hoá',
  },
  {
    label: 'CA. TP Hồ Chí Minh',
    value: 'CA. TP Hồ Chí Minh',
  },
  {
    label: 'CA. Thừa Thiên Huế',
    value: 'CA. Thừa Thiên Huế',
  },
  {
    label: 'CA. Tiền Giang',
    value: 'CA. Tiền Giang',
  },
  {
    label: 'CA. Trà Vinh',
    value: 'CA. Trà Vinh',
  },
  {
    label: 'CA. Tuyên Quang',
    value: 'CA. Tuyên Quang',
  },
  {
    label: 'CA. Vĩnh Long',
    value: 'CA. Vĩnh Long',
  },
  {
    label: 'CA. Vĩnh Phúc',
    value: 'CA. Vĩnh Phúc',
  },
  {
    label: 'CA. Yên Bái',
    value: 'CA. Yên Bái',
  },
];

export const listBankMock = [
  {
    id: 'a3850f6c-c4d1-4eba-85d2-fb45be10d9c7',
    bankName: 'NH TMCP Á Châu',
  },
  {
    id: 'b7423f9e-d5a2-4efa-8a3d-cd32adab7f66',
    bankName: 'NH TMCP Công Thương Việt Nam',
  },
  {
    id: 'c3541f7c-e6b4-4dbf-9b23-ab123fe98d7a',
    bankName: 'NH TMCP Ngoại Thương Việt Nam',
  },
  {
    id: 'd4752f8d-f7c5-4ac3-9b45-cd54fa76ae89',
    bankName: 'NH TMCP Sài Gòn Thương Tín',
  },
  {
    id: 'e5843f9a-g8d6-4edc-9c67-db76ab32cd90',
    bankName: 'NH TMCP Kỹ Thương Việt Nam',
  },
];
