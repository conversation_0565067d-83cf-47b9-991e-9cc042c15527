import { Col, Form, FormInstance, Input, Row } from 'antd';
import './index.scss';
import { useEffect } from 'react';
import { Units } from '../../../../../types/units/units';
import { LEVEL_DVBH } from '../../../../../constants/common';
import FormUploadImage from '../../../../../components/upload/FormUploadImage';

interface CommonFormProps {
  data?: Units;
  onFormInstanceChange?: (formInstance: FormInstance) => void;
}

const DetailsTab: React.FC<CommonFormProps> = ({ data, onFormInstanceChange }) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (onFormInstanceChange) {
      onFormInstanceChange(form);
    }
  }, [form, onFormInstanceChange]);

  useEffect(() => {
    if (data) {
      const allowedFields = ['nameVN', 'code', 'taxCode', 'level', 'parentName', 'logo'];
      const filteredData = Object.fromEntries(Object.entries(data).filter(([key]) => allowedFields.includes(key)));

      if (data.level !== undefined && LEVEL_DVBH[data.level] !== undefined) {
        filteredData.level = `${data.level}. ${LEVEL_DVBH[data.level]}`;
      }

      form.setFieldsValue(filteredData);
    }
  }, [data, form]);

  return (
    <div className="detail-tab-unit">
      <Form form={form}>
        <Row gutter={48}>
          <Col span={12}>
            <Row>
              <Col span={20}>
                <Form.Item required labelCol={{ span: 8 }} label="Tên đơn vị" name="nameVN">
                  <Input disabled />
                </Form.Item>
              </Col>
              <Col span={20}>
                <Form.Item required labelCol={{ span: 8 }} label="Mã đơn vị" name="code">
                  <Input disabled />
                </Form.Item>
              </Col>
              <Col span={20}>
                <Form.Item labelCol={{ span: 8 }} label="Mã số thuế" name="taxCode">
                  <Input disabled />
                </Form.Item>
              </Col>
              <Col span={20}>
                <Form.Item required labelCol={{ span: 8 }} label="Loại đơn vị" name="level">
                  <Input disabled />
                </Form.Item>
              </Col>
              <Col span={20}>
                <Form.Item labelCol={{ span: 8 }} label="Thuộc đơn vị" name="parentName">
                  <Input disabled />
                </Form.Item>
              </Col>
            </Row>
          </Col>
          <Col span={12} className="container-upload-image">
            <Row>
              <Col span={24}>
                <FormUploadImage
                  path="unit"
                  defaultImage={data?.logo}
                  reset={false}
                  fieldName={'logo'}
                  label="Ảnh đại diện"
                  fileSize={0.5}
                  labelCol={{ span: 24 }}
                />
              </Col>
            </Row>
          </Col>
        </Row>
      </Form>
    </div>
  );
};

export default DetailsTab;
