import React, { useEffect, useState } from 'react';
import { Form, Input, Button, Select, Space, Checkbox, Row, Col, FormInstance } from 'antd';
import { CloseOutlined, DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import './index.scss';
import { Other, Units } from '../../../../../types/units/units';

const { Option } = Select;

interface CommonFormProps {
  unitsData?: Units;
  onFormInstanceChange?: (formInstance: FormInstance) => void;
  onFormChange?: (isFormChanged: boolean) => void;
}

const DynamicForm: React.FC<CommonFormProps> = ({ unitsData, onFormInstanceChange, onFormChange }) => {
  const [form] = Form.useForm();

  const [selectedType, setSelectedType] = useState<{ [key: string]: string }>({});
  const [selectedRows, setSelectedRows] = useState<number[]>([]);

  // <PERSON><PERSON> lý thay đổi kiểu dữ liệu
  const handleTypeChange = (type: string, key: number) => {
    setSelectedType(prev => ({
      ...prev,
      [key]: type,
    }));
  };

  // Xử lý sự kiện khi Checkbox được thay đổi
  const handleCheckboxChange = (key: number, checked: boolean) => {
    if (checked) {
      setSelectedRows(prev => [...prev, key]);
    } else {
      setSelectedRows(prev => prev.filter(item => item !== key));
    }
  };
  const handleCheckboxAllChange = (checked: boolean) => {
    if (checked) {
      const newSelectedRows = Array.from({ length: form?.getFieldsValue()?.other?.length }, (_, index) => index);
      setSelectedRows(newSelectedRows);
    } else {
      setSelectedRows([]);
    }
  };

  // setdata vào form trước
  useEffect(() => {
    if (unitsData) {
      form.setFieldsValue({
        ...unitsData,
      });
    }
  }, [form, unitsData]);

  useEffect(() => {
    // lấy dữ liệu của form
    const fieldsValue = form?.getFieldsValue()?.other;
    // update selectedType theo fieldsValue
    const newSelectedType = fieldsValue?.reduce((acc: { [key: string]: string }, item: Other, index: number) => {
      acc[index] = item?.type;
      return acc;
    }, {});
    setSelectedType(newSelectedType);
  }, [form]);

  useEffect(() => {
    if (onFormInstanceChange) {
      onFormInstanceChange(form);
    }
  }, [form, onFormInstanceChange]);

  const isAllChecked = () => {
    if (form.getFieldsValue() && form.getFieldsValue().other && selectedRows.length > 0)
      return selectedRows.length === form.getFieldsValue().other.length;
    return false;
  };

  const handleFormChange = () => {
    if (onFormChange) {
      onFormChange(true);
    }
  };

  return (
    <div className="add-field_tab">
      <div className={'box-detail-units'}>
        <Form
          initialValues={unitsData}
          name="dynamic_form_nest_item"
          layout="vertical"
          form={form}
          onValuesChange={handleFormChange}
        >
          <div className={'box-main-wrap'}>
            <Row gutter={16} className={'box-main-items'}>
              <Col span={1}>
                <Checkbox onChange={e => handleCheckboxAllChange(e.target.checked)} checked={isAllChecked()} />
              </Col>
              <Col span={7}>
                <div className={'box-border-item'}>Type</div>
              </Col>
              <Col span={7}>
                <div className={'box-border-item'}>Main Key</div>
              </Col>
              <Col span={9}></Col>
            </Row>

            <Form.List name="other">
              {(fields, { add, remove }) => (
                <>
                  {fields?.map(({ key, name, ...restField }) => (
                    <Row key={key} gutter={[16, 16]} align="middle" className="box-items">
                      <Col span={1}>
                        <Checkbox
                          onChange={e => handleCheckboxChange(name, e.target.checked)}
                          checked={selectedRows.includes(name)}
                        />
                      </Col>
                      <Col span={7}>
                        <Form.Item
                          {...restField}
                          name={[name, 'type']}
                          rules={[{ required: true, message: 'Select type' }]}
                        >
                          <Select placeholder="Select type" onChange={type => handleTypeChange(type, key)}>
                            <Option value="Trường đơn">Trường đơn</Option>
                            <Option value="Mảng">Mảng</Option>
                            <Option value="Đối tượng">Đối tượng</Option>
                          </Select>
                        </Form.Item>
                      </Col>
                      <Col span={7}>
                        <Form.Item
                          {...restField}
                          name={[name, 'mainKey']}
                          rules={[{ required: true, message: 'Vui lòng nhập Key' }]}
                        >
                          <Input placeholder="Key" maxLength={50} />
                        </Form.Item>
                      </Col>
                      <Col span={9}>
                        {selectedType && selectedType[key] === 'Trường đơn' && (
                          <Form.Item
                            {...restField}
                            name={[name, 'value']}
                            rules={[{ required: true, message: 'Vui lòng nhập giá trị' }]}
                          >
                            <Input placeholder="Value" maxLength={255} />
                          </Form.Item>
                        )}

                        {selectedType && selectedType[key] === 'Mảng' && (
                          <Form.List name={[name, 'arrayData']}>
                            {(arrayFields, { add: addArrayField, remove: removeArrayField }) => (
                              <>
                                {arrayFields.map(({ name: arrayName, key: arrayKey, ...arrayRestField }) => (
                                  <Row gutter={[12, 12]} key={arrayKey}>
                                    <Col span={11}>
                                      <Form.Item
                                        {...arrayRestField}
                                        name={[arrayName, 'arrayValue']}
                                        rules={[{ required: true, message: 'Vui lòng nhập dữ liệu' }]}
                                      >
                                        <Input placeholder="Data input" maxLength={255} />
                                      </Form.Item>
                                    </Col>
                                    <Col span={2}>
                                      <CloseOutlined
                                        onClick={() => {
                                          removeArrayField(arrayName);
                                        }}
                                      />
                                    </Col>
                                  </Row>
                                ))}
                                <Row>
                                  <Col span={11}>
                                    <Form.Item>
                                      <Button
                                        style={{ width: '98%' }}
                                        type="dashed"
                                        onClick={() => {
                                          addArrayField();
                                        }}
                                        icon={<PlusOutlined />}
                                      >
                                        Thêm mảng giá trị
                                      </Button>
                                    </Form.Item>
                                  </Col>
                                </Row>
                              </>
                            )}
                          </Form.List>
                        )}

                        {selectedType && selectedType[key] === 'Đối tượng' && (
                          <Form.List name={[name, 'objectData']}>
                            {(objectFields, { add: addObjectField, remove: removeObjectField }) => (
                              <>
                                {objectFields?.map(({ name: objectName, key: objectKey, ...objectRestField }) => (
                                  <Row gutter={[12, 12]} key={objectKey}>
                                    <Col span={11}>
                                      <Form.Item
                                        {...objectRestField}
                                        name={[objectName, 'objectKey']}
                                        rules={[{ required: true, message: 'Vui lòng nhập Object key' }]}
                                      >
                                        <Input placeholder="Object key" maxLength={50} />
                                      </Form.Item>
                                    </Col>
                                    <Col span={11}>
                                      <Form.Item
                                        {...objectRestField}
                                        name={[objectName, 'objectValue']}
                                        rules={[{ required: true, message: 'Vui lòng nhập Object value' }]}
                                      >
                                        <Input placeholder="Object value" maxLength={255} />
                                      </Form.Item>
                                    </Col>
                                    <Col span={2}>
                                      <CloseOutlined
                                        onClick={() => {
                                          removeObjectField(objectName);
                                        }}
                                      />
                                    </Col>
                                  </Row>
                                ))}
                                <Row>
                                  <Col span={22}>
                                    <Form.Item>
                                      <Button
                                        style={{ width: '100%' }}
                                        type="dashed"
                                        onClick={() => addObjectField()}
                                        icon={<PlusOutlined />}
                                      >
                                        Thêm đối tượng
                                      </Button>
                                    </Form.Item>
                                  </Col>
                                </Row>
                              </>
                            )}
                          </Form.List>
                        )}
                      </Col>
                    </Row>
                  ))}

                  <Space className="box-main-handler">
                    <Button onClick={() => add()} className="border-button">
                      <PlusOutlined /> Thêm trường mới
                    </Button>
                    <Button
                      className="border-button"
                      type="primary"
                      danger
                      onClick={() => {
                        remove(selectedRows);
                        setSelectedRows([]);
                      }}
                      disabled={selectedRows.length === 0}
                    >
                      <DeleteOutlined /> Xóa
                    </Button>
                  </Space>
                </>
              )}
            </Form.List>
          </div>
        </Form>
      </div>
    </div>
  );
};

export default DynamicForm;
