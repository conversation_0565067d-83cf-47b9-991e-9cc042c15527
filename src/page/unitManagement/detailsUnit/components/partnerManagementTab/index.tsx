import { Col, FormInstance, Row, TableColumnsType, Typography } from 'antd';
import InputSearch from '../../../../../components/input/InputSearch';
import TableComponent from '../../../../../components/table';
import './index.scss';
import { useFetch } from '../../../../../hooks';
import { getListOrgPartner } from '../../../../../service/units';
import { Units } from '../../../../../types/units/units';
import { useState } from 'react';

interface CommonFormProps {
  form: FormInstance;
  unitsData?: Units;
}
const { Text } = Typography;
const PartnerManagementTab: React.FC<CommonFormProps> = ({ unitsData }) => {
  const [search, setSearch] = useState<string>('');

  const handleOnChangeSearch = (value: unknown) => {
    setSearch(value as string);
  };

  const { data: partnerManagement } = useFetch<Units[]>({
    queryKeyArrWithFilter: ['partner-management', unitsData?.id || '', search],
    api: getListOrgPartner,
    moreParams: { id: unitsData?.id, search: search },
  });

  const columns: TableColumnsType<Units> = [
    {
      title: 'Tên ĐVBH',
      key: 'partnershipName',
      dataIndex: 'partnershipName',
      width: '345px',
      render: (_, record) => {
        return (
          <Row className="groupNameUnitPartner">
            <Col xs={24}>
              <Text>{record?.partnershipName || '-'}</Text>
            </Col>
          </Row>
        );
      },
    },
    {
      dataIndex: 'taxCode',
      width: '243px',
      key: 'taxCode',
      render: (_, record) => {
        return (
          <Row className="groupNameUnitPartner">
            <Col xs={24}>
              <Text className="text-taxcode">MST: {record?.taxCode || '-'}</Text>
            </Col>
          </Row>
        );
      },
    },
    {
      title: 'Mã ĐVBH',
      width: '243px',
      dataIndex: 'partnershipCode',
      key: 'partnershipCode',
      render: (_, record) => {
        return (
          <Row className="groupNameUnitPartner">
            <Col xs={24}>
              <Text>{record?.partnershipCode || '-'}</Text>
            </Col>
          </Row>
        );
      },
    },

    {
      title: 'Người quản lý',
      dataIndex: 'lineManager',
      width: '264px',
      key: 'lineManager',
      render: (_, record) => {
        return (
          <Row className="groupNameUnitPartner">
            <Col md={12} xs={24}>
              <Text>{record?.lineManager || '-'}</Text>
            </Col>
          </Row>
        );
      },
    },
  ];
  return (
    <>
      <div className="partner-management_tab">
        <div className="header-content">
          <InputSearch showParams={false} onChange={handleOnChangeSearch} />
        </div>
        <TableComponent
          className="table-unit"
          queryKeyArr={['partner-management', unitsData?.id || '', search]}
          columns={columns}
          dataSource={partnerManagement?.data?.data?.rows}
        />
      </div>
    </>
  );
};

export default PartnerManagementTab;
