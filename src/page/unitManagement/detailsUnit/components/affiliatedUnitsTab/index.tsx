import { Col, Row, TableColumnsType } from 'antd';
import { useParams } from 'react-router-dom';
import { useFetch } from '../../../../../hooks';
import { getListAffiliatedUnits } from '../../../../../service/units';
import { LEVEL_DVBH } from '../../../../../constants/common';
import InputSearch from '../../../../../components/input/InputSearch';
import TableComponent from '../../../../../components/table';
import { LevelDVBH, Units } from '../../../../../types/units/units';

const AffiliatedUnitsTab = () => {
  const { id } = useParams();

  const { data, isLoading, isPlaceholderData } = useFetch<Units[]>({
    queryKeyArrWithFilter: ['get-units-affiliated-management', id],
    api: getListAffiliatedUnits,
    moreParams: { id: id },
  });

  const listUnit = data?.data?.data?.rows || [];

  const columns: TableColumnsType<Units> = [
    {
      title: 'Tên ĐVBH',
      dataIndex: 'nameVN',
      key: 'nameVN',
      width: '60%',
      render: (value, record: Units) => {
        return (
          <Row>
            <Col md={12} xs={24}>
              <p>{value}</p>
            </Col>
            {record.taxCode && (
              <Col md={12} xs={24}>
                <p className="color-MST">MST: {record.taxCode}</p>
              </Col>
            )}
          </Row>
        );
      },
    },
    {
      title: 'Loại ĐVBH',
      key: 'level',
      dataIndex: 'level',
      ellipsis: true,
      align: 'left',
      width: '20%',
      render: (value: keyof LevelDVBH) => {
        return (
          <p>
            {value}. {LEVEL_DVBH[value]}
          </p>
        );
      },
    },
    {
      title: 'Mã ĐVBH',
      dataIndex: 'code',
      key: 'code',
      ellipsis: true,
      align: 'left',
      width: '20%',
    },
  ];

  return (
    <div>
      <div className="header-content">
        <InputSearch keySearch="search" />
      </div>
      <TableComponent
        queryKeyArr={['get-units-affiliated-management', id ? id : '']}
        columns={columns}
        dataSource={listUnit}
        loading={isLoading || isPlaceholderData}
      />
    </div>
  );
};

export default AffiliatedUnitsTab;
