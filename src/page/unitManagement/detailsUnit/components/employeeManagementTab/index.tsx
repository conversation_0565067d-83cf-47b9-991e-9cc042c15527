import { Col, FormInstance, Row, Select, TableColumnsType, Tag, Typography } from 'antd';
import { useFetch } from '../../../../../hooks';
import { getListPersonalStaff } from '../../../../../service/units';
import { Employee, Units } from '../../../../../types/units/units';
import './index.scss';
import InputSearch from '../../../../../components/input/InputSearch';
import TableComponent from '../../../../../components/table';
import { useState } from 'react';

const { Text, Title } = Typography;
const { Option } = Select;
interface CommonFormProps {
  form: FormInstance;
  unitsData?: Units;
}
const EmployeeManagementTab: React.FC<CommonFormProps> = ({ unitsData }) => {
  const [search, setSearch] = useState<string>('');

  const handleOnChangeSearch = (value: unknown) => {
    setSearch(value as string);
  };
  const { data: employeeManagement } = useFetch<Employee[]>({
    queryKeyArrWithFilter: ['employee-management', unitsData?.id || '', search],
    api: getListPersonalStaff,
    moreParams: { id: unitsData?.id, search: search },
  });

  const columns: TableColumnsType<Employee> = [
    {
      title: 'Nhân viên',
      key: 'name',
      dataIndex: 'name',
      width: '176px',
      render: (_, record) => {
        return (
          <>
            <Row className="groupNameUnitPartner">
              <Col xs={24}>
                <Text>{record?.name || '-'}</Text>
              </Col>
            </Row>
            <Row className="groupNameUnitPartner">
              <Col xs={24}>
                <Text className="text-email">{record?.email || '-'}</Text>
              </Col>
            </Row>
          </>
        );
      },
    },

    {
      title: 'Chức danh',
      width: '279px',
      dataIndex: 'jobTitleName',
      key: 'jobTitleName',
      render: (_, record) => {
        return (
          <Row className="groupNameUnitPartner">
            <Col xs={24}>
              <Text>{record?.jobTitleName || '-'}</Text>
            </Col>
          </Row>
        );
      },
    },
    {
      title: 'Mã nhân viên',
      dataIndex: 'code',
      width: '148px',
      key: 'code',
      render: (_, record) => {
        return (
          <Row className="groupNameUnitPartner">
            <Col xs={24}>
              <Text>{record?.code || '-'}</Text>
            </Col>
          </Row>
        );
      },
    },
    {
      title: 'Đơn vị trực thuộc',
      dataIndex: 'nameVN',
      width: '179px',
      key: 'nameVN',
      render: (_, record) => {
        return (
          <Row className="groupNameUnitPartner">
            <Col xs={24}>
              <Text>{record?.nameVN || '-'}</Text>
            </Col>
          </Row>
        );
      },
    },

    {
      dataIndex: 'isLineManager',
      align: 'center',
      width: '117px',
      key: 'status',
      render: value => (value ? <Tag color="green">Quản lý</Tag> : ''),
    },
  ];

  return (
    <>
      <div className="employee-management_tab">
        <div style={{ marginBottom: 40, marginTop: 33 }}>
          <Title style={{ marginBottom: 20 }} level={5}>
            Quản lý bởi
          </Title>
          <Row>
            <Col xs={12}>
              <Select value={unitsData?.lineManager} style={{ width: 500 }} disabled>
                <Option value={unitsData?.lineManager}>{unitsData?.lineManager}</Option>
              </Select>
            </Col>
          </Row>
        </div>
        <div style={{ marginBottom: 24 }}>
          <Title style={{ marginBottom: '20px' }} level={5}>
            Nhân viên trực thuộc đơn vị
          </Title>
          <div className="header-content">
            <InputSearch showParams={false} onChange={handleOnChangeSearch} />
          </div>
        </div>
        <TableComponent
          className="table-unit"
          queryKeyArr={['employee-management', unitsData?.id || '', search]}
          columns={columns}
          dataSource={employeeManagement?.data?.data?.rows}
        />
      </div>
    </>
  );
};

export default EmployeeManagementTab;
