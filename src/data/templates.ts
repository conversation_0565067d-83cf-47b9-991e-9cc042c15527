export type Template = {
  id: string;
  name: string;
  value: string;
};

export const templates: Array<Template> = [
  {
    id: '1',
    name: '<PERSON><PERSON><PERSON> hình Email Template',
    value: `
  <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<b><PERSON><PERSON><PERSON> gửi:</b> <PERSON><PERSON><PERSON> {{name}}
<p>Công ty cổ phần đầu tư kinh doanh Bất Đông Sản Hà An xin gửi Quý <PERSON>hách Hàng thông tin thanh toán như sau:
<p>-	Mã Thanh toán : {{code}}
<p>-	<PERSON><PERSON> tiền cần thanh toán &nbsp;&nbsp;: {{amount}} VNĐ ({{amountWord}})
<p>-	Nội dung thanh toán	&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;: {{content}}
<p>
<p><PERSON><PERSON> thực hiện thanh toán, <PERSON><PERSON><PERSON> Hàng vui lòng thực hiện theo các b<PERSON><PERSON>c nh<PERSON> sau:
<p><b>Bước 1:</b> <PERSON><PERSON><PERSON> c<PERSON><PERSON> đ<PERSON> link: <a href="{{url}}">Link</a>
<p>Hoặc quét mã QR:
<p> <img src="{{qrCodeUrl}}">
<p><b>Bước 2:</b>  Chọn hình thức thanh toán.
<p><b>Bước 3:</b>  Nhập thông tin thanh toán.
<p><b>Bước 4:</b> Hoàn tất thanh toán.
<p>Biên lai thanh toán sẽ được gửi tới cho Khách Hàng theo địa chỉ email đã đăng ký.
<p>
<p>Trong trường hợp cần hỗ trợ thêm thông tin, Quý Khách Hàng vui lòng liên hệ :
<p>Phòng Kế toán:
<p>
<p>Kính chúc Quý Khách Hàng và gia đình thật nhiều sức khỏe, hạnh phúc và thành công.
<p>Trân trọng.`,
  },
  {
    id: '2',
    name: 'Cấu hình Email thanh toán thành công',
    value: `
  <div class="header">
  <div>
  <table style="height: 40px; width: 100%; border-collapse: collapse; border: 1px solid white;">
  <tbody>
  <tr style="height: 40px;">
  <td style="width: 73.0114%; height: 40px; border: 1px solid white;">
  <p><em><strong>Công ty CP Dịch Vụ Bất Động Sản Đất Xanh</strong></em></p>
  <p><em>2W, Ung Văn Khiêm, Phường 25, Quận Bình Thạnh, TP. Hồ Chí Minh</em></p>
  <p><em>ĐT: <strong>02862525252</strong></em></p>
  </td>
  <td style="width: 26.9886%; height: 40px;"><img style="float: left;" src="https://dxs-o2o-static.s3-ap-southeast-1.amazonaws.com/1629857886807_logo.png" alt="" width="172" height="64"></td>
  </tr>
  </tbody>
  </table>
  </div>
  </div>
  <table style="height: 80px; width: 100%; border-collapse: collapse; border: 1px solid white;">
  <tbody>
  <tr style="height: 80px;">
  <td style="width: 73.0114%; height: 80px; text-align: center;">
  <h2><strong>PHIẾU XÁC NHẬN CHUYỂN KHOẢN</strong></h2>
  <p><em>Ngày {{receiptDate.day}} tháng {{receiptDate.month}} năm {{receiptDate.year}}</em></p>
  </td>
  <td style="width: 26.9886%; border: 1px solid white; height: 80px;">
  <p><em><sub>Số: {{receiptNum}}</sub></em></p>
  <p><em><sub>Nợ: 121100 &nbsp; &nbsp; &nbsp; &nbsp; {{transferedMoney}}</sub></em></p>
  <p><em><sub>Có: 338870 &nbsp; &nbsp; &nbsp; &nbsp; {{transferedMoney}}</sub></em></p>
  </td>
  </tr>
  </tbody>
  </table>
  <div class="body">
  <div>
  <div>
  <ul>
  <li><strong>Người nộp: </strong> <span style="text-transform: uppercase;">{{payer}}</span></li>
  <li><strong>Địa chỉ: </strong>{{address}}</li>
  <li><strong>Nội dung: </strong>{{ description }}</li>
  <li><strong>Số tiền: </strong>{{ transferedMoney }} <strong>đồng</strong></li>
  <li><strong>Viết bằng chữ: </strong>{{ transferedMoneyString}}.</li>
  </ul>
  </div>
  </div>
  </div>
  <div>
  <div>&nbsp;</div>
  </div>
  <div>
  <div><em>Cám ơn Quý khách đã tin chọn dịch vụ của DatXanh Services (DXS)</em></div>
  <div><em>Trường hợp Quý khách cần nhận bảng cứng Phiếu xác nhận chuyển khoản này, vui lòng liên hệ:</em></div>
  <div><em>Phòng Kế Toán DXS - Lầu 8, 2W Ung Văn Khiêm, Phường 25, Q. Bình Thạnh, TP. HCM.</em></div>
  <div><em>(Vì lý do an toàn phòng chống dịch covid, vui lòng ưu tiên giao dịch online và hạn chế tiếp xúc trực tiếp).</em></div>
  </div>`,
  },
  {
    id: '3',
    name: 'Cấu hình Email tòa nhà',
    value: `
  <p>Kính gửi Quý anh/chị: <b>{{name}}</b><p/>
<p>
<p>Xin trân trọng thông báo, Quý anh/chị đã được tạo tài khoản Prop Care thành công với các thông tin như sau:
<p>
<p>- Tài khoản: <b>{{phone}}</b>
<p>- Mật khẩu: <b>{{password}}</b>
<p>
<p>Vui lòng đăng nhập vào địa chỉ <a href="{{link}}">{{link}}</a> và đổi mật khẩu tài khoản của anh/chị.
<p><font color="#ff0000">Xin lưu ý: Prop Care là phần mềm dùng để cán bộ nhân viên khai báo y tế khi vào tòa nhà DXO.</font></p>
<p>Trong trường hợp cần thêm thông tin chi tiết, Quý anh/chị vui lòng liên hệ Tổng đài {{hotline}} hoặc gửi email tới hòm thư {{supportMail}} để được tư vấn và hỗ trợ.
<p>
<p>Xin chân thành cảm ơn.`,
  },
  {
    id: '4',
    name: 'Cấu hình Email chúc mừng NVKD',
    value: `
  <p>Thân gửi: Tư vấn viên <b>{{name}}</b><p/>
    <p>
    <p>Chúc mừng Anh/Chị đã hoàn thành đăng ký tư vấn thành công sản phẩm Dự án: {{projectName}}, Mã sản phẩm {{propertyUnitCode}} cho khách hàng {{customerName}}
    <p>
    <p>Để kiểm tra thông tin chi tiết, Anh/Chị vui lòng đăng nhập hệ thống <a href="https://realagent.vn/">https://realagent.vn</a></b>
    <p>Trân trọng,
    <p>Phòng Dịch Vụ Khách Hàng DXS</p>
    <p>Bộ Phận Chăm Sóc Khách Hàng</p>`,
  },
];
