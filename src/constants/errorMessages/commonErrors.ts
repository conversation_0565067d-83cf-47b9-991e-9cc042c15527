export const commonErrors: Record<number | string, { message: string; description: string }> = {
  '400': {
    message: 'Bad Request',
    description: '',
  },
  COME0001: {
    message: "We're busy at the moment. Please try again later",
    description: '',
  },
  COME0002: {
    message: 'No data',
    description: '',
  },
  COME0003: {
    message: 'Forbidden',
    description: '',
  },
  COME0004: {
    message: 'Unauthorized',
    description: '',
  },
  COME0006: {
    message: 'User access denied',
    description: '',
  },
  COME0005: {
    message: 'Data is duplicated',
    description: '',
  },
  COME0007: {
    message: 'Mã đã tồn tại trên hệ thống',
    description: '',
  },
  COME0008: {
    message: 'Tên đã tồn tại trên hệ thống',
    description: '',
  },
  COME0009: {
    message: 'Date From cannot be greater than Date To',
    description: '',
  },
  COME0010: {
    message: '<PERSON><PERSON> điện thoại đã tồn tại trên hệ thống',
    description: '',
  },
  COME0011: {
    message: '<PERSON><PERSON><PERSON><PERSON> tìm thấy mã của sàn',
    description: '',
  },

  // orgchart
  ORGE0001: {
    message: 'The name of the business cooperation unit already exists',
    description: '',
  },
  ORGE0002: {
    message: 'The code of the business cooperation unit already exists',
    description: '',
  },
  ORGE0003: {
    message: 'The tax code of the business cooperation unit already exists',
    description: '',
  },
  ORGE0004: {
    message: 'Orgchart not found',
    description: '',
  },
  ORGE0005: {
    message: 'Please remove the list of affiliated employees before disabling the unit',
    description: '',
  },
  ORGE0006: {
    message: 'Please remove all affiliated employees before deleting the unit',
    description: '',
  },
  SPROGRAM0006: {
    message: 'Đã tồn tại đơn vị trong CTBH, vui lòng kiểm tra!',
    description: '',
  },

  // sts
  STSE0001: {
    message: 'Role name is duplicated',
    description: '',
  },
  STSE0002: {
    message: 'Role code is duplicated',
    description: '',
  },
  STSE0003: {
    message: 'Role not found',
    description: '',
  },
  STSE0004: {
    message: 'Role ID format is invalid',
    description: '',
  },
  STSE0005: {
    message: 'Account ID does not exist',
    description: '',
  },
  STSE0006: {
    message: "Account haven't any roles",
    description: '',
  },
  STSE0007: {
    message: "Account haven't any permissions to access other account info",
    description: '',
  },
  STSE0008: {
    message: 'Tên nhóm vai trò đã tồn tại!',
    description: '',
  },
  // project
  PRJE0001: {
    message: 'Mã dự án đã tồn tại!',
    description: '',
  },
  PRJE0002: {
    message: 'Mã dự án không tồn tại',
    description: '',
  },
  PRJE0003: {
    message: 'Block code is duplicated',
    description: '',
  },
  PRJE0004: {
    message: "Project code is not editable due to project type 'investment'",
    description: '',
  },
  PRJE0005: {
    message: 'Missing value Project code',
    description: '',
  },
  PRJE0006: {
    message: 'Missing value Block code',
    description: '',
  },
  PRJE0007: {
    message: 'Duplicate floor display name',
    description: '',
  },
  PRJE0008: {
    message: 'Block ID does not exist',
    description: '',
  },
  PRJE0009: {
    message: 'Tên dự án đã tồn tại!',
    description: '',
  },
  PRJE0010: {
    message: 'Investor not found',
    description: '',
  },
  POLI0002: {
    message: 'Tên chính sách đã tồn tại',
    description: '',
  },

  // masterdata
  MTDE0001: {
    message: 'Folder not found',
    description: '',
  },
  MTDE0002: {
    message: 'File name exist',
    description: '',
  },
  MTDE0003: {
    message: 'Investor not found',
    description: '',
  },
  MTDE0004: {
    message: 'Investor code is duplicated',
    description: '',
  },
  MTDE0005: {
    message: 'The default folder is not deleted',
    description: '',
  },

  PRDCT0001: {
    message: 'Product code is duplicated',
    description: '',
  },
  PRDCT0002: {
    message: 'Product ID does not exist',
    description: '',
  },
  PRDCT0003: {
    message: 'Product location not found on row table',
    description: '',
  },
  PRDCT0004: {
    message: 'No file uploaded',
    description: '',
  },
  PRDCT0005: {
    message: 'Uploaded file is not an Excel file',
    description: '',
  },

  // Employee
  EMPE0001: {
    message: 'Employee code is duplicated',
    description: '',
  },

  // Customer
  DCUSE0001: {
    message: 'Số điện thoại đẫ tồn tài trong hệ thống',
    description: '',
  },
  DCUSE0002: {
    message: 'No customer found',
    description: '',
  },
  DCUSE0003: {
    message: 'Khách hàng đang có giao dịch, không thể vô hiệu hóa',
    description: '',
  },
  DCUSE0004: {
    message: 'Không chỉnh sửa thông tin khách hàng tiềm năng đã lên khách hàng chính thức',
    description: '',
  },
  DCUSE0005: {
    message: 'The maximum number of banks must not exceed 10',
    description: '',
  },
  DCUSE0006: {
    message: 'File của bạn không đúng với template yêu cầu',
    description: '',
  },
  DCUSE0007: {
    message: 'Chỉ được (thao tác: xóa/ vô hiệu) khi khách hàng ở trạng thái Mới',
    description: '',
  },

  // lead repo
  LREPOE0001: {
    message: 'Cấu hình Lead đang được sử dụng',
    description: '',
  },
  LREPOE0002: {
    message: 'Tên kho đã tồn tại',
    description: '',
  },
  LEADE0003: {
    message: 'Bạn không có quyền cập nhật thông tin lead này',
    description: '',
  },
  LEADE0005: {
    message: 'Không tìm thấy nhân viên chăm sóc nào trong cấu hình phân bổ được chỉ định',
    description: '',
  },

  // investor
  INVTOR0001: {
    message: 'Chủ đầu tư đang được gắn với dự án, không thể xóa chủ đầu tư này',
    description: '',
  },

  // sales program
  SPROGRAM0001: {
    message: 'Mã chương trình đã tồn tại',
    description: '',
  },
  SPROGRAM0002: {
    message: 'Tên chương trình đã tồn tại',
    description: '',
  },
  SPROGRAM0003: {
    message: 'Không tìm thấy chương trình',
    description: '',
  },
  SPROGRAM0004: {
    message: 'Đơn vị bán hàng không thuộc dự án',
    description: '',
  },

  SPROGRAM0009: {
    message: 'CTBH không thỏa mãn điều kiện!',
    description: '',
  },

  BLOCK0003: {
    message: 'Không tìm thấy block',
    description: '',
  },

  //lead source
  LEADS0001: {
    message: 'Nguồn Lead đã tồn tại',
    description: '',
  },
  LEADS0002: {
    message: 'Không tìm thấy nguồn Lead',
    description: '',
  },
  LEADS0003: {
    message: '"Nguồn lead đang được sử dụng',
    description: '',
  },
  // sales policy
  SALEPOLICY0001: {
    message: 'Chính sách Phí-Hoa hồng không tồn tại',
    description: '',
  },
  SALEPOLICY0002: {
    message: 'Mã chính sách Phí-Hoa hồng đã tồn tại',
    description: '',
  },
  SALEPOLICY0003: {
    message: 'Không thể thay đổi trạng thái hoặc xóa chính',
    description: '',
  },
  SALEPOLICY0004: {
    message: 'Mã chính sách bán hàng không được để trống',
    description: '',
  },
  SALEPOLICY0005: {
    message: 'Danh sách dự án không được để trống',
    description: '',
  },
  SALEPOLICY0006: {
    message: 'Ngày bắt đầu không được để trống',
    description: '',
  },

  // policy
  POLI0005: {
    message: 'Chính sách đã tồn tại số',
    description: '',
  },
  POLI0009: {
    message: 'Chính sách chưa có số tờ trình',
    description: '',
  },
  POLI0008: {
    message: 'Tờ trình đã được tạo',
    description: '',
  },
  POLI0010: {
    message: 'Tờ trình không khớp với loại tờ trình',
    description: '',
  },
  EAPPPROPOSAL0001: {
    message: 'Lỗi khi call tới eapp',
    description: '',
  },
  POLI0007: {
    message: 'Gửi tờ trình thất bại',
    description: '',
  },

  // project form
  FORMFILE0001: {
    message: 'Tên tệp mẫu không được trùng nhau',
    description: '',
  },
  FORMFILE0002: {
    message: 'Tệp mẫu không tồn tại',
    description: '',
  },
  FILETEMPLATE0001: {
    message: 'Tệp mẫu không đúng định dạng!',
    description: '',
  },

  PRJORGCHARTUSING0001: {
    message: 'Đơn vị sử dụng không thuộc dự án',
    description: '',
  },

  //commision policy
  COMMISSIONPOLICY0001: { message: 'Ngày không hợp lệ', description: '' },
  COMMISSIONPOLICY0002: { message: 'Tỷ lệ hoa hồng không được để trống', description: '' },
  COMMISSIONPOLICY0003: {
    message: 'Không bỏ trống các trường Giá trị trong bảng Tỷ lệ hoa hồng',
    description: '',
  },

  TRANSFORMPROPERTYUNIT0001: {
    message: 'ĐVBH không tồn tại trong CTBH, vui lòng kiểm tra',
    description: '',
  },

  TRANSACTION0002: {
    message: 'Số tiền vượt quá số tiền đăng ký, vui lòng kiểm tra!',
    description: '',
  },

  TRANSACTION0003: { message: 'ID giao dịch không tồn tại!', description: '' },
  TRANSACTION0004: { message: 'Giao dịch đã phê duyệt', description: '' },

  // commission period

  PERIOD0002: {
    message:
      'Đã tồn tại một cấu hình mặc định đang kích hoạt. Vui lòng vô hiệu hóa cấu hình hiện tại trước khi kích hoạt cấu hình này.',
    description: '',
  },
  PERIOD0003: {
    message: 'Đơn vị đã tồn tại trong một cấu hình đang hoạt động',
    description: '',
  },
  PERIOD0004: {
    message: 'Hiện tại đang không có cấu hình thời gian kỳ phí/hoa hồng',
    description: '',
  },

  // handover
  HANDOVER0001: { message: 'Bàn giao đã tồn tại', description: '' },
  HANDOVER0002: { message: 'Vui lòng cấu hình khung thời gian bàn giao', description: '' },
  HANDOVER0003: { message: 'Tên hạng mục không được để trống', description: '' },
  HANDOVER0004: { message: 'Thiết lập bàn giao không tồn tại', description: '' },
  HANDOVER0005: { message: 'Ngày bắt đầu hoặc ngày kết thúc dự kiến không hợp lệ', description: '' },
  HANDOVER0006: { message: 'Số điện thoại đường dây nóng chỉ được chứa số', description: '' },
  HANDOVER0007: {
    message: 'Dự án không tồn tại',
    description: '',
  },
  //commission period
  COMME0015: {
    message: 'Tệp tải lên không có cùng định dạng với tệp mẫu',
    description: '',
  },

  PROPRIMTX0009: {
    message: 'Nhân viên không được đính kèm với bất kỳ tài khoản nào, vui lòng kiểm tra lại!',
    description: '',
  },
  PROPRIMTX0005: {
    message: 'Đã tồn tại phiếu YCDCO. Vui lòng chọn mã YCDCHO khác!',
    description: '',
  },
  PROPRIMTX0006: {
    message: 'Trạng thái đơn vị tài sản không hợp lệ hoặc đã có ưu tiên!',
    description: '',
  },
  PRJE0015: {
    message: 'Đã tồn tại phiếu YCDCO. Vui lòng chọn mã YCDCHO khác!',
    description: '',
  },

  PRIMARYCONTRACT0006: {
    message: ' Đã tồn tại hợp đồng. Vui lòng chọn mã YCĐC khác!',
    description: '',
  },
  PRIMARYCONTRACT0001: {
    message: 'Không tìm thấy thiết lập bàn giao.',
    description: '',
  },
  PRIMARYCONTRACT0002: {
    message: ' Dự án không tồn tại!',
    description: '',
  },
  PRIMARYCONTRACT0003: {
    message: ' Trạng thái bàn giao không đúng.',
    description: '',
  },
  PRIMARYCONTRACT0004: {
    message: 'Hợp đồng không tồn tại!',
    description: '',
  },
  PRIMARYCONTRACT0005: {
    message: ' YCĐC không tồn tại!',
    description: '',
  },
  PRIMARYCONTRACT0007: {
    message: ' Chính sách chiết khấu không tồn tại!',
    description: '',
  },
  PRIMARYCONTRACT0008: {
    message: ' Loại hình bất động sản trong chính sách chiết khấu phải là nhà hoặc đất.',
    description: '',
  },
  PRIMARYCONTRACT0009: {
    message: ' Loại hình bất động sản trong chính sách chiết khấu phải là mặc định.',
    description: '',
  },
  PRIMARYCONTRACT0010: {
    message: ' Chính sách thanh toán không tồn tại!',
    description: '',
  },
  PRIMARYCONTRACT0012: {
    message: 'Đã tồn tại biên bản thanh lý. Vui lòng chọn hợp đồng khác.',
    description: '',
  },

  PRIMARYCONTRACT0013: {
    message: 'Không tìm thấy thanh lý hợp đồng.',
    description: '',
  },
  PRIMARYCONTRACT0014: {
    message: 'Hợp đồng cọc đã được tạo hợp đồng thuê/ mua bán. Vui lòng kiểm tra lại.',
    description: '',
  },
  PRIMARYCONTRACT0015: {
    message: 'Trạng thái thanh lý không hợp lệ.',
    description: '',
  },
  PRIMARYCONTRACT0016: {
    message: 'Trạng thái hiện tại không hợp lệ.',
    description: '',
  },
  PRIMARYCONTRACT0017: {
    message: 'Trạng thái hợp đồng không hợp lệ.',
    description: '',
  },
  PRIMARYCONTRACT0018: {
    message: 'Mã hợp đồng không chính xác.',
    description: '',
  },
  PRIMARYCONTRACT0019: {
    message: 'Trạng thái của giao dịch không hợp lệ',
    description: '',
  },
  PRIMARYCONTRACT0020: {
    message: 'Phiếu đặt cọc đã tồn tại. Vui lòng kiểm tra lại.',
    description: '',
  },
  PRIMARYCONTRACT0021: {
    message: 'Không thể thay đổi sang chính sách thanh toán hiện tại',
    description: '',
  },
  //Training
  TRAINING0001: {
    message: 'Bạn không có quyền thực hiện chức năng này',
    description: '',
  },
  TRAINING0002: {
    message: 'Đơn vị bán hàng đã tồn tại. Vui lòng kiểm tra lại',
    description: '',
  },
  TRAINING0003: {
    message: 'Link sự kiện đã tồn tại. Vui lòng kiểm tra lại',
    description: '',
  },

  EVOUCHER0001: { message: 'Không tìm thấy eVoucher', description: '' },
  EVOUCHER0002: { message: 'Mã e-voucher đã tồn tại', description: '' },
  EVOUCHER0003: { message: 'Số lượng Evoucher của đối tác áp dụng vượt quá hạn mức', description: '' },
  EVOUCHER0004: { message: 'Số lượng Evoucher đã sử dụng vượt quá hạn mức', description: '' },
  EVOUCHER0005: {
    message: 'Không thể cập nhật vì trạng thái áp dụng không phải là Chờ duyệt',
    description: '',
  },
  EVOUCHER0006: { message: 'Số tiền tối đa được giảm không được để trống', description: '' },
  EVOUCHER0007: { message: 'Trạng thái phê duyệt không hợp lệ để áp dụng Evoucher', description: '' },
  EVOUCHER0008: { message: 'EVoucher hiện đang không hoạt động', description: '' },
  EVOUCHER0009: { message: 'Giá trị được giảm không được để trống', description: '' },
  EVOUCHER0010: { message: 'Số lượng evoucher áp dụng cho đơn vị không được để trống', description: '' },
  EVOUCHER0011: { message: 'Vui lòng chọn đối tượng áp dụng', description: '' },
  EVOUCHER0012: { message: 'Nhân viên kinh doanh áp dụng Evoucher không được để trống', description: '' },
  EVOUCHER0013: { message: 'Số lượng áp dụng cho nhân viên không được để trống', description: '' },
  EVOUCHER0014: { message: 'Nhân viên xác nhận không được để trống', description: '' },
  EVOUCHER0015: { message: 'Đơn vị sử dụng không được để trống', description: '' },
  EVOUCHER0016: { message: 'Nhân viên xác nhận không được để trống', description: '' },

  TRAINING0004: { message: 'Tên sheet không hợp lệ. Vui lòng kiểm tra lại', description: '' },
  TRAINING0005: { message: 'Email đã tồn tại. Vui lòng kiểm tra lại', description: '' },
  TRAINING0006: { message: 'Email không được để trống', description: '' },
  TRAINING0007: { message: 'Training not exist', description: '' },

  PROUNIT0012: {
    message: 'Chưa đủ thời gian đăng ký lại sản phẩm này, vui lòng thử lại sau!',
    description: '',
  },
  POLI0011: {
    message: 'Tên đợt thanh toán không được trùng. Vui lòng kiểm tra lại!',
    description: '',
  },
  POLI0012: {
    message: 'Tên đợt thanh toán không hợp lệ: "Khác". Vui lòng kiểm tra lại!',
    description: '',
  },

  //debt penalty interest
  DEBTPEN0001: {
    message:
      'Đã tồn tại đợt thanh toán phát sinh lãi trên hợp đồng, vui lòng đóng các đợt thanh toán đã phát sinh lãi trước khi chỉnh sửa chính sách',
    description: '',
  },

  // commission period management
  FEE0001: {
    message: 'Không thể xóa các giao dịch đang được phê duyệt',
    description: '',
  },
};
