export const PERMISSION_LEAD = {
  create: [['lead', 'create']],
  update: [['lead', 'update']],
  delete: [['lead', 'delete']],
  pull: [['lead', 'pull']],
  reject: [['lead', 'reject']],
  fail: [['lead', 'fail']],
  process: [['lead', 'process']],
  unprocess: [['lead', 'unprocess']],
  assign: [['lead', 'assign']],
  pending: [['lead', 'pending']],
  primaryViewMenu: [['lead', 'primary', 'view', 'menu']],
  viewMenu: [['lead', 'view', 'menu']],
  commonViewMenu: [['lead', 'common', 'view', 'menu']],
  manualDeliver: [['lead', 'manual', 'deliver']],
  revokeDeliver: [['lead', 'revoke', 'deliver']],
  getLeadEventStream: [['lead', 'get', 'lead', 'eventstream']],
  getAllLeadHistory: [['lead', 'get', 'all', 'lead', 'history']],
  getAllLead: [['lead', 'get', 'all', 'lead']],
  getAllByPos: [['lead', 'get', 'all', 'by', 'pos']],
  getId: [['lead', 'get', 'id']],
  getProcessByEmployee: [['lead', 'get', 'process', 'by', 'employee']],
  import: [['lead', 'import']],
  report: [['lead', 'report']],
  reportByImporter: [['lead', 'report', 'by', 'importer']],
  reportByEmp: [['lead', 'report', 'by', 'emp']],
  reportViewAll: [['lead', 'report', 'view', 'all']],
  reportViewMenu: [['lead', 'report', 'view', 'menu']],
  getAllLeadAdvising: [['lead', 'get', 'all', 'lead', 'advising']],
  repositoryManage: [['lead', 'repository', 'manage']],
  configCreate: [['lead', 'config', 'create']],
  configUpdate: [['lead', 'config', 'update']],
  configGetAll: [['lead', 'config', 'get', 'all']],
  configGetId: [['lead', 'config', 'get', 'id']],
  configDelete: [['lead', 'config', 'delete']],
  configChangeStatus: [['lead', 'config', 'changeStatus']],
  getAllSuperAdmin: [['lead', 'get', 'all', 'super', 'admin']],
  getAllCompany: [['lead', 'get', 'all', 'company']],
  getAllUnit: [['lead', 'get', 'all', 'unit']],
};
