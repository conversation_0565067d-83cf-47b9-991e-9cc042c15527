export const PERMISSION_TRAINING = {
  create: [['training', 'create']],
  update: [['training', 'update']],
  delete: [['training', 'delete']],
  getAll: [['training', 'get', 'all']],
  getId: [['training', 'get', 'id']],
  viewHistory: [['training', 'view', 'history']],
  getChatAndReact: [['training', 'get', 'chat', 'and', 'react']],
  getCountParticipantsDialing: [['training', 'get', 'count', 'participants', 'dialing']],
  createUrlLive: [['training', 'create', 'url', 'live']],
  eventPrizesDownloadImportFile: [['training', 'event', 'prizes', 'download', 'import', 'file']],
  eventChatAndReactDownloadImportFile: [['training', 'event', 'chat', 'and', 'react', 'download', 'import', 'file']],
  register: [['training', 'register']],
  game: [['training', 'game']],
  getPrizes: [['training', 'get', 'prizes']],
  qrCodeCheckin: [['training', 'qr', 'code', 'checkin']],
};

export const PERMISSION_TRAINING_CHAT = {
  create: [['chat', 'create']],
  update: [['chat', 'update']],
  delete: [['chat', 'delete']],
  getAll: [['chat', 'get', 'all']],
  getId: [['chat', 'get', 'id']],
  pinned: [['chat', 'pinned']],
};

export const PERMISSION_TRAINING_REACT = {
  create: [['react', 'create']],
  update: [['react', 'update']],
  delete: [['react', 'delete']],
  getAll: [['react', 'get', 'all']],
  getId: [['react', 'get', 'id']],
};
export const PERMISSION_TRAINING_SETTING = {
  create: [['setting', 'create']],
  update: [['setting', 'update']],
  delete: [['setting', 'delete']],
  getAll: [['setting', 'get', 'all']],
  getId: [['setting', 'get', 'id']],
};

export const PERMISSION_TRAINING_USER = {
  create: [['user', 'create']],
  update: [['user', 'update']],
  delete: [['user', 'delete']],
  deleteMany: [['user', 'delete', 'many']],
  deleteManyGuestAll: [['user', 'delete', 'many', 'guest', 'all']],
  getAll: [['user', 'get', 'all']],
  getId: [['user', 'get', 'id']],
  getEvent: [['user', 'get', 'event']],
  getAllRegisterEvent: [['user', 'get', 'all', 'register', 'event']],
  getCountAllAttendGetEvent: [['user', 'get', 'count', 'all', 'attend', 'get', 'event']],
  attendGetEvent: [['user', 'attend', 'get', 'event']],
  attendDownloadImportFile: [['user', 'attend', 'download', 'import', 'file']],
  downloadImportFile: [['user', 'download', 'import', 'file']],
  importFile: [['user', 'import', 'file']],
  resendEmailEvent: [['user', 'resend', 'email', 'event']],
  unMaskInfo: [['user', 'un', 'mask', 'info']],
  getGuestByOrgChart: [['user', 'get', 'guest', 'by', 'orgchart']],
  getListGuest: [['user', 'get', 'list', 'guest']],
  downloadGuestList: [['user', 'download', 'guest', 'list']],
  saveAll: [['user', 'save', 'all']],
};
