// Commission Permissions
export const PERMISSION_COMMISSION = {
  publicGetIndividual: [['commission', 'public', 'get', 'individual']],
  publicGetGroup: [['commission', 'public', 'get', 'group']],
  publicGetAll: [['commission', 'public', 'get', 'all']],
  create: [['commission', 'create']],
  update: [['commission', 'update']],
  delete: [['commission', 'delete']],
  getAll: [['commission', 'get', 'all']],
  getById: [['commission', 'get', 'id']],
  publish: [['commission', 'publish']],
  publishGetId: [['commission', 'publish', 'get', 'id']],
  listCreate: [['commission', 'list', 'create']],
  listUpdate: [['commission', 'list', 'update']],
  listDelete: [['commission', 'list', 'delete']],
  listGetId: [['commission', 'list', 'get', 'id']],
  listGetAll: [['commission', 'list', 'get', 'all']],
  listImport: [['commission', 'list', 'import']],
  calculategdkd: [['commission', 'list', 'calculategdkd']],
  policyCreate: [['commission', 'policy', 'create']],
  policyUpdate: [['commission', 'policy', 'update']],
  policyDelete: [['commission', 'policy', 'delete']],
  policyClone: [['commission', 'policy', 'clone']],
  policyGetAll: [['commission', 'policy', 'get', 'all']],
  policyGetId: [['commission', 'policy', 'get', 'id']],
  policyGet: [['commission', 'policy', 'get']],
  viewHistory: [['commission', 'view', 'history']],
  downloadImport: [['commission', 'download', 'import', 'file']],
  viewSalePolicy: [['commission', 'view', 'sale', 'policy', 'menu']],
  approve: [['commission', 'approve']],
  periodGetAll: [['period', 'get', 'all']],
  periodGetId: [['period', 'get']],
  periodCreate: [['period', 'create']],
  periodUpdate: [['period', 'update']],
  periodDelete: [['period', 'delete']],
  periodChangeStatus: [['period', 'update', 'status']],
};

// Expense Permissions
export const PERMISSION_EXPENSE = {
  create: [['expense', 'list', 'create']],
  update: [['expense', 'list', 'update']],
  delete: [['expense', 'list', 'delete']],
  getId: [['expense', 'list', 'get', 'id']],
  import: [['expense', 'list', 'import']],
  getAll: [['expense', 'list', 'get', 'all']],
};

// Sales (Policy) Permissions
export const PERMISSION_SALES_POLICY = {
  create: [['sales', 'policy', 'create']],
  changeStatus: [['sales', 'policy', 'changeStatus']],
  clone: [['sales', 'policy', 'clone']],
  sync: [['sales', 'policy', 'sync']],
  import: [['sales', 'policy', 'import']],
  get: [['sales', 'policy', 'get']],
  delete: [['sales', 'policy', 'delete']],
  update: [['sales', 'policy', 'update']],
  exportTemplate: [['sales', 'policy', 'exportTemplate']],
  getAll: [['sales', 'policy', 'get', 'all']],
  getById: [['sales', 'policy', 'get', 'id']],
};

// Indicator Permissions
export const PERMISSION_INDICATOR = {
  create: [['indicator', 'create']],
  update: [['indicator', 'update']],
  delete: [['indicator', 'delete']],
  clone: [['indicator', 'clone']],
  changeStatus: [['indicator', 'changeStatus']],
  import: [['indicator', 'import']],
  sync: [['indicator', 'sync']],
  get: [['indicator', 'get']],
  getAll: [['indicator', 'get', 'all']],
  getById: [['indicator', 'get', 'id']],
  listGet: [['indicator', 'list', 'get']],
  listCreate: [['indicator', 'list', 'create']],
  listUpdate: [['indicator', 'list', 'update']],
  listDelete: [['indicator', 'list', 'delete']],
  listGetAll: [['indicator', 'list', 'get', 'all']],
  listGetId: [['indicator', 'list', 'get', 'id']],
};

// Report Permissions
export const PERMISSION_COMMISSION_REPORT = {
  get: [['report', 'get']],
  getAll: [['report', 'get', 'all']],
};
