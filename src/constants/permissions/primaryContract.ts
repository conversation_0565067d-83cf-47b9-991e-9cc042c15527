export const PRIMARY_CONTRACT_PERMISSION = {
  create: [['primary', 'contract', 'create']],
  update: [['primary', 'contract', 'update']],
  delete: [['primary', 'contract', 'delete']],
  getAll: [['primary', 'contract', 'get', 'all']],
  getId: [['primary', 'contract', 'get', 'id']],
  get: [['primary', 'contract', 'get']],
  updateApprovement: [['primary', 'contract', 'update', 'approvement']],
  viewMenu: [['primary', 'contract', 'view', 'menu']],
  getDeptReport: [['primary', 'contract', 'get', 'dept', 'report']],
  exportDeptReport: [['primary', 'contract', 'export', 'dept', 'report']],
  dxgGetAll: [['primary', 'contract', 'dxg', 'get', 'all']],
  requestApproved: [['primary', 'contract', 'request', 'approved']],
  approve: [['primary', 'contract', 'approve']],
  viewContract: [['primary', 'view', 'contract']],
  viewTransaction: [['primary', 'view', 'transaction']],
  handoverCreate: [['primary', 'contract', 'handover', 'create']],
  handoverUpdate: [['primary', 'contract', 'handover', 'update']],
  handoverChangeStatus: [['primary', 'contract', 'handover', 'changeStatus']],
  handoverDelete: [['primary', 'contract', 'handover', 'delete']],
  handoverGetAll: [['primary', 'contract', 'handover', 'get', 'all']],
  handoverGetId: [['primary', 'contract', 'handover', 'get', 'id']],
  handoverScheduleApprove: [['primary', 'contract', 'handover', 'schedule', 'approve']],
  handoverScheduleReject: [['primary', 'contract', 'handover', 'schedule', 'reject']],
  handoverScheduleGetAll: [['primary', 'contract', 'handover', 'schedule', 'get', 'all']],
  handoverScheduleGetId: [['primary', 'contract', 'handover', 'schedule', 'get', 'id']],
  handoverScheduleCreate: [['primary', 'contract', 'handover', 'schedule', 'create']],
  handoverScheduleUpdate: [['primary', 'contract', 'handover', 'schedule', 'update']],
  handoverScheduleDelete: [['primary', 'contract', 'handover', 'schedule', 'delete']],
  handoverScheduleChangeStatus: [['primary', 'contract', 'handover', 'schedule', 'changeStatus']],
  import: [['primary', 'contract', 'import']],
};
