{"name": "crm-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port=3000 --mode development", "start:dev": "vite --port=3000 --mode development", "build": "tsc -b && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "format": "prettier --write .", "prepare": "husky"}, "dependencies": {"@ant-design/icons": "^5.4.0", "@stomp/stompjs": "^7.1.1", "@fullcalendar/core": "^6.1.17", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/list": "^6.1.17", "@fullcalendar/react": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@tanstack/react-query": "^5.51.15", "@tanstack/react-query-devtools": "^5.55.4", "@tsparticles/engine": "^3.5.0", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.5.0", "@types/react-router-dom": "^5.3.3", "antd": "5.19.3", "antd-img-crop": "^4.25.0", "axios": "^1.7.2", "bignumber.js": "^9.1.2", "dayjs": "1.11.13", "emoji-picker-react": "^4.12.2", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "pdfjs-dist": "4.8.69", "process": "^0.11.10", "rc-virtual-list": "^3.18.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-pdf": "9.2.1", "react-quill": "^2.0.0", "react-router-dom": "^6.25.1", "react-window": "^1.8.11", "socket.io-client": "2.3.1", "sockjs-client": "^1.6.1", "uuid": "^11.0.3", "xlsx": "^0.18.5", "yarn": "^1.22.22", "zustand": "^5.0.3"}, "devDependencies": {"@originjs/vite-plugin-federation": "^1.3.5", "@tanstack/eslint-plugin-query": "^5.51.15", "@types/file-saver": "^2.0.7", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.7", "@types/node": "^22.0.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/react-window": "^1.8.8", "@types/sockjs-client": "^1.5.4", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^7.15.0", "@typescript-eslint/parser": "^7.15.0", "@vitejs/plugin-react": "^4.3.1", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "husky": "^9.1.2", "lint-staged": "^15.2.7", "prettier": "^3.3.3", "sass": "1.69.5", "sass-loader": "^13.3.2", "typescript": "5.2.2", "vite": "^5.3.4"}, "lint-staged": {"**/*.{js,jsx,ts,tsx,json,css,scss,md}": "prettier --write"}}