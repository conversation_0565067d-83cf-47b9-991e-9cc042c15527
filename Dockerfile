
FROM node:22.12.0 as build

WORKDIR /src

COPY ["package.json", "yarn.lock*", "./"]

RUN yarn install

COPY . ./

RUN yarn build

FROM nginx:1.27.3-alpine-slim


WORKDIR /var/www/html

# Copy built artifacts

COPY --from=build /src/dist /usr/share/nginx/html
COPY --from=build /src/nginx/nginx.conf /etc/nginx/conf.d/default.conf

COPY --from=build /src/dist /usr/share/nginx/html

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
